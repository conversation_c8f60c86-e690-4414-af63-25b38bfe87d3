//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.8-b130911.1802 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.05.19 at 05:20:23 PM IST 
//

package com.ascent.custumize.query;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="query" type="{}query" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}integer" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "query" })
@XmlRootElement(name = "queries")
public class Queries implements Serializable {

	/**
	 * 
	 */
	@XmlTransient
	private static final long serialVersionUID = -5991170716519824546L;
	protected List<Query> query;
	@XmlAttribute(name = "id")
	protected int id;

	@XmlTransient
	private Map<String, Query> queryMap = new HashMap<String, Query>();

	public Queries() {

	}

	public void bootConf() throws Exception {
		if (query != null && !query.isEmpty()) {
			for (Query qry : query) {
				qry.bootConf();
				queryMap.put(qry.getName(), qry);
			}
		}

	}

	public Query getQueryConf(String qryname) throws Exception {
		Query qryConf = queryMap.get(qryname);
		if (qryConf == null) {
			throw new Exception("No query configured with the name " + qryname);
		}

		return qryConf;
	}

	public Queries(List<Query> query, int id) {
		super();
		this.query = query;
		this.id = id;
	}

	/**
	 * Gets the value of the query property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the query property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getQuery().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Query }
	 * 
	 * 
	 */
	public List<Query> getQuery() {
		if (query == null) {
			query = new ArrayList<Query>();
		}
		return this.query;
	}

	/**
	 * Gets the value of the id property.
	 * 
	 * @return possible object is {@link BigInteger }
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 * @param value
	 *            allowed object is {@link BigInteger }
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

}
