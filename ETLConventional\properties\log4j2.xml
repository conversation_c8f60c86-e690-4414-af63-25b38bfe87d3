<?xml version="1.0" encoding="UTF-8"?>
<configuration status="error">
    <appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>
        </Console>

        <!-- File Appender (Temp/Test) -->
        <File name="log" fileName="./../log/WatchDog.log" append="false">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>
        </File>

        <!-- Daily Rolling File Appender with custom name & path -->
        <RollingFile name="RollingFile"
                     fileName="D:/tomcat1 8.5/apache-tomcat-8.5.99/logs/logs2.log"
                     filePattern="D:/tomcat1 8.5/apache-tomcat-8.5.99/logs/logs2 %d{dd-MM-yyyy}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %class{36} %L %M - %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
            </Policies>
        </RollingFile>
    </appenders>

    <loggers>
        <root level="trace">
            <appender-ref ref="RollingFile"/>
            <appender-ref ref="Console"/>
        </root>
    </loggers>
</configuration>
