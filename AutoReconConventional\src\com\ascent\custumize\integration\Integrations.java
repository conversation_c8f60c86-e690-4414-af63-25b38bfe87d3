//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.06.20 at 06:07:52 AM IST 
//


package com.ascent.custumize.integration;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

import com.ascent.custumize.query.Queries;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Integration" type="{}Integration" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}integer" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "integration"
})
@XmlRootElement(name = "Integrations")
public class Integrations implements Serializable{

    /**
	 * 
	 */
	@XmlTransient
	private static final long serialVersionUID = 8322274121634728533L;
	@XmlElement(name = "Integration")
    protected List<Integration> integration;
    @XmlAttribute(name = "id")
    protected BigInteger id;
    
    @XmlTransient
    Map<String,Integration>  integrationMap=new HashMap<String,Integration>();

    public Integrations(){
    	
    }
    public void bootConf(Queries queries) throws Exception{
    	for(Integration intg:integration){
    		intg.bootConf(queries);
    		integrationMap.put(intg.getName(),intg);
    	}
    }
    /**
     * Gets the value of the integration property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the integration property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getIntegration().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Integration }
     * 
     * 
     */
    public List<Integration> getIntegration() {
        if (integration == null) {
            integration = new ArrayList<Integration>();
        }
        return this.integration;
    }

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setId(BigInteger value) {
        this.id = value;
    }
	public Map<String, Integration> getIntegrationMap() {
		return integrationMap;
	}

	public void setIntegrationMap(Map<String, Integration> integrationMap) {
		this.integrationMap = integrationMap;
	}

}
