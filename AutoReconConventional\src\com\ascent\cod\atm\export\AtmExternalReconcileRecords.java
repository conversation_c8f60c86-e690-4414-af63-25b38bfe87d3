package com.ascent.cod.atm.export;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.mail.test.Test88;
import com.ascent.persistance.LoadRegulator;
import com.ascent.scheduler.MailTrigger;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;

/**
 * Kaushal 
 */
public class AtmExternalReconcileRecords extends BasicDataSource implements PagesConstants{

	private static final long serialVersionUID = 1L;

	private static final String GET_ATM_INTERNAL_SUPPRESS_RECORDS = "GET_ATM_INTERNAL_SUPPRESS_RECORDS";
	private static final String GET_COD_ATM_EXTERNAL_RECORDS = "GET_COD_ATM_EXTERNAL_RECORDS";
	private static final String GET_COD_ATM_INTERNAL_RECORDS = "GET_COD_ATM_INTERNAL_RECORDS";
	private static final String GET_COD_ATM_EXTERNAL_SUPPRESS_RECORDS = "GET_COD_ATM_EXTERNAL_SUPPRESS_RECORDS";
	
	//atmInternalSuppressDataload
	
	
	@SuppressWarnings("unchecked")
	public Map<String, Object> atmInternalSuppressDataload() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> suppressInternal = new ArrayList<Map<String, Object>>();
		Map<String, Object> internalSuppressdatamap = new HashMap<String, Object>();
		
		try {

			queryConf = queryConfs.getQueryConf(GET_ATM_INTERNAL_SUPPRESS_RECORDS);
			suppressInternal = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal----:" + suppressInternal);
			
			List suppressDataList = new ArrayList();
			for (Map<String, Object> recordMap : suppressInternal) {
				
				internalSuppressdatamap.put("content", "PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm Internal Suppress Data:");
				internalSuppressdatamap.put("Mailid", "<EMAIL>");
				suppressDataList.add(recordMap);
				System.out.println("kaushal Records:" + suppressDataList);

			}
			internalSuppressdatamap.put("internalsuppressdataList", suppressDataList);
			System.out.println("Unmatch Kaushal ============:" + internalSuppressdatamap);
			exportExcelAndTriggerMail1(internalSuppressdatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return internalSuppressdatamap;

	}
	
	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMail1(Map<String, Object> internalSuppressdatamap) {
		String fileName = null;
		try {
			// Excel Export Internal Suppress Data 
			fileName = ExportExcelInternalSuppress.exportExcel1((List<Map<String, Object>>) internalSuppressdatamap.get("internalsuppressdataList"));
			//supressFilesFiles[0]=fileName;
		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;

		
	}
///////================internal suppress End	
	
	//getExternalreconcileDataLoad
	
	@SuppressWarnings("unchecked")
	public Map<String, Object> getExternalreconcileDataLoad()
	{
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> externalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> externaldatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_COD_ATM_EXTERNAL_RECORDS);
			externalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal----:" + externalReconcile);
			
			List externalDataList = new ArrayList();
			for (Map<String, Object> recordMap : externalReconcile) {

				
				externaldatamap.put("content", "PFA for :" + recordMap.get("ACTIVE INDEX") + "  Atm External Reconciles Data :");
				externalDataList.add(recordMap);
				System.out.println("kaushal Records:" + externalDataList);

			}
			externaldatamap.put("externalrecondataList", externalDataList);
			System.out.println("Unmatch Kaushal ============:" + externaldatamap);
			exportExcelAndTriggerMailIExternlRecon(externaldatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return externaldatamap;
		
	}
	
	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailIExternlRecon(Map<String, Object> externaldatamap) {

		String fileName = null;

		try {
			// Excel Export Internal Suppress Data 
			fileName = ExportExcelExternalReconcile.exportExcel4((List<Map<String, Object>>) externaldatamap.get("externalrecondataList"));
			//reconcileFiles[0]=fileName;
			
		} catch (IOException e) {
			e.printStackTrace();
		}

		return fileName;
	}	
	
	//==========================================================
	
	//getInternalreconcileDataLoad
	
	@SuppressWarnings("unchecked")
	public Map<String, Object> getInternalreconcileDataLoad()
	{
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> internalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> internaldatamap = new HashMap<String, Object>();

		try {

			queryConf = queryConfs.getQueryConf(GET_COD_ATM_INTERNAL_RECORDS);
			internalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal----:" + internalReconcile);
			
			List internalDataList = new ArrayList();
			for (Map<String, Object> recordMap : internalReconcile) {

				
				internaldatamap.put("content", "PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm Internal Reconciles Data :");
				internalDataList.add(recordMap);
				System.out.println("kaushal Records:" + internalDataList);

			}
			internaldatamap.put("internalrecondataList", internalDataList);
			System.out.println("Unmatch Kaushal ============:" + internaldatamap);
			exportExcelAndTriggerMailIInternalRecon(internaldatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return internaldatamap;
		
	}
	@SuppressWarnings("unchecked")
	public String exportExcelAndTriggerMailIInternalRecon(Map<String, Object> internaldatamap) {

		String fileName = null;

		try {
			// Excel Export Internal recon data export
			fileName = ExportExcelInternalReconcile.exportExcel3((List<Map<String, Object>>) internaldatamap.get("internalrecondataList"));
			
			//reconcileFiles[0]=fileName;
			
		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}	
	
	
	//getExternalSuppressDataLoad
	
	@SuppressWarnings("unchecked")
	public  Map<String, Object> getExternalSuppressDataLoad()
	{
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> suppressexternalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> externalSuppdatamap = new HashMap<String, Object>();

		try {

			queryConf = queryConfs.getQueryConf(GET_COD_ATM_EXTERNAL_SUPPRESS_RECORDS);
			suppressexternalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal----:" + suppressexternalReconcile);
			
			List externalsuppressDataList = new ArrayList();
			for (Map<String, Object> recordMap : suppressexternalReconcile) {
				externalSuppdatamap.put("content", "PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm External Suppress Data :");
				externalsuppressDataList.add(recordMap);
				System.out.println("kaushal Records:" + externalsuppressDataList);

			}
			externalSuppdatamap.put("externalsuppressDataList", externalsuppressDataList);
			System.out.println("Kaushal ============:" + externalSuppdatamap);
			exportExcelExternalSuppress(externalSuppdatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return externalSuppdatamap;	
	}
	
	@SuppressWarnings("unchecked")
	public static String exportExcelExternalSuppress(Map<String, Object> externalSuppdatamap) {


		MailTrigger mailTrigger = new MailTrigger();
		String fileName = null;

		try {
			// Excel Export Internal recon data export
			fileName = ExportExcelExternalSuppress.exportExcel2((List<Map<String, Object>>) externalSuppdatamap.get("externalsuppressDataList"));
			
		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}
	
	public  void trigarMailInternalorExternalRecon() {
		
		 String[] reconcileFiles= new String[2];
		// String[] supressFilesFiles= new String[2];

		 Map<String, Object> internalreconmap= getInternalreconcileDataLoad();
		 String reconcile_internal_file_name = exportExcelAndTriggerMailIInternalRecon(internalreconmap);
		 reconcileFiles[0]= reconcile_internal_file_name;
		 
		 Map<String, Object> externalreconmap= getExternalreconcileDataLoad();
		 String reconcile_external_file_name = exportExcelAndTriggerMailIExternlRecon(externalreconmap);
		 System.out.println("kaushal"+reconcile_external_file_name );
		 reconcileFiles[1]	=  reconcile_external_file_name ;
		 
		 Test88 testmail = new Test88();
		 String masssageformate = testmail.messageFormat();
		 //String mail ="<EMAIL>";
		 try {
			testmail.sendMailWithAttachments(masssageformate,"","<EMAIL>", reconcileFiles);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
					
	}
	public  void trigarMailInternalorExternalSuppress() {
		
		 String[] reconcileFiles= new String[2];
		// String[] supressFilesFiles= new String[2];

		 Map<String, Object> internalsuppressnmap= atmInternalSuppressDataload();
		 String reconcile_internal_file_name = exportExcelAndTriggerMail1(internalsuppressnmap);
		 reconcileFiles[0]= reconcile_internal_file_name;
		 
		 Map<String, Object> externalsuppressmap= getExternalSuppressDataLoad();
		 String reconcile_external_file_name = exportExcelExternalSuppress(externalsuppressmap);
		 System.out.println("kaushal"+reconcile_external_file_name );
		 reconcileFiles[1]	=  reconcile_external_file_name ;
		 
		 Test88 testmail = new Test88();
		 String masssageformate = testmail.messageFormat();
		 //String mail ="<EMAIL>";
		 try {
			testmail.sendMailWithAttachments(masssageformate,"","<EMAIL>", reconcileFiles);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
					
	}
	
	
	/*public static void main(String[] args) {

		AtmExternalReconcileRecords es = new AtmExternalReconcileRecords();
		//es.trigarMailInternalorExternalRecon();
		//es.trigarMailInternalorExternalSuppress();
		//es.getInternalreconcileDataLoad();
		es.atmInternalSuppressDataload();
		//es.getExternalreconcileDataLoad();
		//es.getExternalSuppressDataLoad();	
	}*/
		
}
