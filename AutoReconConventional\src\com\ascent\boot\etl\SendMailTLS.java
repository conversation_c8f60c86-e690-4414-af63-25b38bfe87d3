package com.ascent.boot.etl;


import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

public class SendMailTLS {

	public static void sendMail(final String fromUserName, final String fromUserPassword,
			String toMailId,String filename) {

		Properties props = new Properties();
		props.put("mail.smtp.auth", "true");
		props.put("mail.smtp.starttls.enable", "true");
		props.put("mail.smtp.host", "isf.websitewelcome.com");
		props.put("mail.smtp.port", "587");

		Session session = Session.getInstance(props,
				new javax.mail.Authenticator() {
					protected PasswordAuthentication getPasswordAuthentication() {
						return new PasswordAuthentication(fromUserName,
								fromUserPassword);
					}
				});

		try {

			Message message = new MimeMessage(session);
			message.setFrom(new InternetAddress(fromUserName));
			message.setRecipients(Message.RecipientType.TO,
					InternetAddress.parse(toMailId));
			message.setSubject("Missing file");
			message.setText("File does not match, Please find the file name :," + filename+ "\n\n !");

			// staging_id,id,customer,account_title_1,category,currency,account_officer,open_actual_bal,open_cleared_bal,working_balance,open_available_bal,"
			// alt_acct_id,co_code,consol_key,account_number,opening_date,online_actual_bal,online_cleared_bal,limit_ref,accr_dr_amount,accr_cr_amount,"
			// escrow_process_status

			message.setContent("file does not match :," + filename+ "\n\n !", "text/html; charset=utf-8");
			Transport.send(message);

			System.out.println("Done");

		} catch (MessagingException e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}
}