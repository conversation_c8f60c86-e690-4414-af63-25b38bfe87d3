package com.ascent.banknizwa.source.ej;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.ascent.integration.validation.AscentValidationPlugin;

public class EJEnrichValidationPlugin implements AscentValidationPlugin {

	@Override
	public Boolean validate(Map<String, Object> args) {
		boolean flag = true;
		args.put("ex", false);
		StringBuilder comments = new StringBuilder();

		comments.append((String) args.get("COMMENTS"));

		/*
		 * String c_accep_term_id = (String) args.get("C_ACCEP_TERM_ID"); if
		 * (c_accep_term_id == null || (c_accep_term_id.trim()).isEmpty()) {
		 * flag = false; comments.append("C_ACCEP_TERM_ID is null \\n"); }
		 */
		String retr_ref_no = (String) args.get("STAN");
		if (retr_ref_no == null || (retr_ref_no.trim()).isEmpty()) {
			flag = false;
			comments.append("STAN is null \\n");
		}

		String date_loc_tran = String.valueOf( args.get("TRA_DATE")+"");
		if (date_loc_tran == null || (date_loc_tran.trim()).isEmpty()) {
			flag = false;
			comments.append("TRA_DATE is null \\n");
		}

		/*String time_loc_tran = (String) args.get("TIME_LOC_TRAN");
		if (time_loc_tran == null || (time_loc_tran.trim()).isEmpty()) {
			flag = false;
			comments.append("TIME_LOC_TRAN is null \\n");
		}*/

		String card_no = (String) args.get("PAN");
		String bin = null;
		if (card_no == null || (card_no.trim()).isEmpty()) {
			flag = false;
			comments.append("CARD_NO is null \\n");
		} else {
			if (card_no.length() > 6) {
				bin = card_no.substring(0, 6);
				args.put("BIN", bin);
			}
		}

		/*String acquiring_channel_id = (String) args.get("ACQUIRING_CHANNEL_ID");
		String authorizer = (String) args.get("AUTHORIZER");
		String proc_code = (String) args.get("PROC_CODE");

		String proc_code_2 = null;
		if (proc_code == null || (proc_code.trim()).isEmpty()) {
			flag = false;
			comments.append("PROC_CODE is null \\n");
		} else {
			if (proc_code.length() == 6) {
				proc_code_2 = proc_code.substring(0, 2);
			} else if (proc_code.length() == 5) {
				proc_code_2 = "0" + proc_code.substring(0, 1);
			} else {
				proc_code_2 = "00";
			}
			args.put("PROC_CODE_FIRST_2", proc_code_2);
		}
		if (acquiring_channel_id == null || (acquiring_channel_id.trim()).isEmpty()) {
			flag = false;
			comments.append("ACQUIRING_CHANNEL_ID is null \\n");
		}

		if (authorizer == null || (authorizer.trim()).isEmpty()) {
			flag = false;
			comments.append("AUTHORIZER is null \\n");
		}

		String curr_code_tran = (String) args.get("CURR_CODE_TRAN");
		if (curr_code_tran == null || (curr_code_tran.trim()).isEmpty()) {
			flag = false;
			comments.append("CURR_CODE_TRAN is null \\n");
		}

		String resp_code = (String) args.get("RESP_CODE");
		if (resp_code == null || (resp_code.trim()).isEmpty()) {
			flag = false;
			comments.append("RESP_CODE is null \\n");
		}*/

		//String[] proceCodesDeposit = { "21", "25" };
		//List<String> procCodeDeposit = Arrays.asList(proceCodesDeposit);
		//if (procCodeDeposit.contains(proc_code_2)) {

			String amt_tran_base = args.get("AMOUNT")+"";

			if (amt_tran_base == null || (amt_tran_base.trim()).isEmpty()) {
				flag = false;
				comments.append("AMOUNT is null \\n");
			}
		//}

		args.put("COMMENTS", comments.toString());
		return flag;
	}

}
