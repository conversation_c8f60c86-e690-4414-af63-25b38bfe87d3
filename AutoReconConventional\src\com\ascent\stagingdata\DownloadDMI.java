package com.ascent.stagingdata;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;

import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;
import com.isomorphic.rpc.RPCManager;

public class DownloadDMI extends BasicDataSource{

	
	
	public DSResponse downloadFile(final DSRequest dsRequest, final RPCManager rpcManager) throws Exception
	 {
	Map map=dsRequest.getCriteria();
	System.out.println("RECEIVED ###" + map);
	String id=(String) map.get("id");
	System.out.println("id" + id);
	  final DSResponse dsResponse = new DSResponse();

	  final DownloadItem file = new DownloadItem();
	  final InputStream stream = new FileInputStream(id);
	  file.setFile(stream);

	  dsResponse.setData(file);
    
	  dsResponse.setData(file);

	  return dsResponse;
	 }
}
