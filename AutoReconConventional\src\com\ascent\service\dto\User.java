package com.ascent.service.dto;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class User implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8291425752830905175L;
	public static final String COLON = ":";
	private Integer id;
	private Integer sno;
	private String userId;
	private String userName;
	private String emailId;
	private String phonNo;
	private String deptName;
	private Department deptInstance;
	private String reporting;
	private List<User> reportingUserList = new ArrayList<User>();
	private String systemRole;
	private Role systemRoleInstance;
	private String approvalRole;
	private String approvalDepartment;
	private String allAllowedUserNamesForApproval;
	private List<Role> approvalRoleInstanceList = new ArrayList<Role>();
	private List<Department> approvalDepartmentInstanceList = new ArrayList<Department>();
	private boolean emailNotification;
	private String branchLocation;
	private boolean smsNotification;
	private Privilege privilege;
	private Date createdOn;
	private Integer version;
	private String status;
	private String password;
	private String isLdapUser;

	private Timestamp pwd_exp_date;
	private String account_status;
	private Timestamp updated_on;

	/**
	 * @return the updated_on
	 */
	public Timestamp getUpdated_on() {
		return updated_on;
	}

	/**
	 * @param updated_on
	 *            the updated_on to set
	 */
	public void setUpdated_on(Timestamp updated_on) {
		this.updated_on = updated_on;
	}

	/**
	 * @return the pwd_exp_date
	 */
	public Timestamp getPwd_exp_date() {
		return pwd_exp_date;
	}

	/**
	 * @param pwd_exp_date
	 *            the pwd_exp_date to set
	 */
	public void setPwd_exp_date(Timestamp pwd_exp_date) {
		this.pwd_exp_date = pwd_exp_date;
	}

	/**
	 * @return the account_status
	 */
	public String getAccount_status() {
		return account_status;
	}

	/**
	 * @param account_status
	 *            the account_status to set
	 */
	public void setAccount_status(String account_status) {
		this.account_status = account_status;
	}

	private String reconString = "";

	private ArrayList<String> selectedApprovalRole = new ArrayList<String>();
	private ArrayList<String> selectedReporting = new ArrayList<String>();

	private ArrayList<String> selectedApprovalDepts = new ArrayList<String>();

	public void init(Users users, Roles roles, Departments departments, Privileges privilege) {

		this.systemRoleInstance = roles.getRole(this.systemRole);

		if (this.reporting != null) {
			String reportingTemp[] = reporting.split(COLON);

			if (reportingTemp != null && reportingTemp.length > 0) {
				for (String userString : reportingTemp) {
					User user = users.getUser(userString);
					reportingUserList.add(user);
				}
			}
		}
		if (approvalRole != null) {
			String approvalRoles[] = approvalRole.split(COLON);
			if (approvalRoles != null && approvalRoles.length > 0) {
				for (String roleString : approvalRoles) {
					Role role = roles.getRole(roleString);
					approvalRoleInstanceList.add(role);
				}
			}
		}

		if (approvalDepartment != null) {
			String approvalDepartments[] = approvalDepartment.split(COLON);
			if (approvalDepartments != null && approvalDepartments.length > 0) {
				for (String deptString : approvalDepartments) {
					Department department = departments.getDepartment(deptString);
					approvalDepartmentInstanceList.add(department);
				}
			}
		}

		this.privilege = privilege.getPrivilege(this.systemRole);

		this.deptInstance = departments.getDepartment(deptName);

	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getPhonNo() {
		return phonNo;
	}

	public void setPhonNo(String phonNo) {
		this.phonNo = phonNo;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getReporting() {
		return reporting;
	}

	public void setReporting(String reporting) {
		this.reporting = reporting;
	}

	public String getSystemRole() {
		return systemRole;
	}

	public void setSystemRole(String systemRole) {
		this.systemRole = systemRole;
	}

	public String getApprovalRole() {
		return approvalRole;
	}

	public void setApprovalRole(String approvalRole) {
		this.approvalRole = approvalRole;
	}

	public boolean getEmailNotification() {
		return emailNotification;
	}

	public void setEmailNotification(boolean emailNotification) {
		this.emailNotification = emailNotification;
	}

	public String getBranchLocation() {
		return branchLocation;
	}

	public void setBranchLocation(String branchLocation) {
		this.branchLocation = branchLocation;
	}

	public boolean getSmsNotification() {
		return smsNotification;
	}

	public void setSmsNotification(boolean smsNotification) {
		this.smsNotification = smsNotification;
	}

	public Department getDeptInstance() {
		return deptInstance;
	}

	public void setDeptInstance(Department deptInstance) {
		this.deptInstance = deptInstance;
	}

	public List<User> getReportingUserList() {
		return reportingUserList;
	}

	public void setReportingUserList(List<User> reportingUserList) {
		this.reportingUserList = reportingUserList;
	}

	public Role getSystemRoleInstance() {
		return systemRoleInstance;
	}

	public void setSystemRoleInstance(Role systemRoleInstance) {
		this.systemRoleInstance = systemRoleInstance;
	}

	public List<Role> getApprovalRoleInstanceList() {
		return approvalRoleInstanceList;
	}

	public void setApprovalRoleInstanceList(List<Role> approvalRoleInstanceList) {
		this.approvalRoleInstanceList = approvalRoleInstanceList;
	}

	public String getApprovalDepartment() {
		return approvalDepartment;
	}

	public void setApprovalDepartment(String approvalDepartment) {
		this.approvalDepartment = approvalDepartment;
	}

	public List<Department> getApprovalDepartmentInstanceList() {
		return approvalDepartmentInstanceList;
	}

	public void setApprovalDepartmentInstanceList(List<Department> approvalDepartmentInstanceList) {
		this.approvalDepartmentInstanceList = approvalDepartmentInstanceList;
	}

	public Privilege getPrivilege() {
		return privilege;
	}

	public void setPrivilege(Privilege privilege) {
		this.privilege = privilege;
	}

	public String getAllAllowedUserNamesForApproval() {
		return allAllowedUserNamesForApproval;
	}

	public void setAllAllowedUserNamesForApproval(String allAllowedUserNamesForApproval) {
		this.allAllowedUserNamesForApproval = allAllowedUserNamesForApproval;
	}

	public Integer getSno() {
		return sno;
	}

	public void setSno(Integer sno) {
		this.sno = sno;
	}

	public ArrayList<String> getSelectedApprovalRole() {
		return selectedApprovalRole;
	}

	public void setSelectedApprovalRole(ArrayList<String> selectedApprovalRole) {
		this.selectedApprovalRole = selectedApprovalRole;
	}

	public ArrayList<String> getSelectedReporting() {
		return selectedReporting;
	}

	public void setSelectedReporting(ArrayList<String> selectedReporting) {
		this.selectedReporting = selectedReporting;
	}

	public ArrayList<String> getSelectedApprovalDepts() {
		return selectedApprovalDepts;
	}

	public void setSelectedApprovalDepts(ArrayList<String> selectedApprovalDepts) {
		this.selectedApprovalDepts = selectedApprovalDepts;
	}

	public Date getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(Date createdOn) {
		this.createdOn = createdOn;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((account_status == null) ? 0 : account_status.hashCode());
		result = prime * result
				+ ((allAllowedUserNamesForApproval == null) ? 0 : allAllowedUserNamesForApproval.hashCode());
		result = prime * result + ((approvalDepartment == null) ? 0 : approvalDepartment.hashCode());
		result = prime * result
				+ ((approvalDepartmentInstanceList == null) ? 0 : approvalDepartmentInstanceList.hashCode());
		result = prime * result + ((approvalRole == null) ? 0 : approvalRole.hashCode());
		result = prime * result + ((approvalRoleInstanceList == null) ? 0 : approvalRoleInstanceList.hashCode());
		result = prime * result + ((branchLocation == null) ? 0 : branchLocation.hashCode());
		result = prime * result + ((createdOn == null) ? 0 : createdOn.hashCode());
		result = prime * result + ((deptInstance == null) ? 0 : deptInstance.hashCode());
		result = prime * result + ((deptName == null) ? 0 : deptName.hashCode());
		result = prime * result + ((emailId == null) ? 0 : emailId.hashCode());
		result = prime * result + (emailNotification ? 1231 : 1237);
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((isLdapUser == null) ? 0 : isLdapUser.hashCode());
		result = prime * result + ((password == null) ? 0 : password.hashCode());
		result = prime * result + ((phonNo == null) ? 0 : phonNo.hashCode());
		result = prime * result + ((privilege == null) ? 0 : privilege.hashCode());
		result = prime * result + ((pwd_exp_date == null) ? 0 : pwd_exp_date.hashCode());
		result = prime * result + ((reconString == null) ? 0 : reconString.hashCode());
		result = prime * result + ((reporting == null) ? 0 : reporting.hashCode());
		result = prime * result + ((reportingUserList == null) ? 0 : reportingUserList.hashCode());
		result = prime * result + ((selectedApprovalDepts == null) ? 0 : selectedApprovalDepts.hashCode());
		result = prime * result + ((selectedApprovalRole == null) ? 0 : selectedApprovalRole.hashCode());
		result = prime * result + ((selectedReporting == null) ? 0 : selectedReporting.hashCode());
		result = prime * result + (smsNotification ? 1231 : 1237);
		result = prime * result + ((sno == null) ? 0 : sno.hashCode());
		result = prime * result + ((status == null) ? 0 : status.hashCode());
		result = prime * result + ((systemRole == null) ? 0 : systemRole.hashCode());
		result = prime * result + ((systemRoleInstance == null) ? 0 : systemRoleInstance.hashCode());
		result = prime * result + ((updated_on == null) ? 0 : updated_on.hashCode());
		result = prime * result + ((userId == null) ? 0 : userId.hashCode());
		result = prime * result + ((userName == null) ? 0 : userName.hashCode());
		result = prime * result + ((version == null) ? 0 : version.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		User other = (User) obj;
		if (account_status == null) {
			if (other.account_status != null)
				return false;
		} else if (!account_status.equals(other.account_status))
			return false;
		if (allAllowedUserNamesForApproval == null) {
			if (other.allAllowedUserNamesForApproval != null)
				return false;
		} else if (!allAllowedUserNamesForApproval.equals(other.allAllowedUserNamesForApproval))
			return false;
		if (approvalDepartment == null) {
			if (other.approvalDepartment != null)
				return false;
		} else if (!approvalDepartment.equals(other.approvalDepartment))
			return false;
		if (approvalDepartmentInstanceList == null) {
			if (other.approvalDepartmentInstanceList != null)
				return false;
		} else if (!approvalDepartmentInstanceList.equals(other.approvalDepartmentInstanceList))
			return false;
		if (approvalRole == null) {
			if (other.approvalRole != null)
				return false;
		} else if (!approvalRole.equals(other.approvalRole))
			return false;
		if (approvalRoleInstanceList == null) {
			if (other.approvalRoleInstanceList != null)
				return false;
		} else if (!approvalRoleInstanceList.equals(other.approvalRoleInstanceList))
			return false;
		if (branchLocation == null) {
			if (other.branchLocation != null)
				return false;
		} else if (!branchLocation.equals(other.branchLocation))
			return false;
		if (createdOn == null) {
			if (other.createdOn != null)
				return false;
		} else if (!createdOn.equals(other.createdOn))
			return false;
		if (deptInstance == null) {
			if (other.deptInstance != null)
				return false;
		} else if (!deptInstance.equals(other.deptInstance))
			return false;
		if (deptName == null) {
			if (other.deptName != null)
				return false;
		} else if (!deptName.equals(other.deptName))
			return false;
		if (emailId == null) {
			if (other.emailId != null)
				return false;
		} else if (!emailId.equals(other.emailId))
			return false;
		if (emailNotification != other.emailNotification)
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (isLdapUser == null) {
			if (other.isLdapUser != null)
				return false;
		} else if (!isLdapUser.equals(other.isLdapUser))
			return false;
		if (password == null) {
			if (other.password != null)
				return false;
		} else if (!password.equals(other.password))
			return false;
		if (phonNo == null) {
			if (other.phonNo != null)
				return false;
		} else if (!phonNo.equals(other.phonNo))
			return false;
		if (privilege == null) {
			if (other.privilege != null)
				return false;
		} else if (!privilege.equals(other.privilege))
			return false;
		if (pwd_exp_date == null) {
			if (other.pwd_exp_date != null)
				return false;
		} else if (!pwd_exp_date.equals(other.pwd_exp_date))
			return false;
		if (reconString == null) {
			if (other.reconString != null)
				return false;
		} else if (!reconString.equals(other.reconString))
			return false;
		if (reporting == null) {
			if (other.reporting != null)
				return false;
		} else if (!reporting.equals(other.reporting))
			return false;
		if (reportingUserList == null) {
			if (other.reportingUserList != null)
				return false;
		} else if (!reportingUserList.equals(other.reportingUserList))
			return false;
		if (selectedApprovalDepts == null) {
			if (other.selectedApprovalDepts != null)
				return false;
		} else if (!selectedApprovalDepts.equals(other.selectedApprovalDepts))
			return false;
		if (selectedApprovalRole == null) {
			if (other.selectedApprovalRole != null)
				return false;
		} else if (!selectedApprovalRole.equals(other.selectedApprovalRole))
			return false;
		if (selectedReporting == null) {
			if (other.selectedReporting != null)
				return false;
		} else if (!selectedReporting.equals(other.selectedReporting))
			return false;
		if (smsNotification != other.smsNotification)
			return false;
		if (sno == null) {
			if (other.sno != null)
				return false;
		} else if (!sno.equals(other.sno))
			return false;
		if (status == null) {
			if (other.status != null)
				return false;
		} else if (!status.equals(other.status))
			return false;
		if (systemRole == null) {
			if (other.systemRole != null)
				return false;
		} else if (!systemRole.equals(other.systemRole))
			return false;
		if (systemRoleInstance == null) {
			if (other.systemRoleInstance != null)
				return false;
		} else if (!systemRoleInstance.equals(other.systemRoleInstance))
			return false;
		if (updated_on == null) {
			if (other.updated_on != null)
				return false;
		} else if (!updated_on.equals(other.updated_on))
			return false;
		if (userId == null) {
			if (other.userId != null)
				return false;
		} else if (!userId.equals(other.userId))
			return false;
		if (userName == null) {
			if (other.userName != null)
				return false;
		} else if (!userName.equals(other.userName))
			return false;
		if (version == null) {
			if (other.version != null)
				return false;
		} else if (!version.equals(other.version))
			return false;
		return true;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the password
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * @param password
	 *            the password to set
	 */
	public void setPassword(String password) {
		this.password = password;
	}

	/**
	 * @return the reconString
	 */
	public String getReconString() {
		return reconString;
	}

	/**
	 * @param reconString
	 *            the reconString to set
	 */
	public void setReconString(String reconString) {
		this.reconString = reconString;
	}

	public String getIsLdapUser() {
		return isLdapUser;
	}

	public void setIsLdapUser(String isLdapUser) {
		this.isLdapUser = isLdapUser;
	}

}
