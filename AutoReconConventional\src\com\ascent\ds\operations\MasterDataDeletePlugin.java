package  com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.integration.util.DbUtil;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class MasterDataDeletePlugin extends BasicDataSource implements PagesConstants {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4275100050529576441L;
	private static Logger logger = LogManager.getLogger(MasterDataDeletePlugin.class.getName());
	static Connection connection=null;
	 Map<String, Object> result = null;
	 
	public MasterDataDeletePlugin(){
		 
		logger.trace("MasterDataDeletePlugin*********");
	}
	public DSResponse executeFetch (DSRequest request){
		List<Map<String, Object>> selectedRecords=null;
		 DSResponse dsResponse= new DSResponse();
		 Map criteriaMap= request.getValues();
	//TODO: is user authorized utility to verify user priviliges
	
	HttpSession httpSession = request.getHttpServletRequest().getSession(); 
	String reconName=(String)httpSession.getAttribute("user_selected_recon");
	String businesArea=(String)httpSession.getAttribute("user_selected_business_area");
	User user = (User) httpSession.getAttribute("userId");
	String userId = user.getUserId();
	
	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		dsResponse.setData(result);
		return dsResponse;
	}
	 
		 Map<String,Object> records =(Map<String,Object>) criteriaMap.get("record");
		 System.out.println(records);
		 
		 //String comments=(String) records.get("comments");
		String action="Master Data Delete Operation";
		String tableName=(String) criteriaMap.get("tableName");
		String dsName=(String) criteriaMap.get("dsName");
		 
		selectedRecords = new ArrayList<Map<String, Object>>();
		selectedRecords.add(records);
		Map<String,Object> masterDataDeleteArgs=new HashMap<String, Object>();
		masterDataDeleteArgs.put(SELECTED_RECORDS, selectedRecords);
		masterDataDeleteArgs.put(TABLE_NAME, tableName);
		masterDataDeleteArgs.put(DS_NAME, dsName);
		masterDataDeleteArgs.put(ACTION,action);
		masterDataDeleteArgs.put(USER_ID, userId);
		masterDataDeleteArgs.put(BUSINES_AREA, businesArea);
		masterDataDeleteArgs.put(RECON_NAME, reconName);
		//masterDataDeleteArgs.put(COMMENTS, comments);
		
		boolean updateFlag=updateValuesTxn(records , tableName);
		System.out.println("updated----   "+updateFlag);
		//process(masterDataDeleteArgs);
		
		dsResponse.setData(result); 
		return dsResponse;
		
	}
private Map<String, Object>  process(Map<String,Object> masterDataDeleteArgs){
	try {
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();

		String userId = (String) masterDataDeleteArgs.get(USER_ID);
		String tableName = (String) masterDataDeleteArgs.get(TABLE_NAME);
		String dsName = (String) masterDataDeleteArgs.get(DS_NAME);
		masterDataDeleteArgs.put(PERSIST_CLASS, MASTER_DATA_DELETE_OPREATION_PLUGIN_CLASS_NAME);
		activityDataMap.put("activity_data", masterDataDeleteArgs);
 
	    UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;
			String businessArea = (String) masterDataDeleteArgs.get(BUSINES_AREA);
			String reconName = (String) masterDataDeleteArgs.get(RECON_NAME);
			String comments = (String) masterDataDeleteArgs.get(COMMENTS);
			String moduleName=(String)masterDataDeleteArgs.get(ACTION);
			userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
					MASTER_DATA_DELETE_OPERATION, activityDataMap, activityStatus, comments);

			updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
						}
		else{
			result = persist(activityDataMap, APPROVED, connection);
		}
			
					}catch(Exception e){
						updateResultStatus(result,FAILED,OPERATION_FAILED);
						e.printStackTrace();
					}finally{
						
						DbUtil.closeConnection(connection);
						
					}
	return result;
			
	}
	
public static boolean updateValuesTxn(Map<String,Object> records,String tableName){
		 
		PreparedStatement preparedStatement= null;
		String SID = (String)records.get("ID");
	 
	
			 
			int updateStatus=0;
			final String updateStatusAndVersionQuery ="delete from "+tableName+" where  ID=? "; 
		try {
			
		
			connection=DbUtil.getConnection();
			preparedStatement=connection.prepareStatement(updateStatusAndVersionQuery);
			
			
				preparedStatement.setString(1, SID);
			 
		 updateStatus=preparedStatement.executeUpdate();
			
			
		} catch (SQLException e) {
			 e.printStackTrace();
		}finally{
			DbUtil.closePreparedStatement(preparedStatement);
			DbUtil.closeConnection(connection);
		}
		 if(updateStatus==1){
			 System.out.println("Updated value successfully");
			 return true;
			 
		 }else{
			 System.out.println("Not Updated value");
			 return false;
		 }
	 }
private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}

public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {
		connection=DbUtil.getConnection();
		PreparedStatement preparedStatement=null;
	 System.out.println("*****************"+activityDataMap);
	 Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
	 String comment=(String) activityDataMap.get("comment");
	 System.out.println(comment+"-------"+activityRecordsMap);
	 try
	 {
		if(APPROVED.equalsIgnoreCase(status)){
          String tableName=(String) activityRecordsMap.get(TABLE_NAME);
          List<Map<String,Object>> selectedRecords=(List<Map<String,Object>>) activityRecordsMap.get(SELECTED_RECORDS);
          
          for (Map<String,Object> recordMap:selectedRecords){
        	//  long VERSION = (long) recordMap.get("VERSION");
        	   //++VERSION;
        	   
        	  long SID=(long)recordMap.get("SID");
        	  final String QUERY ="update "+tableName+" set WORKFLOW_STATUS=?,ACTIVE_INDEX=?,VERSION=?,ACTIVITY_COMMENTS=? where SID=? "; 
        	  preparedStatement=connection.prepareStatement(QUERY);
        	  preparedStatement.setString(1, "N");
        	  preparedStatement.setString(2, "N");
        	 // preparedStatement.setLong(3, VERSION);
        	  preparedStatement.setString(4, comment);
        	  preparedStatement.setLong(5, SID);
        	  
        	 int updateStatus= preparedStatement.executeUpdate();
        	  if(updateStatus==1){
        		  System.out.println("UPDATED SUCCESSFULLY... "+updateStatus);
        	  }else{
        		  System.out.println("NOT UPDATED  ... "+updateStatus); 
        	  }
          }
		}
		}catch (Exception e) {
		 
			logger.error(e);
			e.printStackTrace();
		}
	 

		 
	return result;
}

}
