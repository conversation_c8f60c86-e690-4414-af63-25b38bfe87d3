package com.ascent.ds.login;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.LoadRegulator;
import com.ascent.util.AscentAutoReconConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;
import com.sun.mail.util.MailSSLSocketFactory;

public class ForgotPassword extends BasicDataSource {

	static String email_from;
	static String userName;
	static String password;
	static String smptpHost;
	static String smptpPort;

	static {

		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();

		Properties appProps = etlMetaInstance.getApplicationProperties();
		email_from = (String) (String) appProps.get(AscentAutoReconConstants.from);
		userName = (String) appProps.get(AscentAutoReconConstants.username);
		password = (String) appProps.get(AscentAutoReconConstants.password);
		smptpHost = (String) appProps.get(AscentAutoReconConstants.SMTPHost);
		smptpPort = (String) appProps.get(AscentAutoReconConstants.SMTPPort);

	}

	public ForgotPassword() {
		// TODO Auto-generated constructor stub

	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1613609169359483656L;

	private static final String GET_ALL_USERS_EMAILS_QUERY = "GET_ALL_USERS_EMAILS_QUERY";

	public DSResponse executeFetch(final DSRequest request) {

		DSResponse dsResponse = new DSResponse();
		Map<String, Object> criteriaMap = request.getValues();

		String mail = (String) criteriaMap.get("mailID");

		String url = null;
		try {
			url = "<center><a href='http://" + InetAddress.getLocalHost().getHostAddress() + ":"
					+ request.getHttpServletRequest().getServerPort() + ""
					+ request.getHttpServletRequest().getContextPath() + "/ResetPassword.jsp?email_id=" + mail
					+ "&a_status=ACTIVE" + "'><font color='green'> Reset Password </font></a></center>";
		} catch (UnknownHostException e) {

			e.printStackTrace();
		}

		Map<String, Object> respMap = new HashMap<String, Object>();
		List<Map<String, Object>> emailsList = new ArrayList<Map<String, Object>>();

		Map<String, Object> params = new HashMap<String, Object>();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Query queryConf;
		try {
			queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(GET_ALL_USERS_EMAILS_QUERY);
			params.put("email_id", mail);
			LoadRegulator loadRegulator = new LoadRegulator();
			emailsList = loadRegulator.loadCompleteData(params, queryConf);
		} catch (Exception e) {

			e.printStackTrace();
		}

		if (emailsList != null && !emailsList.isEmpty()) {

			for (Map<String, Object> emailsFrmDB : emailsList) {

				String email = (String) emailsFrmDB.get("email_id");
				String isLdapUser = (String) emailsFrmDB.get("isLdapUser");
				if (mail != null && mail.equals(email)) {
					if (!isLdapUser.equals("Y")) {
						forgotPWD_sendMail(url, email, "");
						respMap.put("SUCCESS", MessageConstants.EMAIL_SUCCESSFULLY_SENT);
						dsResponse.setData(respMap);
						return dsResponse;

					}

					respMap.put("SUCCESS", MessageConstants.FORGOT_RESET_CHANAGE_PWD_NOT_ALLOWED);
					dsResponse.setData(respMap);
					return dsResponse;

				} else {
					respMap.put("ERR_MSG", MessageConstants.NOT_VALID_EMAIL_ID);
					dsResponse.setData(respMap);
					return dsResponse;

				}

			}
		} else {

			respMap.put("ERR_MSG", MessageConstants.NOT_VALID_EMAIL_ID);
			dsResponse.setData(respMap);

		}

		return dsResponse;
	}

	public void forgotPWD_sendMail(String url, String email_to, String msg_content) {
		System.out.println("Sending mail....");
		final String user = email_from;
		String to = email_to;

		Properties props = new Properties();
			

		/*props.put("mail.smtp.auth", "false");
		props.put("mail.smtp.starttls.enable", "true");
		props.put("mail.smtp.host", smptpHost);
		props.put("mail.smtp.port", smptpPort);*/
		
		props.setProperty("mail.smtp.auth", "false");
		props.setProperty("mail.smtp.starttls.enable", "true");
		props.setProperty("mail.smtp.ssl.trust", "smtp.gmail.com");
		props.setProperty("mail.smtp.host", smptpHost);
		props.setProperty("mail.smtp.port", smptpPort);
		
		MailSSLSocketFactory sf = null;
		try {
			sf = new MailSSLSocketFactory();
			sf.setTrustAllHosts(true);
		} catch (GeneralSecurityException e1) {

			e1.printStackTrace();
		}
		props.put("mail.smtp.ssl.socketFactory", sf);

		Session session = Session.getInstance(props, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(email_from, password);
			}
		});

		try {

			MimeMessage message = new MimeMessage(session);
			message.setFrom(new InternetAddress(email_from));
			message.addRecipient(Message.RecipientType.TO, new InternetAddress(email_to));
			message.setSubject("Reset Your  Password");
			// message.setSentDate(new Date());
			// String s=getPassword(to);

			// message.setText("Click on this link to change ur password
			// :"+url);
			message.setText("<div><p>" + msg_content + "</p><br/><br/> "
					+ "<div style='border:2px solid gray; margin-top:15px; width:40%;'><center><b>click here to reset password : </b></center> <br/>     "
					+ url + "</div>", "UTF-8", "html");
			// send the message
			Transport.send(message);

			System.out.println("MAIL SENT SUCCESSFULLY... check " + email_to);
			// StoringDetails.saveDetails(id, s);
		} catch (MessagingException e) {
			e.printStackTrace();
		}
	}

	/**
	 * <AUTHOR>
	 * @param user_id
	 *            content email
	 * @throws UnknownHostException
	 */

	public void processMail(int serverPort, String contextPath, String user_id, String email, String acnt_status)
			throws UnknownHostException {
		String url = "<center><a href='http://" + InetAddress.getLocalHost().getHostAddress() + ":" + serverPort + ""
				+ contextPath + "/ResetPassword.jsp?email_id=" + email + "&a_status=" + acnt_status
				+ "'><font color='green'> Reset Password </font></a></center>";
		String msg_content = user_id + " " + MessageConstants.ADMINISTRATOR_MESAAAGE;

		forgotPWD_sendMail(url, email, msg_content);
	}

}
