package com.ascent.boot.web;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URL;
import java.util.Properties;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.boot.recon.ReconMetaInstance;
import com.ascent.custumize.integration.Integrations;
import com.ascent.custumize.query.Queries;

public class AscentWebMetaInstance {

	//private static Logger logger = LogManager.getLogger(AscentWebMetaInstance.class);

	private static AscentWebMetaInstance instance;
	private Properties dbProperties;
	private Queries webQueryConfs = null;
	private ReconMetaInstance reconMetaInstance=null;
	private EtlMetaInstance etlMetaInstance=null;
	private Properties bootProperties;
	private Properties applicationProperties;
	private Properties ldapProps;
	


	private AscentWebMetaInstance() throws Exception {

		String bootPropFileName = "boot.properties";
		String dbPropFileName = "db.properties";
		String applicationPropFileName = "application.properties";

		this.bootProperties = new Properties();
		this.dbProperties = new Properties();
		this.applicationProperties = new Properties();
		this.ldapProps= new Properties();

		InputStream inputStream = getClass().getClassLoader()
				.getResourceAsStream(bootPropFileName);

		if (inputStream != null) {
			try {
				this.bootProperties.load(inputStream);

				//logger.trace("Loaded bootProperties ");
				for (Object key : this.bootProperties.keySet()) {
					//logger.trace(key + " : " + this.bootProperties.get(key));
					this.bootProperties.get(key);
				}
				//logger.trace("Sucessfully ");
				String appMode = (String) this.bootProperties
				.get("APP_MODE");
				try {
					

					InputStream dbInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + dbPropFileName);

					InputStream appInputStream = getClass().getClassLoader()
							.getResourceAsStream(
									appMode + "/" + applicationPropFileName);

					if (dbInputStream != null) {
						try{
							this.dbProperties.load(dbInputStream);

							//logger.trace("Loaded dbProperties ");
							for (Object key : this.dbProperties.keySet()) {
								//logger.trace(key + " : " + this.dbProperties.get(key));
								this.dbProperties.get(key);
							}
							//logger.trace("Sucessfully ");
							
						}catch(Exception e){
							//logger.trace("Unable to load dbProperties ");
							e.printStackTrace();
							throw e;
						
						}
					} else {
						//logger.trace("property file '" + dbPropFileName+ "' not found in the classpath");
						throw new FileNotFoundException("property file '"
								+ dbPropFileName
								+ "' not found in the classpath");
					}

					if (appInputStream != null) {
						
						try{
							this.applicationProperties.load(appInputStream);

							//logger.trace("Loaded applicationProperties ");
							/*for (Object key : this.applicationProperties.keySet()) {
								logger.trace(key + " : " + this.applicationProperties.get(key));
							}*/
							//logger.trace("Sucessfully ");
							
						}catch(Exception e){
							//logger.trace("Unable to load applicationProperties ");
							e.printStackTrace();
							throw e;
						
						}
						
					} else {
						//logger.trace("property file '"+ applicationPropFileName+ "' not found in the classpath");
						throw new FileNotFoundException("property file '"
								+ applicationPropFileName
								+ "' not found in the classpath");
					}

				} catch (Exception e) {
					e.printStackTrace();
					//logger.trace("unable to load properties under the APP_MODE "+appMode);
					throw new Exception("unable to load properties under the APP_MODE "+appMode);
				}
			} catch (Exception e) {
				//logger.trace("Unable to load bootProperties properties");
				e.printStackTrace();
				throw e;
			}

		} else {
			//logger.trace("property file '" + bootPropFileName+ "' not found in the classpath");
			throw new FileNotFoundException("property file '"
					+ bootPropFileName + "' not found in the classpath");
		}
		loadConf();
		loadLdapConf();
		this.reconMetaInstance=ReconMetaInstance.getInstance();
		this.etlMetaInstance=EtlMetaInstance.getInstance();
		
	}

	static {
		try {
			instance = new AscentWebMetaInstance();
		} catch (Exception e) {
			throw new RuntimeException("Exception occured in creating singleton instance");
		}
	}

	public void loadConf() {

		JAXBContext jaxbContext = null;
		Unmarshaller jaxbUnmarshaller = null;
		try {
			InputStream queryConfInputStream = getClass().getClassLoader().getResourceAsStream("web/WEB_QUERY_CONF.xml");

			jaxbContext = JAXBContext.newInstance(Queries.class);

			jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			this.webQueryConfs = (Queries) jaxbUnmarshaller.unmarshal(queryConfInputStream);

			this.webQueryConfs.bootConf();
			//logger.trace("Loaded Web Query Configurations");

			
			jaxbContext = JAXBContext.newInstance(Integrations.class);

			jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			
			

		} catch (Exception e) {
			e.printStackTrace();
		}

	}
	
	public void loadLdapConf() {

		try {
			InputStream queryConfInputStream = getClass().getClassLoader().getResourceAsStream("LdapConf/Client.properties");
			this.ldapProps.load(queryConfInputStream);
			URL url =getClass().getClassLoader().getResource("LdapConf/jaas.conf");
			String path =url.getPath();
			File file = new File(path);
			String absolutePath = file.getCanonicalPath();//"C:/jaas.conf"
			String jassPath=ldapProps.getProperty("jass.conf");
			if(jassPath==null||jassPath.length()==0){
				//throw new Exception("jass.conf is not configured");
			}else{
			this.ldapProps.put("jassPath", ldapProps.getProperty("jass.conf"));
			//logger.trace("{jass.conf absolute path}:=  "+ldapProps.getProperty("jass.conf"));
		    //logger.trace("Loaded LDAP properties  Configurations");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public static AscentWebMetaInstance getInstance() {
		return instance;
	}

	public Properties getDbProperties() {
		return dbProperties;
	}

	public void setDbProperties(Properties dbProperties) {
		this.dbProperties = dbProperties;
	}

	public Queries getWebQueryConfs() {
		return webQueryConfs;
	}

	public ReconMetaInstance getReconMetaInstance() {
		return reconMetaInstance;
	}

	public void setReconMetaInstance(ReconMetaInstance reconMetaInstance) {
		this.reconMetaInstance = reconMetaInstance;
	}

	public EtlMetaInstance getEtlMetaInstance() {
		return etlMetaInstance;
	}

	public void setEtlMetaInstance(EtlMetaInstance etlMetaInstance) {
		this.etlMetaInstance = etlMetaInstance;
	}

	public void setWebQueryConfs(Queries webQueryConfs) {
		this.webQueryConfs = webQueryConfs;
	}

	public Properties getBootProperties() {
		return bootProperties;
	}

	public void setBootProperties(Properties bootProperties) {
		this.bootProperties = bootProperties;
	}

	public Properties getApplicationProperties() {
		return applicationProperties;
	}

	public void setApplicationProperties(Properties applicationProperties) {
		this.applicationProperties = applicationProperties;
	}

	public Properties getLdapProps() {
		return ldapProps;
	}

	public void setLdapProps(Properties ldapProps) {
		this.ldapProps = ldapProps;
	}


}