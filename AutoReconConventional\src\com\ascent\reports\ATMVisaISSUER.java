
package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class ATMVisaISSUER {

	private static Logger logger = LogManager.getLogger(ATMVisaISSUER.class.getName());


	private static final String ATM_VISA_ISSUER_INTERNAL_RECONCILED_RECON="ATM_VISA_ISSUER_INTERNAL_RECONCILED_RECON";
	private static final String ATM_VISA_ISSUER_INTERNAL_UNRECONCILED_RECON="ATM_VISA_ISSUER_INTERNAL_UNRECONCILED_RECON";
	private static final String ATM_VISA_ISSUER_EXTERNAL_RECONCILED_RECON="ATM_VISA_ISSUER_EXTERNAL_RECONCILED_RECON";
	private static final String ATM_VISA_ISSUER_EXTERNAL_UNRECONCILED_RECON="ATM_VISA_ISSUER_EXTERNAL_UNRECONCILED_RECON";
	private static final String ATM_VISA_ISSUER_INTERNAL_SUPPRESSED_RECON="ATM_VISA_ISSUER_INTERNAL_SUPPRESSED_RECON";
	private static final String ATM_VISA_ISSUER_EXTERNAL_SUPPRESSED_RECON="ATM_VISA_ISSUER_EXTERNAL_SUPPRESSED_RECON";

	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		while (rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));
			}
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}

	public List<Map<String, Object>>getInternalatmissuerReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ISSUER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>>getInternalatmissuerUnReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL UNRECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}



	public List<Map<String, Object>>getInternalatmissuerSuppressedData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER_INTERNAL_SUPPRESSED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}



	public List<Map<String, Object>>getExternalatmissuerUnReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ISSUER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}


	public List<Map<String, Object>>getExternalatmissuerReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}


	public List<Map<String, Object>>getExternalatmissuerSuppressedData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER_EXTERNAL_SUPPRESSED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}




	public static void main(String[] args) {
		ATMVisaISSUER c = new ATMVisaISSUER();
		c.getExternalatmissuerReconciledData("2015-01-01", "2019-01-01");
		c.getExternalatmissuerSuppressedData("2015-01-01", "2019-01-01");
		c.getExternalatmissuerUnReconciledData("2015-01-01", "2019-01-01");
		c.getInternalatmissuerReconciledData("2015-01-01", "2019-01-01");
		c.getInternalatmissuerSuppressedData("2015-01-01", "2019-01-01");
		c.getInternalatmissuerUnReconciledData("2015-01-01", "2019-01-01");





	}

}
