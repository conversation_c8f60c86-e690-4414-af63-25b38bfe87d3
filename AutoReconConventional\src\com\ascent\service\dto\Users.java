package com.ascent.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Users  implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1938493925895375359L;
	private static final String COLON = ":";
	List<User> userList = new ArrayList<User>();
	Map<Integer, User> userMap = new HashMap<Integer, User>();
	Map<String, User> userIdMap = new HashMap<String, User>();
	Map<String, List<String>> userListByRole = new HashMap<String, List<String>>();
	Map<String, List<String>> userListByDept = new HashMap<String, List<String>>();

	public void init(Roles roles, Departments departments, Privileges privileges) throws Exception {
		if (this.userList != null) {
			for (User user : userList) {
				user.init(this, roles, departments, privileges);
				userMap.put(user.getId(), user);
				userIdMap.put(user.getUserId(), user);

				List<String> userListForRole = userListByRole.get(user
						.getSystemRole());
				if (userListForRole == null) {
					userListForRole = new ArrayList<String>();
					userListForRole.add(user.getUserId());
					userListByRole.put(user.getSystemRole(), userListForRole);
				} else {
					userListForRole.add(user.getUserId());
				}

				List<String> userListForDept = userListByDept.get(user
						.getDeptName());
				if (userListForDept == null) {
					userListForDept = new ArrayList<String>();
					userListForDept.add(user.getUserId());
					userListByDept.put(user.getDeptName(), userListForDept);
				} else {
					userListForDept.add(user.getUserId());
				}
			}
		}

		Map<String, List<String>> userListByDeptMap =new HashMap<String, List<String>>();
		Map<String, List<String>> userListByRoleMap = new HashMap<String, List<String>>();
		try{
			if( this.getUserListByDept()!=null){
				userListByDeptMap= this.getUserListByDept();
			}
			if(this.getUserListByRole()!=null){
				userListByRoleMap=this.getUserListByRole();
			}
		for (User currentUser : this.getUserList()) {

			String approvalRoles[] = (currentUser.getApprovalRole()).split(COLON);
			String approvalDepts[] = (currentUser.getApprovalDepartment()).split(COLON);

			List<String> approvedUserListByRole = new ArrayList<String>();

			if (approvalRoles != null && approvalRoles.length > 0) {

				for (String roleString : approvalRoles) {
					List<String> roleList=userListByRoleMap
					.get(roleString);
					if(roleList!=null){
						approvedUserListByRole.addAll(roleList);
					}
				}
			}

			List<String> approvedUserListByDept = new ArrayList<String>();

			if (approvalDepts != null && approvalDepts.length > 0) {

				for (String deptString : approvalDepts) {
					List<String> roleList=userListByDeptMap
							.get(deptString);
					if(roleList!=null){
						approvedUserListByDept.addAll(roleList);
					}
				}
				
			}

			StringBuilder allowedApprovers = new StringBuilder();

			
			if (this.getUserList() != null) {
				int count = 0;
				for (User usr : this.getUserList()) {
					
					if (approvedUserListByDept.contains(usr.getUserId())
							|| approvedUserListByRole.contains(usr.getUserId())) {

						if (count == 0) {
							allowedApprovers.append(usr.getUserId());
							count++;
						} else {
							allowedApprovers.append(COLON).append(
									usr.getUserId());
							count++;
						}
						
					}
					
				
				}
			}
			currentUser.setAllAllowedUserNamesForApproval(allowedApprovers.toString());
		}}catch(Exception e){
			e.printStackTrace();
			throw new Exception();
		}
	}

	public User getUser(Integer id) {
		return userMap.get(id);
	}

	public User getUser(String userId) {
		return userIdMap.get(userId);
	}

	public List<User> getUserList() {
		return userList;
	}

	public void setUserList(List<User> userList) {
		this.userList = userList;
	}

	
	public Map<Integer, User> getUserMap() {
		return userMap;
	}

	public void setUserMap(Map<Integer, User> userMap) {
		this.userMap = userMap;
	}

	public Map<String, User> getUserIdMap() {
		return userIdMap;
	}

	public void setUserIdMap(Map<String, User> userIdMap) {
		this.userIdMap = userIdMap;
	}

	public Map<String, List<String>> getUserListByRole() {
		return userListByRole;
	}

	public void setUserListByRole(Map<String, List<String>> userListByRole) {
		this.userListByRole = userListByRole;
	}

	public Map<String, List<String>> getUserListByDept() {
		return userListByDept;
	}

	public void setUserListByDept(Map<String, List<String>> userListByDept) {
		this.userListByDept = userListByDept;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((userIdMap == null) ? 0 : userIdMap.hashCode());
		result = prime * result
				+ ((userList == null) ? 0 : userList.hashCode());
		result = prime * result
				+ ((userListByDept == null) ? 0 : userListByDept.hashCode());
		result = prime * result
				+ ((userListByRole == null) ? 0 : userListByRole.hashCode());
		result = prime * result + ((userMap == null) ? 0 : userMap.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Users other = (Users) obj;
		if (userIdMap == null) {
			if (other.userIdMap != null)
				return false;
		} else if (!userIdMap.equals(other.userIdMap))
			return false;
		if (userList == null) {
			if (other.userList != null)
				return false;
		} else if (!userList.equals(other.userList))
			return false;
		if (userListByDept == null) {
			if (other.userListByDept != null)
				return false;
		} else if (!userListByDept.equals(other.userListByDept))
			return false;
		if (userListByRole == null) {
			if (other.userListByRole != null)
				return false;
		} else if (!userListByRole.equals(other.userListByRole))
			return false;
		if (userMap == null) {
			if (other.userMap != null)
				return false;
		} else if (!userMap.equals(other.userMap))
			return false;
		return true;
	}

}
