package com.ascent.ds.useradministration;

import java.sql.Connection;


import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.administration.AuthorizationBean;
import com.ascent.custumize.query.Query;
import com.ascent.ds.login.SessionChecker;
import com.ascent.integration.util.DbUtil;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.Role;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class AddUserAdminPlugin extends BasicDataSource implements PagesConstants {

	 
	private static final long serialVersionUID = -2875895744205343328L;
	private static final String GET_MAX_ROLE_ID = "GET_MAX_ROLE_ID";
	
	private static final String GET_MAX_DEP_ID="GET_MAX_DEP_ID";
	private static Logger logger = LogManager.getLogger(AddUserAdminPlugin.class);
	
	 
	
	
	
	
	public AddUserAdminPlugin(){
			logger.trace("AddUserAdminPlugin *************");
	}
	 Connection connection=null;
	private Role role;
	public DSResponse executeFetch(final DSRequest dsRequest){
		 
		 
		DSResponse dsResponse= new DSResponse();
		Map<Object,Object> criteriaMap= dsRequest.getValues();
		HttpSession httpSession= dsRequest.getHttpServletRequest().getSession();
		String userSelectedBusinessArea= (String)httpSession.getAttribute("user_selected_business_area");
		String userSelectedRecon=(String)httpSession.getAttribute("user_selected_recon");
		User user = (User) httpSession.getAttribute("userId");
		processData(criteriaMap,user,userSelectedBusinessArea,userSelectedRecon);
	 return dsResponse;
	}
	 
	  
	 public Map<String,Object> processData(Map<Object,Object> criteriaMap,User user,String userSelectedBusinessArea,String userSelectedRecon){
		 Map<String,Object> resultMap= new HashMap<String, Object>();
		 List<Map<String, Object>> record= new ArrayList<Map<String, Object>>();
		 String tableName= (String) criteriaMap.get("tableName");
		 String  dsName=(String) criteriaMap.get("dsName");
		 String  action=(String) criteriaMap.get("action");
		  Map<String,Object> recordMap =(Map<String, Object>) criteriaMap.get("data");//saveRoles
		  System.out.println(recordMap);
		  AddUserAdminPlugin addUserAdminPlugin=new AddUserAdminPlugin();
		  
		  Integer roleId=addUserAdminPlugin.getMaxRole();
		   
		   
		  AuthorizationBean  authorizationBean=new AuthorizationBean();
		  	recordMap.put("roleid", roleId);
		  	recordMap.put("status",PENDING_APPROVAL);
			recordMap.put("active_index","Y");
			recordMap.put("version",null);
			recordMap.put("created_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
		  record.add(recordMap);
		  resultMap.put(TABLE_NAME, tableName);
		  resultMap.put(DS_NAME, dsName);
		  resultMap.put("userSelectedBusinessArea",userSelectedBusinessArea);
		  resultMap.put("userSelectedRecon",userSelectedRecon);
		  resultMap.put(SELECTED_RECORDS, record);
		  resultMap.put(SAVE,action);
		  
		  System.out.println("resultMap ******* "+ resultMap);
		  
		  
		  authorizationBean.saveRoles(resultMap,user);
		  
			 return resultMap;
	 }
	 
	 public Integer getMaxRole() {
			Integer newRoleId =null;
			this.role = new Role();
			CustomerDao cDao = new CustomerDao();
			Map<String, Object> paramValues = new HashMap<String, Object>();
			try {
				Map<String, Object> IdMap = cDao.getDataBaseMap(GET_MAX_ROLE_ID,
						paramValues);
            System.out.println(IdMap+"88");
				Integer integer = (Integer) IdMap.get("maxId");
				int existingRoleId = integer;
				  newRoleId = existingRoleId + 1;

				this.role.setRoleId(newRoleId);
				System.out.println(role.getRoleId());
			 
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			return newRoleId;
			 
		
		}
	 public Integer getMaxDeptId() {
			Integer newRoleId =null;
			this.role = new Role();
			CustomerDao cDao = new CustomerDao();
			Map<String, Object> paramValues = new HashMap<String, Object>();
			try {
				Map<String, Object> IdMap = cDao.getDataBaseMap(GET_MAX_DEP_ID,
						paramValues);
            System.out.println(IdMap+"88");
				Integer integer = (Integer) IdMap.get("maxId");
				int existingRoleId = integer;
				  newRoleId = existingRoleId + 1;

				 
			
			 
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			return newRoleId;
			 
		
		}
		
		
		
		
	
	
	
	

}
