package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class MarkOrphanPlugIn extends BasicDataSource implements PagesConstants {


	private static final long serialVersionUID = -6870261822605048138L;
	

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();

		User user = (User) httpSession.getAttribute("userId");

		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		//TODO: is user authorized utility to verify user priviliges.
		
		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
		String integrationName = (String) reqCriteria.get("integrationName");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String moduleName =(String)reqCriteria.get("moduleName");
		List dsName=(List)reqCriteria.get("dsName");
		List dsList=(List)reqCriteria.get("dsList");
		
		

		//TODO: operation synchronization.
		if (selectedRecords != null) {

			StringBuilder commentSb = new StringBuilder();
			List<Object> workflowIds = new ArrayList<Object>();
			for (Map<String, Object> rec : selectedRecords) {
		if ((rec != null && rec.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec != null && rec.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
			
		}else{
			workflowIds.add(rec.get("RECON_ID"));
		}
		
	}
			String commentPrefix = " ";
			if (workflowIds.size() > 0) {
				result = new HashMap<String, Object>();
				//String commentPrefix = null;
				if (workflowIds.size() == 1) {
					commentSb.append("Selected record with RECON_ID ");
				} else if (workflowIds.size() > 1) {
					commentSb.append("Selected records with RECON_IDs ");
				}
				for (Object obj : workflowIds) {
					if (commentSb.length() != 0) {
						commentSb.append(",");
					}
					commentSb.append(obj);
				}
				commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
				updateResultStatus(result, FAILED, commentPrefix);
				response.setData(result);
				return response;
			}
						
		}

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsName);
		paramsMap.put("dsList", dsList);
		
		
		

		result = process(paramsMap);

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);

		return result;
	}

	

	public Map<String, Object> reject() {
		return null;
	}

	public Map<String, Object> approve() {
		return null;
	}

	// will Submit the operation basis on user credintials
	@SuppressWarnings("finally")
	private Map<String, Object> process(Map<String, Object> markOrphanArgs) {

		Connection connection = null;
		Map<String, Object> result = null;
		try {
			connection = DbUtil.getConnection();
			Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			String userId = (String) markOrphanArgs.get(USER_ID);
			List dsName = (List) markOrphanArgs.get(DS_NAME);
			markOrphanArgs.put(PERSIST_CLASS, MARK_ORPHANS_PLUGIN_CLASS_NAME);
			activityDataMap.put("activity_data", markOrphanArgs);
			
			

			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

			if (userAdminManager.isUserUnderWorkflow(user)) {
				result = new HashMap<String, Object>();

				String activityStatus = PENDING_APPROVAL;

				String businessArea = (String) markOrphanArgs.get(BUSINES_AREA);
				String reconName = (String) markOrphanArgs.get(RECON_NAME);
				String comments = (String) markOrphanArgs.get(COMMENTS);
				String requesterComments=userId+" : "+comments;
				String moduleName=(String)markOrphanArgs.get(MODULE);
				userAdminManager.createActivity(connection, user, businessArea, reconName, "RECON_MARK_ORPHAN",
						MARK_ORPHANS_OPERATION, activityDataMap, activityStatus, comments);

				updateResultStatus(result, SUCCESS,TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				
				String integrationName= (String) markOrphanArgs.get(INTEGRATION_NAME);
				LoadRegulator loadRegulator=new LoadRegulator();
				InsertRegulator insertRegulator=new InsertRegulator();
				//Recon Data Sorces will get through DS_NAME key from front end 
				String reconDataSource=(String) ((List)markOrphanArgs.get(DS_NAME)).get(0);
				String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
				List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) markOrphanArgs.get(SELECTED_RECORDS);
				System.out.println("Recon Table Name : "+reconTableName);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement reconDataSelectPstmt=null;
				PreparedStatement stagingDataSelectPstmt=null;
				PreparedStatement auditDataInsertPstmt=null;
				PreparedStatement stagingDataUpdatePstmt=null;
				PreparedStatement	reconDataUpdatePstmt=null;
				
				try{
					for(Map<String,Object> selectedRec:selectedRecords){
			
						Long reconId=(Long) selectedRec.get("RECON_ID");
				
						reconDataSelectPstmt=connection.prepareStatement("select * from "+reconTableName+" where RECON_ID="+reconId);
					ResultSet rs=	reconDataSelectPstmt.executeQuery();
					
					while(rs.next()){
						String reconSide=(String) rs.getObject("RECON_SIDE");
						long sid=(long) rs.getObject("SID");
						String stgTableName=reconSide+"_STG";
						String auditTableName=reconSide+"_STG_AUDIT";
					
						if(reconSide.equalsIgnoreCase("AUTH_ISS")){
							System.out.println("RECON_SIDE IS--------->"+reconSide);
							stgTableName="AUTH_ISSUER_STG";
							auditTableName="AUTH_ISSUER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("AUTH_ACQ")){
							System.out.println("RECON_SIDE IS--------->"+reconSide);
							stgTableName="AUTH_ACQUIRER_STG";
							auditTableName="AUTH_ACQUIRER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("MAST")){
							System.out.println("RECON_SIDE IS--------->"+reconSide);
							stgTableName="MASTER_STG";
							auditTableName="MASTER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("CTL")){
							System.out.println("RECON_SIDE IS--------->"+reconSide);
							stgTableName="CISO_STG";
							auditTableName="CISO_STG_AUDIT";
						}
						
						
						stagingDataSelectPstmt=connection.prepareStatement("select * from "+stgTableName+" where SID="+sid);
						List<String> columnList=new ArrayList<String>();
						ResultSet stagingRs=stagingDataSelectPstmt.executeQuery();
						ResultSetMetaData rsm=stagingRs.getMetaData();
						int columnCount=rsm.getColumnCount();
						Map stagingDataMap=new HashMap();
						
						Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
						auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
							
						
						long version=0;
						while(stagingRs.next()){
							version= Integer.parseInt(stagingRs.getObject("VERSION").toString());
							++version;
							for(int i=1;i<=columnCount;i++){
								columnList.add(rsm.getColumnName(i));
								stagingDataMap.put(rsm.getColumnName(i), stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
							}
						}
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
						String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='Y',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
						stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
						stagingDataUpdatePstmt.setObject(1, version);
						stagingDataUpdatePstmt.setObject(2, requesterComments);
						stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stagingDataUpdatePstmt.setObject(4, sid);
					    int row=	stagingDataUpdatePstmt.executeUpdate();
						System.out.println("rows success fully updated"+row);
					}
						
					reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='Y',UPDATED_ON=? WHERE RECON_ID="+reconId);
					reconDataUpdatePstmt.setObject(1, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					reconDataUpdatePstmt.executeUpdate();	
						
					}
					
					
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
				}
				//audit();
				return result;
			} else {

			

				result = persist(activityDataMap, APPROVED, connection);

			}
		} catch (Exception e) {
			e.printStackTrace();

			updateResultStatus(result, FAILED, OPERATION_FAILED);
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return result;
		}
		
	}

	public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {

		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator=new LoadRegulator();
		InsertRegulator insertRegulator=new InsertRegulator();
		try {
			Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
			String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
			List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
			String reconDataSource=(String) ((List)activityRecordsMap.get(DS_NAME)).get(0);
			String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
			List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) activityRecordsMap.get(SELECTED_RECORDS);
			 String userId = (String) activityDataMap.get("userId");
			String comments = (String) activityRecordsMap.get(COMMENTS);
			String requesterComments=userId+" : "+comments;
			connection = DbUtil.getConnection();
			Long reconId=null;
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement reconDataSelectPstmt=null;
			PreparedStatement stagingDataSelectPstmt=null;
			PreparedStatement auditDataInsertPstmt=null;
			PreparedStatement stagingDataUpdatePstmt=null;
			PreparedStatement reconDataUpdatePstmt=null;
			

			if (APPROVED.equalsIgnoreCase(status)) {
				try{
					for(Map<String,Object> selectedRec:selectedRecords){
						 reconId=(Long) selectedRec.get("RECON_ID");
						reconDataSelectPstmt=connection.prepareStatement("select * from "+reconTableName+" where RECON_ID="+reconId);
					ResultSet rs=	reconDataSelectPstmt.executeQuery();
					while(rs.next()){
						String reconSide=(String) rs.getObject("RECON_SIDE");
						long sid=(long) rs.getObject("SID");
						String stgTableName=reconSide+"_STG";
						String auditTableName=reconSide+"_STG_AUDIT";
						
						if(reconSide.equalsIgnoreCase("AUTH_ISS")){
							stgTableName="AUTH_ISSUER_STG";
							auditTableName="AUTH_ISSUER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("AUTH_ACQ")){
						
							stgTableName="AUTH_ACQUIRER_STG";
							auditTableName="AUTH_ACQUIRER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("MAST")){
							
							stgTableName="MASTER_STG";
							auditTableName="MASTER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("CTL")){
							
							stgTableName="CISO_STG";
							auditTableName="CISO_STG_AUDIT";
						}
						stagingDataSelectPstmt=connection.prepareStatement("select * from "+stgTableName+" where SID="+sid);
						List<String> columnList=new ArrayList<String>();
						ResultSet stagingRs=stagingDataSelectPstmt.executeQuery();
						ResultSetMetaData rsm=stagingRs.getMetaData();
						int columnCount=rsm.getColumnCount();
						Map stagingDataMap=new HashMap();
						Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
						auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
						long version=0;
						while(stagingRs.next()){
							version= Integer.parseInt(stagingRs.getObject("VERSION").toString());
							++version;
							for(int i=1;i<=columnCount;i++){
								columnList.add(rsm.getColumnName(i));
								stagingDataMap.put(rsm.getColumnName(i), stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
							}
						}
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
						String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?,RECON_ID=?,RECON_STATUS=?  WHERE SID=?";
						stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
						stagingDataUpdatePstmt.setObject(1, version);
						stagingDataUpdatePstmt.setObject(2, requesterComments);
						stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stagingDataUpdatePstmt.setObject(4, null);
						stagingDataUpdatePstmt.setObject(5, null);
						stagingDataUpdatePstmt.setObject(6, sid);
					    int row=	stagingDataUpdatePstmt.executeUpdate();
						
					}
					
					String reconUpdateQuery="UPDATE "+reconTableName+" SET ACTIVE_INDEX='N',WORKFLOW_STATUS='N'  WHERE RECON_ID=?";
					reconDataUpdatePstmt=connection.prepareStatement(reconUpdateQuery);
					reconDataUpdatePstmt.setObject(1, reconId);
					int reconRow=reconDataUpdatePstmt.executeUpdate();
					}
					
					
					
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
					DbUtil.closePreparedStatement(reconDataUpdatePstmt);
				}
				
				
				
				
				
			} else if (REJECTED.equalsIgnoreCase(status)) {
								
				try{
								
					for(Map<String,Object> selectedRec:selectedRecords){
			
						 reconId=(Long) selectedRec.get("RECON_ID");
				
						reconDataSelectPstmt=connection.prepareStatement("select * from "+reconTableName+" where RECON_ID="+reconId);
					ResultSet rs=	reconDataSelectPstmt.executeQuery();
					
					while(rs.next()){
						String reconSide=(String) rs.getObject("RECON_SIDE");
						long sid=(long) rs.getObject("SID");
						String stgTableName=reconSide+"_STG";
						String auditTableName=reconSide+"_STG_AUDIT";
						
						if(reconSide.equalsIgnoreCase("AUTH_ISS")){
							
							stgTableName="AUTH_ISSUER_STG";
							auditTableName="AUTH_ISSUER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("AUTH_ACQ")){
							
							stgTableName="AUTH_ACQUIRER_STG";
							auditTableName="AUTH_ACQUIRER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("MAST")){
							
							stgTableName="MASTER_STG";
							auditTableName="MASTER_STG_AUDIT";
						}else if(reconSide.equalsIgnoreCase("CTL")){
							
							stgTableName="CISO_STG";
							auditTableName="CISO_STG_AUDIT";
						}
						
						stagingDataSelectPstmt=connection.prepareStatement("select * from "+stgTableName+" where SID="+sid);
						List<String> columnList=new ArrayList<String>();
						ResultSet stagingRs=stagingDataSelectPstmt.executeQuery();
						ResultSetMetaData rsm=stagingRs.getMetaData();
						int columnCount=rsm.getColumnCount();
						Map stagingDataMap=new HashMap();
						
						Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
						auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
							
						
						long version=0;
						while(stagingRs.next()){
							version= Integer.parseInt(stagingRs.getObject("VERSION").toString());
							++version;
							for(int i=1;i<=columnCount;i++){
								columnList.add(rsm.getColumnName(i));
								stagingDataMap.put(rsm.getColumnName(i), stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
							}
						}
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
						String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?  WHERE SID=?";
						stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
						stagingDataUpdatePstmt.setObject(1, version);
						stagingDataUpdatePstmt.setObject(2, requesterComments);
						stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));	
						stagingDataUpdatePstmt.setObject(4, sid);
					    int row=	stagingDataUpdatePstmt.executeUpdate();
										
					}
						
						
					reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='N' WHERE RECON_ID="+reconId);
					reconDataUpdatePstmt.executeUpdate();
					
					}
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
					DbUtil.closePreparedStatement(reconDataUpdatePstmt);
				}
				
				
				
				
				
			} else if("PENDING".equalsIgnoreCase(status)){/*



				String tableName=integrationName+"_STG";
				String query="UPDATE "+tableName+" set ACTIVE_INDEX='Y',Status='APPROVED',WORKFLOW_STATUS='Y' where SID=?";
				PreparedStatement updateStmt=null;
				
				try{
					updateStmt=connection.prepareStatement(query);
					
				for(Map<String,Object> rec:records){
					updateStmt.setLong(1,(Long)rec.get(SID));
					updateStmt.addBatch();
				}
				updateStmt.executeUpdate();
				
				}catch(Exception e){
					e.printStackTrace();
				}
				
			
				updateResultStatus(result, FAILED, "Undefined Action");
			*/}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
					throws ClassNotFoundException, SQLException {
		String QueryParam=insertQueryConf.getQueryParam();
		List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,VERSION@BIGINT");
		
		
		if(auditData!=null){
			for(Map<String,Object> auditRec:auditData){
				Map paramValueMap=new HashMap();
				/*Long version= (Long) rec.get("VERSION");
				++version;
				auditRec.put("VERSION",version);*/
				paramValueMap.put("PARAM_VALUE_MAP", auditRec);
				insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
		}
		
	}


}
