function orphansSameSideReversalActivity(listGrid,title){
	var obj = listGrid.getSelectedRecord().activity_data;
	var data=obj.SELECTED_RECORDS;
	var ds=obj.DS_NAME;
	isc.Portlet.create({
		ID :"sameSideReversalActivityPortlet",
		canDragReposition : false,
		showCloseConfirmationMessage : false,
		title : title,
		height : "80%",
		width : "60%",
		left : "4%",
		top : "8%",
		autoCenter : true,
		isModal: true,
		showModalMask: true,
		items : [
		  isc.ListGrid.create({
		     ID : "selectedRecordGrid",
			 minFieldWidth : 150,
			 autoFitFieldWidths : true,
			 progressiveLoading : true,
			 showRollOverCanvas : true,
			 showAllColumns : true,
			 autoFitFields : true,
			 autoFetchData : false,
			 canEdit : false,
			 autoDraw : false,
			 autoFitWidthApproach : "both"
		  }),
		 isc.ListGrid.create({
		     ID : "attachedRecordGrid",
			 minFieldWidth : 150,
			 autoFitFieldWidths : true,
			 progressiveLoading : true,
			 showRollOverCanvas : true,
			 showAllColumns : true,
			 autoFitFields : true,
			 autoFetchData : false,
			 canEdit : false,
			 autoDraw : false,
			 autoFitWidthApproach : "both"
		  }),
		isc.DataSource.load(ds, function() {
						var dsObj = isc.DataSource.get(ds);
						var fields = [];
						var f = dsObj.fields;
						var h = 0;
						for ( var p in f) 
						{
							fields[h] = f[p];
							h++;
						}
				   selectedRecordGrid.setFields(fields);
				   attachedRecordGrid.setFields(fields);
				   var record=[]; var record2=[];
					  record[0]=data[0];record2[0]=data[1];
				   selectedRecordGrid.setData(record);
				   attachedRecordGrid.setData(record2);
					   }, true)
		]
		})
}