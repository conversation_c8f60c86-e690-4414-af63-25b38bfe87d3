package com.ascent.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.validator.Var;

import com.ascent.integration.util.DbUtil;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class DebitAtmDashboard extends BasicDataSource{

	public DebitAtmDashboard() {
		System.out.println("enters into dashboard constructors block");
	}
	String glsource="GL";
	
	String switchsource="SWITCH";
	
	String switchamount="Switch Amount";

	int glmatch=0;
	int glunmatch=0;
	int glforcematch=0;
	int glforceunmatch=0;
	int switchmatch=0;
	int switchunmatch=0;
	int switchforcematch=0;
	int switchforceunmatch=0;
	Object switchsum=0;
	Object glsum=0;
	int glorphantotal=0;
	int switchorphantotal=0;
	int gltotal=0;
	
	int switchtotal=0;
	
	
	public DSResponse executeFetch(DSRequest request){
		  DSResponse response=new DSResponse();
		  System.out.println("debit chart fetch ############ method");
		  Map cmap=request.getCriteria();
		  System.out.println("MAP........." + cmap);
		 
		  List<Map<String,Object>> list=new ArrayList<Map<String,Object>>();
		  try{
			  Connection connection=DbUtil.getConnection();
			  
			  
			  String glsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='GL'";
			     String glsumsql="select SUM(TRA_AMT) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='GL'";
			     String glorphansql="select count(*) from ONUS_ATM_DOHA_DEBIT_GL WITH  (NOLOCK) where RECON_ID is NULL";
			     String irisorphansql="select count(*) from ONUS_ATM_DOHA_DEBIT_IRIS WITH  (NOLOCK) where RECON_ID is NULL";

			     String switchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='SWITCH'";
			     String switchsumsql="select SUM(TRA_AMT) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='SWITCH'";

			     String glmatchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='GL' and MATCH_TYPE='AM'";
			     String glforcematchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='GL' and MATCH_TYPE='MM'";


			     String switchmatchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='SWITCH' and MATCH_TYPE='AM'";
			     String switchforcematchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='SWITCH' and MATCH_TYPE='MM'";

			     String glunmatchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK)  where RECON_SIDE='GL' and MATCH_TYPE='AU'";
			     String glforceunmatchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='GL' and MATCH_TYPE='MU'";

			     String switchunmatchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='SWITCH' and MATCH_TYPE='AU'";
			     String switchforceunmatchsql="select count(*) from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) where RECON_SIDE='SWITCH' and MATCH_TYPE='MU'";

			     Statement stmt=connection.createStatement();
			     ResultSet glrs=stmt.executeQuery(glsql);
			 //   Map<String,Object> map=new HashMap<String,Object>();
				
				if(glrs.next()){
					gltotal=(int) glrs.getObject(1);
				}
				
				 ResultSet switchorphanrs=stmt.executeQuery(irisorphansql);
				 if(switchorphanrs.next()){
					 switchorphantotal=(int) switchorphanrs.getObject(1);
				 }
				 ResultSet glorphanrs=stmt.executeQuery(glorphansql);
				 if(glorphanrs.next()){
					 glorphantotal=(int) glorphanrs.getObject(1);
				 }
				 ResultSet switchrs=stmt.executeQuery(switchsql);
				 if(switchrs.next()){
					 switchtotal=(int) switchrs.getObject(1);
				 }
				 ResultSet switchsumrs=stmt.executeQuery(switchsumsql);
				 if(switchsumrs.next()){
					 switchsum=switchsumrs.getObject(1);
				 }
				 ResultSet glsumrs=stmt.executeQuery(glsumsql);
				 if(glsumrs.next()){
					 glsum=glsumrs.getObject(1);
				 }
				 ResultSet glmatchrs=stmt.executeQuery(glmatchsql);
				 if(glmatchrs.next()){
					 glmatch=(int) glmatchrs.getObject(1);
				 }
				 
				  ResultSet switchmatchrs=stmt.executeQuery(switchmatchsql);
				 if(switchmatchrs.next()){
					 switchmatch=(int) switchmatchrs.getObject(1);
				 }
				 
				 ResultSet glunmatchrs=stmt.executeQuery(glunmatchsql);
				 if(glunmatchrs.next()){
					 glunmatch=(int) glunmatchrs.getObject(1);
				 }
				
				 ResultSet switchunmatchrs=stmt.executeQuery(switchunmatchsql);
				 if(switchunmatchrs.next()){
					 switchunmatch=(int) switchunmatchrs.getObject(1);
				 }
				 ResultSet glforcematchrs=stmt.executeQuery(glforcematchsql);
				 if(glforcematchrs.next()){
					 glforcematch=(int) glforcematchrs.getObject(1);
				 }
				 ResultSet switchforcematchrs=stmt.executeQuery(switchforcematchsql);
				 if(switchforcematchrs.next()){
					 switchforcematch=(int) switchforcematchrs.getObject(1);
				 }
				 ResultSet glforceunmatchrs=stmt.executeQuery(glforceunmatchsql);
				 if(glforceunmatchrs.next()){
					 glforceunmatch=(int) glforceunmatchrs.getObject(1);
				 }
				 ResultSet switchforceunmatchrs=stmt.executeQuery(switchforceunmatchsql);
				 if(switchforceunmatchrs.next()){
					 switchforceunmatch=(int) switchforceunmatchrs.getObject(1);
				 }
				
			
			    Map<String,Object> glmap=new HashMap<String,Object>();
			
			    glmap.put("total", gltotal);
			    glmap.put("source",glsource);
			    glmap.put("am",glmatch);
			    glmap.put("au",glunmatch);
			    glmap.put("mm",glforcematch);
			    glmap.put("mu",glforceunmatch);
			    glmap.put("mu",glforceunmatch);
			    glmap.put("sum",glsum);
			    glmap.put("orphans", glorphantotal);

			    	 list.add(glmap);
					 
			    	
			    	 Map<String,Object> switchmap=new HashMap<String,Object>();
						
			    	 switchmap.put("total", switchtotal);
			    	 
			    	 switchmap.put("source",switchsource);
			    	 
			    	 switchmap.put("am",switchmatch);
			    	 switchmap.put("au",switchunmatch);
			    	 switchmap.put("mm",switchforcematch);
			    	 switchmap.put("mu",switchforceunmatch);
			    	 switchmap.put("sum",switchsum);
			    	 switchmap.put("orphans",switchorphantotal);

				    	 list.add(switchmap);
			    	 
				    	
				    	 connection.close(); 
			    
			  }catch(Exception e){
				  e.printStackTrace();
			  }
		System.out.println(list);
		  response.setData(list);
		  return response;
		  }
				  

		  
	  }



