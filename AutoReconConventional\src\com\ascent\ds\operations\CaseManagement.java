package com.ascent.ds.operations;

import java.io.ByteArrayOutputStream;
import java.io.ObjectOutputStream;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class CaseManagement extends BasicDataSource implements PagesConstants{

	private static final long serialVersionUID = 1L;

	private static final String GET_ESCALATION_DEPARTMENTS = "GET_ESCALATION_DEPARTMENTS";
	private static final String GET_USERS_FOR_DEPARTMENT = "GET_USERS_FOR_DEPARTMENT";
	private static final String GET_CASEID = "GET_CASEID";
	private static final String GET_ALL_REASONS = "GET_ALL_REASONS";
	private static final String CHECK_CASE_BY_SID = "CHECK_CASE_BY_SID";
	//private static final String GET_EMAIL_ID_BY_USER = "GET_EMAIL_ID_BY_USER";

	private static Logger logger = LogManager.getLogger(CaseManagement.class.getName());

	public DSResponse executeFetch(final DSRequest request)throws Exception 
	{
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();

		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();

		DSResponse dsResponse = new DSResponse();

		@SuppressWarnings("rawtypes")
		Map requestParams = request.getCriteria();
		String action = (String) requestParams.get("action");

		if (action != null && action.equals("GetCaseID")) {

			String reconName = (String) requestParams.get("reconName");
			Query queryConf = queryConfs.getQueryConf(GET_CASEID);
			List<Map<String, Object>> caseIdList = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			
			String caseID = caseIdList.get(0).get("CASE_ID").toString();
			
			if ("ONS".equals(reconName)) {
				caseID = "FO_"+caseID;
			} else if ("MPCLEAR".equals(reconName)) {
				caseID = "FM_"+caseID;
			} else if ("SUSPENSE ACCOUNTS".equals(reconName)) {
				caseID = "FS_"+caseID;
			} else if ("ATM/POS VISA ISSUER".equals(reconName)) {
				caseID = "CVI_"+caseID;
			} else if ("ATM VISA ACQUIRER".equals(reconName)) {
				caseID = "CVA_"+caseID;
			} else if ("CREDIT CARD STATEMENT".equals(reconName)) {
				caseID = "CCC_"+caseID;
			} else if ("POS NI ACQUIRER".equals(reconName)) {
				caseID = "CPA_"+caseID;
			} else if ("ATM MASTER CARD ACQUIRER".equals(reconName)) {
				caseID = "CMA_"+caseID;
			} else if ("ATM TRANSACTIONS".equals(reconName)) {
				caseID = "CATM_"+caseID;
			} else if ("CDM".equals(reconName)) {
				caseID = "CCDM_"+caseID;
			} else if ("ACH".equals(reconName)) {
				caseID = "CACH_"+caseID;
			}
			
			dsResponse.setData(caseID);
			logger.debug("caseID : "+caseID);

		}
		
		if (action != null && action.equals("GetAllReasons")) {

			List<Map<String, Object>> reasonList = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(GET_ALL_REASONS);

			reasonList = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(reasonList);
			logger.debug("reasonList : "+reasonList);

		}
		
		if (action != null && action.equals("saveReason")) {

			Connection connection = null;
//			HashMap<String,String> responseMap=new HashMap<String,String>();
			String responseMsg = null;
			HashMap<String,Object> paramValueMap=new HashMap<String,Object>();
			InsertRegulator insertRegulator=new InsertRegulator();

			String reason = String.valueOf(requestParams.get("reason"));
			paramValueMap.put("REASON", reason);

			connection = DbUtil.getConnection();
			Queries  queries=ascentWebMetaInstance.getWebQueryConfs();
			Query query = null;

			try {
				query = queries.getQueryConf("SAVE_CASE_MANAGEMENT_REASON");
			} catch (Exception e) {

				e.printStackTrace();
			}

			String queryString = query.getQueryString();// getQuerieString();
			String queryParam = query.getQueryParam();// getQuerieParam();
			Map<String, Object> parmMap = new HashMap<String, Object>();
			parmMap.put("INSERT_QRY_PARAMS", queryParam);
			parmMap.put("INSERT_QRY", queryString);
			parmMap.put("PARAM_VALUE_MAP", paramValueMap);
			int rowsAffected = insertRegulator.insert(connection, parmMap);
			logger.debug("rowsAffected : "+rowsAffected);

			if(rowsAffected > 0) {
				responseMsg = "Reason added successfully";
			}
			else {
				//responseMap.put("message", "Error while adding Reason");
				responseMsg =  "Reason already existed";
			}

			dsResponse.setData(responseMsg);
		}

		if (action != null && action.equals("GetEscalationDepartments")) {

			List<Map<String, Object>> departmentList = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(GET_ESCALATION_DEPARTMENTS);

			departmentList = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(departmentList);
			logger.debug("departmentList : "+departmentList);

		}

		if (action != null && action.equals("GetUsersForDepartment")) {

			String selectedDepartment = String.valueOf(requestParams.get("selectedDepartment"));

			List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(GET_USERS_FOR_DEPARTMENT);

			paramMap.put("DEPT_NAME", selectedDepartment);
			userList = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(userList);
			logger.debug("userList : "+userList);

		}
		
		if (action != null && action.equals("CheckCase")) {
			
			long sid = Long.parseLong((requestParams.get("sid").toString()));

			List<Map<String, Object>> caseCount = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(CHECK_CASE_BY_SID);

			paramMap.put("sid", sid);
			caseCount = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(caseCount);
			logger.debug("caseCount : "+caseCount);
			
		}
		
		if (action != null && action.equals("saveCaseManagement")) {
			
			Connection connection = null;
			Map<String, Object> result = null;
			boolean responseStatus = false;
			String responseMsg = null;
			CustomerDao customerDao = new CustomerDao();
			DSResponse response = new DSResponse();
			@SuppressWarnings("rawtypes")
			Map reqCriteria = request.getValues();
			HttpSession httpSession = request.getHttpServletRequest().getSession();
			
			connection = DbUtil.getConnection();
			User user = (User) httpSession.getAttribute("userId");

			if (user == null) {
				result = new HashMap<String, Object>();
				result.put(STATUS, FAILED);
				result.put(COMMENT, "Session Already Expired, Please Re-Login");
				response.setData(result);
				return response;
			}

			String businessArea = (String) httpSession.getAttribute("user_selected_business_area");
			String reconName = (String) httpSession.getAttribute("user_selected_recon");
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
			//Map<String, Object> selectedRecords = (Map<String, Object>) reqCriteria.get("selectedRecords");
			//System.out.println("selectedRecords = "+selectedRecords);
			/*for (Map.Entry<String, Object> entry : selectedRecords.entrySet()) {
				List<Map<String, Object>> caseData = (List<Map<String, Object>>) entry.getValue();
				for (Map<String, Object> map : caseData) {
					transactionAmount = Double.parseDouble(map.get("AMOUNT").toString());
					sid = Long.parseLong(map.get("SID").toString());
				}
			}*/
			
			String caseID = reqCriteria.get("case_id").toString();
			String comments = (String) reqCriteria.get("comments");
			String reason = (String) reqCriteria.get("reason");
			String department = (String) reqCriteria.get("department");
			String toMail = (String) reqCriteria.get("user_id");
			String moduleName = (String) reqCriteria.get("moduleName");
			String integrationName = (String) reqCriteria.get("integrationName");
			double transactionAmount= Double.parseDouble(reqCriteria.get("centrifugalAmount").toString());
			long sid = Long.parseLong(reqCriteria.get("sid").toString());
			String dsName = (String) reqCriteria.get("dsName");
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> recordsList = (List<Map<String, Object>>) reqCriteria.get("recordsList");
			String reconDataSource = (String) reqCriteria.get("reconDataSource");
			
			String stgTableName = (String) reqCriteria.get("stgTableName");
			stgTableName = stgTableName.replace("_VIEW", "");
			
			String reconTableName = null;
			if(reconDataSource.contains("_UNMATCH")){
				reconTableName = reconDataSource.substring(0, reconDataSource.length()-8);
			}
			else if(reconDataSource.contains("_TOTAL_SUMMARY")){
				reconTableName = reconDataSource.substring(0, reconDataSource.length()-14);
			}
			
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			Map<String, Object> operationParamsMap = new HashMap<String, Object>();
			
			//keeping(PUT) activity_data in paramsMap
			paramsMap.put(ACTION, action);
			paramsMap.put(USER_ID, user.getUserId());
			paramsMap.put(SELECTED_RECORDS, selectedRecords);
			paramsMap.put(INTEGRATION_NAME, integrationName);
			paramsMap.put(BUSINES_AREA, businessArea);
			paramsMap.put(RECON_NAME, reconName);
			paramsMap.put(COMMENTS, comments);
			paramsMap.put(MODULE, moduleName);
			paramsMap.put(DS_NAME, dsName);
			paramsMap.put("recordsList", recordsList);
			paramsMap.put("stgTableName", stgTableName);
			paramsMap.put("reconTableName", reconTableName);
			paramsMap.put(PERSIST_CLASS, CASE_MANAGEMENT_PLUGIN_CLASS_NAME);
			
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(bos);
			oos.writeUnshared(paramsMap);
			oos.flush();
			oos.close();
			byte[] actData = bos.toByteArray();
			paramsMap.put("activity_data", actData);
			bos.close();
			//keeping(PUT) activity_data in paramsMap end

			
			//keeping(PUT) RECON_CASE_MANAGEMENT table data in paramsMap
			paramsMap.put("comment", comments);
			paramsMap.put("business_area", businessArea);
			paramsMap.put("reason", reason);
			paramsMap.put("department", department);
			paramsMap.put("assignedTO", toMail);
			paramsMap.put("transaction_amount", transactionAmount);
			paramsMap.put("sid", sid);
			paramsMap.put("case_id", caseID);
			
			Long id = System.currentTimeMillis();
			id = id + (Long) loadRegulator.generateLazySeqNo("recon_activity_flow_seq");
			paramsMap.put("activity_id", id);
			paramsMap.put("activity_name", moduleName);
			paramsMap.put("activity_owner", user.getUserId());
			paramsMap.put("created_by", user.getUserId());
			paramsMap.put("allowed_approvers", user.getReporting());
			
			paramsMap.put("activity_type", CASE_MANAGEMENT_OPERATION);
			paramsMap.put("status", PENDING_APPROVAL);
			paramsMap.put("active_index", "Y");
			
			Date d = new Date();
			String date = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(d);
			paramsMap.put("created_on", date);
			paramsMap.put("recently_updated_on", date);
			paramsMap.put("activity_level", 1);
			paramsMap.put("version", 1);
			paramsMap.put("recon", reconName);
			
			
			logger.debug("paramsMap : "+paramsMap);
			
			responseStatus = customerDao.insertData(connection, paramsMap, "INSERT_RECON_CASE_MANAGEMENT");
			
			//keeping(PUT) RECON_CASE_OPERATION table data in operationParamsMap
			operationParamsMap.put("sid", sid);
			operationParamsMap.put("case_id", caseID);
			operationParamsMap.put("operation_name", "Create");
			operationParamsMap.put("operation_date", date);
			operationParamsMap.put("comment", comments);
			operationParamsMap.put("user_id", user.getUserId());
			
			customerDao.insertData(connection, operationParamsMap, "INSERT_RECON_CASE_OPERATION");
			
			if(responseStatus)
				responseMsg = "Case Submitted Sucessfully";
			else
				responseMsg = "Operation Failed";
			
			dsResponse.setData(responseMsg);
			
			
			//Mail Trigger Start
			
			String subject = "Recon : Case Management Creation ("+reconName+") : "+caseID;
			String content = "Dear Sir/Madam, <br> <br> <br> The captioned request has been Created by the " +user.getUserId()+ " and assigned to you for further action. <br> <br>";
			
			Map<String,String> map = new HashMap<String,String>();
			map.put("subject", subject);
			map.put("content", content);
			map.put("comment", comments);
			map.put("toMail", toMail);
			map.put("ccMail", user.getEmailId());
			
			new CaseMailTrigger().setContentMailTrigger(selectedRecords,map);
			
			//Mail Trigger End
			
			
			
			
			//STG & RECON table Audit and Update
			/*for(Map<String,Object> selectedRec:selectedRecords){

				//Insert STG to STG_AUDIT
				String StgAuditInsert="INSERT INTO "+stgTableName+"_AUDIT SELECT * FROM "+stgTableName+" WHERE SID=?";
				auditInsertPstmt=connection.prepareStatement(StgAuditInsert);
				auditInsertPstmt.setObject(1,sid);
				auditInsertPstmt.executeUpdate();
				
				//Update STG table
				String updateQuery="update "+stgTableName+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? from  "+stgTableName+" where sid = ?";
				stgUpdatePstmt=connection.prepareStatement(updateQuery);
				stgUpdatePstmt.setObject(1,"Y");
				stgUpdatePstmt.setObject(2,comments);
				stgUpdatePstmt.setObject(3,Long.valueOf(selectedRec.get("VERSION")+"")+1);
				stgUpdatePstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
				stgUpdatePstmt.setObject(5,sid);
				stgUpdatePstmt.executeUpdate();
				
				//Updating Recon table
				if(!(reconTableName+"_ORPHANS").equalsIgnoreCase(dsName)) {
					String updateReconToAudit="update "+reconTableName+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? where SID = ?";
					updateReconPstmt=connection.prepareStatement(updateReconToAudit);
					updateReconPstmt.setObject(1,"Y");
					updateReconPstmt.setObject(2,comments);
					updateReconPstmt.setObject(3,Long.valueOf(selectedRec.get("VERSION")+"")+1);
					updateReconPstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
					updateReconPstmt.setObject(5,sid);
					updateReconPstmt.executeUpdate();
				}
			}*/
			//STG & RECON table Audit and Update end
			
			/*//Send mail to Case Created User
			String content = "case created by <b>"+user.getUserId()+"</b>,<b>"+user.getEmailId()+"</b>";
			try {
				new MailTrigger().sendEtlExtractionConformationMail(content,user.getEmailId());
			} catch (Exception e) {
				e.printStackTrace();
			}
			//Send mail to Case Created User
			
			//Send mail to Case Assigned User
			Query queryConf = queryConfs.getQueryConf(GET_EMAIL_ID_BY_USER);
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("USER_ID", userId);
			List<Map<String, Object>> mailId = loadRegulator.loadCompleteData(paramMap, queryConf);
			logger.debug("mailId : "+mailId);
			String content1 = "case received by <b>"+userId+"</b>,<b>"+mailId.get(0).get("EMAIL_ID").toString()+"</b>";
			try {
				new MailTrigger().sendEtlExtractionConformationMail(content1,mailId.get(0).get("EMAIL_ID").toString());
			} catch (Exception e) {
				e.printStackTrace();
			}
			//Send mail to Case Created User*/	
			
			connection.close();
			
		}

		return dsResponse;
	}

}

