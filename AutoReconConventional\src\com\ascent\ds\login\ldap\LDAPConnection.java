package com.ascent.ds.login.ldap;

import java.io.File;
import java.util.Hashtable;
import java.util.Properties;
import java.util.ResourceBundle;

import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;

import org.apache.log4j.Logger;

import com.ascent.ds.login.LoginDetails;

public class LDAPConnection {

	private static final Logger logger = Logger.getLogger(LDAPConnection.class);
	private String domain;
	private String ldapHost;
	private String searchBase;
	private LdapContext ldapContext;
	private String adminUser;
	@SuppressWarnings("unused")
	private String organisationUnit;

	private String organisation;
	private String adminPass;
	private String ssl = null;
	private String keystore = null;

	ResourceBundle resourceBundle = ResourceBundle.getBundle(LDAPConstants.BASENAME);

	public LDAPConnection() {

		// this.domain =
		// ResourceBundleUtil.getString(ConfigFileUtil.getProperty("LDAP_SERVER_TYPE"),"domain");
		this.ldapHost = resourceBundle.getString("host");
		// this.searchBase =
		// "DC="+ResourceBundleUtil.getString(ConfigFileUtil.getProperty("LDAP_SERVER_TYPE"),"domain")+","+ResourceBundleUtil.getString(ConfigFileUtil.getProperty("LDAP_SERVER_TYPE"),"searchbase");
		this.organisation = resourceBundle.getString("o");
		
		this.adminUser = resourceBundle.getString("diruser");
		this.adminPass = resourceBundle.getString("dirpassword");
		this.ssl = resourceBundle.getString("ssl");

		this.domain = resourceBundle.getString("domain");

	}

	/***************************************************************************
	 * This method is used for getting the context connecting the Directory
	 * Server
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public LdapContext getContext() {

		Hashtable env = new Hashtable();
		env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		env.put(Context.PROVIDER_URL, ldapHost);
		env.put(Context.SECURITY_AUTHENTICATION, "simple");
		env.put(Context.SECURITY_PRINCIPAL, adminUser + "@" + domain + ".com");
		env.put(Context.SECURITY_CREDENTIALS, adminPass);

		if (ssl.equalsIgnoreCase("true")) {

			keystore = resourceBundle.getString("keystorepath");
			System.setProperty("javax.net.ssl.trustStore", keystore);
			env.put(Context.SECURITY_PROTOCOL, "ssl");

		}

		try {
			ldapContext = new InitialLdapContext(env, null);
			System.out.println("LDAP CONTEXT---->>>>> "+ldapContext);
			System.out.println("Context Created Successfully..");

		} catch (NamingException e) {
			System.out.println(e);
			logger.error("Error in getContext:" + e.getMessage());
			ldapContext = null;
		}

		return ldapContext;
	}

	/***************************************************************************
	 * This method is used for getting the context connecting the Directory
	 * Server for accessing a certain domain
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public LdapContext getContext(String domain) {

		// setDomain(domain);
		this.searchBase = "DC=" + domain + "," + resourceBundle.getString("searchbase");
		// ResourceBundleUtil.getString(ConfigFileUtil.getProperty("LDAP_SERVER_TYPE"),domain)
		Hashtable env = new Hashtable();
		env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		env.put(Context.PROVIDER_URL, ldapHost);
		env.put(Context.SECURITY_AUTHENTICATION, "simple");
		env.put(Context.SECURITY_PRINCIPAL, adminUser + "@" + domain + ".com");
		env.put(Context.SECURITY_CREDENTIALS, adminPass);

		if (ssl.equalsIgnoreCase("true")) {
			keystore = resourceBundle.getString("keystorepath");
			System.setProperty("javax.net.ssl.trustStore", keystore);
			env.put(Context.SECURITY_PROTOCOL, "ssl");

		}

		try {
			ldapContext = new InitialLdapContext(env, null);

		} catch (NamingException e) {

			logger.error("Error in getContext:" + e.getMessage());
			ldapContext = null;
		}

		return ldapContext;
	}

	/***************************************************************************
	 * This method is used for authenticating the user connecting to the
	 * Directory server support user authentication
	 * 
	 * @param user
	 * @param password
	 * @return
	 */

	@SuppressWarnings("unchecked")
	public boolean getContext(String user, String password) {
	

		String userString = LoginDetails.createLdapUserString(user, organisation, domain);
		System.out.println("HELLO  " + userString);
		boolean status = false;
		Hashtable env = new Hashtable();
		env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		env.put(Context.PROVIDER_URL, ldapHost);
		env.put(Context.SECURITY_AUTHENTICATION, "simple");
		env.put(Context.SECURITY_PRINCIPAL, userString);
		env.put(Context.SECURITY_CREDENTIALS, password);

		if (ssl.equalsIgnoreCase("true")) {

			keystore = resourceBundle.getString("keystorepath");
			System.setProperty("javax.net.ssl.trustStore", keystore);
			env.put(Context.SECURITY_PROTOCOL, "ssl");

		}

		try {
			ldapContext = new InitialLdapContext(env, null);
			System.out.println("LDAP CONTEXT---->>>>> "+ldapContext);
			status = true;

		} catch (NamingException e) {

			// e.printStackTrace();
			logger.error("Error in getContext:" + e.getMessage());
			status = false;
			ldapContext = null;
		} finally {

			try {
				if (ldapContext != null)
					ldapContext.close();
			} catch (NamingException e) {

				// e.printStackTrace();
				logger.error("Error in getContext:" + e.getMessage());
			}
		}

		return status;
	}

	/***************************************************************************
	 * This method is used for authenticating the user connecting to the
	 * Directory server support user authentication for a specific domain
	 * 
	 * @param user
	 * @param password
	 * @return
	 */

	@SuppressWarnings("unchecked")
	public boolean getContext(String user, String password, String domain) {

		boolean status = false;

		Hashtable env = new Hashtable();
		// Hashtable<String, String> env = new Hashtable<String, String>();

		env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		env.put(Context.PROVIDER_URL, ldapHost);
		env.put(Context.SECURITY_AUTHENTICATION, "simple");
		// env.put(Context.SECURITY_PRINCIPAL, user + "@" + domain + ".com");
		env.put(Context.SECURITY_PRINCIPAL, "cn=" + user + ",ou=ascent1,dc=maxcrc,dc=com");
		env.put(Context.SECURITY_CREDENTIALS, password);
		env.put(Context.REFERRAL, "follow");

		if (ssl.equalsIgnoreCase("true")) {
			keystore = resourceBundle.getString("keystorepath");
			System.setProperty("javax.net.ssl.trustStore", keystore);
			env.put(Context.SECURITY_PROTOCOL, "ssl");

		}

		try {
			ldapContext = new InitialLdapContext(env, null);
			status = true;

		} catch (NamingException e) {

			logger.error("Error in getContext:" + e.getMessage());
			status = false;
			ldapContext = null;
		} finally {

			try {
				ldapContext.close();
			} catch (NamingException e) {

				logger.error("Error in getContext:" + e.getMessage());
			}
		}

		return status;
	}

	public void closeConnection(LdapContext context) {

		try {
			if (ldapContext != null)
				ldapContext.close();
		} catch (NamingException e) {
			// TODO Auto-generated catch block
			logger.error("Error in closeConnection:" + e.getMessage());
		}

	}

	public String getDomain() {
		return this.domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getSearchBase() {
		return searchBase;
	}

	public void setSearchBase(String searchBase) {
		this.searchBase = searchBase;
	}
}
