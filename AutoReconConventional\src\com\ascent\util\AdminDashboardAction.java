package com.ascent.util;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.ascent.ds.login.UsersSessionCounterListener;
import com.google.gson.Gson;

/**
 * Servlet implementation class AdminDashboardAction
 */
@WebServlet("/AdminDashboardAction")
public class AdminDashboardAction extends HttpServlet {
	private static final long serialVersionUID = 1L;

	/**
	 * @see HttpServlet#HttpServlet()
	 */
	public AdminDashboardAction() {
		super();
		// TODO Auto-generated constructor stub
	}

	protected void doGet(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		String first=request.getParameter("first");
		String second=request.getParameter("Last");
		System.out.println(first);
		

		List<Map<String, Object>> reportValues = new ArrayList<Map<String, Object>>();
		HttpSession httpSession = request.getSession();
		
		
		
		Map<String, Object> map = new HashMap<String, Object>();
		UsersSessionCounterListener counter = (UsersSessionCounterListener) httpSession
				.getAttribute("counter");
		if(counter!=null){
			int activeSessionNumber = counter.getActiveSessionNumber();

			if (activeSessionNumber != 0) {

				map.put("ACTIVATED_USERS", activeSessionNumber);
			} else {
				map.put("ACTIVATED_USERS", 0);
			}
		}else{
			map.put("ACTIVATED_USERS", 0);
		}
	


		AdminDashboard ch = new AdminDashboard();
		Map<String, Object> activitiesCount = ch.getActivitiesCount();
		Map<String, Object> approvedactivitiesCount = ch.getApprovedActivitiesCount();
		Map<String, Object> recentactivitiesCount = ch.getRecentActivitiesCount();

		try {
			reportValues = ch.getReportValues(first,second);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		reportValues.add(map);
		reportValues.add(activitiesCount);
		reportValues.add(approvedactivitiesCount);
		reportValues.add(recentactivitiesCount);

		response.setContentType("application/json");
		List list=new ArrayList();
		list.add(reportValues);
		list.add(map);
		list.add(activitiesCount);
		list.add(approvedactivitiesCount);
		list.add(recentactivitiesCount);

		Gson gson = new Gson();

		String jsonData = gson.toJson(reportValues);

		PrintWriter printWriter = response.getWriter();
		System.out.println("JSAON DATA"+jsonData);
		printWriter.print(gson.toJson(list));

	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {

	}

}
