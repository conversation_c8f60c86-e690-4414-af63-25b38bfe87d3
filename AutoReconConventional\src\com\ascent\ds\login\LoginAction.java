package com.ascent.ds.login;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.SchedulerException;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.ds.login.ldap.ADUserAccess;
import com.ascent.ds.login.ldapv2.LDAPClient;
import com.ascent.integration.util.DbUtil;
import com.ascent.mailschedular.MailSchedular;
import com.ascent.recon.util.PasswordGeneratorUtil;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.PrivilegeDetails;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class LoginAction extends BasicDataSource {

	private static Logger logger = LogManager.getLogger(LoginAction.class.getName());
	public AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Properties ldapProperties=ascentWebMetaInstance.getLdapProps();
	int loginAttempt = 1;

	public LoginAction() {

	}

	static {
		try {
			new MailSchedular().mailSchedule();

		} catch (SchedulerException e) {
			logger.error(e);
			e.printStackTrace();
		}
	}

	private static final long serialVersionUID = 1L;
	private static final String ACTION = "LOGIN";
	@SuppressWarnings("unused")
	private static final String LOCKED = "LOCKED";
	private static final String UNLOCKED = "UNLOCKED";

	Boolean upstreamFlag = false;
	Boolean reportsFlag = false;
	Boolean activityManagerFlag = false;
	Boolean userAdministrationFlag = false;
	Boolean masterDataFlag = false;
	Boolean stagingDataFlag = false;
	Boolean reconFlag = false;
	Boolean onUsReconFlag = false;
	Boolean issuerReconFlag = false;
	Boolean acquirerReconFlag = false;
	Boolean payrollReconFlag = false;

	Boolean CARDFlag = false;

   
	Boolean PAYMENTFlag = false;
	Boolean ASSETOPERATIONFlag = false;
	Boolean UTILITYPAYMENTFlag = false;
	Boolean TRADEFINANCEFlag = false;

	
	
	/// CONVENTIONAL START ///////
		Boolean FINANCEDEPTFlag          = false; 
		Boolean CENTRALOPERATIONDEPTFlag = false;
		
		Boolean CBO_FINANCE_DEPT 				= false;
		Boolean ONS_FINANCE_DEPT 				= false;
		Boolean SUSPENSE_ACCOUNTS_FINANCE_DEPT  = false;
		Boolean MPCLEAR_FINANCE_DEPT            = false;
		
		Boolean ATM_POS_VISA_ISSUER_CARD_DEPT 		= false;
		Boolean ATM_VISA_ACQUIRER_CARD_DEPT   		= false;
		Boolean ATM_MASTER_CARD_ACQUIRER_CARD_DEPT  = false;
		Boolean POS_NI_ACQUIRER_CARD_DEPT 			= false; 
		Boolean CREDIT_CARD_STATEMENT_CARD_DEPT     = false;
		Boolean ECOM_OFFUS_CARD_DEPT     = false;
		Boolean ECOM_ONUS_CARD_DEPT     = false;
		
		
		Boolean ATM_TRANSACTIONS_CENTRAL_DEPT =  false;
		Boolean CDM_CENTRAL_DEPT              = false;
		Boolean ACH_CENTRAL_DEPT              = false;
		
		
		
		Boolean FINANCEONSFlag          = false; 
	/// CONVENTIONAL END ///////
	
	Boolean dashBoardFlag = false;

	Boolean ATM_DEBIT_CARD = false;
	
	Boolean MASTER_CARD_CREDIT = false;
	Boolean  OMANET = false;
	Boolean MASTER_CARD_DEBIT = false;
	Boolean CENTRAL_BANK_ECC=false;
	Boolean PAYMENT_ORDER = false;
	Boolean CENTRAL_BANK_FT = false;

	boolean PAYROLL =false;

	Boolean NOSTRO = false;
	
	Boolean LOAN_AND_INSURANCE = false;
	Boolean UTIL_PAY = false;
	Boolean TRADE_DEAL = false;
	Boolean SUSPENSE_GL = false;
	Boolean MARGIN_GL = false;

	Boolean ISSUER_POS_MASTER_CREDIT_CARD = false;
	Boolean ACQUIRER_ATM_NAPS_CARDS = false;
	Boolean ACQUIRER_ATM_NAPS2_CARDS = false;
	Boolean ACQUIRER_ATM_VISA_CARDS = false;
	Boolean ACQUIRER_ATM_UP_CARDS = false;
	Boolean ACQUIRER_POS_NAPS_CARD = false;
	Boolean ACQUIRER_POS_VISA_CARDS = false;
	Boolean ACQUIRER_POS_MASTER_CARDS = false;
	Boolean ACQUIRER_POS_UP_CARDS = false;
	Boolean ACQUIRER_POS_QPAY_CARDS = false;
	Boolean ACQUIRER_ATM_MASTER_CARDS = false;

	Boolean IRIS_FLAG = false;
	Boolean CTL_ONLAUTH_ISSUER_FLAG = false;
	Boolean CTL_EOD_POS_batches_FLAG = false;
	Boolean CTL_EOD_POS_mtransactions_FLAG = false;
	Boolean CTL_EOD_POS_miso_FLAG = false;
	Boolean QCB_QPAY_ACQUIRER_ONLY_FLAG = false;
	Boolean CTL_EOD_batches_FLAG = false;
	Boolean CTL_EOD_ctransactions_FLAG = false;
	Boolean CTL_EOD_ciso_FLAG = false;
	Boolean Visa_FLAG = false;
	Boolean Master_FLAG = false;
	Boolean GL_FLAG = false;

	Boolean Recon_Action_View = false;
	Boolean Recon_Process_Flag = false;
	Map<String, Map<String, Object>> cssMainMap = new HashMap<String, Map<String, Object>>();

	public DSResponse executeFetch(final DSRequest request) throws Exception {

		HttpSession httpSession = request.getHttpServletRequest().getSession();
		DSResponse dsResponse = new DSResponse();
		LoginDetails details = new LoginDetails();
		cssMainMap = new HashMap<String, Map<String, Object>>();
		@SuppressWarnings("rawtypes")
		Map reqCriteria = request.getValues();

		String userName = String.valueOf(reqCriteria.get("name"));
		updateActiveUsersAfterThirtyDays(userName); // Update user not usind for morethan 30days
		String userEnteredPassword = String.valueOf(reqCriteria.get("password"));
		String selectedBisunessArea = "";
		String selectedRecon = "";
		if (reqCriteria.get("selectedBusinessArea") != null && reqCriteria.get("selectedRecon") != null) {

			selectedBisunessArea = String.valueOf(reqCriteria.get("selectedBusinessArea"));
			selectedRecon = String.valueOf(reqCriteria.get("selectedRecon"));
		}

		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userName);

		//@ankush added 
		CustomerDao customerDao = new CustomerDao();
		PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
		 
		String stringData =loadPasswordPolicy.getAllowUserToChangeOwnPwd();
		String errorMsg = "";
		Map<String, Object> responseMap = new HashMap<String, Object>();
		Map<String, Object> userAuditLogsMap = new HashMap<String, Object>();
		Map<String, Object> userParamValueMap = new HashMap<String, Object>();
		
		if (userName != null) {
			
			if (user != null) {
				httpSession.setAttribute("isLdapUser", user.getIsLdapUser());
				httpSession.setAttribute("AllowUserToChangeOwnPwd", stringData);//@added3-8-2017 by ankush for pass policy
				String departmentname = "";
				if (user.getDeptName() != null) {

					departmentname = user.getDeptName();
				}

				System.out.println(" IS LDAP USER ---->>" + user.getIsLdapUser());
				boolean isvalidUser = false;
				
				
				

				if (user.getIsLdapUser().equals("Y")) {

					 isvalidUser = checkUserwithldapV2(userName, departmentname, userEnteredPassword);

					if (isvalidUser != false) {

						dsResponse = authenticateUser(user, httpSession, details, selectedBisunessArea, selectedRecon,
								responseMap, userAuditLogsMap, userParamValueMap, dsResponse, errorMsg);
						
						///////
						System.out.println("responseString:   "+responseMap.get("responseString").toString());
						
						if(responseMap.get("responseString")!="login")
						{
							boolean multiLogin=false;
							multiLogin= multipleLoginCheck(httpSession, user);
							if(multiLogin)
								{
									responseMap.put("ERROR_MSG", MessageConstants.ALREADY_LOGGED_IN);
									String data = "login";
									responseMap.put("responseString", data);
									dsResponse.setData(responseMap);
								}

						}
						//////

					} else {
						String data = "login";
						responseMap.put("responseString", data);
						 errorMsg = "Invalid Credentials.. Please Contact Administrator.. ";
						responseMap.put("ERROR_MSG", MessageConstants.INVALID_CREDENTIALS);
						dsResponse.setData(responseMap);
						// return dsResponse;
					}

					return dsResponse;
				} else {
					
					disableInactiveUserAccount();
					dsResponse = authenticateUserWithPassPolicy(user, userEnteredPassword, httpSession, details,
							selectedBisunessArea, selectedRecon, responseMap, userAuditLogsMap, userParamValueMap,
							dsResponse, errorMsg);
					//
					System.out.println("responseString:   "+responseMap.get("responseString").toString());
					
					if(responseMap.get("responseString")!="login")
					{
						boolean multiLogin=false;
						multiLogin= multipleLoginCheck(httpSession, user);
						if(multiLogin)
							{
								responseMap.put("ERROR_MSG", MessageConstants.ALREADY_LOGGED_IN);
								String data = "login";
								responseMap.put("responseString", data);
								dsResponse.setData(responseMap);
							}

					}
				}

			} else {
				String data = "login";
				responseMap.put("responseString", data);
				// errorMsg = "Invalid UserName " + userName + " Please, Enter
				// Valid UserName ";
				responseMap.put("ERROR_MSG",
						MessageConstants.INVALID_USERNAME + userName + MessageConstants.INVALID_USERNAME_REQ_MSG);
				dsResponse.setData(responseMap);
				return dsResponse;
			}
		} else {
			
			
			System.out.println("nbsnbc    errr in login     ");
			
			String data = "login";
			responseMap.put("responseString", data);
			// errorMsg = "UserName Should Not Be Empty";
			responseMap.put("ERROR_MSG", MessageConstants.EMPTY_USERNAME_FIELD);
			dsResponse.setData(responseMap);
			return dsResponse;
		}
		
		return dsResponse;

	}

	private Map<String, Map<String, Object>> getCssMap(User user) {

		Map<String, PrivilegeDetails> map = null;
		if (user.getPrivilege() == null) {
			map = new HashMap<>();
		} else {
			if (user.getPrivilege().getPrivilegeDetailsMap() != null) {
				map = user.getPrivilege().getPrivilegeDetailsMap();
			} else {
				map = new HashMap<>();
			}
		}

		Set<String> keySet = map.keySet();

		Map<String, Object> cssMap = new HashMap<String, Object>();
		setUpCssMap(cssMap);
		@SuppressWarnings("unused")
		Set<String> innerTempSet = new TreeSet<String>(keySet);
		for (String string : keySet) {

			
			
			///////////////////////////////////////////////  CONVENTIONAL ADDED START ////////////////////////////////////////////////////////
			
					if (string.contains("OMAN:Recon:FINANCE DEPARTMENT")) 
					{
						FINANCEDEPTFlag = true;
						reconFlag = true;
		
						cssMap.put("reconFlag", reconFlag);
						cssMap.put("FINANCEDEPTFlag", FINANCEDEPTFlag);  // NEED TO CHANGE THE KEY(onUsReconFlag in CONVENTIONAL) in all
						
					}
					
						if (string.contains("CBO")) {
							CBO_FINANCE_DEPT = true;
							cssMap.put("CBO", CBO_FINANCE_DEPT);
						}
						if (string.contains("ONS")) {
							ONS_FINANCE_DEPT = true;
							cssMap.put("ONS", ONS_FINANCE_DEPT);
						}
						if (string.contains("SUSPENSE ACCOUNTS")) {
							SUSPENSE_ACCOUNTS_FINANCE_DEPT = true;
							cssMap.put("SUSPENSE_ACCOUNTS", SUSPENSE_ACCOUNTS_FINANCE_DEPT);
						}
						if (string.contains("MPCLEAR")) {
							MPCLEAR_FINANCE_DEPT = true;
							cssMap.put("MPCLEAR", MPCLEAR_FINANCE_DEPT);
						}
					if (string.contains("OMAN:Recon:CARD DEPARTMENT")) 
					{
						CARDFlag = true;
						reconFlag = true;
			
						cssMap.put("reconFlag", reconFlag);
						cssMap.put("CARDDEPTFlag", CARDFlag);
				    }
					
						if (string.contains("ATM/POS VISA ISSUER")) {                   
							ATM_POS_VISA_ISSUER_CARD_DEPT = true;
							cssMap.put("ATM_POS_VISA_ISSUER", ATM_POS_VISA_ISSUER_CARD_DEPT);
						}
						if (string.contains("ATM VISA ACQUIRER")) {
							ATM_VISA_ACQUIRER_CARD_DEPT = true;
							cssMap.put("ATM_VISA_ACQUIRER", ATM_VISA_ACQUIRER_CARD_DEPT);
						}
						if (string.contains("ATM MASTER CARD ACQUIRER")) {
							ATM_MASTER_CARD_ACQUIRER_CARD_DEPT = true;
							cssMap.put("ATM_MASTER_CARD_ACQUIRER", ATM_MASTER_CARD_ACQUIRER_CARD_DEPT);
						}
						if (string.contains("POS NI ACQUIRER")) {
							POS_NI_ACQUIRER_CARD_DEPT = true;
							cssMap.put("POS_NI_ACQUIRER", POS_NI_ACQUIRER_CARD_DEPT);
						}
						if (string.contains("CREDIT CARD STATEMENT")) {
							CREDIT_CARD_STATEMENT_CARD_DEPT = true;
							cssMap.put("CREDIT_CARD_STATEMENT", CREDIT_CARD_STATEMENT_CARD_DEPT);
						}
						if (string.contains("OPG BD ACQ")) {
							ECOM_ONUS_CARD_DEPT = true;
							cssMap.put("ECOM_ONUS", ECOM_ONUS_CARD_DEPT);
						}
						if (string.contains("OPG OTHER BANK ACQ")) {
							ECOM_OFFUS_CARD_DEPT = true;
							cssMap.put("ECOM_OFFUS", ECOM_OFFUS_CARD_DEPT);
						}
				if (string.contains("OMAN:Recon:CENTRAL OPERATION DEPARTMENT")) 
				{
					CENTRALOPERATIONDEPTFlag = true;
					reconFlag = true;
				
					cssMap.put("reconFlag", reconFlag);
					cssMap.put("CENTRALOPERATIONDEPTFlag", CENTRALOPERATIONDEPTFlag);
				}	
						if (string.contains("ATM TRANSACTIONS")) {           
							ATM_TRANSACTIONS_CENTRAL_DEPT = true;
							cssMap.put("ATM_TRANSACTIONS", ATM_TRANSACTIONS_CENTRAL_DEPT);
						}
						if (string.contains("CDM")) {
							CDM_CENTRAL_DEPT = true;
							cssMap.put("CDM", CDM_CENTRAL_DEPT);
						}
						if (string.contains("ACH")) {
							ACH_CENTRAL_DEPT = true;
							cssMap.put("ACH", ACH_CENTRAL_DEPT);
						}
				
			///////////////////////////////////////////////////////  CONVENTIONAL ADDED END ////////////////////////////////////////////////////////
			
			
			if (string.contains("MUSCAT:Recon:CARD")) {
				CARDFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("onUsReconFlag", CARDFlag);

			}
			
			
			if (string.contains("ATM - DEBIT CARD")) {
				ATM_DEBIT_CARD = true;
				cssMap.put("ATM_DEBIT_CARD", ATM_DEBIT_CARD);
			}
			if (string.contains("MASTER CARD CREDIT")) {
				MASTER_CARD_CREDIT = true;
				cssMap.put("MASTER_CARD_CREDIT", MASTER_CARD_CREDIT);
			}
			
			if (string.contains("OMANET")) {
				OMANET = true;
				cssMap.put("OMANET", OMANET);
			}
			if (string.contains("CENTRAL-BANK-ECC")) {
				CENTRAL_BANK_ECC = true;
				cssMap.put("CENTRAL_BANK_ECC", CENTRAL_BANK_ECC);
			}
			
			if (string.contains("MASTER CARD DEBIT")) {
				MASTER_CARD_DEBIT = true;
				cssMap.put("MASTER_CARD_DEBIT", MASTER_CARD_DEBIT);
			}
		
			
			
			if (string.contains("MUSCAT:Recon:PAYMENT")) {
				PAYMENTFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("PAYMENTFlag", PAYMENTFlag);

			}
			if (string.contains("PAYMENT ORDER")) {
				PAYMENT_ORDER = true;
				cssMap.put("PAYMENT_ORDER", PAYMENT_ORDER);
			}
			if (string.contains("MUSCAT:Recon:ASSET OPERATION")) {
				ASSETOPERATIONFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("ASSETOPERATIONFlag", ASSETOPERATIONFlag);

			}
			
			if (string.contains("LOAN AND INSURANCE") || string.contains("FINANCE AND INSURANCE")) {
				PAYMENT_ORDER = true;
				cssMap.put("PAYMENT_ORDER", PAYMENT_ORDER);
			}
			if (string.contains("CENTRAL BANK FT")) {
				CENTRAL_BANK_FT = true;
				cssMap.put("CENTRAL_BANK_FT", CENTRAL_BANK_FT);
			}
			if (string.contains("NOSTRO")) {
				NOSTRO = true;
				cssMap.put("NOSTRO", NOSTRO);
			}
			
			if (string.contains("LOAN AND INSURANCE") || string.contains("FINANCE AND INSURANCE")) {
				LOAN_AND_INSURANCE = true;
				cssMap.put("LOAN_AND_INSURANCE", LOAN_AND_INSURANCE);
			}
			if (string.contains("MUSCAT:Recon:UTILITY PAYMENT")) {
				UTILITYPAYMENTFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("UTILITYPAYMENTFlag", UTILITYPAYMENTFlag);

			}
			if (string.contains("UTIL PAY")) {
				UTIL_PAY = true;
				cssMap.put("UTIL_PAY", UTIL_PAY);
			}
			if (string.contains("MUSCAT:Recon:TRADE FINANCE")) {
				TRADEFINANCEFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("TRADEFINANCEFlag", TRADEFINANCEFlag);

			}
			if (string.contains("TRADE DEAL")) {
				TRADE_DEAL = true;
				cssMap.put("TRADE_DEAL", TRADE_DEAL);
			}
			if (string.contains("SUSPENSE GL")) {
				SUSPENSE_GL = true;
				cssMap.put("SUSPENSE_GL", SUSPENSE_GL);
			}
			if (string.contains("MARGIN GL")) {
				MARGIN_GL = true;
				cssMap.put("MARGIN_GL", MARGIN_GL);
			}

			if (string.contains("QATAR:Recon:ISSUER")) {
				issuerReconFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("issuerReconFlag", issuerReconFlag);

			}
			if (string.contains("ATM - NAPS - DEBIT CARD")) {
			//	ISSUER_ATM_NAPS_DEBIT_CARD = true;
			//	cssMap.put("ISSUER_ATM_NAPS_DEBIT_CARD", ISSUER_ATM_NAPS_DEBIT_CARD);

			}
			

			

			if (string.contains("POS - MASTER - CREDIT CARD")) {
				ISSUER_POS_MASTER_CREDIT_CARD = true;
				cssMap.put("ISSUER_POS_MASTER_CREDIT_CARD", ISSUER_POS_MASTER_CREDIT_CARD);

			}

			
			
			if (string.contains("QATAR:Recon:ACQUIRER")) {
				acquirerReconFlag = true;
				reconFlag = true;

				cssMap.put("reconFlag", reconFlag);
				cssMap.put("acquirerReconFlag", acquirerReconFlag);

			}
			if (string.contains("ATM - NAPS - CARDS")) {
				ACQUIRER_ATM_NAPS_CARDS = true;
				cssMap.put("ACQUIRER_ATM_NAPS_CARDS", ACQUIRER_ATM_NAPS_CARDS);

			}

			if (string.contains("ATM - NAPS 2 - CARDS")) {
				ACQUIRER_ATM_NAPS2_CARDS = true;
				cssMap.put("ACQUIRER_ATM_NAPS2_CARDS", ACQUIRER_ATM_NAPS2_CARDS);

			}

			if (string.contains("ATM - VISA - CARDS")) {
				ACQUIRER_ATM_VISA_CARDS = true;
				cssMap.put("ACQUIRER_ATM_VISA_CARDS", ACQUIRER_ATM_VISA_CARDS);

			}

			if (string.contains("ATM - UP - CARDS")) {
				ACQUIRER_ATM_UP_CARDS = true;
				cssMap.put("ACQUIRER_ATM_UP_CARDS", ACQUIRER_ATM_UP_CARDS);

			}
			if (string.contains("POS - NAPS - CARDS")) {
				ACQUIRER_POS_NAPS_CARD = true;
				cssMap.put("ACQUIRER_POS_NAPS_CARD", ACQUIRER_POS_NAPS_CARD);
			}

			if (string.contains("POS - VISA - CARDS")) {
				ACQUIRER_POS_VISA_CARDS = true;
				cssMap.put("ACQUIRER_POS_VISA_CARDS", ACQUIRER_POS_VISA_CARDS);
			}

			if (string.contains("POS - MASTER - CARDS")) {
				ACQUIRER_POS_MASTER_CARDS = true;
				cssMap.put("ACQUIRER_POS_MASTER_CARDS", ACQUIRER_POS_MASTER_CARDS);

			}
			if (string.contains("POS - UP - CARDS")) {
				ACQUIRER_POS_UP_CARDS = true;
				cssMap.put("ACQUIRER_POS_UP_CARDS", ACQUIRER_POS_UP_CARDS);

			}

			if (string.contains("POS - QPAY - CARDS")) {
				ACQUIRER_POS_QPAY_CARDS = true;
				cssMap.put("ACQUIRER_POS_QPAY_CARDS", ACQUIRER_POS_QPAY_CARDS);

			}
			if (string.contains("ATM-MASTER-CARDS")) {
				ACQUIRER_ATM_MASTER_CARDS = true;
				cssMap.put("ACQUIRER_ATM_MASTER_CARDS", ACQUIRER_ATM_MASTER_CARDS);

			}

		

			if (string.contains("Upstream:")) {
				upstreamFlag = true;
				cssMap.put("upstreamFlag", upstreamFlag);

			}
			if (string.contains("IRIS")) {
				IRIS_FLAG = true;
				cssMap.put("IRIS_FLAG", IRIS_FLAG);

			}
			if (string.contains("CTL_ONLAUTH_ISSUER")) {
				CTL_ONLAUTH_ISSUER_FLAG = true;
				cssMap.put("CTL_ONLAUTH_ISSUER_FLAG", CTL_ONLAUTH_ISSUER_FLAG);

			}
			if (string.contains("CTL_EOD_POS batches")) {
				CTL_EOD_POS_batches_FLAG = true;
				cssMap.put("CTL_EOD_POS_batches_FLAG", CTL_EOD_POS_batches_FLAG);

			}
			if (string.contains("CTL_EOD_POS mtransactions")) {
				CTL_EOD_POS_mtransactions_FLAG = true;
				cssMap.put("CTL_EOD_POS_mtransactions_FLAG", CTL_EOD_POS_mtransactions_FLAG);

			}
			if (string.contains("CTL_EOD_POS miso")) {
				CTL_EOD_POS_miso_FLAG = true;
				cssMap.put("CTL_EOD_POS_miso_FLAG", CTL_EOD_POS_miso_FLAG);

			}
			if (string.contains("QCB_QPAY_ACQUIRER_ONLY")) {
				QCB_QPAY_ACQUIRER_ONLY_FLAG = true;
				cssMap.put("QCB_QPAY_ACQUIRER_ONLY_FLAG", QCB_QPAY_ACQUIRER_ONLY_FLAG);

			}
			if (string.contains("CTL_EOD_batches")) {
				CTL_EOD_batches_FLAG = true;
				cssMap.put("CTL_EOD_batches_FLAG", CTL_EOD_batches_FLAG);

			}
			if (string.contains("CTL_EOD_ctransactions")) {
				CTL_EOD_ctransactions_FLAG = true;
				cssMap.put("CTL_EOD_ctransactions_FLAG", CTL_EOD_ctransactions_FLAG);

			}
			if (string.contains("CTL_EOD ciso")) {
				CTL_EOD_ciso_FLAG = true;
				cssMap.put("CTL_EOD_ciso_FLAG", CTL_EOD_ciso_FLAG);

			}
			if (string.contains("Visa")) {
				Visa_FLAG = true;
				cssMap.put("Visa_FLAG", Visa_FLAG);

			}
			if (string.contains("Master")) {
				Master_FLAG = true;
				cssMap.put("Master_FLAG", Master_FLAG);

			}

			if (string.contains("GL")) {
				GL_FLAG = true;
				cssMap.put("GL_FLAG", GL_FLAG);

			} // Recon Process
			if (string.contains("Recon Process")) {
				Recon_Process_Flag = true;
				cssMap.put("Recon_Process_Flag", Recon_Process_Flag);
			}
			if (string.contains("Recon Action View")) {
				Recon_Action_View = true;
				cssMap.put("Recon_Action_View", Recon_Action_View);
			}

			if (string.contains("Reports")) {
				reportsFlag = true;
				cssMap.put("reportsFlag", reportsFlag);
			}
			if (string.contains("Activity Manager")) {
				activityManagerFlag = true;
				cssMap.put("activityManagerFlag", activityManagerFlag);
			}

			if (string.contains("User Administration")) {
				userAdministrationFlag = true;
				cssMap.put("userAdministrationFlag", userAdministrationFlag);
			}
			/*
			 * masterDataFlag = true; cssMap.put("masterDataFlag",
			 * masterDataFlag);
			 */
			if (string.contains("Master Data")) {
				masterDataFlag = true;
				cssMap.put("masterDataFlag", masterDataFlag);
			}
			if (string.contains("Dashboard")) {
				dashBoardFlag = true;
				cssMap.put("dashBoardFlag", dashBoardFlag);
			}
			// dashBoardFlag
			cssMainMap.put(string, cssMap);
		}

		return cssMainMap;
	}

	void setUpccMainMap(Map<String, Map<String, Object>> cssMainMap) {
		Map<String, Object> cssMap = new HashMap<String, Object>();
		setUpCssMap(cssMap);
		cssMainMap.put("DEPOSITS", cssMap);
		cssMap.put("dashBoardFlag", cssMap);
		cssMainMap.put("upstreamFlag", cssMap);//
		cssMainMap.put("IRIS_FLAG", cssMap);
		cssMainMap.put("CTL_ONLAUTH_ISSUER_FLAG", cssMap);
		cssMainMap.put("CTL_EOD_POS_batches_FLAG", cssMap);
		cssMainMap.put("CTL_EOD_POS_mtransactions_FLAG", cssMap);
		cssMainMap.put("CTL_EOD_POS_miso_FLAG", cssMap);
		cssMainMap.put("QCB_QPAY_ACQUIRER_ONLY_FLAG", cssMap);
		cssMainMap.put("CTL_EOD_batches_FLAG", cssMap);
		cssMainMap.put("CTL_EOD_ctransactions_FLAG", cssMap);
		cssMainMap.put("CTL_EOD_ciso_FLAG", cssMap);
		cssMainMap.put("Visa_FLAG", cssMap);
		cssMainMap.put("Master_FLAG", cssMap);
		cssMainMap.put("GL_FLAG", cssMap);
		//
		cssMainMap.put("stagingDataFlag", cssMap);
		cssMainMap.put("reportsFlag", cssMap);
		cssMainMap.put("activityManagerFlag", cssMap);
		cssMainMap.put("masterDataFlag", cssMap);
		cssMainMap.put("acquirerReconFlag", cssMap);
		cssMainMap.put("userAdministrationFlag", cssMap);
		cssMainMap.put("PAYMENTFlag", cssMap);
		cssMainMap.put("CARDFlag", cssMap);
		
		
		////  CONVENTIONAL START /////////
		
			cssMainMap.put("FINANCEDEPTFlag", cssMap);
			cssMainMap.put("CENTRALOPERATIONDEPTFlag", cssMap);
			
		
		//// CONVENTIONAL END

		cssMainMap.put("ASSETOPERATIONFlag", cssMap);
		cssMainMap.put("UTILITYPAYMENTFlag", cssMap);
		cssMainMap.put("TRADEFINANCEFlag", cssMap);
		cssMainMap.put("ATM_DEBIT_CARD", cssMap);
		cssMainMap.put("MASTER_CARD_CREDIT", cssMap);
		cssMainMap.put("OMANET", cssMap);
		cssMainMap.put("MASTER_CARD_DEBIT", cssMap);

		cssMainMap.put("PAYROLL", cssMap);
		cssMainMap.put("PAYMENT_ORDER", cssMap);
		cssMainMap.put("CENTRAL_BANK_FT", cssMap);
		cssMainMap.put("NOSTRO", cssMap);
		cssMainMap.put("LOAN_AND_INSURANCE", cssMap);
		cssMainMap.put("UTIL_PAY", cssMap);//
		cssMainMap.put("TRADE_DEAL", cssMap);
		cssMainMap.put("SUSPENSE_GL", cssMap);

		cssMainMap.put("MARGIN_GL", cssMap);
		cssMainMap.put("ACQUIRER_ATM_NAPS2_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_ATM_VISA_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_ATM_UP_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_POS_NAPS_CARD", cssMap);
		cssMainMap.put("ACQUIRER_POS_VISA_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_POS_MASTER_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_POS_UP_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_POS_QPAY_CARDS", cssMap);
		cssMainMap.put("ACQUIRER_ATM_MASTER_CARDS", cssMap);
		cssMainMap.put("PAYROLL", cssMap);
		cssMainMap.put("reconFlag", cssMap);
		//
		cssMainMap.put("Recon_Process_Flag", cssMap);
		cssMainMap.put("Recon_Action_View", cssMap);

	}

	private void setUpCssMap(Map<String, Object> cssMap) {

		cssMap.put("DEPOSITS", false);
		cssMap.put("upstreamFlag", false);//
		cssMap.put("IRIS_FLAG", false);
		cssMap.put("CTL_ONLAUTH_ISSUER_FLAG", false);
		cssMap.put("CTL_EOD_POS_batches_FLAG", false);
		cssMap.put("CTL_EOD_POS_mtransactions_FLAG", false);
		cssMap.put("CTL_EOD_POS_miso_FLAG", false);
		cssMap.put("QCB_QPAY_ACQUIRER_ONLY_FLAG", false);
		cssMap.put("CTL_EOD_batches_FLAG", false);
		cssMap.put("CTL_EOD_ctransactions_FLAG", false);
		cssMap.put("CTL_EOD_ciso_FLAG", false);
		cssMap.put("Visa_FLAG", false);
		cssMap.put("Master_FLAG", false);
		cssMap.put("GL_FLAG", false);
		//
		cssMap.put("dashBoardFlag", false);
		cssMap.put("stagingDataFlag", false);
		cssMap.put("reportsFlag", false);
		cssMap.put("activityManagerFlag", false);
		cssMap.put("masterDataFlag", false);
		cssMap.put("acquirerReconFlag", false);
		cssMap.put("userAdministrationFlag", false);
		cssMap.put("issuerReconFlag", false);
		cssMap.put("onsReconFlag", false);
		
		cssMap.put("FINANCEONSFlag", false);
		
	////  CONVENTIONAL START /////////
		
				/*cssMainMap.put("FINANCEDEPTFlag", cssMap);
				cssMainMap.put("CENTRALOPERATIONDEPTFlag", cssMap);*/
			
	//// CONVENTIONAL END
		cssMap.put("PAYMENTFlag", false);
		cssMap.put("ASSETOPERATIONFlag", false);
		cssMap.put("UTILITYPAYMENTFlag", false);
		cssMap.put("TRADEFINANCEFlag", false);
		cssMap.put("POS_CREDIT_CARD_FLAG", false);
		cssMap.put("POS_PAYROLL_CARD_FLAG", false);

		cssMap.put("ATM_DEBIT_CARD", false);
		cssMap.put("MASTER_CARD_CREDIT", false);
		cssMap.put("OMANET", false);
		cssMap.put("MASTER_CARD_DEBIT", false);
		cssMap.put("PAYROLL", false);
		cssMap.put("PAYMENT_ORDER", false);//
		cssMap.put("CENTRAL_BANK_FT", false);
		cssMap.put("NOSTRO", false);

		cssMap.put("LOAN_AND_INSURANCE", false);
		cssMap.put("UTIL_PAY", false);
		cssMap.put("TRADE_DEAL", false);
		cssMap.put("SUSPENSE_GL", false);
		cssMap.put("MARGIN_GL", false);
		cssMap.put("ACQUIRER_POS_VISA_CARDS", false);
		cssMap.put("ACQUIRER_POS_MASTER_CARDS", false);
		cssMap.put("ACQUIRER_POS_UP_CARDS", false);
		cssMap.put("ACQUIRER_POS_QPAY_CARDS", false);
		cssMap.put("ACQUIRER_ATM_MASTER_CARDS", false);
		cssMap.put("PAYROLL", false);
		cssMap.put("reconFlag", false);

		cssMap.put("Recon_Process_Flag", false);
		cssMap.put("Recon_Action_View", false);
		
		//DHOFAR STARTS
		
		cssMap.put("FINANCEDEPTFlag", false);
			cssMap.put("ONS", false);
			cssMap.put("CBO", false);
			cssMap.put("SUSPENSE_ACCOUNTS", false);
			cssMap.put("MPCLEAR", false);
			
		cssMap.put("CARDDEPTFlag", false);
			cssMap.put("ATM_POS_VISA_ISSUER", false);
			cssMap.put("ATM_VISA_ACQUIRER", false);
			cssMap.put("ATM_MASTER_CARD_ACQUIRER", false);
			cssMap.put("POS_NI_ACQUIRER", false);
			cssMap.put("CREDIT_CARD_STATEMENT", false);
			cssMap.put("ECOM_OFFUS", false);
			cssMap.put("ECOM_ONUS", false);
			
		cssMap.put("CENTRALOPERATIONDEPTFlag", false);
			cssMap.put("CDM", false);
			cssMap.put("ATM_TRANSACTIONS", false);
			cssMap.put("ACH", false);

		//DHOFAR END
		
	}

	public boolean checkUserwithldap(String user_id, String deptName, String password) {
		ADUserAccess adUserAccess = new ADUserAccess();
		user_id = user_id + ",ou=" + deptName;

		boolean isValidUser = adUserAccess.authenticateUser(user_id, password);
		return isValidUser;
	}

	public DSResponse authenticateUser(User user, HttpSession httpSession, LoginDetails details,
			String selectedBisunessArea, String selectedRecon, Map<String, Object> responseMap,
			Map<String, Object> userAuditLogsMap, Map<String, Object> userParamValueMap, DSResponse dsResponse,
			String errorMsg) throws Exception {

		String usersRole = user.getSystemRole();
		String user_Name = user.getUserName();

		if (user.getPrivilege() != null) {
			boolean flag = false;
			boolean flag1 = false;
			String stringTemp = "";
			List<PrivilegeDetails> privDetailsList = user.getPrivilege().getPrivilegeDetails();
			
			System.out.println(privDetailsList);

			for (PrivilegeDetails privilegeDetails : privDetailsList) {
				String recon = privilegeDetails.getRecon();

				String module = privilegeDetails.getModule();
				String[] splitedRecon = recon.split("@");

				for (int i = 0; i < splitedRecon.length; i++) {
					if (!stringTemp.contains(splitedRecon[i])) {
						stringTemp = stringTemp + "," + "'" + splitedRecon[i] + "'";

					}

				}

				if ((module.equals("User Administration") || module.equals("Master Data")) && !flag) {
					stringTemp = stringTemp + "''";
					flag = true;
				}

				if ((module.equals("Recon") && recon.equals("PAYROLL")) && !flag1) {
					stringTemp = stringTemp + "," + "'" + recon + "'";
					flag1 = true;
				}
			}
			if (!stringTemp.isEmpty() && !stringTemp.equals("''")) {
				stringTemp = stringTemp.substring(1, stringTemp.length());
			} else {
				stringTemp = "''";
			}
			httpSession.setAttribute("recon", stringTemp);
			user.setReconString(stringTemp);
			if (usersRole != null && usersRole.contains("Admin") && selectedBisunessArea.equals("")
					&& selectedRecon.equals("")) {

				if (user.getPrivilege().getPrivilegeDetailsMap() != null) {
					if (user.getPrivilege().getPrivilegeDetailsMap() != null) {

						Map<String, Map<String, Object>> cssMap = getCssMap(user);
						httpSession.setAttribute("cssMap", cssMap);
						httpSession.setAttribute("userId", user);
						httpSession.setAttribute("userName", user_Name);
						httpSession.setAttribute("usersRole", usersRole);
						List<Map<String, Object>> reconNamesList = details.getAllReconName();
						httpSession.setAttribute("List_of_All_Recon", reconNamesList);
						httpSession.setAttribute("user_selected_business_area", "Admin");
						httpSession.setAttribute("user_selected_recon", "");
						String sucess = "Admin_Index";
						responseMap.put("responseString", sucess);

						dsResponse.setData(responseMap);

						userParamValueMap.put("user_id", user.getUserId());
						userParamValueMap.put("action", ACTION);
						userParamValueMap.put("date_time", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						userParamValueMap.put("bussiness_area", "Admin");
						userParamValueMap.put("recon_name", selectedRecon);
						userParamValueMap.put("user_role", usersRole);

						userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

						int userAuditLogs = details.userAuditLogs(userAuditLogsMap);
						System.out.println("LOGS VALUE____>>>>> " + userAuditLogs);

						return dsResponse;
					} else {
						String data = "login";
						responseMap.put("responseString", data);
						// errorMsg = "Invalid User " + user_Name + " Please,
						// Enter Valid Login Crediantials ";
						responseMap.put("ERROR_MSG", MessageConstants.INVALID_USERNAME + user_Name
								+ MessageConstants.INVALID_USERNAME_REQ_MSG);
						dsResponse.setData(responseMap);
						return dsResponse;
					}

				} else {
					String data = "login";

					responseMap.put("responseString", data);
					// errorMsg = "Invalid User " + user_Name + " Please, Enter
					// Valid Login Crediantials ";
					responseMap.put("ERROR_MSG",
							MessageConstants.INVALID_USERNAME + user_Name + MessageConstants.INVALID_USERNAME_REQ_MSG);
					dsResponse.setData(responseMap);
					return dsResponse;
				}

			} else {

				if (user.getSystemRole() != null && !user.getSystemRole().contains("Admin")
						&& !selectedBisunessArea.equals("") && !selectedRecon.equals("")) {

					for (PrivilegeDetails privilegeDetails : privDetailsList) {
						String recon = privilegeDetails.getRecon();
						String[] splitedReconArr = recon.split("@");
						for (int i = 0; i < splitedReconArr.length; i++) {
							if (selectedRecon.equals(splitedReconArr[i])) {

								if (user.getPrivilege().getPrivilegeDetailsMap() != null) {
									if (user.getPrivilege().getPrivilegeDetailsMap() != null) {

										Map<String, Map<String, Object>> cssMap = getCssMap(user);
										httpSession.setAttribute("cssMap", cssMap);
										httpSession.setAttribute("userId", user);
										httpSession.setAttribute("userName", user_Name);
										httpSession.setAttribute("usersRole", usersRole);
										List<Map<String, Object>> reconNamesList = details.getAllReconName();
										httpSession.setAttribute("List_of_All_Recon", reconNamesList);
										httpSession.setAttribute("user_selected_business_area", selectedBisunessArea);
										httpSession.setAttribute("user_selected_recon", selectedRecon);
										String sucess = "index";
										responseMap.put("responseString", sucess);
										dsResponse.setData(responseMap);

										userParamValueMap.put("user_id", user.getUserId());
										userParamValueMap.put("action", ACTION);
										userParamValueMap.put("date_time",
												new Timestamp(Calendar.getInstance().getTimeInMillis()));
										userParamValueMap.put("bussiness_area", selectedBisunessArea);
										userParamValueMap.put("recon_name", selectedRecon);
										userParamValueMap.put("user_role", usersRole);

										userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

										int userAuditLogs = details.userAuditLogs(userAuditLogsMap);
										System.out.println("LOGS VALUE____>>>>> " + userAuditLogs);

										return dsResponse;
									} else {
										String data = "login";
										responseMap.put("responseString", data);

										responseMap.put("ERROR_MSG", MessageConstants.INVALID_USERNAME + user_Name
												+ MessageConstants.INVALID_USERNAME_REQ_MSG);
										dsResponse.setData(responseMap);
										return dsResponse;
									}

								} else {
									String data = "login";

									responseMap.put("responseString", data);

									responseMap.put("ERROR_MSG", MessageConstants.INVALID_USERNAME + user_Name
											+ MessageConstants.INVALID_USERNAME_REQ_MSG);
									dsResponse.setData(responseMap);
									return dsResponse;
								}

							} else { 
								String data = "login";
								responseMap.put("responseString", data);
								/*
								 * errorMsg = "User " + user_Name +
								 * " Not Authorised To Access The  " +
								 * selectedRecon + " Recon ";
								 */
								responseMap.put("ERROR_MSG",
										MessageConstants.USER + user_Name + MessageConstants.NOT_AUTORISED_REQ_MSG
												+ selectedRecon + MessageConstants.RECON);
								dsResponse.setData(responseMap);
								// return dsResponse;
							}

						}
					}

				} else {
					String data = "login";
					String adminRole = user.getSystemRole();
					responseMap.put("responseString", data);
					/*
					 * errorMsg = " ' " + user_Name +
					 * " '  Not Authorize to Login because  your role is ' " +
					 * adminRole + " ' , Select CheckBox 'Login as a Admin' ";
					 */
					responseMap.put("ERROR_MSG", MessageConstants.USER + user_Name + MessageConstants.NO_LOGIN_ACCESS
							+ adminRole + MessageConstants.ADMIN_SUGGESTIONS);
					dsResponse.setData(responseMap);
					// return dsResponse;
				}

			}
		} else {

			Map<String, Map<String, Object>> cssMap = new HashMap<String, Map<String, Object>>();//
			setUpccMainMap(cssMap);
			httpSession.setAttribute("cssMap", cssMap);
			httpSession.setAttribute("userId", user);
			httpSession.setAttribute("userName", user_Name);
			httpSession.setAttribute("usersRole", usersRole);
			List<Map<String, Object>> reconNamesList = details.getAllReconName();
			httpSession.setAttribute("List_of_All_Recon", reconNamesList);
			httpSession.setAttribute("user_selected_business_area", selectedBisunessArea);
			httpSession.setAttribute("user_selected_recon", selectedRecon);
			String sucess = "index";
			responseMap.put("responseString", sucess);
			dsResponse.setData(responseMap);

			userParamValueMap.put("user_id", user.getUserId());
			userParamValueMap.put("action", ACTION);
			userParamValueMap.put("date_time", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			userParamValueMap.put("bussiness_area", selectedBisunessArea);
			userParamValueMap.put("recon_name", selectedRecon);
			userParamValueMap.put("user_role", usersRole);

			userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

			int userAuditLogs = details.userAuditLogs(userAuditLogsMap);
			System.out.println("LOGS VALUE____>>>>> " + userAuditLogs);

			return dsResponse;

		}

		return dsResponse;
	}

	Integer pwdfailureAttempts = 0;

	static Map<String, Object> loginAttemptChecker = new HashMap<String, Object>();

	public DSResponse authenticateUserWithPassPolicy(User user, String userEnteredPassword, HttpSession httpSession,
			LoginDetails details, String selectedBisunessArea, String selectedRecon, Map<String, Object> responseMap,
			Map<String, Object> userAuditLogsMap, Map<String, Object> userParamValueMap, DSResponse dsResponse,
			String errorMsg) throws Exception {
		boolean password_expiry_warning_flag = false;
		// user.setLoginAttempt(user.getLoginAttempt()+1);
		String usersRole = user.getSystemRole();

		String passFromDb = user.getPassword();
		String user_Name = user.getUserName();
		String user_id = user.getUserId();
		CustomerDao customerDao = new CustomerDao();
		PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
		Integer maxPwdFailure = loadPasswordPolicy.getPwdMaxFailure();
		
// PASSWORD ENCRIPTION IS ADDED AND CHECKING WITH DB STORED PASSWORD AND USER ENTERED PASSWORD---28-05-2017
		/*boolean validatePassword = false;
		try {
			System.out.println("cmng password :" + userEnteredPassword);

			validatePassword = PasswordGeneratorUtil.validatePassword(userEnteredPassword,passFromDb);
			System.out.println("validatePassword :" + validatePassword);
		} catch (NoSuchAlgorithmException | InvalidKeySpecException e) {

			e.printStackTrace();
		}
		if (validatePassword) {*/
		
		
	/////////////  ADDED PASSWORD DECRYPTION CODE START ////////////////////

		
	boolean password_Check_flag = PasswordGeneratorUtil.validatePassword(userEnteredPassword, passFromDb);
	
	System.out.println("password_Check_flag  "+password_Check_flag);
	
	/////////////  ADDED PASSWORD DECRYPTION CODE END ////////////////////
			
		//if (userEnteredPassword != null && userEnteredPassword.equals(passFromDb)) {
		if (userEnteredPassword != null && password_Check_flag == true) {
 
			Timestamp verifyPasswordExpiry = new Timestamp(Calendar.getInstance().getTimeInMillis());
			System.out.println(loadPasswordPolicy.getPwdExpiryWarning());
			Integer expiryWarningDaysBefore = loadPasswordPolicy.getPwdExpiryWarning();

			Timestamp expiryDate = user.getPwd_exp_date();
			Integer daysToExpiry = details.remainingDaysToExpiry(user_id);
			System.out.println(daysToExpiry + " ***** " + verifyPasswordExpiry + " ___----__  " + expiryDate);
		 
			long before=expiryWarningDaysBefore;//3l;
			Date date1 = null;//myFormat.parse(todayDate);
			date1=new Date(Calendar.getInstance().getTimeInMillis());
			Date date2 = null;//myFormat.parse(expDate);
			date2=new Date(expiryDate.getTime());
			long diff = date2.getTime() - date1.getTime();
			long c=TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
			c=daysToExpiry;
			 
			System.out.println(c);
			 if((c>0&&c<=before)){// before no of days and on day
				 String msg="Your password is expiring in next "+c +" Days, Click 'OK' to reset password";
				
				String data = "PWD_EXPIRED";
				responseMap.put("responseString", data);
				responseMap.put("USER_ID", user_id);
				responseMap.put("EXPIRED_ERROR_MSG",MessageConstants.USER + user_Name + msg);
				dsResponse.setData(responseMap);
			}else if(c<before&&c<0){
				 
				String msg="Your password is expired before "+(-c) +" Days, Click 'OK' to reset password";
				String data = "PWD_EXPIRED";
				responseMap.put("responseString", data);
				responseMap.put("USER_ID", user_id);
				responseMap.put("EXPIRED_ERROR_MSG",MessageConstants.USER + user_Name + msg);
				dsResponse.setData(responseMap);
			}else if(c<before&&c==0){
			 
				String msg="Your password is expiring Today , Click 'OK' to reset password";
				String data = "PWD_EXPIRED";
				responseMap.put("responseString", data);
				responseMap.put("USER_ID", user_id);
				responseMap.put("EXPIRED_ERROR_MSG",
						MessageConstants.USER + user_Name + msg);
				dsResponse.setData(responseMap);
			}else{


				System.out.println("HELLOO " + expiryDate.compareTo(verifyPasswordExpiry));

				if (!user.getAccount_status().isEmpty() && !user.getAccount_status().equals("LOCKED")) {

					if (!user.getAccount_status().equals(UNLOCKED)) {

						if (user.getPrivilege() != null) {
							boolean flag = false;
							boolean flag1 = false;
							String stringTemp = "";

							List<PrivilegeDetails> privDetailsList = user.getPrivilege().getPrivilegeDetails();

							for (PrivilegeDetails privilegeDetails : privDetailsList) {
								String recon = privilegeDetails.getRecon();

								String module = privilegeDetails.getModule();
								String[] splitedRecon = recon.split("@");

								for (int i = 0; i < splitedRecon.length; i++) {
									if (!stringTemp.contains(splitedRecon[i])) {
										stringTemp = stringTemp + "," + "'" + splitedRecon[i] + "'";

									}

								}

								if ((module.equals("User Administration") || module.equals("Master Data")) && !flag) {
									stringTemp = stringTemp + "''";
									flag = true;
								}

								if ((module.equals("Recon") && recon.equals("PAYROLL")) && !flag1) {
									stringTemp = stringTemp + "," + "'" + recon + "'";
									flag1 = true;
								}
							}
							if (!stringTemp.isEmpty() && !stringTemp.equals("''")) {
								stringTemp = stringTemp.substring(1, stringTemp.length());
							} else {
								stringTemp = "''";
							}
							httpSession.setAttribute("recon", stringTemp);
							user.setReconString(stringTemp);
							if (usersRole != null && usersRole.contains("Admin") && selectedBisunessArea.equals("")
									&& selectedRecon.equals("")) {

								if (user.getPrivilege().getPrivilegeDetailsMap() != null) {
									if (user.getPrivilege().getPrivilegeDetailsMap() != null) {

										Map<String, Map<String, Object>> cssMap = getCssMap(user);
										httpSession.setAttribute("cssMap", cssMap);
										httpSession.setAttribute("userId", user);
										httpSession.setAttribute("userName", user_Name);
										httpSession.setAttribute("usersRole", usersRole);
										List<Map<String, Object>> reconNamesList = details.getAllReconName();
										httpSession.setAttribute("List_of_All_Recon", reconNamesList);
										httpSession.setAttribute("user_selected_business_area", "Admin");
										httpSession.setAttribute("user_selected_recon", "");
										String sucess = "Admin_Index";
										responseMap.put("responseString", sucess);

										dsResponse.setData(responseMap);

										userParamValueMap.put("user_id", user.getUserId());
										userParamValueMap.put("action", ACTION);
										userParamValueMap.put("date_time",
												new Timestamp(Calendar.getInstance().getTimeInMillis()));
										userParamValueMap.put("bussiness_area", "Admin");
										userParamValueMap.put("recon_name", selectedRecon);
										userParamValueMap.put("user_role", usersRole);

										userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

										int userAuditLogs = details.userAuditLogs(userAuditLogsMap);
										System.out.println("LOGS VALUE____>>>>> " + userAuditLogs);
										loginAttempt = 1;
										loginAttemptChecker.put(user.getUserId(), null);
										
										//// ON 12NOv,2018 start /////////// 
										Calendar calendar = Calendar.getInstance();
										String UPDATE_SQL = "UPDATE USERS SET UPDATED_ON = '"+ new Timestamp(calendar.getTimeInMillis()) +"' where user_id = '"+user.getUserId()+"'";
										PreparedStatement pstmt = DbUtil.getConnection().prepareStatement(UPDATE_SQL);
										System.out.println("UPDATE_SQL   "+UPDATE_SQL);
										int result = pstmt.executeUpdate();
										//// ON 12NOv,2018 end ///////////
										
										
										return dsResponse;
									} else {
										String data = "login";
										responseMap.put("responseString", data);
										errorMsg = "Invalid User " + user_Name
												+ " Please, Enter Valid Login Crediantials ";
										responseMap.put("ERROR_MSG", errorMsg);
										dsResponse.setData(responseMap);
										return dsResponse;
									}

								} else {
									String data = "login";

									responseMap.put("responseString", data);
									errorMsg = "Invalid User " + user_Name + " Please, Enter Valid Login Crediantials ";
									responseMap.put("ERROR_MSG", errorMsg);
									dsResponse.setData(responseMap);
									return dsResponse;
								}

							} else {

								if (user.getSystemRole() != null && !user.getSystemRole().contains("Admin")
										&& !selectedBisunessArea.equals("") && !selectedRecon.equals("")) {

									for (PrivilegeDetails privilegeDetails : privDetailsList) {
										String recon = privilegeDetails.getRecon();
										String[] splitedReconArr = recon.split("@");
										for (int i = 0; i < splitedReconArr.length; i++) {
											if (selectedRecon.equals(splitedReconArr[i])) {

												if (user.getPrivilege().getPrivilegeDetailsMap() != null) {
													if (user.getPrivilege().getPrivilegeDetailsMap() != null) {

														Map<String, Map<String, Object>> cssMap = getCssMap(user);
														httpSession.setAttribute("cssMap", cssMap);
														httpSession.setAttribute("userId", user);
														httpSession.setAttribute("userName", user_Name);
														httpSession.setAttribute("usersRole", usersRole);
														List<Map<String, Object>> reconNamesList = details
																.getAllReconName();
														httpSession.setAttribute("List_of_All_Recon", reconNamesList);
														httpSession.setAttribute("user_selected_business_area",
																selectedBisunessArea);
														httpSession.setAttribute("user_selected_recon", selectedRecon);
														String sucess = "index";
														responseMap.put("responseString", sucess);
														dsResponse.setData(responseMap);

														userParamValueMap.put("user_id", user.getUserId());
														userParamValueMap.put("action", ACTION);
														userParamValueMap.put("date_time", new Timestamp(
																Calendar.getInstance().getTimeInMillis()));
														userParamValueMap.put("bussiness_area", selectedBisunessArea);
														userParamValueMap.put("recon_name", selectedRecon);
														userParamValueMap.put("user_role", usersRole);

														userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

														int userAuditLogs = details.userAuditLogs(userAuditLogsMap);
														System.out.println("LOGS VALUE__>>>>> " + userAuditLogs);
														loginAttempt = 1;
														loginAttemptChecker.put(user.getUserId(), null);
														
														//// ON 12NOv,2018 start /////////// 
														Calendar calendar = Calendar.getInstance();
														String UPDATE_SQL = "UPDATE USERS SET UPDATED_ON = '"+ new Timestamp(calendar.getTimeInMillis()) +"' where user_id = '"+user.getUserId()+"'";
														System.out.println("UPDATE_SQL   "+UPDATE_SQL);
														PreparedStatement pstmt = DbUtil.getConnection().prepareStatement(UPDATE_SQL);
														int result = pstmt.executeUpdate();
													    //// ON 12NOv,2018 end ///////////

														return dsResponse;

													} else {
														String data = "login";
														responseMap.put("responseString", data);
														errorMsg = "Invalid User " + user_Name
																+ " Please, Enter Valid Login Crediantials ";
														responseMap.put("ERROR_MSG", errorMsg);
														dsResponse.setData(responseMap);
														return dsResponse;
													}

												} else {
													String data = "login";

													responseMap.put("responseString", data);
													errorMsg = "Invalid User " + user_Name
															+ " Please, Enter Valid Login Crediantials ";
													responseMap.put("ERROR_MSG", errorMsg);
													dsResponse.setData(responseMap);
													return dsResponse;
												}

											} else {
												
												System.err.println("from error block");
												String data = "login";
												responseMap.put("responseString", data);
												errorMsg = "User " + user_Name + " Not Authorised To Access The  "
														+ selectedRecon + " Recon ";
												responseMap.put("ERROR_MSG", errorMsg);
												dsResponse.setData(responseMap);
												// return dsResponse;
											}

										}
									}

								} else {
									String data = "login";
									String adminRole = user.getSystemRole();
									responseMap.put("responseString", data);
									errorMsg = " ' " + user_Name + " '  Not Authorize to Login because  your role is ' "
											+ adminRole + " ' , Select CheckBox 'Login as a Admin' ";
									responseMap.put("ERROR_MSG", errorMsg);
									dsResponse.setData(responseMap);
									// return dsResponse;
								}

							}
						} else {

							/*Map<String, Map<String, Object>> cssMap = new HashMap<String, Map<String, Object>>();//
							setUpccMainMap(cssMap);
							httpSession.setAttribute("cssMap", cssMap);
							httpSession.setAttribute("userId", user);
							httpSession.setAttribute("userName", user_Name);
							httpSession.setAttribute("usersRole", usersRole);
							List<Map<String, Object>> reconNamesList = details.getAllReconName();
							httpSession.setAttribute("List_of_All_Recon", reconNamesList);
							httpSession.setAttribute("user_selected_business_area", selectedBisunessArea);
							httpSession.setAttribute("user_selected_recon", selectedRecon);
							String sucess = "index";
							responseMap.put("responseString", sucess);
							dsResponse.setData(responseMap);

							userParamValueMap.put("user_id", user.getUserId());
							userParamValueMap.put("action", ACTION);
							userParamValueMap.put("date_time", new Timestamp(Calendar.getInstance().getTimeInMillis()));
							userParamValueMap.put("bussiness_area", selectedBisunessArea);
							userParamValueMap.put("recon_name", selectedRecon);
							userParamValueMap.put("user_role", usersRole);

							userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

							int userAuditLogs = details.userAuditLogs(userAuditLogsMap);*/
							System.err.println("from error block12");
							String data = "login";
							responseMap.put("responseString", data);
							errorMsg = "User " + user_Name + " Not Authorised To Access The  "
									+ selectedRecon + " Recon ";
							responseMap.put("ERROR_MSG", errorMsg);
							dsResponse.setData(responseMap);
							System.out.println("LOGS VALUE____>>>>> "  );
							loginAttempt = 1;
							loginAttemptChecker.put(user.getUserId(), null);
							return dsResponse;

						}

					} else {
						String data = "login";
						responseMap.put("responseString", data);

						responseMap.put("ERROR_MSG",
								MessageConstants.USER + user_Name + MessageConstants.ADMINISTRATOR_MESAAGE_CHECK_MAIL);
						dsResponse.setData(responseMap);
					}

				} else {

					String data = "login";
					responseMap.put("responseString", data);

					responseMap.put("ERROR_MSG", MessageConstants.USER + user_Name + MessageConstants.ACCOUNT_LOCKED);
					dsResponse.setData(responseMap);

				}

			
			}
			
			
/*			if (daysToExpiry < expiryWarningDaysBefore) {
				password_expiry_warning_flag = true;
				System.out.println(expiryWarningDaysBefore + " PASSWORD EXPIRY FLAG " + password_expiry_warning_flag);

			}*/
			
		     

			// if(maxPwdFailure>=)
			//if(nextdate.before(expiryDate)){
			// modified by ankush 
		/*	if (verifyPasswordExpiry.compareTo(expiryDate) < 0 || verifyPasswordExpiry.compareTo(expiryDate) == 0) { } else { }*/

		} else {

			String data = "login";
			responseMap.put("responseString", data);
			responseMap.put("ERROR_MSG", MessageConstants.PASSWORD_ERR_MSG);
			dsResponse.setData(responseMap);

			if (loginAttemptChecker.get(user.getUserId()) == null) {
				loginAttemptChecker.put(user.getUserId(), loginAttempt);
			} else {
				int counter = (int) loginAttemptChecker.get(user.getUserId()) + 1;
				loginAttemptChecker.put(user.getUserId(), counter);
			}

			System.out.println(maxPwdFailure + "?<<0--MAX PWD FAILURE**--->   "
					+ loginAttemptChecker.get(user.getUserId()) + "   <<<<--- LOGIN and hash code " + user.hashCode());
			// maxPwdFailure++;
			if ((Integer) loginAttemptChecker.get(user.getUserId()) > maxPwdFailure) {
				String account_status = "LOCKED";
				Integer account_locked_status = details.lockUserAccount(user_id, account_status);

				if (user.getAccount_status().equals("ACTIVE")) {
					loginAttemptChecker.put(user.getUserId(), null);
					System.out.println("ACTIVE STATUS --->" + user.getUserId());
				}

				if (account_locked_status > 0) {
					responseMap.put("ERROR_MSG", MessageConstants.MAX_ATTEMPTS_OF_PWD);
					dsResponse.setData(responseMap);
					// return dsResponse;
				} else {
					responseMap.put("ERROR_MSG", MessageConstants.PASSWORD_ERR_MSG);
					dsResponse.setData(responseMap);
					// return dsResponse;
				}

			}

		}

		System.out.println("******** ---->>>" + loginAttemptChecker);
		return dsResponse;
	}
	/**For LDAP integration  <AUTHOR> date 30-06-207
	 *  providing authentication from LDAP server
	 * @param user_id
	 * @param deptName
	 * @param password
	 * @return
	 */
	public boolean checkUserwithldapV2(String user_id, String deptName, String password) {
		LDAPClient adUserAccess = new LDAPClient();
 
		//boolean isValidUser = adUserAccess.kerberosAuthentication(user_id, password,ldapProperties);
		boolean isValidUser = adUserAccess.validateLDapUser(user_id, password,ldapProperties);
		return isValidUser;
	}
	
	
	/**For disableInactiveUserAccount  <AUTHOR> date 13-07-2017

	 */
	public boolean disableInactiveUserAccount() {
	CustomerDao customerDao= new CustomerDao();	
	 try {
	boolean c=	customerDao.insertData(DbUtil.getConnection(), null, "UPDATE_INACTIVE_USER_STATUS");
	
	System.out.println("inactivce user query status "+c);
		return true;
	} catch (Exception e) {
		 
		return false;
	}

	}
	
	public static void updateActiveUsersAfterThirtyDays(String username) throws Exception
	{
		
		boolean flag = false;
		Connection conne = DbUtil.getConnection();
		Statement stmt = null;
		
		String sql_checkusers = "SELECT * FROM USERS where  user_name = '"+username+"'";
		stmt = conne.createStatement();
		ResultSet userseresult = stmt.executeQuery(sql_checkusers);
		List list  = getRsNextBatch(userseresult);
		
		for(int i=0;i<list.size();i++)
		{
			Map map = new HashMap();
			map = (Map) list.get(i);
			String user_id = map.get("user_id").toString();
			java.util.Date currentdate = null;
			currentdate=new Date(Calendar.getInstance().getTimeInMillis());
			
			java.util.Date datefromDb = null;
				try {
					datefromDb =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(map.get("pwd_exp_date").toString());
					 System.out.println("date11  "+datefromDb.getTime());  
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} 
				long diff = currentdate.getTime() - datefromDb.getTime();
				long daysToExpiry=TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
				if(daysToExpiry>=30)
				{
				//// RAMA ADDED ON 12NOv,2018 start /////////// 
					
					String UPDATE_SQL = "UPDATE USERS SET  account_status = 'LOCKED'  where user_name = '"+user_id+"'";
					System.out.println("UPDATE_SQL   "+UPDATE_SQL);
					PreparedStatement pstmt = DbUtil.getConnection().prepareStatement(UPDATE_SQL);
					int result = pstmt.executeUpdate();
				    //// RAMA ADDED ON 12NOv,2018 end ///////////
				}
		}
	}
	public static List<Map<String, Object>> getRsNextBatch(ResultSet rs)
			throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		int recCnt = 0;
		int batchSize = 500;
		while (recCnt < batchSize && rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));

			}
			recCnt++;
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}
	
	public boolean multipleLoginCheck(HttpSession httpSession, User user)
	{
		boolean userPresent= false;
		boolean multiLogin= false;

		Map<String, Object> login_status = new HashMap<String, Object>();
		
		if(user != null && user.getUserId() != null)
			{
			userPresent= checkAndAddUser(user.getUserId());
			
			if(userPresent)
			{
			
			login_status.put("user_id", user.getUserId());

			CustomerDao customerDao= new CustomerDao();
			Map<String, Object> fetchLoginStatus = customerDao.checkLoginStatus(login_status);
			
			String storedSession= (String)fetchLoginStatus.get("session");
			String currentSession= httpSession.toString();
			Long lastAccesedTime=(Long)fetchLoginStatus.get("session_last_accesed");
			Long currentTime=System.currentTimeMillis();
			System.out.println("storedSession:   "+storedSession);
			System.out.println("currentSession:   "+currentSession);
			System.out.println("lastAccesedTime:   "+lastAccesedTime);
			System.out.println("currentTime:   "+currentTime);
			System.out.println("httpSession.getMaxInactiveInterval():   "+httpSession.getMaxInactiveInterval());
			Long maxInactiveInterval= (long) httpSession.getMaxInactiveInterval();
			maxInactiveInterval= maxInactiveInterval*1000;
			
			if(fetchLoginStatus.get("login_flag").equals("T"))
			{
				if((storedSession.equals(currentSession)&& currentTime-lastAccesedTime>3600000) 
						|| (!storedSession.equals(currentSession)&& currentTime-lastAccesedTime>maxInactiveInterval))
					{
						Map<String, Object> temp_login_status = new HashMap<String, Object>();
						temp_login_status.put("user_id", user.getUserId());
						temp_login_status.put("login_flag", "F");
						temp_login_status.put("session",httpSession.toString());
						temp_login_status.put("session_created", httpSession.getCreationTime());
						temp_login_status.put("session_last_accesed", httpSession.getLastAccessedTime());
						
						int login_flag_update = customerDao.setLoginStatus(temp_login_status);
						System.out.println("login_flag_update: "+login_flag_update);
						
						if(login_flag_update==1){
							fetchLoginStatus.put("login_flag", "F");
							}
					}
				else
				{
					System.out.println("You are already logged in from a different session. Please logout first.");
					multiLogin=true;	
				}
	
			}
			
			if(fetchLoginStatus.get("login_flag").equals("F"))
			{
				login_status.put("login_flag", "T");
				login_status.put("session",httpSession.toString());
				login_status.put("session_created", httpSession.getCreationTime());
				login_status.put("session_last_accesed", httpSession.getLastAccessedTime());
				
				int login_flag_update= customerDao.setLoginStatus(login_status);
				System.out.println("login_flag_update: "+login_flag_update);
				
			}
			
		}
			else
			{
				System.out.println("User Addition for multi login failed");
			}
	
		}
		
		return multiLogin;
	}
	
	
	public boolean checkAndAddUser(String user_id)
	{
		boolean userPresent=false;
		CustomerDao customerDao= new CustomerDao();
		List<String> userList= customerDao.fetchUsers();
		
		if(userList.contains(user_id))
		{
				userPresent= true;
		}
		else if(!userList.contains(user_id))
		{
			int userAdded= customerDao.addUser(user_id);
			if(userAdded==1)
			{
				userPresent=true;
			}	
		}
		
		return userPresent;
	}
	 
	
}
