<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">
	
	<query id="17">
        <name>ONS_INSERT_QRY</name>
		<targetTables>FINANCE_ONS_RECON</targetTables>
        <queryString>
				INSERT INTO FINANCE_ONS_RECON (
					ID,SID,RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,TRA_CUR,REF_NUM,WORKFLOW_STATUS,SOURCE_TARGET,
					MAIN_REV_IND,RECON_ID,VERSION,MATCH_TYPE,ACTIVE_INDEX,USER_ID,UPDATED_ON,CREATED_ON,COMMENTS,
					SUPPORTING_DOC_ID,RULE_NAME,ACTIVITY_STATUS,OPERATION,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS,
					PAN_CARD,ACCOUNT_NUMBER
				)
				VALUES
				(
					?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?
				)
		
  
	</queryString>
		<queryParam>ID@BIGINT,SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TRA_CUR@VARCHAR,
		REF_NUM@VARCHAR,WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,
		MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,
		SUPPORTING_DOC_ID@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,
		ACTIVITY_COMMENTS@VARCHAR,PAN_CARD@VARCHAR,ACCOUNT_NUMBER@VARCHAR

		</queryParam>
    </query>
     	
	<query id="18">
        <name>FINANCE_ONS_RECON_UPSTREAM_QRY</name>
		<targetTables>FIN_ONS_CBS_STG,FIN_ONS_CBO_STG</targetTables>
        <queryString>
				SELECT * FROM(
					select 'CBS' AS RECON_SIDE,SID, VALUE_DATE AS TRA_DATE,AMOUNT AS TRA_AMT ,TRAN_REF_NUM as REF_NUM 
					,CURRENCY AS TRA_CUR ,DRCR AS DEB_CRE_IND,'FIN_ONS_CBS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
					BUSINESS_AREA,PAN_CARD_NUMBER as PAN_CARD,ACNT as ACCOUNT_NUMBER FROM 
					FIN_ONS_CBS_STG WITH (NOLOCK) WHERE (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'
						UNION ALL
					SELECT 'CBO' AS RECON_SIDE,SID, VALUE_DATE AS  TRA_DATE,AMOUNT AS TRA_AMT ,TRAN_REF_NUM AS REF_NUM,
					CURRENCY AS TRA_CUR,DRCR AS DEB_CRE_IND,'FIN_ONS_CBO_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
					BUSINESS_AREA,PAN_NUMBER as PAN_CARD,ACCOUNT as ACCOUNT_NUMBER 
					FROM FIN_ONS_CBO_STG WITH (NOLOCK) WHERE  (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'
              ) AS A 
 				ORDER BY REF_NUM,TRA_AMT, TRA_DATE
		</queryString>
		<queryParam>
				
		</queryParam>
    </query>
    	


<query id="2">
        <name>FIN_ONS_CBO_STG_AUDIT_INSERT_QRY</name>
		<targetTables>FIN_ONS_CBO_STG</targetTables>
        <queryString>
		INSERT INTO FIN_ONS_CBO_STG_AUDIT
           (SID,TRAN_REF_NUM,INTERNAL_REF_NUM,ACCOUNT,TRAN_DATE,VALUE_DATE,AMOUNT,DRCR,CURRENCY,PAN_NUMBER
           ,ORIGINATOR_BID,DESTINATION_BID,ACQUIRING_INSTITUTION_ID,CARD_ACCEPTOR_NAME,MERCHANT_CATEGORY_CODE,TRANSACTION_TYPE
           ,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS
           ,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA,FREE_TEXT_1,FREE_TEXT_2,FREE_TEXT_3,FREE_TEXT_4,FREE_TEXT_5
           ,FREE_TEXT_6,FREE_TEXT_7,FREE_TEXT_8,FREE_TEXT_9,FREE_TEXT_10,FREE_CODE_1,FREE_CODE_2,FREE_CODE_3,FREE_CODE_4,FREE_CODE_5
           ,FREE_DATE_1,FREE_DATE_2,FREE_DATE_3,FREE_DATE_4,FREE_DATE_5)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?)			
			 </queryString>
		<queryParam>
			
			SID@BIGINT,TRAN_REF_NUM@VARCHAR,INTERNAL_REF_NUM@VARCHAR,ACCOUNT@VARCHAR,TRAN_DATE@DATE,VALUE_DATE@DATE,AMOUNT@DECIMAL,
		   DRCR@VARCHAR,CURRENCY@VARCHAR,PAN_NUMBER@VARCHAR ,ORIGINATOR_BID@VARCHAR,DESTINATION_BID@VARCHAR,ACQUIRING_INSTITUTION_ID@VARCHAR,
		   CARD_ACCEPTOR_NAME@VARCHAR,MERCHANT_CATEGORY_CODE@INTEGER,TRANSACTION_TYPE@VARCHAR ,COMMENTS@VARCHAR,VERSION@INTEGER,
		   ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP, RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR
           ,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR,FREE_TEXT_1@VARCHAR,FREE_TEXT_2@VARCHAR,
		   FREE_TEXT_3@VARCHAR,FREE_TEXT_4@VARCHAR,FREE_TEXT_5@VARCHAR ,FREE_TEXT_6@VARCHAR,FREE_TEXT_7@VARCHAR,FREE_TEXT_8@VARCHAR,
		   FREE_TEXT_9@VARCHAR,FREE_TEXT_10@VARCHAR,FREE_CODE_1@VARCHAR,FREE_CODE_2@VARCHAR,FREE_CODE_3@VARCHAR,FREE_CODE_4@VARCHAR,
		   FREE_CODE_5@VARCHAR,FREE_DATE_1@DATE,FREE_DATE_2@DATE,FREE_DATE_3@DATE,FREE_DATE_4@DATE,FREE_DATE_5@DATE
		</queryParam>
    </query>
   <query id="2">
        <name>FIN_ONS_CBS_STG_AUDIT_INSERT_QRY</name>
		<targetTables>FIN_ONS_CBS_STG</targetTables>
        <queryString>
		INSERT INTO FIN_ONS_CBS_STG_AUDIT
           (SID,INTERNAL_REF_NUM,ACNT,TRAN_DATE,VALUE_DATE,AMOUNT,DRCR,CURRENCY,PAN_CARD_NUMBER
           ,ACCT_BRANCH_ID,TRAN_PARTICULAR,TRAN_REMARKS,TRAN_ENTRY_USER,TRAN_POSTED_USER,COMMENTS
           ,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID
           ,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA,TRAN_REF_NUM)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		   ?,?,?,?,?,?,?,?,?,?,
		   ?,?,?,?,?,?,?,?)		
			 </queryString>
		<queryParam>
			SID@BIGINT,INTERNAL_REF_NUM@VARCHAR,ACNT@VARCHAR,TRAN_DATE@DATE,VALUE_DATE@DATE,AMOUNT@DECIMAL,
		    DRCR@VARCHAR,CURRENCY@VARCHAR,PAN_CARD_NUMBER@VARCHAR,ACCT_BRANCH_ID@VARCHAR,TRAN_PARTICULAR@VARCHAR,TRAN_REMARKS@VARCHAR,
		    TRAN_ENTRY_USER@VARCHAR,TRAN_POSTED_USER@VARCHAR,COMMENTS@VARCHAR,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,
		    UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,
		    OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR,TRAN_REF_NUM@VARCHAR
		</queryParam>
    </query>
		
    

	<query id="17">
        <name>PAYMENT_ORDER_RECON_UPDATE_QRY</name>
		<targetTables>PAYMENT_ORDER_RECON</targetTables>
        <queryString>
				UPDATE ONUS_ATM_DEBIT_RECON SET
					ID=?,TRA_AMT=?,TRA_DATE=?,DEB_CRE_IND=?,TRA_CUR=?,CHECK_NO=?,WORKFLOW_STATUS=?,SOURCE_TARGET=?,
					MAIN_REV_IND=?,RECON_ID=?,VERSION=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,CREATED_ON=?,COMMENTS=?,
					SUPPORTING_DOC_ID=?,RULE_NAME=?,ACTIVITY_STATUS=?,OPERATION=?,STATUS=?,BUSINESS_AREA=?,ACTIVITY_COMMENTS=?
				WHERE SID=? AND RECON_SIDE=?
		
  
	</queryString>
		<queryParam>
				ID@BIGINT,SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TRA_CUR@VARCHAR,
		CHECK_NO@VARCHAR,WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,
		MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,
		SUPPORTING_DOC_ID@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,
		ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
    </query>

</queries>