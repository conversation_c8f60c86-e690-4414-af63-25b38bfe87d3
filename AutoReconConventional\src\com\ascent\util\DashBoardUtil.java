package com.ascent.util;

import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.integration.util.DbUtil;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class DashBoardUtil extends BasicDataSource {

	/**
	 * 
	 */
	public DashBoardUtil() {

		System.out.println("DashBoardUtil");

	}

	private static final long serialVersionUID = 1L;

	public DSResponse executeFetch(DSRequest request) throws SQLException {
		DSResponse response = new DSResponse();

		Map cmap = request.getCriteria();

		HttpSession httpSession = request.getHttpServletRequest().getSession();// GETTING
																				// SESSION

		// OBJECT

		if (httpSession.getAttribute("fromDate") == null && httpSession.getAttribute("toDate") == null) {

			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DATE, -1);
			java.util.Date ystrdayDate2 = (java.util.Date) cal.getTime();
			java.sql.Date fromDate = new java.sql.Date(ystrdayDate2.getTime());

			Calendar cal2 = Calendar.getInstance();
			java.util.Date fromDate2 = (java.util.Date) cal2.getTime();
			java.sql.Date toDatetoCalender = new java.sql.Date(fromDate2.getTime());
			httpSession.setAttribute("fromDate", fromDate);
			httpSession.setAttribute("toDate", toDatetoCalender);

			System.out
					.println(fromDate + "&&&&&&&&&&&&&&&&*(****************(((((((((((((((((((((((" + toDatetoCalender);

			List<Map<String, Object>> responseList = null;
			responseList = DashBoardUtil.dashboard(fromDate, toDatetoCalender, cmap);
			response.setData(responseList);
			return response;

		} else {

			Date fromDate = (Date) httpSession.getAttribute("fromDate");
			Date toDatetoCalender = (Date) httpSession.getAttribute("toDate");
			httpSession.setAttribute("fromDate", fromDate);
			httpSession.setAttribute("toDate", toDatetoCalender);
			List<Map<String, Object>> responseList = null;
			responseList = DashBoardUtil.dashboard(fromDate, toDatetoCalender, cmap);
			response.setData(responseList);
			return response;

		}

	}

	private static List<Map<String, Object>> dashboard(Date fromDate, Date toDatetoCalender, Map map) {
		java.util.Date date1= new java.util.Date();
        System.out.println(new Timestamp(date1.getTime()));


		String name = "bar";
		String recdatasource = (String) map.get("Recon_DataSource");
		List<String> dsList = (List) map.get("dataSourceList");
		List<String> reconSideList = (List) map.get("Recon_Side_List");
		List<String> dateList = (List<String>) map.get("dateList");
		List<String> orphandateList = (List<String>) map.get("orphandateList");
		List<String> orphanamtList = (List<String>) map.get("orphanamtList");

		List<String> amtList = (List<String>) map.get("amtList");
		String aging = (String) map.get("aging");

		System.out.println("reconSideList :" + reconSideList);
		Connection connection = DbUtil.getConnection();
		Statement stmt = null;
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try {
			stmt = connection.createStatement();

			Calendar cal = Calendar.getInstance();
			cal.setTime(toDatetoCalender);
			String todaydate = new java.sql.Date(cal.getTime().getTime()).toString();

			cal.add(Calendar.DAY_OF_MONTH, -5);
			Date before5DaysDate = new java.sql.Date(cal.getTime().getTime());
			String before5days = new java.sql.Date(cal.getTime().getTime()).toString();

			cal.add(Calendar.DAY_OF_MONTH, -5);
			Date before10DaysDate = new java.sql.Date(cal.getTime().getTime());
			String before10Days = new java.sql.Date(cal.getTime().getTime()).toString();

			cal.add(Calendar.DAY_OF_MONTH, -5);
			Date before15DaysDate = new java.sql.Date(cal.getTime().getTime());
			String before15Days = new java.sql.Date(cal.getTime().getTime()).toString();

			cal.add(Calendar.DAY_OF_MONTH, -5);
			Date before20DaysDate = new java.sql.Date(cal.getTime().getTime());
			String before20Days = new java.sql.Date(cal.getTime().getTime()).toString();

			cal.add(Calendar.DAY_OF_MONTH, -5);
			Date before25DaysDate = new java.sql.Date(cal.getTime().getTime());
			String before25Days = new java.sql.Date(cal.getTime().getTime()).toString();

			cal.add(Calendar.DAY_OF_MONTH, -5);

			Date before30DaysDate = new java.sql.Date(cal.getTime().getTime());
			String before30Days = new java.sql.Date(cal.getTime().getTime()).toString();

			String fiveDaySql = "select count(*),RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " between '" + before5days + "' and '"
					+ todaydate + "')  and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";

			String tenDaySql = "select count(*),RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " between '" + before10Days
					+ "' and DATEADD(DAY,-1,'" + before5days
					+ "'))  and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";

			String fifteenDaySql = "select count(*),RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " between '" + before15Days
					+ "' and DATEADD(DAY,-1,'" + before10Days
					+ "'))  and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";

			String twentyDaySql = "select count(*),RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where  MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " between '" + before20Days
					+ "' and DATEADD(DAY,-1,'" + before15Days
					+ "'))  and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";

			String twentyfiveDaySql = "select count(*),RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where  MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " between '" + before25Days
					+ "' and DATEADD(DAY,-1,'" + before20Days
					+ "'))  and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";
			String thirtyDaySql = "select count(*),RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where  MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " between '" + before30Days
					+ "' and DATEADD(DAY,-1,'" + before25Days
					+ "'))  and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";

			String remainingDaySql = "select count(*) ,RECON_SIDE  from  " + recdatasource + " WITH  (NOLOCK)  where " + dateList.get(0)
					+ "  in (" + "select " + dateList.get(0) + " from " + recdatasource
					+ " WITH  (NOLOCK)  where MATCH_TYPE in('AU','MU') and " + dateList.get(0) + " <'" + before30Days
					+ "') and MATCH_TYPE in('AU','MU') group by RECON_SIDE order by RECON_SIDE";
			boolean ss = false;
			if (compareTo(fromDate, before5DaysDate)) {

				tenDaySql = null;
				fifteenDaySql = null;
				twentyDaySql = null;
				twentyfiveDaySql = null;
				thirtyDaySql = null;
				remainingDaySql = null;
			} else if (compareTo(fromDate, before10DaysDate)) {

				fifteenDaySql = null;
				twentyDaySql = null;
				twentyfiveDaySql = null;
				thirtyDaySql = null;
				remainingDaySql = null;
			} else if (compareTo(fromDate, before15DaysDate)) {
				twentyDaySql = null;
				twentyfiveDaySql = null;
				thirtyDaySql = null;
				remainingDaySql = null;
			} else if (compareTo(fromDate, before20DaysDate)) {
				twentyfiveDaySql = null;
				thirtyDaySql = null;
				remainingDaySql = null;
			} else if (compareTo(fromDate, before25DaysDate)) {

				thirtyDaySql = null;
				remainingDaySql = null;
			} else if (compareTo(fromDate, before30DaysDate)) {

				remainingDaySql = null;
			}

			// String[]
			// sqlList={twoDaySql,fiveDaySql,tenDaySql,thirtyDaySql,remainingDaySql};
			// for(){}
			Map<String, String> dateMap = new HashMap<String, String>();
			dateMap.put("0-5", fiveDaySql);
			dateMap.put("6-10", tenDaySql);
			dateMap.put("11-15", fifteenDaySql);
			dateMap.put("16-20", twentyDaySql);
			dateMap.put("21-25", twentyfiveDaySql);
			dateMap.put("26-30", thirtyDaySql);

			dateMap.put(">30", remainingDaySql);
			List<Map<String, Object>> surend = new ArrayList<Map<String, Object>>();
			List chartList = new ArrayList();
			List reconSideListSurend = new ArrayList();
			String[] daterangeset = { "0-5", "6-10", "11-15", "16-20", "21-25", "26-30", ">30" };
			for (String key : daterangeset) {
				Map mainMap = new HashMap();
				mainMap.put("TRA_DATE", key);
				if (dateMap.get(key) != null) {

					ResultSet agingrs = stmt.executeQuery(dateMap.get(key));

					while (agingrs.next()) {

						mainMap.put(agingrs.getObject(2), agingrs.getObject(1));

						// list.add(agingmap);

					}
				}
				for (String reconSide : reconSideList) {
					if (mainMap.get(reconSide) == null) {
						mainMap.put(reconSide, 0);
					}
				}

				chartList.add(mainMap);

			}

			Map chartMap = new HashMap();
			chartMap.put("chartData", chartList);
			chartMap.put("reconSideListforAUaging", reconSideList);
			list.add(chartMap);
			List orphanSideList = new ArrayList();

			for (int side = 1; side < reconSideList.size(); side++) {
				orphanSideList.add(reconSideList.get(side));

			}
			Map<String, Map> sideRangeMap = new HashMap<String, Map>();
			String[] ordaterangeset = { "0-5", "6-10", "11-15", "16-20", "21-25", "26-30", ">30" };
			for (int i = 0; i < dsList.size(); i++) {

				String orfiveDaySql = "select count(*) from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ " between '" + before5days + "' and '" + todaydate + "'";

				String ortenDaySql = "select count(*) from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ " between '" + before10Days + "' and DATEADD(DAY,-1,'" + before5days + "')";

				String orfifteenDaySql = "select count(*) from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ " between '" + before15Days + "' and DATEADD(DAY,-1,'" + before10Days + "')";

				String ortwentyDaySql = "select count(*) from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ "  between '" + before20Days + "' and DATEADD(DAY,-1,'" + before15Days + "')";

				String ortwentyfiveDaySql = "select count(*) from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ " between '" + before25Days + "' and DATEADD(DAY,-1,'" + before20Days + "')";
				String orthirtyDaySql = "select count(*)  from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ "  between '" + before30Days + "' and DATEADD(DAY,-1,'" + before25Days + "')";

				String orremainingDaySql = "select count(*)  from  " + dsList.get(i) + " WITH  (NOLOCK)  where " + orphandateList.get(i)
						+ " <'" + before30Days + "'";
				boolean orss = false;
				if (compareTo(fromDate, before5DaysDate)) {

					ortenDaySql = null;
					orfifteenDaySql = null;
					ortwentyDaySql = null;
					ortwentyfiveDaySql = null;
					orthirtyDaySql = null;
					orremainingDaySql = null;
				} else if (compareTo(fromDate, before10DaysDate)) {

					orfifteenDaySql = null;
					ortwentyDaySql = null;
					ortwentyfiveDaySql = null;
					orthirtyDaySql = null;
					orremainingDaySql = null;
				} else if (compareTo(fromDate, before15DaysDate)) {
					ortwentyDaySql = null;
					ortwentyfiveDaySql = null;
					orthirtyDaySql = null;
					orremainingDaySql = null;
				} else if (compareTo(fromDate, before20DaysDate)) {
					ortwentyfiveDaySql = null;
					orthirtyDaySql = null;
					orremainingDaySql = null;
				} else if (compareTo(fromDate, before25DaysDate)) {

					orthirtyDaySql = null;
					orremainingDaySql = null;
				} else if (compareTo(fromDate, before30DaysDate)) {

					orremainingDaySql = null;
				}

				Map<String, String> ordateMap = new HashMap<String, String>();
				ordateMap.put("0-5", orfiveDaySql);
				ordateMap.put("6-10", ortenDaySql);
				ordateMap.put("11-15", orfifteenDaySql);
				ordateMap.put("16-20", ortwentyDaySql);
				ordateMap.put("21-25", ortwentyfiveDaySql);
				ordateMap.put("26-30", orthirtyDaySql);

				ordateMap.put(">30", orremainingDaySql);
				List<Map<String, Object>> orsurend = new ArrayList<Map<String, Object>>();
				List orchartList = new ArrayList();
				List orreconSideListSurend = new ArrayList();

				Map dateRangeCount = new HashMap();
				for (String key : ordaterangeset) {
					Map ormainMap = new HashMap();
					ormainMap.put("TRA_DATE", key);
					if (ordateMap.get(key) != null) {

						ResultSet oragingrs = stmt.executeQuery(ordateMap.get(key));

						while (oragingrs.next()) {

							ormainMap.put("orcount", oragingrs.getObject(1));
							dateRangeCount.put(key, oragingrs.getObject(1));
							// list.add(agingmap);

						}
					}

					orchartList.add(ormainMap);

				}

				sideRangeMap.put(orphanSideList.get(i).toString(), dateRangeCount);

			}
			List dateWiseDataList = new ArrayList();
			for (String datekey : ordaterangeset) {
				Map mainMap = new HashMap();
				mainMap.put("TRA_DATE", datekey);
				for (String side : sideRangeMap.keySet()) {
					Map m = sideRangeMap.get(side);
					mainMap.put(side, m.get(datekey));

				}

				dateWiseDataList.add(mainMap);

			}

			Map orphanchartMap = new HashMap();
			orphanchartMap.put("orphanchartData", dateWiseDataList);
			orphanchartMap.put("orphanSideList", orphanSideList);
			list.add(orphanchartMap);

			for (int i = 0; i < reconSideList.size(); i++) {
				int match = 0;
				int unmatch = 0;
				int forcematch = 0;
				int forceunmatch = 0;
				Object sum = 0;
				Object orphansum = 0;
				int orphantotal = 0;
				int total = 0;
				Object forcematchsum = 0;
				Object unmatchsum = 0;
				Object forceunmatchsum = 0;
				Object matchsum = 0;
				Object date = 0;

				String sql = "select count(*) from " + recdatasource + " WITH  (NOLOCK)  where RECON_SIDE='" + reconSideList.get(i)
						+ "' and " + dateList.get(i) + " between '" + fromDate + "'  and '" + toDatetoCalender + "' ";
				System.out.println("sql count"+sql);
				String orphansql = "";
				String orphansumsql = "";
				if (i > 0) {
					orphansql = "select count(*) from " + dsList.get(i - 1) + " WITH  (NOLOCK)  where " + orphandateList.get(i - 1)
							+ " between '" + fromDate + "'  and '" + toDatetoCalender + "' ";
					
					
					System.out.println("orphansql count"+orphansql);
					orphansumsql = "select ISNULL(Totalmain,0) - ISNULL(Totalreversal,0) as Difference  from  ( select (select sum("
							+ orphanamtList.get(i - 1) + ") from " + dsList.get(i - 1)
							+ " WITH  (NOLOCK)  where MAIN_REV_IND='MAIN' and " + orphandateList.get(i - 1) + " between '" + fromDate
							+ "'  and '" + toDatetoCalender + "' ) as Totalmain, (select sum("
							+ orphanamtList.get(i - 1) + ") from " + dsList.get(i - 1)
							+ " WITH  (NOLOCK) where MAIN_REV_IND='REVERSAL' and " + orphandateList.get(i - 1) + " between '"
							+ fromDate + "'  and '" + toDatetoCalender + "' ) As Totalreversal ) temp";
					System.out.println("orphansumsql count"+orphansumsql);
					ResultSet orphanrs = stmt.executeQuery(orphansql);
					if (orphanrs.next()) {
						if (orphanrs.getObject(1) != null)
							orphantotal = (int) orphanrs.getObject(1);
					}
					ResultSet orphansumrs = stmt.executeQuery(orphansumsql);
					if (orphansumrs.next()) {
						if (orphansumrs.getObject(1) != null)
							orphansum = orphansumrs.getObject(1);
					}
				}
				String matchsql = "select count(*) from " + recdatasource + " WITH  (NOLOCK) where RECON_SIDE='" + reconSideList.get(i)
						+ "' and MATCH_TYPE='AM' and " + dateList.get(i) + " between '" + fromDate + "'  and '"
						+ toDatetoCalender + "' ";
				
				System.out.println("matchsql count"+matchsql);
				
				String unmatchsql = "select count(*) from " + recdatasource + " WITH  (NOLOCK) where RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='AU' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ";
				
				System.out.println("unmatchsql count"+unmatchsql);
				
				String forceunmatchsql = "select count(*) from " + recdatasource + " WITH  (NOLOCK) where RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='MU' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ";
				
				System.out.println("forceunmatchsql count"+forceunmatchsql);
				
				String forcematchsql = "select count(*) from " + recdatasource + " WITH  (NOLOCK) where RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='MM' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ";
				
				System.out.println("forcematchsql count"+forcematchsql);

				// Amount queries
				/*
				 * String sumsql = "select SUM(" + amtList.get(i) + ") from " +
				 * recdatasource + " where RECON_SIDE='" + reconSideList.get(i)
				 * + "' and " + dateList.get(i) + " between '" + fromDate +
				 * "'  and '" + toDatetoCalender + "' ";
				 */
				String sumsql = "select ISNULL(Totalmain,0) - ISNULL(Totalreversal,0) as Difference  from  ( select (select sum("
						+ amtList.get(i) + ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='MAIN' and  RECON_SIDE='"
						+ reconSideList.get(i) + "' and " + dateList.get(i) + " between '" + fromDate + "'  and '"
						+ toDatetoCalender + "' ) as Totalmain, (select sum(" + amtList.get(i) + ") from "
						+ recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='REVERSAL' and RECON_SIDE='" + reconSideList.get(i)
						+ "' and " + dateList.get(i) + " between '" + fromDate + "'  and '" + toDatetoCalender
						+ "' ) As Totalreversal ) temp";
				System.out.println("sumsql : "+sumsql);
				
				// String sumsql = "select age from JNK_VIEW where name='ujnk'";
				String matchsumsql = "select ISNULL(Totalmain,0) - ISNULL(Totalreversal,0) as Difference  from  ( select (select sum("
						+ amtList.get(i) + ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='MAIN' and  RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='AM' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) as Totalmain, (select sum(" + amtList.get(i)
						+ ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='REVERSAL' and RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='AM' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) As Totalreversal ) temp";

				System.out.println("matchsumsql:  "+matchsumsql);
				
				String forcematchsumsql = "select ISNULL(Totalmain,0) - ISNULL(Totalreversal,0) as Difference  from  ( select (select sum("
						+ amtList.get(i) + ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='MAIN' and  RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='MM' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) as Totalmain, (select sum(" + amtList.get(i)
						+ ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='REVERSAL' and RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='MM' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) As Totalreversal ) temp";
				
				System.out.println("forcematchsumsql:  "+forcematchsumsql);
				
				
				
				String unmatchsumsql = "select ISNULL(Totalmain,0) - ISNULL(Totalreversal,0) as Difference  from  ( select (select sum("
						+ amtList.get(i) + ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='MAIN' and  RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='AU' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) as Totalmain, (select sum(" + amtList.get(i)
						+ ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='REVERSAL' and RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='AU' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) As Totalreversal ) temp"; // unmatched
				// amount
				System.out.println("unmatchsumsql:  "+unmatchsumsql);
				
				String forceunmatchsumsql = "select ISNULL(Totalmain,0) - ISNULL(Totalreversal,0) as Difference  from  ( select (select sum("
						+ amtList.get(i) + ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='MAIN' and  RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='MU' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) as Totalmain, (select sum(" + amtList.get(i)
						+ ") from " + recdatasource + " WITH  (NOLOCK) where MAIN_REV_IND='REVERSAL' and RECON_SIDE='"
						+ reconSideList.get(i) + "' and MATCH_TYPE='MU' and " + dateList.get(i) + " between '"
						+ fromDate + "'  and '" + toDatetoCalender + "' ) As Totalreversal ) temp";
				
				System.out.println("forceunmatchsumsql:  "+forceunmatchsumsql);
				ResultSet dateRs = stmt.executeQuery(sql);

				if (dateRs.next()) {
					if (dateRs.getObject(1) != null)
						total = (int) dateRs.getObject(1);

				}
				ResultSet matchrs = stmt.executeQuery(matchsql);
				if (matchrs.next()) {
					if (matchrs.getObject(1) != null)
						match = (int) matchrs.getObject(1);
				}
				ResultSet unmatchrs = stmt.executeQuery(unmatchsql);
				if (unmatchrs.next()) {
					if (unmatchrs.getObject(1) != null)
						unmatch = (int) unmatchrs.getObject(1);
				}
				ResultSet forcematchrs = stmt.executeQuery(forcematchsql);
				if (forcematchrs.next()) {
					if (forcematchrs.getObject(1) != null)
						forcematch = (int) forcematchrs.getObject(1);
				}
				ResultSet forceunmatchrs = stmt.executeQuery(forceunmatchsql);
				if (forceunmatchrs.next()) {
					if (forceunmatchrs.getObject(1) != null)
						forceunmatch = (int) forceunmatchrs.getObject(1);
				}

				// Amount Resultsets
				ResultSet sumrs = stmt.executeQuery(sumsql);
				if (sumrs.next()) {
					if (sumrs.getObject(1) != null)
						sum = sumrs.getObject(1);

				}

				ResultSet matchsumrs = stmt.executeQuery(matchsumsql);
				if (matchsumrs.next()) {
					if (matchsumrs.getObject(1) != null)
						matchsum = matchsumrs.getObject(1);
				}
				// System.out.println(matchsum);
				ResultSet unmatchsumrs = stmt.executeQuery(unmatchsumsql);
				if (unmatchsumrs.next()) {
					if (unmatchsumrs.getObject(1) != null)
						unmatchsum = unmatchsumrs.getObject(1);
				}
				ResultSet forcematchsumrs = stmt.executeQuery(forcematchsumsql);
				if (forcematchsumrs.next()) {
					if (forcematchsumrs.getObject(1) != null)
						forcematchsum = forcematchsumrs.getObject(1);
				}
				ResultSet forceunmatchsumrs = stmt.executeQuery(forceunmatchsumsql);
				if (forceunmatchsumrs.next()) {
					if (forceunmatchsumrs.getObject(1) != null)
						forceunmatchsum = forceunmatchsumrs.getObject(1);
				}

				Map<String, Object> datamap = new HashMap<String, Object>();
				int maintotal = total + orphantotal;

				String floatsum = sum.toString();
				String floatmatchsum = matchsum.toString();
				String floatunmatchsum = unmatchsum.toString();
				String floatforceunmatchsum = forceunmatchsum.toString();

				String floatforcematchsum = forcematchsum.toString();

				String floatorphansum = orphansum.toString();
				Double mainsum = Double.parseDouble(floatsum) + Double.parseDouble(floatorphansum);

				Double mainmatchsum = Double.parseDouble(floatmatchsum);
				Double mainunmatchsum = Double.parseDouble(floatunmatchsum);

				Double mainforcematchsum = Double.parseDouble(floatforcematchsum);
				Double mainforceunmatchsum = Double.parseDouble(floatforceunmatchsum);
				Double mainorphansum = Double.parseDouble(floatorphansum);

				/*
				 *  RAMA  COMMENTED  START
				 *  
				 *  
				 * if (mainsum < 0)
					mainsum = -mainsum;

				if (mainmatchsum < 0)
					mainmatchsum = -mainmatchsum;
				if (mainunmatchsum < 0)
					mainunmatchsum = -mainunmatchsum;
				if (mainforcematchsum < 0)
					mainforcematchsum = -mainforcematchsum;
				if (mainforceunmatchsum < 0)
					mainforceunmatchsum = -mainforceunmatchsum;
				if (mainorphansum < 0)
					mainorphansum = -mainorphansum;
					
					
					RAMA  COMMENTED  END
					*/

				// number = (number < 0 ? -number : number);
				datamap.put("total", maintotal);
				datamap.put("source", reconSideList.get(i));
				datamap.put("am", match);
				datamap.put("au", unmatch);
				datamap.put("mm", forcematch);
				datamap.put("mu", forceunmatch);
				datamap.put("mu", forceunmatch);
				datamap.put("sum", mainsum);
				datamap.put("orphans", orphantotal);
				datamap.put("matchedsum", mainmatchsum);
				datamap.put("unmatchedsum", mainunmatchsum);
				datamap.put("orphansum", mainorphansum);
				datamap.put("forcematchedsum", mainforcematchsum);
				datamap.put("forceunmatchedsum", mainforceunmatchsum);

				list.add(datamap);

			}

		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			try {
				if (!stmt.isClosed()) {
					stmt.close();
				}
				if (!connection.isClosed()) {
					connection.close();
				}
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		java.util.Date date2= new java.util.Date();
		 System.out.println(new Timestamp(date2.getTime()));
		return list;
	}

	public static boolean compareTo(java.sql.Date date1, java.sql.Date date2) {

		// returns negative value if date1 is before date2
		// returns 0 if dates are even
		// returns positive value if date1 is after date2
		// return date1.getTime() - date2.getTime();
		if (date1.getTime() >= date2.getTime()) {
			return true;
		} else {
			return false;
		}

	}

}
