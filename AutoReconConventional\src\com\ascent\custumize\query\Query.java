//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.8-b130911.1802 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.05.19 at 05:20:23 PM IST 
//

package com.ascent.custumize.query;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

import com.ascent.integration.util.ColumnTypeUtil;



/**
 * <p>
 * Java class for query complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="query">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="queryType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="querieString" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="querieParam" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="queryParamLiteralValues" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}integer" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "query", propOrder = { "name", "targetTables", "queryType", "queryString", "queryParam",
		"queryParamLiteralValues" })
public class Query implements Serializable{

	/**
	 * 
	 */
	@XmlTransient
	private static final long serialVersionUID = -5039912023893836962L;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = false)
	protected String targetTables;
	@XmlElement(required = true)
	protected String queryType;
	@XmlElement(required = true)
	protected String queryString;
	@XmlElement(required = true)
	protected String queryParam;
	@XmlElement(required = true)
	protected String queryParamLiteralValues;
	@XmlAttribute(name = "id")
	protected int id;

	@XmlTransient
	List<String> queryParamList = new ArrayList<String>();
	@XmlTransient
	Map<String, Integer> queryParamTypeMap = new HashMap<String, Integer>();
	@XmlTransient
	List<String> targetTableNameList = new ArrayList<String>();
	@XmlTransient
	String reconTableName;

	public Query() throws Exception {

	}

	public void bootConf() throws Exception {
		this.queryParamList = new ArrayList<String>();
		this.queryParamTypeMap = new HashMap<String, Integer>();
		this.targetTableNameList = new ArrayList<String>();
		this.reconTableName="";
		
		
		
		
		if (this.targetTables != null && !this.targetTables.isEmpty()) {

			if (targetTables.contains(",")) {
				String[] temp = this.targetTables.split(",");

				for (String tableName : temp) {
					if (tableName != null && !tableName.isEmpty()) {
						if (tableName.startsWith("$") && tableName.endsWith("$")) {
							reconTableName = tableName;
						} else {
							targetTableNameList.add(tableName);
						}
					}

				}
			} else {
				if (targetTables.startsWith("$") && targetTables.endsWith("$")) {
					reconTableName = targetTables;
				} else {
					targetTableNameList.add(targetTables);
				}
			}
		}
		if (queryParam != null && queryParam.length() > 0) {
			queryParam = queryParam.replaceAll("\\n", "");
			queryParam = queryParam.replaceAll("\\s", "");
			queryParam = queryParam.trim();
			if (queryParam != null && !queryParam.isEmpty()) {
				String[] paramArrayWithType = queryParam.split(",");

				/*if (queryParamArr != null && queryParamArr.length > 0) {
					for (String paramWithType : queryParamArr) {
						String[] paramWithTypeArr = paramWithType.split("@");
						if (paramWithTypeArr != null && paramWithTypeArr.length == 2) {
							queryParamList.add(paramWithTypeArr[0]);
							queryParamTypeMap.put(paramWithTypeArr[0], paramWithTypeArr[1]);
						} else {
							throw new Exception("query : " + name + " not properly configured");
						}
					}
				}*/
				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
					this.queryParamTypeMap = getParamTypeMap(paramArrayWithType);
					this.queryParamList =getParamList(paramArrayWithType);
				}
			}
		}
	}
	
	public static List<String> getParamList(String[] updateQryParams) {
		List<String> paramList = new ArrayList<String>();
		for (String token : updateQryParams) {
			if (token.contains("@")) {
				String[] tokenArr = token.split("@");
				if (tokenArr.length > 1) {
					paramList.add(tokenArr[0]);
				}
			}
		}
		return paramList;
	}

	
	public static Map<String, Integer> getParamTypeMap(String[] updateQryParams) throws Exception {
		Map<String, Integer> paramTypeMap = new HashMap<String, Integer>();
		for (String token : updateQryParams) {
			if (token.contains("@")) {
				String[] tokenArr = token.split("@");
				if (tokenArr.length > 1) {
				Integer type=	ColumnTypeUtil.dbTypes
					.get(tokenArr[1]);
				if(type==null){
						throw new Exception(tokenArr[1] +" is invalid param type for the query param:"+token);
				}
					paramTypeMap.put(tokenArr[0],
							type);
				}else {
					throw new Exception("Missing param type for the param:"+token);
				}
			}else {
				throw new Exception("Missing param type for the param:"+token);
			}
		}
		return paramTypeMap;
	}

	public Query(String name, String queryType, String querieString, String querieParam, String queryParamLiteralValues,
			int id) {
		super();
		this.name = name;
		this.queryType = queryType;
		this.queryString = querieString;
		this.queryParam = querieParam;
		this.queryParamLiteralValues = queryParamLiteralValues;
		this.id = id;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the queryType property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getQueryType() {
		return queryType;
	}

	/**
	 * Sets the value of the queryType property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQueryType(String value) {
		this.queryType = value;
	}

	/**
	 * Gets the value of the querieString property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getQueryString() {
		return queryString;
	}

	/**
	 * Sets the value of the querieString property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQueryString(String value) {
		this.queryString = value;
	}

	/**
	 * Gets the value of the querieParam property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getQueryParam() {
		return queryParam;
	}

	/**
	 * Sets the value of the querieParam property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQueryParam(String value) {
		this.queryParam = value;
	}

	/**
	 * Gets the value of the queryParamLiteralValues property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getQueryParamLiteralValues() {
		return queryParamLiteralValues;
	}

	/**
	 * Sets the value of the queryParamLiteralValues property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQueryParamLiteralValues(String value) {
		this.queryParamLiteralValues = value;
	}

	/**
	 * Gets the value of the id property.
	 * 
	 * @return possible object is {@link BigInteger }
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 * @param value
	 *            allowed object is {@link BigInteger }
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	

	public List<String> getQueryParamList() {
		return queryParamList;
	}

	public void setQueryParamList(List<String> queryParamList) {
		this.queryParamList = queryParamList;
	}

	public String getTargetTables() {
		return targetTables;
	}

	public void setTargetTables(String targetTables) {
		this.targetTables = targetTables;
	}

	public List<String> getTargetTableNameList() {
		return targetTableNameList;
	}

	public void setTargetTableNameList(List<String> targetTableNameList) {
		this.targetTableNameList = targetTableNameList;
	}

	public String getReconTableName() {
		return reconTableName;
	}

	public void setReconTableName(String reconTableName) {
		this.reconTableName = reconTableName;
	}

	public Map<String, Integer> getQueryParamTypeMap() {
		return queryParamTypeMap;
	}

	public void setQueryParamTypeMap(Map<String, Integer> queryParamTypeMap) {
		this.queryParamTypeMap = queryParamTypeMap;
	}

}
