package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class SuspenceFinance 
{
	private static final String SUSPENCE_INTERNAL_RECONCILE_REPORT = "SUSPENCE_INTERNAL_RECONCILE_REPORT";
	private static final String SUSPENCE_INTERNAL_UNRECONCILE_REPORT = "SUSPENCE_INTERNAL_UNRECONCILE_REPORT";

	private static final String SUSPENCE_EXTERNAL_RECONCILE_REPORT = "SUSPENCE_EXTERNAL_RECONCILE_REPORT";
	private static final String SUSPENCE_EXTERNAL_UNRECONCILE_REPORT = "SUSPENCE_EXTERNAL_UNRECONCILE_REPORT";
	
	private static final String SUSPENCE_EXTERNAL_SUPPRESS_REPORT = "SUSPENCE_EXTERNAL_SUPPRESS_REPORT";
	private static final String SUSPENCE_INTERNAL_SUPPRESS_REPORT = "SUSPENCE_INTERNAL_SUPPRESS_REPORT";
	
	private static final String SUSPENCE_AGING_REPORT = "SUSPENCE_AGING_REPORT";
	
	private static final String SUSPENCE_INTERNAL_DRCR = "SUSPENCE_INTERNAL_DRCR";
	private static final String SUSPENCE_EXTERNAL_DRCR = "SUSPENCE_EXTERNAL_DRCR";


		LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> SuspenceInternalReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_INTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BRANCH_ID", rset.getString(2));
				map.put("TRAN_ID", rset.getString(3));
				map.put("TRAN_DATE", rset.getString(4));
				map.put("VALUE_DATE", rset.getString(5));
				map.put("ACCT_NUMBER", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("AMOUNT", rset.getString(8));
				map.put("TRAN_PARTICULAR", rset.getString(9));
				map.put("REFERENCE_NUMBER", rset.getString(10));
				map.put("TRAN_REMARKS", rset.getString(11));
				map.put("TRAN_CRNCY_CODE", rset.getString(12));
				map.put("REF_CRNCY_CODE", rset.getString(13));
				map.put("REF_AMT", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE_INDEX", rset.getString(17));
				map.put("WORKFLOW_STATUS", rset.getString(18));
				map.put("UPDATED_ON", rset.getString(19));
				map.put("CREATED_ON", rset.getString(20));
				map.put("RECON_STATUS", rset.getString(21));
				map.put("RECON_ID", rset.getString(22));
				map.put("ACTIVITY_COMMENTS", rset.getString(23));
				map.put("MAIN_REV_IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE_NAME", rset.getString(26));
				map.put("BUSINESS_AREA", rset.getString(27));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> SuspenceInternalUnReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_INTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BRANCH_ID", rset.getString(2));
				map.put("TRAN_ID", rset.getString(3));
				map.put("TRAN_DATE", rset.getString(4));
				map.put("VALUE_DATE", rset.getString(5));
				map.put("ACCT_NUMBER", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("AMOUNT", rset.getString(8));
				map.put("TRAN_PARTICULAR", rset.getString(9));
				map.put("REFERENCE_NUMBER", rset.getString(10));
				map.put("TRAN_REMARKS", rset.getString(11));
				map.put("TRAN_CRNCY_CODE", rset.getString(12));
				map.put("REF_CRNCY_CODE", rset.getString(13));
				map.put("REF_AMT", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE_INDEX", rset.getString(17));
				map.put("WORKFLOW_STATUS", rset.getString(18));
				map.put("UPDATED_ON", rset.getString(19));
				map.put("CREATED_ON", rset.getString(20));
				map.put("RECON_STATUS", rset.getString(21));
				map.put("RECON_ID", rset.getString(22));
				map.put("ACTIVITY_COMMENTS", rset.getString(23));
				map.put("MAIN_REV_IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE_NAME", rset.getString(26));
				map.put("BUSINESS_AREA", rset.getString(27));
				map.put("AGE", rset.getString(28));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> SuspenceExternalReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_EXTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BRANCH_ID", rset.getString(2));
				map.put("TRAN_ID", rset.getString(3));
				map.put("TRAN_DATE", rset.getString(4));
				map.put("VALUE_DATE", rset.getString(5));
				map.put("ACCT_NUMBER", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("AMOUNT", rset.getString(8));
				map.put("TRAN_PARTICULAR", rset.getString(9));
				map.put("REFERENCE_NUMBER", rset.getString(10));
				map.put("TRAN_REMARKS", rset.getString(11));
				map.put("TRAN_CRNCY_CODE", rset.getString(12));
				map.put("REF_CRNCY_CODE", rset.getString(13));
				map.put("REF_AMT", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE_INDEX", rset.getString(17));
				map.put("WORKFLOW_STATUS", rset.getString(18));
				map.put("UPDATED_ON", rset.getString(19));
				map.put("CREATED_ON", rset.getString(20));
				map.put("RECON_STATUS", rset.getString(21));
				map.put("RECON_ID", rset.getString(22));
				map.put("ACTIVITY_COMMENTS", rset.getString(23));
				map.put("MAIN_REV_IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE_NAME", rset.getString(26));
				map.put("BUSINESS_AREA", rset.getString(27));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> SuspenceExternalUnReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_EXTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BRANCH_ID", rset.getString(2));
				map.put("TRAN_ID", rset.getString(3));
				map.put("TRAN_DATE", rset.getString(4));
				map.put("VALUE_DATE", rset.getString(5));
				map.put("ACCT_NUMBER", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("AMOUNT", rset.getString(8));
				map.put("TRAN_PARTICULAR", rset.getString(9));
				map.put("REFERENCE_NUMBER", rset.getString(10));
				map.put("TRAN_REMARKS", rset.getString(11));
				map.put("TRAN_CRNCY_CODE", rset.getString(12));
				map.put("REF_CRNCY_CODE", rset.getString(13));
				map.put("REF_AMT", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE_INDEX", rset.getString(17));
				map.put("WORKFLOW_STATUS", rset.getString(18));
				map.put("UPDATED_ON", rset.getString(19));
				map.put("CREATED_ON", rset.getString(20));
				if(rset.getString(21)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON_STATUS", rset.getString(21));
				map.put("RECON_ID", rset.getString(22));
				map.put("ACTIVITY_COMMENTS", rset.getString(23));
				map.put("MAIN_REV_IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE_NAME", rset.getString(26));
				map.put("BUSINESS_AREA", rset.getString(27));
				map.put("AGE", rset.getString(28));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	public List<Map<String, Object>> SuspenceInternalSupress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf( SUSPENCE_INTERNAL_SUPPRESS_REPORT);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BRANCH_ID", rset.getString(2));
				map.put("TRAN_ID", rset.getString(3));
				map.put("TRAN_DATE", rset.getString(4));
				map.put("VALUE_DATE", rset.getString(5));
				map.put("ACCT_NUMBER", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("AMOUNT", rset.getString(8));
				map.put("TRAN_PARTICULAR", rset.getString(9));
				map.put("REFERENCE_NUMBER", rset.getString(10));
				map.put("TRAN_REMARKS", rset.getString(11));
				map.put("TRAN_CRNCY_CODE", rset.getString(12));
				map.put("REF_CRNCY_CODE", rset.getString(13));
				map.put("REF_AMT", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE_INDEX", rset.getString(17));
				map.put("WORKFLOW_STATUS", rset.getString(18));
				map.put("UPDATED_ON", rset.getString(19));
				map.put("CREATED_ON", rset.getString(20));
				map.put("RECON_STATUS", rset.getString(21));
				map.put("RECON_ID", rset.getString(22));
				map.put("ACTIVITY_COMMENTS", rset.getString(23));
				map.put("MAIN_REV_IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE_NAME", rset.getString(26));
				map.put("BUSINESS_AREA", rset.getString(27));
				map.put("VERIFIER USER ID", rset.getString(28));
				map.put("VERIFIER COMMENTS", rset.getString(29));
				map.put("MAKER USER ID", rset.getString(30));
				map.put("MAKER COMMENTS", rset.getString(31));
				
				
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> SuspenceExternalSupress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_EXTERNAL_SUPPRESS_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BRANCH_ID", rset.getString(2));
				map.put("TRAN_ID", rset.getString(3));
				map.put("TRAN_DATE", rset.getString(4));
				map.put("VALUE_DATE", rset.getString(5));
				map.put("ACCT_NUMBER", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("AMOUNT", rset.getString(8));
				map.put("TRAN_PARTICULAR", rset.getString(9));
				map.put("REFERENCE_NUMBER", rset.getString(10));
				map.put("TRAN_REMARKS", rset.getString(11));
				map.put("TRAN_CRNCY_CODE", rset.getString(12));
				map.put("REF_CRNCY_CODE", rset.getString(13));
				map.put("REF_AMT", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE_INDEX", rset.getString(17));
				map.put("WORKFLOW_STATUS", rset.getString(18));
				map.put("UPDATED_ON", rset.getString(19));
				map.put("CREATED_ON", rset.getString(20));
				map.put("RECON_STATUS", rset.getString(21));
				map.put("RECON_ID", rset.getString(22));
				map.put("ACTIVITY_COMMENTS", rset.getString(23));
				map.put("MAIN_REV_IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE_NAME", rset.getString(26));
				map.put("BUSINESS_AREA", rset.getString(27));
				map.put("VERIFIER USER ID", rset.getString(28));
				map.put("VERIFIER COMMENTS", rset.getString(29));
				map.put("MAKER USER ID", rset.getString(30));
				map.put("MAKER COMMENTS", rset.getString(31));
				
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> SuspenseAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_AGING_REPORT);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> suspenceInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> suspenceExternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SUSPENCE_EXTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public static void main(String[] args) {
		SuspenceFinance a = new SuspenceFinance();
		a.SuspenceInternalReconcile("2018-01-01", "2018-10-01");
		a.SuspenceInternalUnReconcile("2018-01-01", "2018-10-01");
		a.SuspenceExternalReconcile("2018-01-01", "2018-10-01");
		a.SuspenceExternalUnReconcile("2018-01-01", "2018-10-01");
		a.SuspenceInternalSupress("2018-01-01", "2018-10-01");
		a.SuspenceExternalSupress("2018-01-01", "2018-10-01");
		a.suspenceInternalDrcr("2018-01-01", "2018-10-01");
		a.suspenceExternalDrcr("2018-01-01", "2018-10-01");
		a.SuspenseAgingMethod();
		}

}
