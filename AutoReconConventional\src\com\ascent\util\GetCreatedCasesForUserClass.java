package com.ascent.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpSession;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GetCreatedCasesForUserClass extends BasicDataSource{

	private static final long serialVersionUID = 1L;

	public DSResponse executeFetch(final DSRequest request)throws Exception
	{ 

		DSResponse response=new DSResponse();

		try{
			User user=null;
			if(request.getHttpServletRequest()==null)
			{
				user=(User)request.getCriteria().get("userId");
			}
			else
			{
				HttpSession httpSession = request.getHttpServletRequest().getSession();
				user = (User) httpSession.getAttribute("userId");
			}

			String query = null;
			if(user.getSystemRole().contains("Maker"))
				query = "GET_CREATED_CASES_BY_MAKER_USER";
			else 
				query = "GET_CREATED_CASES_BY_CHECKER_USER";

			ArrayList<Map<String, Object>> createdCases  = (ArrayList<Map<String, Object>>) getCreatedCasesForUser(user, query);
			response.setData(createdCases);
		} 
		catch(Exception e)
		{
			e.printStackTrace();
		}
		return response;
	}

	public List<Map<String, Object>> getCreatedCasesForUser(User user, String queryName)
			throws ClassNotFoundException, SQLException, IOException {

		CustomerDao customerDao = new CustomerDao();

		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("created_by", user.getUserId());

		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, queryName);

		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data.get("activity_data") != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();

					@SuppressWarnings("unchecked")
					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}
		return activityDataList;
	}


}
