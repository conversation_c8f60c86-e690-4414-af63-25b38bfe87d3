# JDBC Nostro Properties
# This file contains database connection properties for Nostro operations

# Database connection settings
jdbc.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
jdbc.url=**************************************************************<PERSON>on@123;dataBaseName=BANK_DHOFAR_CONVENTIONAL
jdbc.username=Ascent
jdbc.password=AutoRecon@123
jdbc.database=BANK_DHOFAR_CONVENTIONAL

# Connection pool settings
jdbc.initialSize=5
jdbc.maxActive=20
jdbc.maxIdle=10
jdbc.minIdle=5
jdbc.maxWait=30000

# Validation settings
jdbc.validationQuery=SELECT 1
jdbc.testOnBorrow=true
jdbc.testOnReturn=false
jdbc.testWhileIdle=true
jdbc.timeBetweenEvictionRunsMillis=30000
