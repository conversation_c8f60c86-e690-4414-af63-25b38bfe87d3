AmCharts.translations["export"]||(AmCharts.translations["export"]={}),AmCharts.translations["export"].en||(AmCharts.translations["export"].en={"fallback.save.text":"CTRL + C to copy the data into the clipboard.","fallback.save.image":"Rightclick -> Save picture as... to save the image.","capturing.delayed.menu.label":"{{duration}}","capturing.delayed.menu.title":"Click to cancel","menu.label.print":"Print","menu.label.undo":"Undo","menu.label.redo":"Redo","menu.label.cancel":"Cancel","menu.label.save.image":"Download as ...","menu.label.save.data":"Save as ...","menu.label.draw":"Annotate ...","menu.label.draw.change":"Change ...","menu.label.draw.add":"Add ...","menu.label.draw.shapes":"Shape ...","menu.label.draw.colors":"Color ...","menu.label.draw.widths":"Size ...","menu.label.draw.opacities":"Opacity ...","menu.label.draw.text":"Text","menu.label.draw.modes":"Mode ...","menu.label.draw.modes.pencil":"Pencil","menu.label.draw.modes.line":"Line","menu.label.draw.modes.arrow":"Arrow","label.saved.from":"Saved from: "}),function(){AmCharts["export"]=function(e,t){var a={name:"export",version:"1.4.34",libs:{async:!0,autoLoad:!0,reload:!1,resources:["fabric.js/fabric.min.js","FileSaver.js/FileSaver.min.js","jszip/jszip.min.js","xlsx/xlsx.min.js",{"pdfmake/pdfmake.min.js":["pdfmake/vfs_fonts.js"]}],namespaces:{"pdfmake.js":"pdfMake","jszip.js":"JSZip","xlsx.js":"XLSX","fabric.js":"fabric","FileSaver.js":"saveAs"},loadTimeout:1e4},config:{},setup:{chart:e,hasBlob:!1,wrapper:!1,isIE:!!window.document.documentMode,IEversion:window.document.documentMode},drawing:{enabled:!1,undos:[],redos:[],buffer:{position:{x1:0,y1:0,x2:0,y2:0,xD:0,yD:0}},handler:{undo:function(e,t){var i=a.drawing.undos.pop();if(i){i.selectable=!0,a.drawing.redos.push(i),"added"==i.action&&a.setup.fabric.remove(i.target);var r=JSON.parse(i.state);i.target.set(r),i.target instanceof fabric.Group&&a.drawing.handler.change({color:r.cfg.color,width:r.cfg.width,opacity:r.cfg.opacity},!0,i.target),a.setup.fabric.renderAll(),i.state!=i.target.recentState||t||a.drawing.handler.undo(i,!0)}},redo:function(e,t){var i=a.drawing.redos.pop();if(i){i.selectable=!0,a.drawing.undos.push(i),"added"==i.action&&a.setup.fabric.add(i.target);var r=JSON.parse(i.state);i.target.recentState=i.state,i.target.set(r),i.target instanceof fabric.Group&&a.drawing.handler.change({color:r.cfg.color,width:r.cfg.width,opacity:r.cfg.opacity},!0,i.target),a.setup.fabric.renderAll(),"addified"==i.action&&a.drawing.handler.redo()}},done:function(e){a.drawing.enabled=!1,a.drawing.buffer.enabled=!1,a.drawing.undos=[],a.drawing.redos=[],a.createMenu(a.config.menu),a.setup.fabric.deactivateAll(),a.setup.wrapper&&(a.setup.chart.containerDiv.removeChild(a.setup.wrapper),a.setup.wrapper=!1)},add:function(e){var t=a.deepMerge({top:a.setup.fabric.height/2,left:a.setup.fabric.width/2},e||{}),i=-1!=t.url.indexOf(".svg")?fabric.loadSVGFromURL:fabric.Image.fromURL;i(t.url,function(e,i){var r=void 0!==i?fabric.util.groupSVGElements(e,i):e,n=!1;(r.height>a.setup.fabric.height||r.width>a.setup.fabric.width)&&(n=a.setup.fabric.height/2/r.height),t.top>a.setup.fabric.height&&(t.top=a.setup.fabric.height/2),t.left>a.setup.fabric.width&&(t.left=a.setup.fabric.width/2),r.set({originX:"center",originY:"center",top:t.top,left:t.left,width:n?r.width*n:r.width,height:n?r.height*n:r.height,fill:a.drawing.color}),a.setup.fabric.add(r)})},change:function(e,t,i){var r,n,o,s=a.deepMerge({},e||{}),l=i||a.drawing.buffer.target,d=l?l._objects?l._objects:[l]:null;if(s.mode&&(a.drawing.mode=s.mode),s.width&&(a.drawing.width=s.width,a.drawing.fontSize=3*s.width),s.fontSize&&(a.drawing.fontSize=s.fontSize),s.color&&(a.drawing.color=s.color),s.opacity&&(a.drawing.opacity=s.opacity),o=a.getRGBA(a.drawing.color),o.pop(),o.push(a.drawing.opacity),a.drawing.color="rgba("+o.join()+")",a.setup.fabric.freeDrawingBrush.color=a.drawing.color,a.setup.fabric.freeDrawingBrush.width=a.drawing.width,l){for(r=JSON.parse(l.recentState).cfg,r&&(s.color=s.color||r.color,s.width=s.width||r.width,s.opacity=s.opacity||r.opacity,s.fontSize=s.fontSize||3*s.width,o=a.getRGBA(s.color),o.pop(),o.push(s.opacity),s.color="rgba("+o.join()+")"),n=0;n<d.length;n++)d[n]instanceof fabric.Text||d[n]instanceof fabric.PathGroup||d[n]instanceof fabric.Triangle?((s.color||s.opacity)&&d[n].set({fill:s.color}),s.fontSize&&d[n].set({fontSize:s.fontSize})):(d[n]instanceof fabric.Path||d[n]instanceof fabric.Line)&&(l instanceof fabric.Group?(s.color||s.opacity)&&d[n].set({stroke:s.color}):((s.color||s.opacity)&&d[n].set({stroke:s.color}),s.width&&d[n].set({strokeWidth:s.width})));t||(r=JSON.stringify(a.deepMerge(l.saveState().originalState,{cfg:{color:s.color,width:s.width,opacity:s.opacity}})),l.recentState=r,a.drawing.redos=[],a.drawing.undos.push({action:"modified",target:l,state:r})),a.setup.fabric.renderAll()}},text:function(e){var t=a.deepMerge({text:a.i18l("menu.label.draw.text"),top:a.setup.fabric.height/2,left:a.setup.fabric.width/2,fontSize:a.drawing.fontSize,fontFamily:a.setup.chart.fontFamily||"Verdana",fill:a.drawing.color},e||{});t.click=function(){};var i=new fabric.IText(t.text,t);return a.setup.fabric.add(i),a.setup.fabric.setActiveObject(i),i.selectAll(),i.enterEditing(),i},line:function(e){var t,i,r,n,o=a.deepMerge({x1:a.setup.fabric.width/2-a.setup.fabric.width/10,x2:a.setup.fabric.width/2+a.setup.fabric.width/10,y1:a.setup.fabric.height/2,y2:a.setup.fabric.height/2,angle:90,strokeLineCap:a.drawing.lineCap,arrow:a.drawing.arrow,color:a.drawing.color,width:a.drawing.width,group:[]},e||{}),s=new fabric.Line([o.x1,o.y1,o.x2,o.y2],{stroke:o.color,strokeWidth:o.width,strokeLineCap:o.strokeLineCap});if(o.group.push(s),o.arrow&&(o.angle=o.angle?o.angle:a.getAngle(o.x1,o.y1,o.x2,o.y2),"start"==o.arrow?(r=o.y1+o.width/2,n=o.x1+o.width/2):"middle"==o.arrow?(r=o.y2+o.width/2-(o.y2-o.y1)/2,n=o.x2+o.width/2-(o.x2-o.x1)/2):(r=o.y2+o.width/2,n=o.x2+o.width/2),i=new fabric.Triangle({top:r,left:n,fill:o.color,height:7*o.width,width:7*o.width,angle:o.angle,originX:"center",originY:"bottom"}),o.group.push(i)),"config"!=o.action){if(o.arrow){var l=new fabric.Group(o.group);return l.set({cfg:o,fill:o.color,action:o.action,selectable:!0,known:"change"==o.action}),"change"==o.action&&a.setup.fabric.setActiveObject(l),a.setup.fabric.add(l),l}return a.setup.fabric.add(s),s}for(t=0;t<o.group.length;t++)o.group[t].noUndo=!0,a.setup.fabric.add(o.group[t]);return o}}},defaults:{position:"top-right",fileName:"amCharts",action:"download",overflow:!0,path:(e.path||"")+"plugins/export/",formats:{JPG:{mimeType:"image/jpg",extension:"jpg",capture:!0},PNG:{mimeType:"image/png",extension:"png",capture:!0},SVG:{mimeType:"text/xml",extension:"svg",capture:!0},PDF:{mimeType:"application/pdf",extension:"pdf",capture:!0},CSV:{mimeType:"text/plain",extension:"csv"},JSON:{mimeType:"text/plain",extension:"json"},XLSX:{mimeType:"application/octet-stream",extension:"xlsx"}},fabric:{backgroundColor:"#FFFFFF",removeImages:!0,forceRemoveImages:!1,selection:!1,loadTimeout:5e3,drawing:{enabled:!0,arrow:"end",lineCap:"butt",mode:"pencil",modes:["pencil","line","arrow"],color:"#000000",colors:["#000000","#FFFFFF","#FF0000","#00FF00","#0000FF"],shapes:["11.svg","14.svg","16.svg","17.svg","20.svg","27.svg"],width:1,fontSize:11,widths:[1,5,10,15],opacity:1,opacities:[1,.8,.6,.4,.2],menu:void 0,autoClose:!0},border:{fill:"",fillOpacity:0,stroke:"#000000",strokeWidth:1,strokeOpacity:1}},pdfMake:{images:{},pageOrientation:"portrait",pageMargins:40,pageOrigin:!0,pageSize:"A4",pageSizes:{"4A0":[4767.87,6740.79],"2A0":[3370.39,4767.87],A0:[2383.94,3370.39],A1:[1683.78,2383.94],A2:[1190.55,1683.78],A3:[841.89,1190.55],A4:[595.28,841.89],A5:[419.53,595.28],A6:[297.64,419.53],A7:[209.76,297.64],A8:[147.4,209.76],A9:[104.88,147.4],A10:[73.7,104.88],B0:[2834.65,4008.19],B1:[2004.09,2834.65],B2:[1417.32,2004.09],B3:[1000.63,1417.32],B4:[708.66,1000.63],B5:[498.9,708.66],B6:[354.33,498.9],B7:[249.45,354.33],B8:[175.75,249.45],B9:[124.72,175.75],B10:[87.87,124.72],C0:[2599.37,3676.54],C1:[1836.85,2599.37],C2:[1298.27,1836.85],C3:[918.43,1298.27],C4:[649.13,918.43],C5:[459.21,649.13],C6:[323.15,459.21],C7:[229.61,323.15],C8:[161.57,229.61],C9:[113.39,161.57],C10:[79.37,113.39],RA0:[2437.8,3458.27],RA1:[1729.13,2437.8],RA2:[1218.9,1729.13],RA3:[864.57,1218.9],RA4:[609.45,864.57],SRA0:[2551.18,3628.35],SRA1:[1814.17,2551.18],SRA2:[1275.59,1814.17],SRA3:[907.09,1275.59],SRA4:[637.8,907.09],EXECUTIVE:[521.86,756],FOLIO:[612,936],LEGAL:[612,1008],LETTER:[612,792],TABLOID:[792,1224]}},menu:void 0,divId:null,menuReviver:null,menuWalker:null,fallback:!0,keyListener:!0,fileListener:!0},i18l:function(e,t){var i=t?langugage:a.setup.chart.language?a.setup.chart.language:"en",r=AmCharts.translations[a.name][i]||AmCharts.translations[a.name].en;return r[e]||e},download:function(e,t,i){if(window.saveAs&&a.setup.hasBlob){a.toBlob({data:e,type:t},function(e){saveAs(e,i)})}else if(a.config.fallback&&"text/plain"==t){var r=document.createElement("div"),n=document.createElement("div"),o=document.createElement("textarea");n.innerHTML=a.i18l("fallback.save.text"),r.appendChild(n),r.appendChild(o),n.setAttribute("class","amcharts-export-fallback-message"),r.setAttribute("class","amcharts-export-fallback"),a.setup.chart.containerDiv.appendChild(r),o.setAttribute("readonly",""),o.value=e,o.focus(),o.select(),a.createMenu([{"class":"export-main export-close",label:"Done",click:function(){a.createMenu(a.config.menu),a.setup.chart.containerDiv.removeChild(r)}}])}else{if(!a.config.fallback||"image"!=t.split("/")[0])throw new Error("Unable to create file. Ensure saveAs (FileSaver.js) is supported.");var r=document.createElement("div"),n=document.createElement("div"),s=a.toImage({data:e});n.innerHTML=a.i18l("fallback.save.image"),r.appendChild(n),r.appendChild(s),n.setAttribute("class","amcharts-export-fallback-message"),r.setAttribute("class","amcharts-export-fallback"),a.setup.chart.containerDiv.appendChild(r),a.createMenu([{"class":"export-main export-close",label:"Done",click:function(){a.createMenu(a.config.menu),a.setup.chart.containerDiv.removeChild(r)}}])}return e},loadResource:function(e,t){var i,r,n,o,s,l,d=-1!=e.indexOf("//")?e:[a.libs.path,e].join(""),c=function(){if(t)for(i=0;i<t.length;i++)a.loadResource(t[i])};for(-1!=e.indexOf(".js")?(n=document.createElement("script"),n.setAttribute("type","text/javascript"),n.setAttribute("src",d),a.libs.async&&n.setAttribute("async","")):-1!=e.indexOf(".css")&&(n=document.createElement("link"),n.setAttribute("type","text/css"),n.setAttribute("rel","stylesheet"),n.setAttribute("href",d)),i=0;i<document.head.childNodes.length;i++)if(o=document.head.childNodes[i],s=o?o.src||o.href:!1,l=o?o.tagName:!1,o&&s&&-1!=s.indexOf(e)){a.libs.reload&&document.head.removeChild(o),r=!0;break}for(i in a.libs.namespaces){var f=a.libs.namespaces[i],s=e.toLowerCase(),o=i.toLowerCase();if(-1!=s.indexOf(o)&&void 0!==window[f]){r=!0;break}}(!r||a.libs.reload)&&(n.addEventListener("load",c),document.head.appendChild(n),a.listenersToRemove||(a.listenersToRemove=[]),a.listenersToRemove.push({node:n,method:c,event:"load"}))},loadDependencies:function(){var e,t;if(a.libs.autoLoad)for(e=0;e<a.libs.resources.length;e++)if(a.libs.resources[e]instanceof Object)for(t in a.libs.resources[e])a.loadResource(t,a.libs.resources[e][t]);else a.loadResource(a.libs.resources[e])},pxToNumber:function(e,t){return e||!t?Number(String(e).replace("px",""))||0:void 0},numberToPx:function(e){return String(e)+"px"},cloneObject:function(e){var t,i,r,n,o;t=Array.isArray(e)?[]:{};for(r in e)i=e[r],n="object"==typeof i,o=i instanceof Date,t[r]=n&&!o?a.cloneObject(i):i;return t},deepMerge:function(e,t,i){var r,n,o=t instanceof Array?"array":"object";for(r in t)"array"==o&&isNaN(r)||(n=t[r],(void 0==e[r]||i)&&(n instanceof Array?e[r]=new Array:n instanceof Function?e[r]=function(){}:n instanceof Date?e[r]=new Date:n instanceof Object?e[r]=new Object:n instanceof Number?e[r]=new Number:n instanceof String&&(e[r]=new String)),(e instanceof Object||e instanceof Array)&&(n instanceof Object||n instanceof Array)&&!(n instanceof Function||n instanceof Date||a.isElement(n))&&"chart"!=r?a.deepMerge(e[r],n,i):e instanceof Array&&!i?e.push(n):e[r]=n);return e},isElement:function(e){return e instanceof Object&&e&&1===e.nodeType},isHashbanged:function(e){var t=String(e).replace(/\"/g,"");return"url"==t.slice(0,3)?t.slice(t.indexOf("#")+1,t.length-1):!1},isPressed:function(e){return"mousemove"==e.type&&1===e.which||("touchmove"==e.type||1===e.buttons||1===e.button||1===e.which?a.drawing.buffer.isPressed=!0:a.drawing.buffer.isPressed=!1),a.drawing.buffer.isPressed},removeImage:function(e){if(e){if(a.config.fabric.forceRemoveImages)return!0;if(a.config.fabric.removeImages&&a.isTainted(e))return!0;if(a.setup.isIE&&(10==a.setup.IEversion||11==a.setup.IEversion)&&-1!=e.toLowerCase().indexOf(".svg"))return!0}return!1},isTainted:function(e){var t=String(window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:""));if(e){if(-1!=t.indexOf(":\\")||-1!=e.indexOf(":\\")||-1!=t.indexOf("file://")||-1!=e.indexOf("file://"))return!0;if(-1!=e.indexOf("//")&&-1==e.indexOf(t.replace(/.*:/,"")))return!0}return!1},isSupported:function(){return a.config.enabled&&(!(a.setup.isIE&&a.setup.IEversion<=9)||Array.prototype.indexOf&&document.head&&a.config.fallback!==!1)?!0:!1},getAngle:function(e,t,a,i){var r,n=a-e,o=i-t;return r=0==n?0==o?0:o>0?Math.PI/2:3*Math.PI/2:0==o?n>0?0:Math.PI:0>n?Math.atan(o/n)+Math.PI:0>o?Math.atan(o/n)+2*Math.PI:Math.atan(o/n),180*r/Math.PI},gatherAttribute:function(e,t,i,r){var n,r=r?r:0,i=i?i:3;return e&&(n=e.getAttribute(t),!n&&i>r)?a.gatherAttribute(e.parentNode,t,i,r+1):n},gatherClassName:function(e,t,i,r){var n,r=r?r:0,i=i?i:3;if(a.isElement(e)){if(n=-1!=(e.getAttribute("class")||"").split(" ").indexOf(t),!n&&i>r)return a.gatherClassName(e.parentNode,t,i,r+1);n&&(n=e)}return n},gatherElements:function(e,t,i){var r,n;for(r=0;r<e.children.length;r++){var o=e.children[r];if("clipPath"==o.tagName){var s={},l=fabric.parseTransformAttribute(a.gatherAttribute(o,"transform"));for(n=0;n<o.childNodes.length;n++)o.childNodes[n].setAttribute("fill","transparent"),s={x:a.pxToNumber(o.childNodes[n].getAttribute("x")),y:a.pxToNumber(o.childNodes[n].getAttribute("y")),width:a.pxToNumber(o.childNodes[n].getAttribute("width")),height:a.pxToNumber(o.childNodes[n].getAttribute("height"))};e.clippings[o.id]={svg:o,bbox:s,transform:l}}else if("pattern"==o.tagName){var d={node:o,source:o.getAttribute("xlink:href"),width:Number(o.getAttribute("width")),height:Number(o.getAttribute("height")),repeat:"repeat",offsetX:0,offsetY:0};for(n=0;n<o.childNodes.length;n++)if("rect"==o.childNodes[n].tagName)d.fill=o.childNodes[n].getAttribute("fill");else if("image"==o.childNodes[n].tagName){var c=fabric.parseAttributes(o.childNodes[n],fabric.SHARED_ATTRIBUTES);c.transformMatrix&&(d.offsetX=c.transformMatrix[4],d.offsetY=c.transformMatrix[5])}a.removeImage(d.source)?e.patterns[o.id]=d.fill?d.fill:"transparent":e.patterns[d.node.id]=d}else if("image"==o.tagName)i.included++,fabric.Image.fromURL(o.getAttribute("xlink:href"),function(e){i.loaded++});else{var c=["fill","stroke"];for(n=0;n<c.length;n++){var f=c[n],p=o.getAttribute(f),u=a.getRGBA(p);p&&!u&&(o.setAttribute(f,"none"),o.setAttribute(f+"-opacity","0"))}}}return e},getRGBA:function(e,t){return"none"!=e&&"transparent"!=e&&!a.isHashbanged(e)&&(e=new fabric.Color(e),e._source)?t?e:e.getSource():!1},gatherPosition:function(e,t){var i,r=a.drawing.buffer.position,n=fabric.util.invertTransform(a.setup.fabric.viewportTransform);return"touchmove"==e.type&&("touches"in e?e=e.touches[0]:"changedTouches"in e&&(e=e.changedTouches[0])),i=fabric.util.transformPoint(a.setup.fabric.getPointer(e,!0),n),1==t&&(r.x1=i.x,r.y1=i.y),r.x2=i.x,r.y2=i.y,r.xD=r.x1-r.x2<0?-1*(r.x1-r.x2):r.x1-r.x2,r.yD=r.y1-r.y2<0?-1*(r.y1-r.y2):r.y1-r.y2,r},modifyFabric:function(){fabric.ElementsParser.prototype.resolveGradient=function(e,t){var a=e.get(t);if(/^url\(/.test(a)){var i=a.slice(a.indexOf("#")+1,a.length-1);fabric.gradientDefs[this.svgUid][i]&&e.set(t,fabric.Gradient.fromElement(fabric.gradientDefs[this.svgUid][i],e))}},fabric.Text.fromElement=function(e,t){if(!e)return null;var a=fabric.parseAttributes(e,fabric.Text.ATTRIBUTE_NAMES);t=fabric.util.object.extend(t?fabric.util.object.clone(t):{},a),t.top=t.top||0,t.left=t.left||0,"dx"in a&&(t.left+=a.dx),"dy"in a&&(t.top+=a.dy),"fontSize"in t||(t.fontSize=fabric.Text.DEFAULT_SVG_FONT_SIZE),t.originX||(t.originX="left");var i="",r=[];if("textContent"in e)if(e.childNodes)for(var n=0;n<e.childNodes.length;n++)r.push(e.childNodes[n].textContent);else r.push(e.textContent);else"firstChild"in e&&null!==e.firstChild&&"data"in e.firstChild&&null!==e.firstChild.data&&r.push(e.firstChild.data);i=r.join("\n");var o=new fabric.Text(i,t),s=0;return"left"===o.originX&&(s=o.getWidth()/2),"right"===o.originX&&(s=-o.getWidth()/2),r.length>1?o.set({left:o.getLeft()+s,top:o.getTop()+o.fontSize*(r.length-1)*(.18+o._fontSizeFraction),textAlign:t.originX,lineHeight:r.length>1?.965:1.16}):o.set({left:o.getLeft()+s,top:o.getTop()-o.getHeight()/2+o.fontSize*(.18+o._fontSizeFraction)}),o}},capture:function(e,t){var i,r=a.deepMerge(a.deepMerge({},a.config.fabric),e||{}),n=[],o={x:0,y:0,pX:0,pY:0,width:a.setup.chart.divRealWidth,height:a.setup.chart.divRealHeight},s={loaded:0,included:0};a.modifyFabric(),a.handleCallback(r.beforeCapture,r);var l=a.setup.chart.containerDiv.getElementsByTagName("svg");for(i=0;i<l.length;i++){var d={svg:l[i],parent:l[i].parentNode,children:l[i].getElementsByTagName("*"),offset:{x:0,y:0},patterns:{},clippings:{}};d=a.gatherElements(d,r,s),n.push(d)}if(a.config.legend&&a.setup.chart.legend&&a.setup.chart.legend.divId){var d={svg:a.setup.chart.legend.container.container,parent:a.setup.chart.legend.container.container.parentNode,children:a.setup.chart.legend.container.container.getElementsByTagName("*"),offset:{x:0,y:0},legend:{type:-1!=["top","left"].indexOf(a.config.legend.position)?"unshift":"push",position:a.config.legend.position,width:a.config.legend.width?a.config.legend.width:a.setup.chart.legend.container.width,height:a.config.legend.height?a.config.legend.height:a.setup.chart.legend.container.height},patterns:{},clippings:{}};-1!=["left","right"].indexOf(d.legend.position)?(o.width+=d.legend.width,o.height=d.legend.height>o.height?d.legend.height:o.height):-1!=["top","bottom"].indexOf(d.legend.position)&&(o.height+=d.legend.height),d=a.gatherElements(d,r,s),n[d.legend.type](d)}if(a.drawing.enabled=r.drawing.enabled="draw"==r.action,a.drawing.buffer.enabled=a.drawing.enabled,a.setup.wrapper=document.createElement("div"),a.setup.wrapper.setAttribute("class",a.setup.chart.classNamePrefix+"-export-canvas"),a.setup.chart.containerDiv.appendChild(a.setup.wrapper),"stock"==a.setup.chart.type){var c={top:0,right:0,bottom:0,left:0};a.setup.chart.leftContainer&&(o.width-=a.setup.chart.leftContainer.offsetWidth,c.left=a.setup.chart.leftContainer.offsetWidth+2*a.setup.chart.panelsSettings.panelSpacing),a.setup.chart.rightContainer&&(o.width-=a.setup.chart.rightContainer.offsetWidth,c.right=a.setup.chart.rightContainer.offsetWidth+2*a.setup.chart.panelsSettings.panelSpacing),a.setup.chart.periodSelector&&-1!=["top","bottom"].indexOf(a.setup.chart.periodSelector.position)&&(o.height-=a.setup.chart.periodSelector.offsetHeight+a.setup.chart.panelsSettings.panelSpacing,c[a.setup.chart.periodSelector.position]+=a.setup.chart.periodSelector.offsetHeight+a.setup.chart.panelsSettings.panelSpacing),a.setup.chart.dataSetSelector&&-1!=["top","bottom"].indexOf(a.setup.chart.dataSetSelector.position)&&(o.height-=a.setup.chart.dataSetSelector.offsetHeight,c[a.setup.chart.dataSetSelector.position]+=a.setup.chart.dataSetSelector.offsetHeight),a.setup.wrapper.style.paddingTop=a.numberToPx(c.top),a.setup.wrapper.style.paddingRight=a.numberToPx(c.right),a.setup.wrapper.style.paddingBottom=a.numberToPx(c.bottom),a.setup.wrapper.style.paddingLeft=a.numberToPx(c.left)}for(a.setup.canvas=document.createElement("canvas"),a.setup.wrapper.appendChild(a.setup.canvas),a.setup.fabric=new fabric.Canvas(a.setup.canvas,a.deepMerge({width:o.width,height:o.height,isDrawingMode:!0},r)),a.deepMerge(a.setup.fabric,r),a.deepMerge(a.setup.fabric.freeDrawingBrush,r.drawing),a.deepMerge(a.drawing,r.drawing),a.drawing.handler.change(r.drawing),a.setup.fabric.on("mouse:down",function(e){a.gatherPosition(e.e,1);a.drawing.buffer.pressedTS=Number(new Date),a.isPressed(e.e)}),a.setup.fabric.on("mouse:move",function(e){var t=a.gatherPosition(e.e,2);if(a.isPressed(e.e),a.drawing.buffer.isPressed&&!a.drawing.buffer.line&&!a.drawing.buffer.isSelected&&"pencil"!=a.drawing.mode&&(t.xD>5||t.xD>5)&&(a.drawing.buffer.hasLine=!0,a.setup.fabric.isDrawingMode=!1,a.setup.fabric._onMouseUpInDrawingMode(e),a.drawing.buffer.line=a.drawing.handler.line({x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2,arrow:"line"==a.drawing.mode?!1:a.drawing.arrow,action:"config"})),a.drawing.buffer.line){var r,n,o,s=a.drawing.buffer.line;for(s.x2=t.x2,s.y2=t.y2,i=0;i<s.group.length;i++)r=s.group[i],r instanceof fabric.Line?r.set({x2:s.x2,y2:s.y2}):r instanceof fabric.Triangle&&(s.angle=a.getAngle(s.x1,s.y1,s.x2,s.y2)+90,"start"==s.arrow?(n=s.y1+s.width/2,o=s.x1+s.width/2):"middle"==s.arrow?(n=s.y2+s.width/2-(s.y2-s.y1)/2,o=s.x2+s.width/2-(s.x2-s.x1)/2):(n=s.y2+s.width/2,o=s.x2+s.width/2),r.set({top:n,left:o,angle:s.angle}));a.setup.fabric.renderAll()}}),a.setup.fabric.on("mouse:up",function(e){if(Number(new Date)-a.drawing.buffer.pressedTS<200){var t=a.setup.fabric.findTarget(e.e);t&&t.selectable&&a.setup.fabric.setActiveObject(t)}if(a.drawing.buffer.line){for(i=0;i<a.drawing.buffer.line.group.length;i++)a.drawing.buffer.line.group[i].remove();delete a.drawing.buffer.line.action,delete a.drawing.buffer.line.group,a.drawing.handler.line(a.drawing.buffer.line)}a.drawing.buffer.line=!1,a.drawing.buffer.hasLine=!1,a.drawing.buffer.isPressed=!1}),a.setup.fabric.on("object:selected",function(e){a.drawing.buffer.isSelected=!0,a.drawing.buffer.target=e.target,a.setup.fabric.isDrawingMode=!1}),a.setup.fabric.on("selection:cleared",function(e){a.drawing.buffer.onMouseDown=a.setup.fabric.freeDrawingBrush.onMouseDown,a.drawing.buffer.target=!1,a.drawing.buffer.isSelected&&(a.setup.fabric._isCurrentlyDrawing=!1,a.setup.fabric.freeDrawingBrush.onMouseDown=function(){}),setTimeout(function(){a.drawing.buffer.isSelected=!1,a.setup.fabric.isDrawingMode=!0,a.setup.fabric.freeDrawingBrush.onMouseDown=a.drawing.buffer.onMouseDown},10)}),a.setup.fabric.on("path:created",function(e){var t=e.path;return Number(new Date)-a.drawing.buffer.pressedTS<200||a.drawing.buffer.hasLine?(a.setup.fabric.remove(t),void a.setup.fabric.renderAll()):void 0}),a.setup.fabric.on("object:added",function(e){var t=e.target,i=a.deepMerge(t.saveState().originalState,{cfg:{color:a.drawing.color,width:a.drawing.width,opacity:a.drawing.opacity,fontSize:a.drawing.fontSize}});return Number(new Date)-a.drawing.buffer.pressedTS<200&&!t.noUndo?(a.setup.fabric.remove(t),void a.setup.fabric.renderAll()):(i=JSON.stringify(i),t.recentState=i,!t.selectable||t.known||t.noUndo||(t.isAnnotation=!0,a.drawing.undos.push({action:"added",target:t,state:i}),a.drawing.undos.push({action:"addified",target:t,state:i}),a.drawing.redos=[]),t.known=!0,void(a.setup.fabric.isDrawingMode=!0))}),a.setup.fabric.on("object:modified",function(e){var t=e.target,i=JSON.parse(t.recentState),r=a.deepMerge(t.saveState().originalState,{cfg:i.cfg});r=JSON.stringify(r),t.recentState=r,a.drawing.undos.push({action:"modified",target:t,state:r}),a.drawing.redos=[]}),a.setup.fabric.on("text:changed",function(e){var t=e.target;clearTimeout(t.timer),t.timer=setTimeout(function(){var e=JSON.stringify(t.saveState().originalState);t.recentState=e,a.drawing.redos=[],a.drawing.undos.push({action:"modified",target:t,state:e})},250)}),a.drawing.enabled?(a.setup.wrapper.setAttribute("class",a.setup.chart.classNamePrefix+"-export-canvas active"),a.setup.wrapper.style.backgroundColor=r.backgroundColor,a.setup.wrapper.style.display="block"):(a.setup.wrapper.setAttribute("class",a.setup.chart.classNamePrefix+"-export-canvas"),a.setup.wrapper.style.display="none"),i=0;i<n.length;i++){var d=n[i],f=a.gatherClassName(d.parent,a.setup.chart.classNamePrefix+"-legend-div",1),p=a.gatherClassName(d.parent,a.setup.chart.classNamePrefix+"-stock-panel-div"),u=a.gatherClassName(d.parent,a.setup.chart.classNamePrefix+"-scrollbar-chart-div");"stock"==a.setup.chart.type&&a.setup.chart.legendSettings.position?-1!=["top","bottom"].indexOf(a.setup.chart.legendSettings.position)?d.parent.style.top&&d.parent.style.left?(d.offset.y=a.pxToNumber(d.parent.style.top),d.offset.x=a.pxToNumber(d.parent.style.left)):(d.offset.x=o.x,d.offset.y=o.y,o.y+=a.pxToNumber(d.parent.style.height),p?(o.pY=a.pxToNumber(p.style.marginTop),d.offset.y+=o.pY):u&&(d.offset.y+=o.pY)):-1!=["left","right"].indexOf(a.setup.chart.legendSettings.position)&&(d.offset.y=a.pxToNumber(d.parent.style.top)+o.pY,d.offset.x=a.pxToNumber(d.parent.style.left)+o.pX,f?o.pY+=a.pxToNumber(p.style.height)+a.setup.chart.panelsSettings.panelSpacing:u&&(d.offset.y-=a.setup.chart.panelsSettings.panelSpacing)):("absolute"==d.parent.style.position?(d.offset.absolute=!0,d.offset.top=a.pxToNumber(d.parent.style.top),d.offset.right=a.pxToNumber(d.parent.style.right,!0),d.offset.bottom=a.pxToNumber(d.parent.style.bottom,!0),d.offset.left=a.pxToNumber(d.parent.style.left),d.offset.width=a.pxToNumber(d.parent.style.width),d.offset.height=a.pxToNumber(d.parent.style.height)):d.parent.style.top&&d.parent.style.left?(d.offset.y=a.pxToNumber(d.parent.style.top),d.offset.x=a.pxToNumber(d.parent.style.left)):d.legend?"left"==d.legend.position?o.x+=d.legend.width:"right"==d.legend.position?d.offset.x+=o.width-d.legend.width:"top"==d.legend.position?o.y+=d.legend.height:"bottom"==d.legend.position&&(d.offset.y+=o.height-d.legend.height):(d.offset.x=o.x,d.offset.y=o.y+o.pY,o.y+=a.pxToNumber(d.parent.style.height)),f&&p&&p.style.marginTop?(o.y+=a.pxToNumber(p.style.marginTop),d.offset.y+=a.pxToNumber(p.style.marginTop)):a.setup.chart.legend&&-1!=["left","right"].indexOf(a.setup.chart.legend.position)&&(d.offset.y=a.pxToNumber(d.parent.style.top),d.offset.x=a.pxToNumber(d.parent.style.left))),fabric.parseSVGDocument(d.svg,function(e){return function(i,l){var d,c=fabric.util.groupSVGElements(i,l),f=[],p={selectable:!1,isCoreElement:!0};for(e.offset.absolute?(void 0!==e.offset.bottom?p.top=o.height-e.offset.height-e.offset.bottom:p.top=e.offset.top,void 0!==e.offset.right?p.left=o.width-e.offset.width-e.offset.right:p.left=e.offset.left):(p.top=e.offset.y,p.left=e.offset.x),d=0;d<c.paths.length;d++){var u=null;if(c.paths[d]){if(a.removeImage(c.paths[d]["xlink:href"]))continue;if(c.paths[d].fill instanceof Object)"radial"==c.paths[d].fill.type&&-1==["pie","gauge"].indexOf(a.setup.chart.type)&&(c.paths[d].fill.coords.r2=-1*c.paths[d].fill.coords.r1,c.paths[d].fill.coords.r1=0,c.paths[d].set({opacity:c.paths[d].fillOpacity}));else if((u=a.isHashbanged(c.paths[d].fill))&&e.patterns&&e.patterns[u]){var g=e.patterns[u];s.included++,fabric.Image.fromURL(g.source,function(e,t){return function(i){s.loaded++,i.set({top:e.offsetY,left:e.offsetX,width:e.width,height:e.height}),a.setup.fabric._isRetinaScaling()&&i.set({top:e.offsetY/2,left:e.offsetX/2,scaleX:.5,scaleY:.5});var r=new fabric.StaticCanvas(void 0,{backgroundColor:e.fill,width:i.getWidth(),height:i.getHeight()});r.add(i);var n=new fabric.Pattern({source:r.getElement(),offsetX:c.paths[t].width/2,offsetY:c.paths[t].height/2,repeat:"repeat"});c.paths[t].set({fill:n,opacity:c.paths[t].fillOpacity})}}(g,d))}(u=a.isHashbanged(c.paths[d].clipPath))&&e.clippings&&e.clippings[u]&&(!function(t,a){var i=c.paths[t].toSVG;c.paths[t].toSVG=function(t){return i.apply(this,[function(i){return t(i,e.clippings[a])}])}}(d,u),c.paths[d].set({clipTo:function(t,i){return function(t){var r=e.clippings[i],n=this.transformMatrix||[1,0,0,1,0,0],o={top:r.bbox.y,left:r.bbox.x,width:r.bbox.width,height:r.bbox.height};"map"==a.setup.chart.type&&(o.top+=r.transform[5],o.left+=r.transform[4]),r.bbox.x&&n[4]&&r.bbox.y&&n[5]&&(o.top-=n[5],o.left-=n[4]),t.rect(o.left,o.top,o.width,o.height)}}(d,u)}))}f.push(c.paths[d])}if(c.paths=f,c.set(p),a.setup.fabric.add(c),e.svg.parentNode&&e.svg.parentNode.getElementsByTagName){var h=e.svg.parentNode.getElementsByClassName(a.setup.chart.classNamePrefix+"-balloon-div");for(d=0;d<h.length;d++)if(r.balloonFunction instanceof Function)r.balloonFunction.apply(a,[h[d],e]);else{var m=h[d],b=fabric.parseStyleAttribute(m),w=fabric.parseStyleAttribute(m.childNodes[0]),v=new fabric.Text(m.innerText||m.textContent||m.innerHTML,{selectable:!1,top:b.top+e.offset.y,left:b.left+e.offset.x,fill:w.color,fontSize:w.fontSize,fontFamily:w.fontFamily,textAlign:w["text-align"],isCoreElement:!0});a.setup.fabric.add(v)}}if(e.svg.nextSibling&&"A"==e.svg.nextSibling.tagName){var m=e.svg.nextSibling,b=fabric.parseStyleAttribute(m),v=new fabric.Text(m.innerText||m.textContent||m.innerHTML,{selectable:!1,top:b.top+e.offset.y,left:b.left+e.offset.x,fill:b.color,fontSize:b.fontSize,fontFamily:b.fontFamily,opacity:b.opacity,isCoreElement:!0});a.setup.fabric.add(v)}if(n.pop(),!n.length)var y=Number(new Date),x=setInterval(function(){var e=Number(new Date);(s.loaded==s.included||e-y>a.config.fabric.loadTimeout)&&(clearTimeout(x),a.handleBorder(r),a.handleCallback(r.afterCapture,r),a.setup.fabric.renderAll(),a.handleCallback(t,r))},AmCharts.updateRate)}}(d),function(e,t){var i,n=a.gatherAttribute(e,"class"),o=a.gatherAttribute(e,"visibility"),s=a.gatherAttribute(e,"clip-path");if(t.className=String(n),t.classList=String(n).split(" "),t.clipPath=s,t.svg=e,"hidden"==o)t.opacity=0;else{var l=["fill","stroke"];for(i=0;i<l.length;i++){var d=l[i],c=String(e.getAttribute(d)||"none"),f=Number(e.getAttribute(d+"-opacity")||"1"),p=a.getRGBA(c);p&&(p.pop(),p.push(f),t[d]="rgba("+p.join()+")",t[d+a.capitalize("opacity")]=f)}}a.handleCallback(r.reviver,t,e)})}},toCanvas:function(e,t){var i=a.deepMerge({},e||{}),r=a.setup.canvas;return a.handleCallback(t,r,i),r},toImage:function(e,t){var i=a.deepMerge({format:"png",quality:1,multiplier:a.config.multiplier},e||{}),r=i.data,n=document.createElement("img");return i.data||(r=i.lossless||"svg"==i.format?a.toSVG(a.deepMerge(i,{getBase64:!0})):a.setup.fabric.toDataURL(i)),n.setAttribute("src",r),a.handleCallback(t,n,i),n},toBlob:function(e,t){var i,r=a.deepMerge({data:"empty",type:"text/plain"},e||{}),n=/^data:.+;base64,(.*)$/.exec(r.data);return n&&(r.data=n[0],r.type=r.data.slice(5,r.data.indexOf(",")-7),r.data=a.toByteArray({data:r.data.slice(r.data.indexOf(",")+1,r.data.length)})),i=r.getByteArray?r.data:new Blob([r.data],{type:r.type}),a.handleCallback(t,i,r),i},toJPG:function(e,t){var i=a.deepMerge({format:"jpeg",quality:1,multiplier:a.config.multiplier},e||{});i.format=i.format.toLowerCase();var r=a.setup.fabric.toDataURL(i);return a.handleCallback(t,r,i),r},toPNG:function(e,t){var i=a.deepMerge({format:"png",quality:1,multiplier:a.config.multiplier},e||{}),r=a.setup.fabric.toDataURL(i);return a.handleCallback(t,r,i),r},toSVG:function(e,t){var i=[],r=a.deepMerge({reviver:function(e,t){var r=new RegExp(/\bstyle=(['"])(.*?)\1/),n=r.exec(e)[0].slice(7,-1),o=n.split(";"),s=[];for(i1=0;i1<o.length;i1++)if(o[i1]){var l=o[i1].replace(/\s/g,"").split(":"),d=l[0],c=l[1];if(-1!=["fill","stroke"].indexOf(d))if(c=a.getRGBA(c,!0)){var f="#"+c.toHex(),p=c._source[3];s.push([d,f].join(":")),s.push([d+"-opacity",p].join(":"))}else s.push(o[i1]);else"opactiy"!=d&&s.push(o[i1])}if(e=e.replace(n,s.join(";")),t){var u=2,g=e.slice(-u);"/>"!=g&&(u=3,g=e.slice(-u));var h=e.slice(0,e.length-u),m=' clip-path="url(#'+t.svg.id+')" ',b=(new XMLSerializer).serializeToString(t.svg);e=h+m+g,i.push(b)}return e}},e||{}),n=a.setup.fabric.toSVG(r,r.reviver);if(i.length){var o=n.slice(0,n.length-6),s=n.slice(-6);
n=o+i.join("")+s}return r.getBase64&&(n="data:image/svg+xml;base64,"+btoa(n)),a.handleCallback(t,n,r),n},toPDF:function(e,t){function i(e){if("number"==typeof e||e instanceof Number)e={left:e,right:e,top:e,bottom:e};else if(e instanceof Array)if(2===e.length)e={left:e[0],top:e[1],right:e[0],bottom:e[1]};else{if(4!==e.length)throw"Invalid pageMargins definition";e={left:e[0],top:e[1],right:e[2],bottom:e[3]}}else e={left:a.defaults.pdfMake.pageMargins,top:a.defaults.pdfMake.pageMargins,right:a.defaults.pdfMake.pageMargins,bottom:a.defaults.pdfMake.pageMargins};return e}function r(e,t){var i=a.defaults.pdfMake.pageSizes[String(e).toUpperCase()].slice();if(!i)throw new Error('The given pageSize "'+e+'" does not exist!');return"landscape"==t&&i.reverse(),i}var n=a.deepMerge(a.deepMerge({multiplier:a.config.multiplier||2,pageOrigin:void 0===a.config.pageOrigin?!0:!1},a.config.pdfMake),e||{},!0),o=new pdfMake.createPdf(n);if(n.images.reference=a.toPNG(n),!n.content){var s=[],l=r(n.pageSize,n.pageOrientation),d=i(n.pageMargins);l[0]-=d.left+d.right,l[1]-=d.top+d.bottom,n.pageOrigin&&(s.push(a.i18l("label.saved.from")),s.push(window.location.href),l[1]-=28.128),s.push({image:"reference",fit:l}),n.content=s}return t&&o.getDataUrl(function(e){return function(t){e.apply(a,arguments)}}(t)),o},toPRINT:function(e,t){var i,r=a.deepMerge({delay:1,lossless:!1},e||{}),n=a.toImage(r),o=[],s=document.body.childNodes;for(n.setAttribute("style","width: 100%; max-height: 100%;"),i=0;i<s.length;i++)a.isElement(s[i])&&(o[i]=s[i].style.display,s[i].style.display="none");return document.body.appendChild(n),window.print(),setTimeout(function(){for(i=0;i<s.length;i++)a.isElement(s[i])&&(s[i].style.display=o[i]);document.body.removeChild(n),a.handleCallback(t,n,r)},r.delay),n},toJSON:function(e,t){var i=a.deepMerge({dateFormat:a.config.dateFormat||"dateObject"},e||{},!0);i.data=i.data?i.data:a.getChartData(i);var r=JSON.stringify(i.data,void 0,"	");return a.handleCallback(t,r,i),r},toCSV:function(e,t){function i(e,t){return"string"==typeof e&&(o.escape&&(e=e.replace('"','""')),o.quotes&&(e=['"',e,'"'].join(""))),e}var r,n,o=a.deepMerge({data:a.getChartData(e),delimiter:",",quotes:!0,escape:!0,withHeader:!0},e||{},!0),s="",l=[],d=[];for(f in o.data[0])d.push(i(f)),l.push(f);o.withHeader&&(s+=d.join(o.delimiter)+"\n");for(r in o.data)if(d=[],!isNaN(r)){for(n in l)if(!isNaN(n)){var c=l[n],f=o.data[r][c];d.push(i(f,c))}s+=d.join(o.delimiter)+"\n"}return a.handleCallback(t,s,o),s},toXLSX:function(e,t){function i(e,t){t&&(e+=1462);var a=Date.parse(e),i=60*e.getTimezoneOffset()*1e3;return(a-i-new Date(Date.UTC(1899,11,30)))/864e5}function r(e,t){for(var a={},r={s:{c:1e7,r:1e7},e:{c:0,r:0}},n=0;n!=e.length;++n)for(var o=0;o!=e[n].length;++o){r.s.r>n&&(r.s.r=n),r.s.c>o&&(r.s.c=o),r.e.r<n&&(r.e.r=n),r.e.c<o&&(r.e.c=o);var s={v:e[n][o]};if(null!=s.v){var l=XLSX.utils.encode_cell({c:o,r:n});"number"==typeof s.v?s.t="n":"boolean"==typeof s.v?s.t="b":s.v instanceof Date?(s.t="n",s.z=XLSX.SSF._table[14],s.v=i(s.v)):s.t="s",a[l]=s}}return r.s.c<1e7&&(a["!ref"]=XLSX.utils.encode_range(r)),a}var n=a.deepMerge({name:"amCharts",dateFormat:a.config.dateFormat||"dateObject",withHeader:!0,stringify:!1},e||{},!0),o="",s={SheetNames:[],Sheets:{}};return n.data=n.data?n.data:a.getChartData(n),s.SheetNames.push(n.name),s.Sheets[n.name]=r(a.toArray(n)),o=XLSX.write(s,{bookType:"xlsx",bookSST:!0,type:"base64"}),o="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+o,a.handleCallback(t,o,n),o},toArray:function(e,t){var i,r,n=a.deepMerge({data:a.getChartData(e),withHeader:!1,stringify:!0},e||{},!0),o=[],s=[];for(r in n.data[0])s.push(r);n.withHeader&&o.push(s);for(i in n.data){var l=[];if(!isNaN(i)){for(r in s)if(!isNaN(r)){var r=s[r],d=n.data[i][r];d=null==d?"":n.stringify?String(d):d,l.push(d)}o.push(l)}}return a.handleCallback(t,o,n),o},toByteArray:function(e,t){function i(e){var t=e.charCodeAt(0);return t===s?62:t===l?63:d>t?-1:d+10>t?t-d+26+26:f+26>t?t-f:c+26>t?t-c+26:void 0}function r(e){function t(e){d[f++]=e}var a,r,n,s,l,d;if(e.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var c=e.length;l="="===e.charAt(c-2)?2:"="===e.charAt(c-1)?1:0,d=new o(3*e.length/4-l),n=l>0?e.length-4:e.length;var f=0;for(a=0,r=0;n>a;a+=4,r+=3)s=i(e.charAt(a))<<18|i(e.charAt(a+1))<<12|i(e.charAt(a+2))<<6|i(e.charAt(a+3)),t((16711680&s)>>16),t((65280&s)>>8),t(255&s);return 2===l?(s=i(e.charAt(a))<<2|i(e.charAt(a+1))>>4,t(255&s)):1===l&&(s=i(e.charAt(a))<<10|i(e.charAt(a+1))<<4|i(e.charAt(a+2))>>2,t(s>>8&255),t(255&s)),d}var n=a.deepMerge({},e||{}),o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="+".charCodeAt(0),l="/".charCodeAt(0),d="0".charCodeAt(0),c="a".charCodeAt(0),f="A".charCodeAt(0),p=r(n.data);return a.handleCallback(t,p,n),p},handleCallback:function(e){var t,i=Array();if(e&&e instanceof Function){for(t=0;t<arguments.length;t++)t>0&&i.push(arguments[t]);return e.apply(a,i)}},handleBorder:function(e){if(a.config.border instanceof Object){var t=a.deepMerge(a.defaults.fabric.border,e.border||{},!0),i=new fabric.Rect;t.width=a.setup.fabric.width-t.strokeWidth,t.height=a.setup.fabric.height-t.strokeWidth,i.set(t),a.setup.fabric.add(i)}},handleDropbox:function(e){if(a.drawing.enabled)if(e.preventDefault(),e.stopPropagation(),"dragover"==e.type)a.setup.wrapper.setAttribute("class",a.setup.chart.classNamePrefix+"-export-canvas active dropbox");else if(a.setup.wrapper.setAttribute("class",a.setup.chart.classNamePrefix+"-export-canvas active"),"drop"==e.type&&e.dataTransfer.files.length)for(var t=0;t<e.dataTransfer.files.length;t++){var i=new FileReader;i.onloadend=function(t){return function(){a.drawing.handler.add({url:i.result,top:e.layerY-10*t,left:e.layerX-10*t})}}(t),i.readAsDataURL(e.dataTransfer.files[t])}},handleReady:function(e){var t=this,a=Number(new Date);t.handleCallback(e,"data",!1);for(filename in t.libs.namespaces){var i=t.libs.namespaces[filename];!function(i){var r=setInterval(function(){var n=Number(new Date);(n-a>t.libs.loadTimeout||i in window)&&(clearTimeout(r),t.handleCallback(e,i,n-a>t.libs.loadTimeout))},AmCharts.updateRate)}(i)}},getChartData:function(e){function t(e,t,r){function n(e,t){return-1!=l.dataFields.indexOf(e)?n([e,".",t].join("")):e}e&&l.exportTitles&&"gantt"!=a.setup.chart.type&&(i=n(e,r),l.dataFieldsMap[i]=e,l.dataFields.push(i),l.titles[i]=t||i)}var i,r,n,o,s,l=a.deepMerge({data:[],titles:{},dateFields:[],dataFields:[],dataFieldsMap:{},exportTitles:a.config.exportTitles,exportFields:a.config.exportFields,exportSelection:a.config.exportSelection,columnNames:a.config.columnNames},e||{},!0),d=["valueField","openField","closeField","highField","lowField","xField","yField"];if(0==l.data.length)if("stock"==a.setup.chart.type){for(l.data=a.cloneObject(a.setup.chart.mainDataSet.dataProvider),t(a.setup.chart.mainDataSet.categoryField),l.dateFields.push(a.setup.chart.mainDataSet.categoryField),r=0;r<a.setup.chart.mainDataSet.fieldMappings.length;r++){var c=a.setup.chart.mainDataSet.fieldMappings[r];for(n=0;n<a.setup.chart.panels.length;n++){var f=a.setup.chart.panels[n];for(o=0;o<f.stockGraphs.length;o++){var p=f.stockGraphs[o];for(i4=0;i4<d.length;i4++)p[d[i4]]==c.toField&&t(c.fromField,p.title,d[i4])}}}if(a.setup.chart.comparedGraphs.length){for(s=[],r=0;r<l.data.length;r++)s.push(l.data[r][a.setup.chart.mainDataSet.categoryField]);for(r=0;r<a.setup.chart.comparedGraphs.length;r++){var p=a.setup.chart.comparedGraphs[r];for(n=0;n<p.dataSet.dataProvider.length;n++){var u=p.dataSet.categoryField,g=p.dataSet.dataProvider[n][u],h=s.indexOf(g);if(-1!=h)for(o=0;o<p.dataSet.fieldMappings.length;o++){var c=p.dataSet.fieldMappings[o],i=p.dataSet.id+"_"+c.toField;l.data[h][i]=p.dataSet.dataProvider[n][c.fromField],l.titles[i]||t(i,p.dataSet.title)}}}}}else if("gantt"==a.setup.chart.type){t(a.setup.chart.categoryField),l.dateFields.push(a.setup.chart.categoryField);var m=a.setup.chart.segmentsField;for(r=0;r<a.setup.chart.dataProvider.length;r++){var b=a.setup.chart.dataProvider[r];if(b[m])for(n=0;n<b[m].length;n++)b[m][n][a.setup.chart.categoryField]=b[a.setup.chart.categoryField],l.data.push(b[m][n])}for(r=0;r<a.setup.chart.graphs.length;r++){var p=a.setup.chart.graphs[r];for(n=0;n<d.length;n++){var w=d[n],v=p[w];p.title;t(v,p.title,w)}}}else if(-1!=["pie","funnel"].indexOf(a.setup.chart.type))l.data=a.setup.chart.dataProvider,t(a.setup.chart.titleField),l.dateFields.push(a.setup.chart.titleField),t(a.setup.chart.valueField);else if("map"!=a.setup.chart.type)for(l.data=a.setup.chart.dataProvider,a.setup.chart.categoryAxis&&(t(a.setup.chart.categoryField,a.setup.chart.categoryAxis.title),a.setup.chart.categoryAxis.parseDates!==!1&&l.dateFields.push(a.setup.chart.categoryField)),r=0;r<a.setup.chart.graphs.length;r++){var p=a.setup.chart.graphs[r];for(n=0;n<d.length;n++){var w=d[n],v=p[w];t(v,p.title,w)}}return a.processData(l)},getAnnotations:function(e,t){var i,r=a.deepMerge({},e||{},!0),n=[];for(i=0;i<a.setup.fabric._objects.length;i++)if(!a.setup.fabric._objects[i].isCoreElement){var o=a.setup.fabric._objects[i].toJSON();a.handleCallback(r.reviver,o,i),n.push(o)}return a.handleCallback(t,n),n},setAnnotations:function(e,t){var i=a.deepMerge({data:[]},e||{},!0);return fabric.util.enlivenObjects(i.data,function(e){e.forEach(function(e,t){a.handleCallback(i.reviver,e,t),a.setup.fabric.add(e)}),a.handleCallback(t,i)}),i.data},processData:function(t){var i,r,n=a.deepMerge({data:[],titles:{},dateFields:[],dataFields:[],dataFieldsMap:{},dataDateFormat:a.setup.chart.dataDateFormat,dateFormat:a.config.dateFormat||a.setup.chart.dataDateFormat||"YYYY-MM-DD",exportTitles:a.config.exportTitles,exportFields:a.config.exportFields,exportSelection:a.config.exportSelection,columnNames:a.config.columnNames,processData:a.config.processData},t||{},!0);if(n.data.length){for(i=0;i<n.data.length;i++)for(r in n.data[i])-1==n.dataFields.indexOf(r)&&(n.dataFields.push(r),n.dataFieldsMap[r]=r);void 0!==n.exportFields&&(n.dataFields=n.dataFields.filter(function(e){return-1!=n.exportFields.indexOf(e)}));var o=[];for(i=0;i<n.data.length;i++){var s={},l=!1;for(r=0;r<n.dataFields.length;r++){var d=n.dataFields[r],c=n.dataFieldsMap[d],f=n.columnNames&&n.columnNames[d]||n.titles[d]||d,p=n.data[i][c];null==p&&(p=void 0),n.exportTitles&&"gantt"!=a.setup.chart.type&&f in s&&(f+=["( ",d," )"].join("")),-1!=n.dateFields.indexOf(c)&&(n.dataDateFormat&&(p instanceof String||"string"==typeof p)?p=AmCharts.stringToDate(p,n.dataDateFormat):n.dateFormat&&(p instanceof Number||"number"==typeof p)&&(p=new Date(p)),n.exportSelection&&(p instanceof Date?(p<e.startDate||p>e.endDate)&&(l=!0):(i<e.startIndex||i>e.endIndex)&&(l=!0)),n.dateFormat&&"dateObject"!=n.dateFormat&&p instanceof Date&&(p=AmCharts.formatDate(p,n.dateFormat))),s[f]=p}l||o.push(s)}n.data=o}return void 0!==n.processData&&(n.data=a.handleCallback(n.processData,n.data,n)),n.data},capitalize:function(e){return e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()},createMenu:function(t,i){function r(t,i){var n,o,s=document.createElement("ul");for(n=0;n<t.length;n++){var l="string"==typeof t[n]?{format:t[n]}:t[n],d=document.createElement("li"),c=document.createElement("a"),f=document.createElement("img"),p=document.createElement("span"),u=String(l.action?l.action:l.format).toLowerCase();if(l.format=String(l.format).toUpperCase(),a.config.formats[l.format]?l=a.deepMerge({label:l.icon?"":l.format,format:l.format,mimeType:a.config.formats[l.format].mimeType,extension:a.config.formats[l.format].extension,capture:a.config.formats[l.format].capture,action:a.config.action,fileName:a.config.fileName},l):l.label||(l.label=l.label?l.label:a.i18l("menu.label."+u)),(-1==["CSV","JSON","XLSX"].indexOf(l.format)||-1==["map","gauge"].indexOf(a.setup.chart.type))&&(a.setup.hasBlob||"UNDEFINED"==l.format||!l.mimeType||"image"==l.mimeType.split("/")[0]||"text/plain"==l.mimeType)){if("draw"==l.action)a.config.fabric.drawing.enabled?(l.menu=l.menu?l.menu:a.config.fabric.drawing.menu,l.click=function(e){return function(){this.capture(e,function(){this.createMenu(e.menu)})}}(l)):l.menu=[];else if(!l.populated&&l.action&&-1!=l.action.indexOf("draw.")){var g=l.action.split(".")[1],h=l[g]||a.config.fabric.drawing[g]||[];for(l.menu=[],l.populated=!0,o=0;o<h.length;o++){var m={label:h[o]};if("shapes"==g){var b=-1==h[o].indexOf("//"),w=(b?a.config.path+"shapes/":"")+h[o];m.action="add",m.url=w,m.icon=w,m.ignore=b,m["class"]="export-drawing-shape"}else"colors"==g?(m.style="background-color: "+h[o],m.action="change",m.color=h[o],m["class"]="export-drawing-color"):"widths"==g?(m.action="change",m.width=h[o],m.label=document.createElement("span"),m.label.style.width=a.numberToPx(h[o]),m.label.style.height=a.numberToPx(h[o]),m["class"]="export-drawing-width"):"opacities"==g?(m.style="opacity: "+h[o],m.action="change",m.opacity=h[o],m.label=100*h[o]+"%",m["class"]="export-drawing-opacity"):"modes"==g&&(m.label=a.i18l("menu.label.draw.modes."+h[o]),m.click=function(e){return function(){a.drawing.mode=e}}(h[o]),m["class"]="export-drawing-mode");l.menu.push(m)}}else l.click||l.menu||l.items||(a.drawing.handler[u]instanceof Function?(l.action=u,l.click=function(e){return function(){this.drawing.handler[e.action](e)}}(l)):a.drawing.enabled?l.click=function(e){return function(){this.config.drawing.autoClose&&this.drawing.handler.done(),this["to"+e.format](e,function(t){"download"==e.action&&this.download(t,e.mimeType,[e.fileName,e.extension].join("."))})}}(l):"UNDEFINED"!=l.format&&(l.click=function(e){return function(){if(e.capture||"print"==e.action||"PRINT"==e.format)this.capture(e,function(){this.config.drawing.autoClose&&this.drawing.handler.done(),this["to"+e.format](e,function(t){"download"==e.action&&this.download(t,e.mimeType,[e.fileName,e.extension].join("."))})});else{if(!this["to"+e.format])throw new Error("Invalid format. Could not determine output type.");this["to"+e.format](e,function(t){this.download(t,e.mimeType,[e.fileName,e.extension].join("."))})}}}(l)));(void 0===l.menu||l.menu.length)&&(c.setAttribute("href","#"),c.addEventListener("click",function(e,t){return function(i){i.preventDefault();var r=[i,t];return("draw"==t.action||"PRINT"==t.format||"UNDEFINED"!=t.format&&t.capture)&&!a.drawing.enabled&&(t.delay=t.delay?t.delay:a.config.delay,t.delay)?void a.delay(t,e):void e.apply(a,r)}}(l.click||function(e){e.preventDefault()},l)),d.appendChild(c),a.isElement(l.label)?p.appendChild(l.label):p.innerHTML=l.label,l["class"]&&(d.className=l["class"]),l.style&&d.setAttribute("style",l.style),l.icon&&(f.setAttribute("src",(l.ignore||-1!=l.icon.slice(0,10).indexOf("//")?"":e.pathToImages)+l.icon),c.appendChild(f)),l.label&&c.appendChild(p),l.title&&c.setAttribute("title",l.title),a.config.menuReviver&&(d=a.config.menuReviver.apply(a,[l,d])),l.elements={li:d,a:c,img:f,span:p},(l.menu||l.items)&&"draw"!=l.action?r(l.menu||l.items,d).childNodes.length&&s.appendChild(d):s.appendChild(d))}}return s.childNodes.length&&i.appendChild(s),s}return i||("string"==typeof a.config.divId?a.config.divId=i=document.getElementById(a.config.divId):i=a.isElement(a.config.divId)?a.config.divId:a.setup.chart.containerDiv),a.isElement(a.setup.menu)?a.setup.menu.innerHTML="":a.setup.menu=document.createElement("div"),a.setup.menu.setAttribute("class",a.setup.chart.classNamePrefix+"-export-menu "+a.setup.chart.classNamePrefix+"-export-menu-"+a.config.position+" amExportButton"),a.config.menuWalker&&(r=a.config.menuWalker),r.apply(this,[t,a.setup.menu]),a.setup.menu.childNodes.length&&i.appendChild(a.setup.menu),a.setup.menu},delay:function(e,t){var i,r,n=a.deepMerge({delay:3,precision:2},e||{}),o=Number(new Date),s=a.createMenu([{label:a.i18l("capturing.delayed.menu.label").replace("{{duration}}",AmCharts.toFixed(n.delay,n.precision)),title:a.i18l("capturing.delayed.menu.title"),"class":"export-delayed-capturing",click:function(){clearTimeout(i),clearTimeout(r),a.createMenu(a.config.menu)}}]),l=s.getElementsByTagName("a")[0];i=setInterval(function(){var e=n.delay-(Number(new Date)-o)/1e3;0>=e?(clearTimeout(i),"draw"!=n.action&&a.createMenu(a.config.menu)):l&&(l.innerHTML=a.i18l("capturing.delayed.menu.label").replace("{{duration}}",AmCharts.toFixed(e,2)))},AmCharts.updateRate),r=setTimeout(function(){t.apply(a,arguments)},1e3*n.delay)},migrateSetup:function(e){function t(e){var i;for(i in e){var r=e[i];"export"==i.slice(0,6)&&r?a.menu.push(i.slice(6)):"userCFG"==i?t(r):"menuItems"==i?a.menu=r:"libs"==i?a.libs=r:"string"==typeof i&&(a[i]=r)}}var a={enabled:!0,migrated:!0,libs:{autoLoad:!0},menu:[]};return t(e),a},clear:function(){a.setup=void 0,a.docListener&&document.removeEventListener("keydown",a.docListener);var e=a.listenersToRemove;if(e)for(var t=0;t<e.length;t++){var i=e[t];i.node.removeEventListener(i.event,i.method)}a.listenersToRemove=[]},loadListeners:function(){function e(e){e&&(e.set({top:e.top+10,left:e.left+10}),a.setup.fabric.add(e))}a.config.keyListener&&"attached"!=a.config.keyListener&&(a.docListener=function(t){var i=a.drawing.buffer.target;8!=t.keyCode&&46!=t.keyCode||!i?27==t.keyCode&&a.drawing.enabled?(t.preventDefault(),a.drawing.handler.done()):67==t.keyCode&&(t.metaKey||t.ctrlKey)&&i?a.drawing.buffer.copy=i:88==t.keyCode&&(t.metaKey||t.ctrlKey)&&i?(a.drawing.buffer.copy=i,a.setup.fabric.remove(i)):86==t.keyCode&&(t.metaKey||t.ctrlKey)?a.drawing.buffer.copy&&e(a.drawing.buffer.copy.clone(e)):90==t.keyCode&&(t.metaKey||t.ctrlKey)&&(t.preventDefault(),t.shiftKey?a.drawing.handler.redo():a.drawing.handler.undo()):(t.preventDefault(),a.setup.fabric.remove(i))},a.config.keyListener="attached",document.addEventListener("keydown",a.docListener)),a.config.fileListener&&(a.setup.chart.containerDiv.addEventListener("dragover",a.handleDropbox),a.setup.chart.containerDiv.addEventListener("dragleave",a.handleDropbox),a.setup.chart.containerDiv.addEventListener("drop",a.handleDropbox))},init:function(){clearTimeout(a.timer),a.timer=setInterval(function(){a.setup.chart.containerDiv&&(clearTimeout(a.timer),a.config.enabled&&(a.setup.chart.AmExport=a,a.config.overflow&&(a.setup.chart.div.style.overflow="visible"),a.loadListeners(),a.createMenu(a.config.menu),a.handleReady(a.config.onReady)))},AmCharts.updateRate)},construct:function(){a.drawing.handler.cancel=a.drawing.handler.done;try{a.setup.hasBlob=!!new Blob}catch(e){}window.safari=window.safari?window.safari:{},a.defaults.fabric.drawing.fontSize=a.setup.chart.fontSize||11,a.config.drawing=a.deepMerge(a.defaults.fabric.drawing,a.config.drawing||{},!0),a.config.border&&(a.config.border=a.deepMerge(a.defaults.fabric.border,a.config.border||{},!0)),a.deepMerge(a.defaults.fabric,a.config,!0),a.deepMerge(a.defaults.fabric,a.config.fabric||{},!0),a.deepMerge(a.defaults.pdfMake,a.config,!0),a.deepMerge(a.defaults.pdfMake,a.config.pdfMake||{},!0),a.deepMerge(a.libs,a.config.libs||{},!0),a.config.drawing=a.defaults.fabric.drawing,a.config.fabric=a.defaults.fabric,a.config.pdfMake=a.defaults.pdfMake,a.config=a.deepMerge(a.defaults,a.config,!0),a.config.fabric.drawing.enabled&&void 0===a.config.fabric.drawing.menu&&(a.config.fabric.drawing.menu=[],a.deepMerge(a.config.fabric.drawing.menu,[{"class":"export-drawing",menu:[{label:a.i18l("menu.label.draw.add"),menu:[{label:a.i18l("menu.label.draw.shapes"),action:"draw.shapes"},{label:a.i18l("menu.label.draw.text"),action:"text"}]},{label:a.i18l("menu.label.draw.change"),menu:[{label:a.i18l("menu.label.draw.modes"),action:"draw.modes"},{label:a.i18l("menu.label.draw.colors"),action:"draw.colors"},{label:a.i18l("menu.label.draw.widths"),action:"draw.widths"},{label:a.i18l("menu.label.draw.opacities"),action:"draw.opacities"},"UNDO","REDO"]},{label:a.i18l("menu.label.save.image"),menu:["PNG","JPG","SVG","PDF"]},"PRINT","CANCEL"]}])),void 0===a.config.menu&&(a.config.menu=[],a.deepMerge(a.config,{menu:[{"class":"export-main",menu:[{label:a.i18l("menu.label.save.image"),menu:["PNG","JPG","SVG","PDF"]},{label:a.i18l("menu.label.save.data"),menu:["CSV","XLSX","JSON"]},{label:a.i18l("menu.label.draw"),action:"draw",menu:a.config.fabric.drawing.menu},{format:"PRINT",label:a.i18l("menu.label.print")}]}]})),a.libs.path||(a.libs.path=a.config.path+"libs/"),a.isSupported()&&(a.loadDependencies(a.libs.resources,a.libs.reload),a.setup.chart.addClassNames=!0,a.setup.chart[a.name]=a,a.init())}};if(t)a.config=t;else if(a.setup.chart[a.name])a.config=a.setup.chart[a.name];else{if(!a.setup.chart.amExport&&!a.setup.chart.exportConfig)return;a.config=a.migrateSetup(a.setup.chart.amExport||a.setup.chart.exportConfig)}return a.construct(),a.deepMerge(this,a)}}(),AmCharts.addInitHandler(function(e){new AmCharts["export"](e)},["pie","serial","xy","funnel","radar","gauge","stock","map","gantt"]);