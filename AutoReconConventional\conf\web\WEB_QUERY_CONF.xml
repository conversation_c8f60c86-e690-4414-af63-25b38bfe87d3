<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">
<query id="500">
		<name>RECONCILED_PAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT TRA_AMT,TRA_DATE,DEB_CRE_IND ,CHQ_NO,TRA_CUR,MAIN_REV_IND,MATCH_TYPE  
			FROM PAYMENT_ORDER_RECON WITH (NOLOCK) WHERE MATCH_TYPE='AM'OR MATCH_TYPE='MM' ORDER BY TRA_DATE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_CBO_TRX_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
		select TRANSACTION_TYPE, sum(TRANSACTION_AMOUNT) as TRANSACTION_AMOUNT
		from
		ISSUER_ONS_IMAL_CBO_STG WITH (NOLOCK) where CURRENCY_CODE in('512','414','634','682','048','784')
		and SYSTEM_OWNER_BUISINESS_DATE between ? and ?
		group by TRANSACTION_TYPE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="501">
		<name>DEBIT_UNRECONCILED_PAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
	SELECT TRA_DATE,TRA_AMT,DEB_CRE_IND ,CHQ_NO,TRA_CUR,MAIN_REV_IND,MATCH_TYPE,COMMENTS    
	FROM PAYMENT_ORDER_RECON WITH (NOLOCK) WHERE  DEB_CRE_IND='D' AND ( MATCH_TYPE='AU'OR MATCH_TYPE='MU') ORDER BY TRA_DATE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="502">
		<name>CREDIT_UNRECONCILED_PAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
	SELECT TRA_AMT,TRA_DATE,DEB_CRE_IND ,CHQ_NO,TRA_CUR,MAIN_REV_IND,MATCH_TYPE,COMMENTS  
	FROM PAYMENT_ORDER_RECON WITH (NOLOCK) WHERE  DEB_CRE_IND='C' AND ( MATCH_TYPE='AU'OR MATCH_TYPE='MU') ORDER BY TRA_DATE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="504">
		<name>BALANCE_UNRECONCILED_PAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
SELECT 
	   (SELECT  isnull(SUM(TRA_AMT),0.00) AS AMOUNT
	FROM PAYMENT_ORDER_RECON WITH (NOLOCK) WHERE  DEB_CRE_IND='D' AND ( MATCH_TYPE='AU'OR MATCH_TYPE='MU')) - 
	(SELECT isnull(SUM(TRA_AMT),0.00) AS AMOUNT
	FROM PAYMENT_ORDER_RECON WHERE  DEB_CRE_IND='C' AND ( MATCH_TYPE='AU'OR MATCH_TYPE='MU') )

	AS DIFF
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="503">
		<name>ORPHAN_PAYMENTORDER_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
				SELECT GL_CODE,CIF_SUB_NO,OP_NO,TRANS_DATE,DESCRIPTION,AMOUNT,
			CURRENCY_NAME FROM IMLO_PAYMENTS_STG ORDER BY TRANS_DATE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
			<query id="503">
		<name>UNRECONCILED_TRADEMARGIN_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

SELECT RECON_SIDE,BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NUM,TRA_AMT,MARGIN_AMOUNT,TRA_STATUS,SOURCE_TARGET
MATCH_TYPE ,COMMENTS FROM TRADE_MARGIN_RECON WHERE  MATCH_TYPE='AU' OR MATCH_TYPE='MU'  
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="110">
		<name>LOAD_PASSWORD_POLICY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM PASSWORD_POLICY WITH (NOLOCk) WHERE VERSION=(SELECT
			MAX(VERSION) FROM
			PASSWORD_POLICY  WITH()where  ACTIVE_INDEX='Y' and  use_policy='Y') AND ACTIVE_INDEX='Y'
			 and  use_policy='Y'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="121">
		<name>LOCK_USER_ACCOUNT_QUERY</name>
		<queryType>Update</queryType>
		<queryString>UPDATE users set updated_on= ? ,account_status=? where
			user_id=?
		</queryString>
		<queryParam>updated_on@TIMESTAMP,account_status@VARCHAR,user_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="121">
		<name>GET_DATE_DIFF_FOR_PASSWORD_EXPIRY_NOTIFICATION</name>
		<queryType>Select</queryType>
		<queryString>select DATEDIFF(DAY,getdate(),pwd_exp_date) as expdate
			from users WITH (NOLOCK) where user_id=?
		</queryString>
		<queryParam>user_id@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="120">
		<name>INSERT_USER_lOG_AUDIT_QRY</name>
		<queryType>Insert</queryType>
		<queryString>

			insert into User_Audit
			(
			user_id,action,date_time,bussiness_area,recon_name,user_role
			)
			values
			(
			?,?,?,?,?,?
			)

		</queryString>
		<queryParam>user_id@VARCHAR,action@VARCHAR,date_time@TIMESTAMP,bussiness_area@VARCHAR,recon_name@VARCHAR,user_role@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>GET_ALL_USERS_EMAILS_QUERY</name>
		<queryType>Select</queryType>
		<queryString>Select email_id,isLdapUser from users WITH (NOLOCK) where email_id=? and
			status='APPROVED' and active_index='Y'
		</queryString>
		<queryParam>email_id@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>CHANGE_USER_PASSWORD_QUERY</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set password=?,updated_on=?,pwd_exp_date = ?
			where user_id=?
		</queryString>
		<queryParam>password@VARCHAR,updated_on@TIMESTAMP,pwd_exp_date@TIMESTAMP,user_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>INSERT_USER_AUDIT_QUERY</name>
		<queryType>INSERT</queryType>
		<queryString>insert into users_audit select * from users where email_id=?</queryString>
		<queryParam>email_id@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>RESET_USER_PASSWORD_QUERY</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set password=?,updated_on=?,pwd_exp_date = ?,account_status = ?,version=version+1
			where email_id=?
		</queryString>
		<queryParam>password@VARCHAR,updated_on@TIMESTAMP,pwd_exp_date@TIMESTAMP,account_status@VARCHAR,email_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="3">
  <name>select_recon_activity_flow</name>
  <queryType>Select</queryType>
  <queryString>select * from recon_activity_flow rec_fl  WITH (NOLOCK)
  where status='APPROVED' and version=(select MAX(version) 
  from recon_activity_flow WITH (NOLOCK) where activity_id=rec_fl.activity_id and status='APPROVED') 
  and activity_data is not null</queryString>
  <queryParam></queryParam>
  <queryParamLiteralValues></queryParamLiteralValues>
 </query>

<query id="3">
		<name>SELCT_PASS_POLICY</name>
		<queryType>Select</queryType>
		<queryString>Select * from PASS_POLICY</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="3">
		<name>SOURCES_NAME_QUERY</name>
		<queryType>Select</queryType>
		<queryString>select * from SOURCES</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="3">
		<name>GET_ALL_MODULES_QUERY</name>
		<queryType>Select</queryType>
		<queryString>select * from MODULES WITH (NOLOCK)</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1">
		<name>LoadAllUsers</name>
		<queryType>Insert</queryType>
		<queryString>select * from users  rl WITH (NOLOCK)
		where status='APPROVED' and version = (select MAX(version) from users WITH (NOLOCK)
		where user_id=rl.user_id and status='APPROVED')</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="2">
		<name>LoadAllRoles</name>
		<queryType>Insert</queryType>
		<queryString>select * from roles rl WITH (NOLOCK)
		where status='APPROVED' and version = (select MAX(version) from roles WITH (NOLOCK)
		where roleid=rl.roleid and status='APPROVED') </queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="3">
		<name>LoadAllDepartments</name>
		<queryType>Insert</queryType>
		<queryString>select * from departments dep WITH (NOLOCK) 
		where status='APPROVED' and version = (select MAX(version) from departments WITH (NOLOCK)
		where dept_id=dep.dept_id and status='APPROVED')</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="4">
		<name>LoadAllPrevieleges</name>
		<queryType>select</queryType>
		<queryString>select * from privileges prv WITH (NOLOCK)
		where status='APPROVED' and version = (select MAX(version) from privileges WITH (NOLOCK)
		where role=prv.role and status='APPROVED')</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="5">
		<name>LoadAllPrevielegeDetails</name>
		<queryType>select</queryType>
		<queryString>select * from privilegedetails where pid=? </queryString>
		<queryParam>pid@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="6">
		<name>GetActivityAudit</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,transaction_amount
			from recon_activity_flow a WITH (NOLOCK) where a.activity_id=?
		</queryString>
		<queryParam>activity_id@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- <query id="7">
		<name>GetActivityByUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and activity_owner=? and recon=? and
			activity_level=(
			select max(activity_level) from recon_activity_flow b
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>activity_owner@VARCHAR,recon@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	
	
	<query id="7">
		<name>GetActivityByUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount,
			recent_actor
			from
			recon_activity_flow a WITH (NOLOCK) where active_index='Y' and ? IN
			(  SELECT * FROM (
			select VALUE from recon_activity_flow CROSS APPLY STRING_SPLIT(activity_owner,',') X ) S
			)
			and recon=? and 
			activity_level=(
			select max(activity_level) from recon_activity_flow b WITH (NOLOCK)
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>activity_owner@VARCHAR,recon@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	 
	<query id="8">
		<name>GetActivity</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow WITH (NOLOCK) where activity_id = ? and activity_level=?
		</queryString>
		<queryParam>activity_id@BIGINT,activity_level@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="9">
		<name>CreateActivity</name>
		<queryType>INSERT</queryType>
		<queryString>
			insert into recon_activity_flow
			(
			activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,
			active_index,created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			)
			values
			(
			?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,?,?,?,?

			)

		</queryString>
		<queryParam>activity_id@BIGINT,activity_name@VARCHAR,activity_type@VARCHAR,activity_level@INTEGER,
			allowed_approvers@VARCHAR,activity_owner@VARCHAR,recent_actor@VARCHAR,activity_data@LONGVARBINARY,status@VARCHAR,
			active_index@VARCHAR,created_on@TIMESTAMP,recently_updated_on@TIMESTAMP,version@INTEGER,comment@VARCHAR,business_area@VARCHAR,recon@VARCHAR,transaction_amount@DECIMAL
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="10">
		<name>GetActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			<!-- select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where CHARINDEX(?, allowed_approvers)!=0 and
			activity_level=(
			select max(activity_level) from recon_activity_flow b
			where b.activity_id=a.activity_id) -->
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a WITH (NOLOCK) where allowed_approvers=(select user_name from 
			users WITH (NOLOCK)where user_id=?) and
			activity_level=(
			select max(activity_level) from recon_activity_flow b WITH (NOLOCK)
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="11">
		<name>GetRejectedActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			<!-- select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and status like 'Rej%'
			and CHARINDEX(?, allowed_approvers)!=0 and activity_level=(
			select
			max(activity_level) from recon_activity_flow b where
			b.activity_id=a.activity_id) -->
			SELECT sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and status like 'Rej%'
			and allowed_approvers=(select user_name from users where user_id=?) and activity_level=(
			select
			max(activity_level) from recon_activity_flow b where
			b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>GetApprovedActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			<!-- select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and status like
			'Appro%'and CHARINDEX(?, allowed_approvers)!=0 and activity_level=(
			select max(activity_level) from recon_activity_flow b where
			b.activity_id=a.activity_id) -->
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and status like
			'Appro%'and allowed_approvers=(select user_name from users where user_id=?) and activity_level=(
			select max(activity_level) from recon_activity_flow b where
			b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="13">
		<name>GetPendigActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			<!-- select
		   sno,activity_id,activity_name,activity_type,activity_level,
		   allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
		   created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
		   from
		   recon_activity_flow a where active_index='Y' and status like 'Pend%'
		   and(( CHARINDEX(?, allowed_approvers)!=0) )  and activity_level=(
		   select
		   max(activity_level) from recon_activity_flow b where
		   b.activity_id=a.activity_id) -->
		   <!-- select
		   sno,activity_id,activity_name,activity_type,activity_level,
		   allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
		   created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
		   from
		   recon_activity_flow a where active_index='Y' and status like 'Pend%'
		   and(( allowed_approvers=(select user_name from users where user_id=?)) )  and activity_level=(
		   select
		   max(activity_level) from recon_activity_flow b where
		   b.activity_id=a.activity_id) -->
		   select
		   sno,activity_id,activity_name,activity_type,activity_level,
		   allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
		   created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount from
		   recon_activity_flow a WITH (NOLOCK) where active_index='Y' and status like 'Pend%'
		   and(( allowed_approvers=(select user_name from users WITH (NOLOCK) where user_id=?)) ) and recon=?
		   and activity_level=(select max(activity_level) from recon_activity_flow b WITH (NOLOCK)
		    where
		   b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR,recon@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="13">
		<name>GetPendigCaseManagementForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
		   case_id,activity_id,activity_name,activity_type,activity_level,
		   allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
		   created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
		   from
		   RECON_CASE_MANAGEMENT a WITH (NOLOCK) where active_index='Y' and status like 'Pend%'
		   and(( CHARINDEX(?, allowed_approvers)!=0) )  and activity_level=(
		   select
		   max(activity_level) from RECON_CASE_MANAGEMENT b WITH (NOLOCK) where
		   b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="14">
		<name>GetActivityAudit</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from recon_activity_flow a WITH (NOLOCK) where a.activity_id=?
		</queryString>
		<queryParam>activity_id@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="15">
        <name>LoadAllFeatures</name>
        <queryType>select</queryType>
        <queryString>select * from features WITH (NOLOCK)</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="16">
        <name>ALL_RECON_NAMES_QRY</name>
        <queryType>select</queryType>
        <queryString>select *  from RECON WITH (NOLOCK)</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="17">
        <name>GET_ALL_GEOGRAPHY_NAME_QRY</name>
        <queryType>select</queryType>
        <queryString>select * from GEOGRAPHY WITH (NOLOCK)</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="18">
        <name>GET_ALL_BUSINESS_AREAS_FOR_SPECIFIC_GEOGRAPHY</name>
        <queryType>select</queryType>
        <queryString>select * from business_area WITH (NOLOCK) where 
        geography_sno=(select sno from GEOGRAPHY WITH (NOLOCK) where geography_name=? )</queryString>
        <queryParam>geography_name@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="19">
        <name>GET_ALL_RECONS_FOR_SPECIFIC_BUSINESS_AREA</name>
        <queryType>select</queryType>
        <queryString>
		
			select * from RECON WITH (NOLOCK) where business_area_sno=(
				select SNO from Business_Area WITH (NOLOCK) where business_area=? and geography_sno=(
					select sno from GEOGRAPHY WITH (NOLOCK) where geography_name=?
				) 
			)
		
		</queryString>
        <queryParam>business_area@VARCHAR,geography_name@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="20">
		<name>SetActivityDataReconActivity</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE recon_activity_flow set activity_data = ? where activity_id = ?</queryString>
		<queryParam>activity_data@LONGVARBINARY,activity_id@BIGINT</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="2">
		<name>GET_MAX_ROLE_ID</name>
		<queryType>Select</queryType>
		<queryString>SELECT MAX(roleid) as maxId FROM ROLES WITH (NOLOCK)</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="2">
		<name>GET_MAX_DEP_ID</name>
		<queryType>Select</queryType>
		<queryString>SELECT MAX(id) as maxId FROM departments WITH (NOLOCK)</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues> 
	</query>
	<query id="2">
		<name>GET_MAX_PRIVILAGE_ID</name>
		<queryType>Select</queryType>
		<queryString>SELECT MAX(privilegeId) as maxId FROM privileges WITH (NOLOCK)</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues> 
	</query>
	<query id="2">
        <name>getMaxVersionFromPrevileges</name>
        <queryType>SELECT</queryType>
        <queryString>SELECT  MAX(version) as max FROM privileges WITH (NOLOCK)
        where status = 'APPROVED' and role=?</queryString>
        <queryParam>role@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
        <name>savePrevielege</name>
        <queryType>Insert</queryType>
        <queryString>INSERT INTO privileges(privilegeId,sno,role,privilge_details,status,version) VALUES (?,?, ?,?,?,?)</queryString>
        <queryParam>privilegeId@INTEGER,sno@INTEGER,role@VARCHAR,privilge_details@LONGVARBINARY,status@VARCHAR,version@INTEGER</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
     <query id="12">
		<name>privilegeStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE privileges set status=?,active_index=? where privilegeId=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,privilegeId@VARCHAR</queryParam>
		 <queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="2">
        <name>saveRoles</name>
        <queryType>Insert</queryType>
        <queryString>INSERT INTO roles(role, discription,roleid ,status,version,active_index,created_on,updated_on) VALUES (?, ?,?,?,?,?,?,?)</queryString>
        <queryParam>role@VARCHAR,discription@VARCHAR,roleid@INTEGER,status@VARCHAR,version@INTEGER,active_index@VARCHAR,created_on@TIMESTAMP,updated_on@TIMESTAMP</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
        <name>updateRoles</name>
        <queryType>UPDATE</queryType>
        <queryString>UPDATE roles set role=?, discription=?, status = ?, updated_on=? where roleid = ? and version=?</queryString>
        <queryParam>role@VARCHAR,discription@VARCHAR,status@VARCHAR,updated_on@TIMESTAMP,roleid@INTEGER,version@INTEGER</queryParam>
         <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
     <query id="12">
		<name>RoleStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE roles set status=?,active_index=?, updated_on=? where roleid=? and version=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,updated_on@TIMESTAMP,roleid@VARCHAR,version@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
     
    <query id="2">
        <name>saveDepartments</name>
        <queryType>Insert</queryType>
        <queryString>INSERT INTO departments(dept_name,category,location,dept_id,status,active_index,version,created_on,updated_on) VALUES (?,?,?,?,?,?,?,?,?)</queryString>
        <queryParam>dept_name@VARCHAR,category@VARCHAR,location@VARCHAR,dept_id@VARCHAR,status@VARCHAR,active_index@VARCHAR,version@INTEGER,created_on@TIMESTAMP,updated_on@TIMESTAMP</queryParam>
       <queryParamLiteralValues></queryParamLiteralValues>
    </query>
 
	<query id="2"> 
        <name>updateDeparments</name>
        <queryType>Insert</queryType>
        <queryString>UPDATE departments SET dept_name=?,category=?,location=?,status = ?, updated_on=? WHERE dept_id=? and version=?</queryString>
        <queryParam>dept_name@VARCHAR,category@VARCHAR,location@VARCHAR,status@VARCHAR,updated_on@TIMESTAMP,dept_id@VARCHAR,version@INTEGER</queryParam>
       <queryParamLiteralValues></queryParamLiteralValues>
    </query>
     <query id="2">
        <name>updateDepartMent</name>
        <queryType>UPDATE</queryType>
        <queryString>UPDATE departments set status = ?, updated_on=? where dept_id = ? and version=?</queryString>
        <queryParam>status@VARCHAR,updated_on@TIMESTAMP,dept_id@VARCHAR,version@INTEGER</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
		<name>DepartmentStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE departments set status=?,active_index=?, updated_on=? where dept_id=? and version=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,updated_on@TIMESTAMP,dept_id@VARCHAR,version@INTEGER</queryParam>
	  <queryParamLiteralValues></queryParamLiteralValues>
	</query>
    
     <query id="2">
		<name>saveUsers</name>
		<queryType>Insert</queryType>
		<queryString>INSERT INTO
			users(user_id,user_name,email_id,phon_number,dept_name,reporting,system_role,approval_role,branch_location,approval_department,status,version,password,updated_on,isLdapUser,account_status,pwd_exp_date,active_index,created_on)
			VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>user_id@VARCHAR,user_name@VARCHAR,email_id@VARCHAR,phon_number@VARCHAR,dept_name@VARCHAR,reporting@VARCHAR,system_role@VARCHAR,approval_role@VARCHAR,branch_location@VARCHAR,approval_department@VARCHAR,status@VARCHAR,version@INTEGER,password@VARCHAR,updated_on@TIMESTAMP,isLdapUser@VARCHAR,account_status@VARCHAR,pwd_exp_date@TIMESTAMP,active_index@VARCHAR,created_on@TIMESTAMP
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="2">
		<name>updateUsers</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set
			user_name=?,phon_number=?,email_id=?,dept_name=?,reporting=?,system_role=?,approval_role=?,branch_location=?,status
			= ?,updated_on=?,isLdapUser=?,account_status=? where
			user_id = ? and
			version=?
		</queryString>
		<queryParam>user_name@VARCHAR,phon_number@VARCHAR,email_id@VARCHAR,dept_name@VARCHAR,reporting@VARCHAR,system_role@VARCHAR,approval_role@VARCHAR,branch_location@VARCHAR,status@VARCHAR,updated_on@TIMESTAMP,isLdapUser@VARCHAR,account_status@VARCHAR,user_id@VARCHAR,version@INTEGER
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
    
     <query id="12">
		<name>UserStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set status=?,active_index=?,updated_on=? where user_id=? and version=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,updated_on@TIMESTAMP,user_id@VARCHAR,version@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	  <query id="2">
        <name>loadPrevielegesDetailsRoleWise</name>
        <queryType>Select</queryType>
        <queryString>select privilge_details from privileges prv  WITH (NOLOCK)
        where role=? and status='APPROVED' and version = (select MAX(version) from privileges WITH (NOLOCK)
        where role=prv.role and status='APPROVED') </queryString>
        <queryParam>role@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
     <query id="20">
		<name>UpdateReconActivity</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE recon_activity_flow set active_index = ? where activity_id = ?</queryString>
		<queryParam>active_index@VARCHAR,activity_id@BIGINT</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
 
	<query id="21">
		<name>ATM_RECON_ATM_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'ATM_IRIS' AS RECON_SIDE,SID,RETR_REF_NO, AMT_SETT AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,TIME_LOC_TRAN,PROC_CODE_FIRST_2 as PROC_CODE,
					C_ACCEP_TERM_ID,DEB_CRE_INDI AS DEB_CRE_IND,RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,
					AUTHORIZER,ACQUIRING_CHANNEL_ID,PAN,time_in_MILLIS,
					'ATM_IRIS_STG' as  SOURCE_TARGET,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
				FROM 	ATM_IRIS_STG  WITH (NOLOCK)
				WHERE 	 SID=?
				
				
				
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	
	
	
	
	<query id="22">
		<name>ATM_RECON_ATM_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 	'ATM_GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,TRA_TIME as TIME_LOC_TRAN,PROC_CODE,
				 C_ACCEP_TERM_ID,DEB_CRE_IND,'' AS RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,
						'' AS PAN,time_in_MILLIS,'ATM_GL_1002_STG' as  SOURCE_TARGET,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				FROM 	ATM_GL_1002_STG 
				WHERE 	 SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	//CENTRAL BANK
	
	
		
	<query id="211">
		<name>CBO_FT_RECON_RTGS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'RTGS' AS RECON_SIDE,SID, abs(Deponent_Name) AS TRA_AMT,VALUE_DATE AS TRA_DATE,
					'' AS DEB_CRE_IND,'' AS TRA_CUR,
					 REF_NUM,TRN as DESCRIPTION,
					WORKFLOW_STATUS,
					'RTGS_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
				FROM 	RTGS_STG WITH (NOLOCK) WHERE SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
		<query id="212">
		<name>CBO_FT_RECON_IMAL_CBO_FT</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 	'IMAL_CBO_FT' AS RECON_SIDE,SID,ABS(OL_AMOUNT) AS TRA_AMT,OD_TRANS_DATE AS TRA_DATE,
				CASE WHEN OL_AMOUNT &lt;0 THEN 'D' ELSE 'C' END  AS DEB_CRE_IND,OS_CURRENCY_NAME AS TRA_CUR,
						REF_NUM ,OS_DESCRIPTION AS DESCRIPTION,WORKFLOW_STATUS,'IMAL_CBO_FT_STG' as  SOURCE_TARGET,
						MAIN_REV_IND,RECON_ID,VERSION
				FROM 	IMAL_CBO_FT_STG  WITH (NOLOCK) WHERE SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
		<query id="213">
		<name>CBO_FT_RECON_ACH</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'ACH' AS RECON_SIDE, SID, 
					
					abs(INSTRUCTIONAMOUNT) AS TRA_AMT ,
					VALUE_DATE AS TRA_DATE,
					Debit_Credit AS DEB_CRE_IND,'' AS TRA_CUR,
					 REF_NUM,Batch_Reference as DESCRIPTION,
					WORKFLOW_STATUS,
					'ACH_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
				FROM 	ACH_STG  WHERE SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//UTILITY PAYMENTS
	
	
	
		<query id="214">
		<name>UTILITY_PAYMENTS_RECON_BILL_IMAL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'BILL_IMAL' AS RECON_SIDE,SID,
TRA_DATE,TRA_TIME,ACCOUNT_NUMBER,CHANNEL,BILL_REF AS TRA_REF_NO,BENEFICIARY_NUM AS REF_NUM,TRA_AMT,TRA_STATUS,
TRA_CATEGORY ,'' AS CHANNEL_STATUS

,WORKFLOW_STATUS,Business_Area AS BUISINESS_AREA,'BILL_IMAL_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
FROM BILL_IMAL_STG WHERE SID=? AND TRA_STATUS='APPROVED'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="215">
		<name>UTILITY_PAYMENTS_RECON_UBP</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 
'UBP' AS RECON_SIDE,SID,
TRA_DATE,TRA_TIME,ACCOUNT_NUM AS ACCOUNT_NUMBER,CHANNEL,TRA_ID AS TRA_REF_NO,CONSUMER_NUM AS REF_NUM,TRA_AMT,BANK_STATUS AS  TRA_STATUS,

CONNECTION_TYPE AS  TRA_CATEGORY ,CHANNEL_STATUS
,WORKFLOW_STATUS,Business_Area AS BUISINESS_AREA,'UBP_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
FROM UBP_STG WHERE SID=? AND CHANNEL_MSG='Success'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//MARGIN RECON 
	
			<query id="216">
		<name>TRADE_MARGIN_RECON_TM_GL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'TM_GL' AS RECON_SIDE,SID, BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NUM,
			SHORT_NAME_ENG,abs(CV_AVIL_BAL) as TRA_AMT,FC_AVL_BAL,0.0 AS MARGIN_AMOUNT,'' AS TRA_STATUS,
			'TM_GL_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,NULL AS TRA_DATE
			 from TM_GL_STG
			WHERE SID =? AND GL_CODE IN ('202601','202603')
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="217">
		<name>TRADE_MARGIN_RECON_LG</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'LG' AS RECON_SIDE,SID, 900 AS BRANCH_CODE,512 AS CUR_CODE,202603 AS GL_CODE,CIF_NUM,
			APPLICANT_NAME AS SHORT_NAME_ENG,AMOUNT AS TRA_AMT,AMENDED_FC_AMOUNT AS FC_AVL_BAL,
			MARGIN_AMOUNT,TRA_STATUS
			,'LG_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS
			 FROM LG_STG WHERE SID=? AND  MARGIN_AMOUNT &gt;0.0 AND TRA_STATUS='CLOSED'
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="218">
		<name>TRADE_MARGIN_RECON_LC</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	select 'LC' AS RECON_SIDE,SID, BRANCH AS BRANCH_CODE,TFSLC_LC_CURRENCY AS CUR_CODE,202601 AS GL_CODE,CIF_NUM,
			CIF_SHORT_NAME_ENG AS SHORT_NAME_ENG,MARGIN_AMT_CV AS TRA_AMT,AMENDED_FC_AMOUNT AS FC_AVL_BAL,
			MARGIN_AMOUNT,TRA_STATUS
			,'LC_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,EXPIRY_DATE AS TRA_DATE
			 FROM LC_stg WHERE MARGIN_AMT_CV &gt;0.0 AND TRA_STATUS='CLOSED' and SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	
	
	
	//MASTER CARD DEBIT RECON BY SHIVAM
	
	
	<query id="216">
		<name>MATM_RECON_IPM</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'IPM' AS RECON_SIDE, SID,
SUBSTRING(RMR_PAN,1,6)+'******'+SUBSTRING(RMR_PAN,len(RMR_PAN)-3,4) as PAN,'' AS TRA_TYPE,rmr_comp_trans_amt AS TRA_AMT,rmr_trace_numb AS STAN,RMR_TERM_ID AS TERM_ID,
rmr_tran_date AS TRA_DATE,rmr_retref_num AS RET_REF_NO,rmr_curr_code AS TRA_CUR,RMR_BILL_CURR AS LOCAL_CUR,
CAST (rmr_comp_code AS VARCHAR(10)) AS RESP_CODE,rmr_msg_rsncode AS TRA_CODE,WORKFLOW_STATUS,
					'IPM_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
FROM IPM_STG WHERE SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="217">
		<name>MATM_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'IRIS' AS RECON_SIDE, SID,
card_num as PAN,trx_type as TRA_TYPE,LOCAL_AMOUNT AS TRA_AMT,stan AS STAN,term_id AS TERM_ID,
tra_date AS TRA_DATE,ret_ref_no AS RET_REF_NO,trX_currency AS TRA_CUR,local_currency AS LOCAL_CUR,
STATUS AS RESP_CODE,'' AS TRA_CODE,WORKFLOW_STATUS,	'IRIS_STG' as  SOURCE_TARGET,
MAIN_REV_IND,RECON_ID,VERSION FROM IRIS_STG
WHERE SID=? AND NETWORK='MASTERCARD' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="218">
		<name>MATM_RECON_IMAL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	SELECT 'IMAL' AS RECON_SIDE, SID,
SUBSTRING(card_no,1,6)+'******'+SUBSTRING(card_no,len(card_no)-3,4) as PAN,TRX_DESC AS TRA_TYPE,amount AS TRA_AMT,stan AS STAN,'' AS TERM_ID,
CREATED_DATE AS TRA_DATE,rrn AS RET_REF_NO,'512' AS TRA_CUR,'512' LOCAL_CUR,
status AS RESP_CODE,'' AS TRA_CODE ,
WORKFLOW_STATUS,'IMAL_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
FROM IMAL_STG WHERE SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	
	
	
	//SUSPENS RECON 
	
	
	<query id="219">
		<name>TRADE_SUSPENS_RECON_SUSPENS_GL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
			select 'SUSPENS_GL' AS RECON_SIDE,SID, BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NUM,
			SHORT_NAME_ENG,abs(CV_AVIL_BAL) as TRA_AMT,YTD_CV_BAL,YTD_FC_BAL,
			'SUSPENS_GL_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,'' AS TRA_DATE 
			 from SUSPENS_GL_STG WHERE SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="220">
		<name>TRADE_SUSPENS_RECON_SUSPENS_CSM</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	select 'SUSPENS_CSM' AS RECON_SIDE,SID,  BRANCH_CODE, CUR_CODE, GL_CODE,CIF_NO AS CIF_NUM,
			SHORT_NAME_ENG,ABS(AMOUNT) AS TRA_AMT,YTD_CV_BAL,YTD_FC_BAL
			
			,'SUSPENS_CSM_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS, TRA_DATE
			 FROM SUSPENS_CSM_STG WHERE SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
//DEBIT ATM ONUS  written by shivam 10/06/2017
<query id="21">
		<name>ONUS_ATM_DEBIT_RECON_ATM_GL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'ATM_GL' AS RECON_SIDE,SID, abs(AMOUNT) AS TRA_AMT,TRANSACTION_DATE AS TRA_DATE,
					TRANSACTION_TYPE AS DEB_CRE_IND,CURRENCY_NAME as TRA_CUR,
					Substring(DESCRIPTION,1, 6) +'******'+Substring(DESCRIPTION, Charindex(',', DESCRIPTION)-4, 4) as PAN,
					WORKFLOW_STATUS,Substring(DESCRIPTION, Charindex('ref', DESCRIPTION)+4, 6) as STAN,
					'ATM_GL_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
				FROM 	ATM_GL_STG    
				WHERE
						  SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="22">
		<name>ONUS_ATM_DEBIT_RECON_EJ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 	'EJ' AS RECON_SIDE,SID,AMOUNT AS TRA_AMT,TRA_DATE,
				TXN_TYPE AS DEB_CRE_IND,CURRENCY,
						PAN AS PAN,WORKFLOW_STATUS,STAN,'EJ_STG' as  SOURCE_TARGET,
						MAIN_REV_IND,RECON_ID,VERSION
				FROM 	EJ_STG 
				WHERE 	 SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
////  ISLAMIC RECON 	
<query id="24">
		<name>FINANCE_ONS_RECON_FIN_ONS_CBO_STG</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'CBO' AS RECON_SIDE,SID, VALUE_DATE AS  TRA_DATE,AMOUNT AS TRA_AMT ,TRAN_REF_NUM AS REF_NUM,
					CURRENCY AS TRA_CUR,DRCR AS DEB_CRE_IND,'FIN_ONS_CBO_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
					BUSINESS_AREA,PAN_NUMBER as PAN_CARD,ACCOUNT as ACCOUNT_NUMBER FROM FIN_ONS_CBO_STG
                    WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>	
<query id="24">
		<name>FINANCE_ONS_RECON_CBS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'CBS' AS RECON_SIDE,SID, VALUE_DATE AS TRA_DATE,AMOUNT AS TRA_AMT ,TRAN_REF_NUM as REF_NUM 
					,CURRENCY AS TRA_CUR ,DRCR AS DEB_CRE_IND,'FIN_ONS_CBS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
					BUSINESS_AREA,PAN_CARD_NUMBER as PAN_CARD,ACNT as ACCOUNT_NUMBER FROM FIN_ONS_CBS_STG
                    WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>



//PAYMENT ORDER WRITTEN BY SHIVAM 13/06/2017

<query id="23">
		<name>PAYMENT_ORDER_RECON_IMAL_PAYMENTS_DEBITS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'IMAL_PAYMENTS_DEBITS' AS RECON_SIDE,SID, TRANS_DATE AS TRA_DATE,ABS(AMOUNT) AS TRA_AMT , Substring(DESCRIPTION, Charindex('Remittance No  ', DESCRIPTION)+len('Remittance No  ')+4, 8) as CHECK_NO
,CURRENCY_NAME AS TRA_CUR ,'D' AS DEB_CRE_IND,'IMLO_PAYMENTS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA,DESCRIPTION
 FROM IMLO_PAYMENTS_STG WHERE AMOUNT &gt;0
				 and SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>PAYMENT_ORDER_RECON_IMAL_PAYMENTS_CREDIT</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'IMAL_PAYMENTS_CREDIT' AS RECON_SIDE,SID, TRANS_DATE AS  TRA_DATE,AMOUNT AS TRA_AMT ,CHQ_NO ,
CURRENCY_NAME AS TRA_CUR,'C' AS DEB_CRE_IND,'IMLO_PAYMENTS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA,DESCRIPTION
 FROM IMLO_PAYMENTS_STG
WHERE AMOUNT &lt;0
				 and SID=?
				 --Substring(DESCRIPTION, Charindex('Cheque No:', DESCRIPTION)+len('Cheque No:')+1, 8) CHECK_NO,
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	
<query id="24">
		<name>ECC_RECON_CBO_IMAL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	
			select 
		'CBO_IMAL' AS RECON_SIDE,SID,
		CHEQUE_NO AS CHEQUE_NO ,OD_TRANS_DATE AS  TRA_DATE,ABS(OL_AMOUNT) AS TRA_AMT_RECON,OL_AMOUNT AS TRA_AMT,CASE WHEN OL_AMOUNT &lt;0 THEN 'C' ELSE 'D' END AS DEB_CRE_IND,
		CASE WHEN CHARINDEX('Outward',OS_DESCRIPTION)&gt;0 THEN 'OUTWARD'
		WHEN CHARINDEX('Inward',OS_DESCRIPTION)&gt;0 THEN 'INWARD'
		WHEN CHARINDEX('Void ',OS_DESCRIPTION)&gt;0 THEN 'VOID'
		ELSE NULL END AS TRX_TYPE,OD_TRANS_DATE AS SESSION_DATE,'' AS PAYING_BANK,'' REASON,OS_TRX_STATUS
		,WORKFLOW_STATUS,Business_Area AS BUISINESS_AREA,'CBO_IMAL_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
		FROM CBO_IMAL_STG where SID = ?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>SUSPENSE_GL_RECON_SUSP_GL_CREDIT</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	
			SELECT 'SUSP_GL_CREDIT' AS RECON_SIDE,SID, TRANS_DATE AS  TRA_DATE,AMOUNT AS TRA_AMT,ABS(AMOUNT) AS TRA_AMT_RECON ,TRADE_NO 
			,BRANCH_CODE,OP_NO,CIF_SUB_NO,
			CURRENCY_CODE AS TRA_CUR,'C' AS DEB_CRE_IND,'TRADE_FINANCE_SUMMARY_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
			FROM TRADE_FINANCE_SUMMARY_STG WHERE AMOUNT&lt;0  and SID = ?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	//RETAIL ASSET WRITTEN BY SHIVAM
	
	<query id="23">
		<name>RETAIL_ASSET_RECON_PO</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'PO' AS RECON_SIDE,SID, abs(OL_AMOUNT) AS TRA_AMT,OD_TRANS_DATE AS TRA_DATE,
					case when OL_AMOUNT &lt;0 then 'C' ELSE 'D' END  AS DEB_CRE_IND,OS_CURRENCY_NAME as TRA_CUR,
					FACILITY_NUMBER,BRANCH_CODE,FACILITY_NUMBER,
					WORKFLOW_STATUS,Business_Area AS BUISINESS_AREA,
					'RETAIL_ASSET_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,
					OL_BRANCH_CODE,OL_GL_CODE,OL_CIF_NO,OL_CURRENCY_CODE,OL_SL_NO
				FROM 	RETAIL_ASSET_STG 
				WHERE SID=?  --CHARINDEX('TRF PO',OS_DESCRIPTION) &gt;=1  and SID=?
				
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>RETAIL_ASSET_RECON_TRADE</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'TRADE' AS RECON_SIDE,SID, abs(OL_AMOUNT) AS TRA_AMT,OD_TRANS_DATE AS TRA_DATE,
					case when OL_AMOUNT &lt;0 then 'D' ELSE 'C' END  AS DEB_CRE_IND,OS_CURRENCY_NAME as TRA_CUR,
					FACILITY_NUMBER,BRANCH_CODE,FACILITY_NUMBER,
					WORKFLOW_STATUS,Business_Area AS BUISINESS_AREA,
					'RETAIL_ASSET_STG' as  SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,
					OL_BRANCH_CODE,OL_GL_CODE,OL_CIF_NO,OL_CURRENCY_CODE,OL_SL_NO
				FROM 	RETAIL_ASSET_STG 
				WHERE CHARINDEX('TRF PO',OS_DESCRIPTION)=0
				 AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//ONS OMNET RECON
	
	
		<query id="34">
		<name>ISSUER_ONS_IMAL_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'IRIS' AS RECON_SIDE,SID, RET_REF_NO,TRA_DATE AS TRA_DATE,TRX_AMOUNT AS TRA_AMT, 
		'IRIS_STG' AS SOURCE_TARGET,AUTHORIZER,ACQ_COUNTRY AS ACQUIRING_CHANNEL_ID,
		STAN, PAN,'' AS DEB_CRE_IND,MAIN_REV_IND,
		TRX_CURRENCY AS TRAN_CUR,WORKFLOW_STATUS,'' AS C_ACCEP_NAME_LOC,AMT_LOCAL AS LOCAL_AMT,RECON_ID,VERSION,CARD_NUM
		 FROM IRIS_STG WHERE   SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<query id="35">
		<name>ISSUER_ONS_IMAL_RECON_ONS_IMAL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'ONS_IMAL' AS RECON_SIDE,SID,RET_REF_NUM AS RET_REF_NO,OD_TRANS_DATE as TRA_DATE, abs(OL_AMOUNT) AS TRA_AMT,
'ONS_IMAL_STG' AS SOURCE_TARGET,
		''  AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID, STAN,
		 PAN,CASE when OL_AMOUNT &lt;0 then 'D' ELSE 'C' END DEB_CRE_IND,MAIN_REV_IND,OL_CURRENCY_CODE AS TRAN_CUR,
		WORKFLOW_STATUS,'' AS C_ACCEP_NAME_LOC,OL_AMOUNT AS LOCAL_AMT,RECON_ID,VERSION,CARD_NUM
	 FROM ONS_IMAL_STG WHERE   SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="36">
		<name>ISSUER_ONS_IMAL_RECON_CBO</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 'CBO' AS RECON_SIDE,SID,RET_REF_NUM AS RET_REF_NO,SYSTEM_OWNER_BUISINESS_DATE AS TRA_DATE,TRANSACTION_AMOUNT AS TRA_AMT, 
		'CBO_STG' AS SOURCE_TARGET,
		'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,
		'' AS STAN,CARD_NUM AS PAN,tra_amt_sign AS DEB_CRE_IND,
		MAIN_REV_IND,CURRENCY_CODE AS TRAN_CUR,WORKFLOW_STATUS,'' AS C_ACCEP_NAME_LOC,TRANSACTION_AMOUNT AS LOCAL_AMT,RECON_ID,VERSION
		 ,PAN AS CARD_NUM FROM  CBO_STG WHERE  SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//CREDIT_ATM
	
	<query id="23">
		<name>ONUS_ATM_CREDIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		
		
SELECT	'IRIS' AS RECON_SIDE,SID,AMT_TRAN AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,RETR_REF_NO,
TRAN_CUR,C_ACCEP_TERM_ID,SYS_TRACE_AUDIT_NO AS STAN,
PROC_CODE_FIRST_2 AS PROC_CODE,MAIN_REV_IND,BUSINESS_AREA,CHANNEL,TIME_IN_MILLIS,'IRIS_STG' as  SOURCE_TARGET,
	'' as  I000_MSG_TYPE,'' as DEB_CRE_IND,RESP_CODE,'' AS SERNO,MAIN_REV_IND,AMT_TRAN AS LOCAL_AMT
	FROM	IRIS_STG 
	WHERE	AUTHORIZER IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND ACQUIRING_CHANNEL_ID IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND PROC_CODE_FIRST_2=(select TRAN_CODE from MDT_PROCESS_CODES 
	 where TRAN_NAME='Withdrawal' and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
	  AND RESP_CODE IN(select CODE from MDT_RESPONSE_CODES 
	  where NAME in('APPROVED','ERR_ACQ_REVERSAL') 
	  and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='CREDIT CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			)
			and SID=? and status not in ('Suppress')
	</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="24">
		<name>ONUS_ATM_CREDIT_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		
		SELECT 'GL_1472' AS RECON_SIDE,gl1472.SID,gl1472.TRA_AMT,gl1472.TRA_DATE,gl1472.RETR_REF_NO,
gl1472.TRAN_CUR,gl1472.C_ACCEP_TERM_ID,STAN,
gl1472.PROC_CODE,gl1472.MAIN_REV_IND,gl1472.BUSINESS_AREA,gl1472.CHANNEL,
			gl1472.TIME_IN_MILLIS,'GL_1472_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,
			'' AS RESP_CODE,'' AS SERNO,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
			 FROM GL_1472_STG gl1472 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="25">
		<name>ONUS_ATM_CREDIT_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		
SELECT 'GL_1002' AS RECON_SIDE,gl1002.SID,gl1002.TRA_AMT,gl1002.TRA_DATE,gl1002.RETR_REF_NO,
	gl1002.TRAN_CUR,gl1002.C_ACCEP_TERM_ID,STAN,
gl1002.PROC_CODE,gl1002.MAIN_REV_IND,gl1002.BUSINESS_AREA,gl1002.CHANNEL,
	gl1002.TIME_IN_MILLIS,
	'GL_1002_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,'' AS RESP_CODE,'' AS SERNO,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				 FROM GL_1002_STG gl1002 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="26">
		<name>ONUS_ATM_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select	'CTL' AS RECON_SIDE,citxn.SID,I004_AMT_TRXN AS TRA_AMT, I013_TRXN_DATE AS TRA_DATE, 
			I037_RET_REF_NUM AS RETR_REF_NO,
			ctxn.TRAN_CUR,'' AS C_ACCEP_TERM_ID,'' AS STAN,
 PROC_CODE,MAIN_REV_IND,BUSINESS_AREA,CHANNEL,
			
			ctxn.TIME_IN_MILLIS,'CISO_STG' as  SOURCE_TARGET,I000_MSG_TYPE,'' as DEB_CRE_IND,'' AS RESP_CODE,
			citxn.SERNO,MAIN_REV_IND,I006_AMT_BILL AS LOCAL_AMT
			 from BATCHES_STG batch
	inner join CTXNS_STG ctxn on batch.serno=ctxn.BATCHSERNO
	inner join CISO_STG citxn on ctxn.SERNO=citxn.SERNO 
	where	charindex('Transfer',batch.FILENAME )!=0 and I018_MERCH_TYPE=6011 
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='CREDIT CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			) and STGENERAL='POST'
			AND citxn.SID=? and citxn.status not in ('Suppress')

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//DEPOSIT
	
	<query id="27">
		<name>ONUS_ATM_DEPOSIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
				SELECT 
					'IRIS' AS RECON_SIDE,SID,RETR_REF_NO, AMT_TRAN_BASE AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,TIME_LOC_TRAN,PROC_CODE_FIRST_2 as PROC_CODE,
					C_ACCEP_TERM_ID,DEB_CRE_INDI AS DEB_CRE_IND,RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,
					AUTHORIZER,ACQUIRING_CHANNEL_ID,PAN,time_in_MILLIS,
					'IRIS_STG' as  SOURCE_TARGET,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
				FROM 	IRIS_STG 
				WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('21','25') and SID=?	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="28">
		<name>ONUS_ATM_DEPOSIT_RECON_GL_1006</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1006' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,TRA_TIME as TIME_LOC_TRAN,PROC_CODE,
				 C_ACCEP_TERM_ID,DEB_CRE_IND,'' AS RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,
						'' AS PAN,time_in_MILLIS,'GL_1006_STG' as  SOURCE_TARGET,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				FROM 	GL_1006_STG 
				WHERE 	 PROC_CODE in ('21','25') and SID=?	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//PAYROLL_ATM
	
	<query id="29">
		<name>ONUS_ATM_PAYROL_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	select	'CTL' AS RECON_SIDE,citxn.SID,I004_AMT_TRXN AS TRA_AMT, I013_TRXN_DATE AS TRA_DATE, 
			I037_RET_REF_NUM AS RETR_REF_NO,ctxn.TIME_IN_MILLIS,I000_MSG_TYPE,'' AS RESP_CODE,
			'' AS DEB_CRE_IND, 'CISO_STG' as  SOURCE_TARGET,'' AS TRAN_CUR,MAIN_REV_IND,I006_AMT_BILL AS LOCAL_AMT
			 from BATCHES_STG batch
	inner join CTXNS_STG ctxn on batch.serno=ctxn.BATCHSERNO
	inner join CISO_STG citxn on ctxn.SERNO=citxn.SERNO 
	where	charindex('Transfer',batch.FILENAME )!=0 and I018_MERCH_TYPE=6011 
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='PAYROL CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			)
			and citxn.SID=? and citxn.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="30">
		<name>ONUS_ATM_PAYROL_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,AMT_TRAN AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,RETR_REF_NO,TIME_IN_MILLIS,'' AS I000_MSG_TYPE,
	RESP_CODE,'' AS DEB_CRE_IND,
	'IRIS_STG' as  SOURCE_TARGET,TRAN_CUR,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
	FROM	IRIS_STG 
	WHERE	AUTHORIZER IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND ACQUIRING_CHANNEL_ID IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND PROC_CODE_FIRST_2=(select TRAN_CODE from MDT_PROCESS_CODES 
	 where TRAN_NAME='Withdrawal' and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
	  AND RESP_CODE IN(select CODE from MDT_RESPONSE_CODES 
	  where NAME in('APPROVED','ERR_ACQ_REVERSAL') 
	  and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='PAYROL CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			) 
			and SID=? and status not in ('Suppress')
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	<query id="31">
		<name>ONUS_ATM_PAYROL_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'GL_1472' AS RECON_SIDE,gl1472.SID,gl1472.TRA_AMT,gl1472.TRA_DATE,gl1472.RETR_REF_NO,
gl1472.TRAN_CUR,gl1472.C_ACCEP_TERM_ID,STAN,
gl1472.PROC_CODE,gl1472.MAIN_REV_IND,gl1472.BUSINESS_AREA,gl1472.CHANNEL,
			gl1472.TIME_IN_MILLIS,'GL_1472_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,'' AS RESP_CODE,'' AS SERNO,
			gl1472.WORKFLOW_STATUS,EQU_TRA_AMT AS LOCAL_AMT
	FROM	GL_1472_STG gl1472  
 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	<query id="32">
		<name>ONUS_ATM_PAYROL_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_1002' AS RECON_SIDE,gl1002.SID,gl1002.TRA_AMT,gl1002.TRA_DATE,gl1002.RETR_REF_NO,
	gl1002.TRAN_CUR,gl1002.C_ACCEP_TERM_ID,STAN,
gl1002.PROC_CODE,gl1002.MAIN_REV_IND,gl1002.BUSINESS_AREA,gl1002.CHANNEL,
	gl1002.TIME_IN_MILLIS,
	'GL_1002_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,'' AS RESP_CODE,'' AS SERNO,
	gl1002.WORKFLOW_STATUS,EQU_TRA_AMT AS LOCAL_AMT
	FROM	GL_1002_STG gl1002
	 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//CREDIT_POS
	
	<query id="33">
		<name>ONUS_POS_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	SELECT	'CTL' AS RECON_SIDE,MITXN.SID,I037_RET_REF_NUM,I004_AMT_TRXN,I002_NUMBER,I013_TRXN_DATE AS TRA_DATE,
					I000_MSG_TYPE,I039_RESP_CD AS I039_RSP_CD,MTXN.PROC_CODE,I018_MERCH_TYPE,I038_AUTH_ID,MITXN.BUSINESS_AREA,CHANNEL,BIN,MTXN.TIME_IN_MILLIS,
					'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
	FROM	BATCHES_STG BATCH INNER JOIN MTXNS_STG MTXN 
	ON		BATCH.SERNO=MTXN.INBATCHSERNO INNER JOIN MISO_STG MITXN 
	ON		MTXN.SERNO=MITXN.SERNO 
	WHERE	MITXN.CHANNELSERNO=180 AND I018_MERCH_TYPE!=6011 
			AND BIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461) 
			and  ((CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0))
			and MITXN.SID=? and MITXN.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="34">
		<name>ONUS_POS_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'AUTH_ISS' AS RECON_SIDE,SID,I037_RET_REF_NUM,I004_AMT_TRXN,I002_NUMBER,TRA_DATE,
					I000_MSG_TYPE,I039_RSP_CD,PROC_CODE,I018_MERCH_TYPE,I038_AUTH_ID,BUSINESS_AREA,CHANNEL,BIN, 
					TIME_IN_MILLIS,'AUTH_ISSUER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
	FROM	AUTH_ISSUER_STG 
	WHERE	SOURCE NOT IN('MAST','VISA') AND I018_MERCH_TYPE!=6011 AND I039_RSP_CD IN('00')
			AND BIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
			AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//DEBIT_POS
	
<query id="35">
		<name>ONUS_POS_DEBIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'CTL' AS RECON_SIDE,MITXN.SID,I037_RET_REF_NUM AS RETR_REF_NO,I004_AMT_TRXN AS TRA_AMT,
			I013_TRXN_DATE AS TRA_DATE,'' AS DEB_CRE_IND,I000_MSG_TYPE,MTXN.TIME_IN_MILLIS,'MISO_STG' as  SOURCE_TARGET,
			MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
	 FROM BATCHES_STG BATCH
	INNER JOIN MTXNS_STG MTXN ON BATCH.SERNO=MTXN.INBATCHSERNO
	INNER JOIN MISO_STG MITXN ON MTXN.SERNO=MITXN.SERNO 
	WHERE	MITXN.CHANNELSERNO=181 AND I018_MERCH_TYPE!=6011 AND BIN IN(428246,484823,434141) 
			AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0)
			and MITXN.SID=? and MITXN.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>	
	
	
	<query id="36">
		<name>ONUS_POS_DEBIT_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_1472' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,DEB_CRE_IND,'' AS I000_MSG_TYPE,
	TIME_IN_MILLIS,'GL_1472_STG' as  SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM	GL_1472_STG 
	WHERE	PROC_CODE=00 AND AUTHORIZING_CHANNEL IN(0001,0050,9898,9999,01010) 
			AND ACQUIRING_CHANNEL_ID IN(0001,0050,9898,9999,01010)
			and SID=? and status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>	
	
	
	
	//PAYROLL_POS
	
	
	<query id="37">
		<name>ONUS_POS_PAYROL_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
				SELECT	'CTL' AS RECON_SIDE,MITXN.SID,I037_RET_REF_NUM,I002_NUMBER,I004_AMT_TRXN,
				I000_MSG_TYPE,MTXN.TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,
				I013_TRXN_DATE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
				FROM	BATCHES_STG BATCH INNER JOIN MTXNS_STG MTXN 
				ON		BATCH.SERNO=MTXN.INBATCHSERNO INNER JOIN MISO_STG MITXN 
				ON		MTXN.SERNO=MITXN.SERNO 
				WHERE	MITXN.CHANNELSERNO=180 AND I018_MERCH_TYPE!=6011 
						AND BIN IN(404618) 
					
						AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0)
						and MITXN.SID=? and MITXN.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="38">
		<name>ONUS_POS_PAYROL_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'AUTH_ISS' AS RECON_SIDE,SID,I037_RET_REF_NUM,I002_NUMBER,I004_AMT_TRXN,I000_MSG_TYPE, TIME_IN_MILLIS,'AUTH_ISSUER_STG' AS SOURCE_TARGET,
LTIMESTAMP AS I013_TRXN_DATE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
				FROM	AUTH_ISSUER_STG 
				WHERE	SOURCE NOT IN('MAST','VISA') AND I018_MERCH_TYPE!=6011 
						AND BIN IN(404618) AND I039_RSP_CD IN('00') and SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	/*-------------------------ISSUER QUERIES------------------------*/
	
	//MASTER_CREDIT_ATM
	
	
	<query id="40">
		<name>ISSUER_MATM_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE ,I037_RET_REF_NUM,I000_MSG_TYPE,I013_TRXN_DATE AS TRA_DATE,
'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,I038_AUTH_ID,'' AS RESP_CODE,'' as REASONCODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
 and CHARINDEX ('TT11',FILENAME)!=0 and I018_MERCH_TYPE=6011
  AND ciso.SID=?  AND ciso.STATUS NOT IN('Supress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="41">
		<name>ISSUER_MATM_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,I005_AMT_SETTLE,I037_RET_REF_NUM,I000_MSG_TYPE,LTIMESTAMP AS TRA_DATE,
'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN as BIN,I018_MERCH_TYPE,I038_AUTH_ID,I039_RSP_CD AS RESP_CODE,
REASONCODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='MAST'  and I018_MERCH_TYPE=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD='00' AND REASONCODE IN('0') AND SID=? AND  STATUS NOT IN('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//MASTER_CREDIT_POS
	
	<query id="42">
		<name>ISSUER_MPOS_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE ,I037_RET_REF_NUM,I000_MSG_TYPE,I013_TRXN_DATE AS TRA_DATE,
'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,I038_AUTH_ID,'' AS RESP_CODE,'' as REASONCODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
 and CHARINDEX ('TT112T0',FILENAME)!=0 and I018_MERCH_TYPE!=6011
  AND ciso.SID=? AND ciso.STATUS NOT IN('Supress')

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="43">
		<name>ISSUER_MPOS_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,I005_AMT_SETTLE,I037_RET_REF_NUM,I000_MSG_TYPE,LTIMESTAMP AS TRA_DATE,
'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN as BIN,I018_MERCH_TYPE,I038_AUTH_ID,I039_RSP_CD AS RESP_CODE,
REASONCODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='MAST'  and I018_MERCH_TYPE!=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD='00' AND REASONCODE IN('0') AND SID=? AND  STATUS NOT IN('Suppress') 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	// NAPS_DEBIT_ATM
	
	<query id="44">
		<name>ISSUER_NATM_DEBIT_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,RETR_REF_NO,TRAN_DATE AS TRA_DATE,TRANS_AMOUNT AS TRA_AMT,TRAN_TIME,TIME_IN_MILLIS, 
		'QCB_STG' AS SOURCE_TARGET,
		'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PROC_COD,BIN,'' AS C_ACCEP_TERM_ID,'' AS RESP_CODE,
		'' AS STAN,CARD_NUMBER,'' AS DEB_CRE_IND,TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT
		 FROM QCB_STG 
		WHERE BIN IN(434141,484823,428246) 
		AND CHANNEL='ATM' AND ISS_BANK_CODE=3 AND ACQ_BANK_CODE!=3
		AND TRAN_TYPE IN(10,98,99) 
		AND SID=? 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="45">
		<name>ISSUER_NATM_DEBIT_RECON_GL_1015</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1015' AS RECON_SIDE,SID,RETR_REF_NO,SETTL_DATE as TRA_DATE,TRA_AMT,TRA_TIME,TIME_IN_MILLIS,'GL_1015_STG' AS SOURCE_TARGET,
		AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_COD,'' AS BIN,C_ACCEP_TERM_ID,'' AS RESP_CODE,STAN,
		'' AS CARD_NUMBER,DEB_CRE_IND,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1015_STG
		 WHERE AUTHORIZING_CHANNEL IN('9999')
		 AND PROC_CODE=01 AND ACQUIRING_CHANNEL_ID=28 
		 AND TELL_ID=9953 AND SID=? 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="46">
		<name>ISSUER_NATM_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,AMT_SETT AS TRA_AMT,TIME_LOC_TRAN AS TRA_TIME,TIME_IN_MILLIS, 
		'IRIS_STG' AS SOURCE_TARGET,AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PROC_COD,BIN,C_ACCEP_TERM_ID,RESP_CODE,
		SYS_TRACE_AUDIT_NO AS STAN,PAN AS CARD_NUMBER,'' AS DEB_CRE_IND,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
		 FROM IRIS_STG 
		WHERE AUTHORIZER IN('9999') 
		AND ACQUIRING_CHANNEL_ID=28 AND RESP_CODE IN('000','036','037') AND PROC_CODE_FIRST_2=01
		AND BIN IN(434141,484823,428246) 
		AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS_DEBIT_POS
	
	
	<query id="46">
		<name>ISSUER_NPOS_DEBIT_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,RETR_REF_NO,TRAN_DATE AS TRA_DATE,TRANS_AMOUNT AS TRA_AMT,TRAN_TIME,TIME_IN_MILLIS, 
		'QCB_STG' AS SOURCE_TARGET,
		'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PROC_COD,BIN,'' AS C_ACCEP_TERM_ID,'' AS RESP_CODE,'' AS STAN,CARD_NUMBER,
		TRAN_TYPE,'' AS DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT
		 FROM QCB_STG 
		WHERE BIN IN(434141,484823,428246) 
		AND CHANNEL='POS' AND ISS_BANK_CODE=3 AND ACQ_BANK_CODE!=3
		AND TRAN_TYPE IN(10,98,99) 
		AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="47">
		<name>ISSUER_NPOS_DEBIT_RECON_GL_1016</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1016' AS RECON_SIDE,SID,RETR_REF_NO,SETTL_DATE AS TRA_DATE,TRA_AMT,TRA_TIME,TIME_IN_MILLIS,'GL_1016_STG' AS SOURCE_TARGET,
		AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_COD,'' AS BIN,C_ACCEP_TERM_ID,'' AS RESP_CODE,
		STAN,'' AS CARD_NUMBER,'' AS TRAN_TYPE,DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1016_STG
		 WHERE AUTHORIZING_CHANNEL='9999'
		 AND PROC_CODE IN(00,06) AND ACQUIRING_CHANNEL_ID=0028 
		 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="48">
		<name>ISSUER_NPOS_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,AMT_SETT AS TRA_AMT,TIME_LOC_TRAN AS TRA_TIME,TIME_IN_MILLIS, 
		'IRIS_STG' AS SOURCE_TARGET,AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PROC_COD,BIN,C_ACCEP_TERM_ID,RESP_CODE,
		SYS_TRACE_AUDIT_NO AS STAN,PAN AS CARD_NUMBER,'' AS TRAN_TYPE,'' AS DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
		 FROM IRIS_STG 
		WHERE AUTHORIZER IN('9999') 
		AND ACQUIRING_CHANNEL_ID=28 AND RESP_CODE IN('000','036','037') AND PROC_CODE_FIRST_2 IN(00,06)
		AND BIN IN(434141,484823,428246) 
		AND SID=?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	// VISA_CREDIT_ATM
	
	<query id="49">
		<name>ISSUER_VATM_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE as AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,I013_TRXN_DATE AS TRA_DATE,
'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,I038_AUTH_ID,'' AS RESP_CODE,'' as REASONCODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
 and CHARINDEX ('SAVE.INCOMING',FILENAME)!=0 and I018_MERCH_TYPE=6011 and STGENERAL IN('NEW','POST')
  AND ciso.SID=?  AND ciso.STATUS NOT IN('Supress')


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="50">
		<name>ISSUER_VATM_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,LTIMESTAMP AS TRA_DATE,
'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN as BIN,I018_MERCH_TYPE,I038_AUTH_ID,I039_RSP_CD AS RESP_CODE,
REASONCODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='VISA'  and I018_MERCH_TYPE=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD='00' AND REASONCODE IN('0') AND SID=? AND  STATUS NOT IN('Suppress') 


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//VISA_DEBIT_ATM
	
	<query id="51">
		<name>ISSUER_VATM_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,SYS_TRACE_AUDIT_NO AS STAN,AMT_TRAN AS IRIS_AMT_TRAN,AMT_SETT AS IRIS_AMT_SETT,
					0 AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,DATE_LOC_TRAN AS TRA_DATE,
					'IRIS_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,RESP_CODE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
			FROM	IRIS_STG_VISA_ISS_ATM where SID=?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="52">
		<name>ISSUER_VATM_DEBIT_RECON_GL_2247</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_2247' AS RECON_SIDE,SID,STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,TRA_AMT AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,
					0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,TRA_DATE,'GL_2247_STG' AS SOURCE_TARGET,
					DEB_CRE_IND,'' AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT 
			FROM	GL_2247_STG 
			WHERE PROC_CODE=01 AND AUTHORIZING_CHANNEL='9999' 
AND ACQUIRING_CHANNEL_ID=0007 AND TELL_ID=9953 
					AND SID=?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="53">
		<name>ISSUER_VATM_DEBIT_RECON_VISA</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'VISA' AS RECON_SIDE,VISA.SID,VISA.AUTH_CODE AS STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,0 AS GL_TRA_AMT,
					SOURCE_AMT AS VISA_SOURCE_AMT,VISA.DEST_AMT AS VISA_DEST_AMT,'' AS RETR_REF_NO ,VISA.TRAN_CODE AS TRAN_CODE,VISA.PURCHASE_DATE AS TRA_DATE,
					'VISA_ISSUER_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,'' AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,VISA.DEST_AMT AS LOCAL_AMT
			FROM	ISSUER_VATM_DEBIT_VISA_ISSUER_STG VISA WHERE SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	// VISA_CREDIT_POS
	
	<query id="54">
		<name>ISSUER_VPOS_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE AS AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,I038_AUTH_ID,'' AS REASONCODE,
I013_TRXN_DATE AS TRA_DATE,'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,'' AS SOURCE,'' AS RESP_CODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
  and CHARINDEX ('SAVE.INCOMING',FILENAME)!=0 and I018_MERCH_TYPE!=6011 AND STGENERAL IN('NEW','POST')
  AND ciso.SID=? AND CISO.STATUS NOT IN ('Suppress')

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="55">
		<name>ISSUER_VPOS_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,I038_AUTH_ID,REASONCODE,
LTIMESTAMP AS TRA_DATE,'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN AS BIN,I018_MERCH_TYPE,SOURCE,
I039_RSP_CD AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='VISA'  and I018_MERCH_TYPE!=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD IN('00') AND REASONCODE IN('0') 
AND SID=? 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	// VISA_DEBIT_POS
	
	
	<query id="56">
		<name>ISSUER_VPOS_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,SYS_TRACE_AUDIT_NO AS STAN,AMT_TRAN AS IRIS_AMT_TRAN,AMT_SETT AS IRIS_AMT_SETT,
					0 AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,DATE_LOC_TRAN AS TRA_DATE,
					'IRIS_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,RESP_CODE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
			FROM	IRIS_STG_VISA_ISS_POS where SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="57">
		<name>ISSUER_VPOS_DEBIT_RECON_GL_2247</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_2247' AS RECON_SIDE,SID,STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,TRA_AMT AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,
					0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,TRA_DATE,'GL_2247_STG' AS SOURCE_TARGET,DEB_CRE_IND
					,'' AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
			FROM	GL_2247_STG 
			WHERE	PROC_CODE=00 AND AUTHORIZING_CHANNEL='9999' AND ACQUIRING_CHANNEL_ID=0007 AND TELL_ID=9953
					AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="58">
		<name>ISSUER_VPOS_DEBIT_RECON_VISA</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'VISA' AS RECON_SIDE,VISA.SID,VISA.AUTH_CODE AS STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,0 AS GL_TRA_AMT,
					SOURCE_AMT AS VISA_SOURCE_AMT,VISA.DEST_AMT AS VISA_DEST_AMT,'' AS RETR_REF_NO ,VISA.TRAN_CODE AS TRAN_CODE,VISA.PURCHASE_DATE AS TRA_DATE,
					'VISA_ISSUER_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,'' AS RESP_CODE,MAIN_REV_IND,'' AS TRAN_CUR,VISA.Source_Amt AS LOCAL_AMT
			FROM	ISSUER_VATM_DEBIT_VISA_ISSUER_STG VISA WHERE SID=?
			

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	/*-------------------------ACCQUIRER QUERIES------------------------*/
	
	//MASTER_CREDIT_ATM
	
	<query id="59">
		<name>ACQ_MATM_CRDS_RECON_MAST</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'MAST' AS RECON_SIDE,SID,refrence_no AS RETR_REF_NO,trans_amount as TRA_AMT,0 AS AMT_SETT,tran_date AS TRA_DATE,TIME_IN_MILLIS,
	'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,resp_code AS RESP_CODE,
	 trans_amt_dr_cr_indicator AS DEB_CRE_IND,'MASTER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,trans_amount AS LOCAL_AMT
  FROM MASTER_STG 
		WHERE processor_acq_iss='A'  and resp_code='00' AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="60">
		<name>ACQ_MATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SID,SID,RETR_REF_NO,AMT_TRAN AS TRA_AMT,AMT_SETT,DATE_LOC_TRAN AS TRA_DATE,TIME_IN_MILLIS,
		AUTHORIZER, ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE ,RESP_CODE,
		'' AS DEB_CRE_IND,'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT

FROM IRIS_STG
	WHERE AUTHORIZER=0040 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE_FIRST_2=01  AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="61">
		<name>ACQ_MATM_CRDS_RECON_GL_1482</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1482' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,0 AS AMT_SETT,TRA_DATE,TIME_IN_MILLIS,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,
'GL_1482_STG' AS SOURCE_TARGET,EQU_TRA_AMT AS LOCAL_AMT 
FROM GL_1482_STG
		WHERE AUTHORIZING_CHANNEL=0040 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE=01 
		AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="62">
		<name>ACQ_MATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,0 AS AMT_SETT,TRA_DATE,TIME_IN_MILLIS,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,DEB_CRE_IND,
'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT FROM GL_1002_STG
WHERE AUTHORIZING_CHANNEL=0040 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE=01 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//MASTER_CREDIT_POS 
	
	
	<query id="63">
		<name>ACQ_MPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I037_RET_REF_NUM, I002_NUMBER, MISO.CHANNELSERNO,I004_AMT_TRXN, I013_TRXN_DATE AS TRA_DATE, I000_MSG_TYPE,MISO.SERNO,'' AS RESP_CODE,
TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT  FROM BATCHES_STG BATCH
	INNER JOIN  MTXNS_STG MTXN
	ON MTXN.INBATCHSERNO=BATCH.SERNO
	INNER JOIN  MISO_STG MISO
	ON MISO.SERNO=MTXN.SERNO WHERE (MISO.CHANNELSERNO=177 AND I018_MERCH_TYPE!=6011 AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0)
	OR (MISO.CHANNELSERNO=179  AND CHARINDEX('a2016',FILENAME)!=0 AND BIN LIKE '5%')) AND STGENERAL IN('NEW','POST')
	AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="64">
		<name>ACQ_MPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,I037_RET_REF_NUM, I002_NUMBER,'' AS CHANNELSERNO, I004_AMT_TRXN, LTIMESTAMP AS TRA_DATE, I000_MSG_TYPE,
	'' AS SERNO,I039_RESP_CD AS RESP_CODE,TIME_IN_MILLIS,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM AUTH_ACQUIRER_STG 
	WHERE PROCESS_NAME='APIMAST' AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS2_CREDIT_ATM
	
	<query id="65">
		<name>ACQ_NAPS2_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,AMT_SETT AS TRA_AMT,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,TIME_IN_MILLIS,
	AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE,RESP_CODE,'' AS DEB_CRE_IND,PAN AS CARD_NO,
	'IRIS_STG' AS SOURCE_TARGET,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
	FROM IRIS_STG WHERE AUTHORIZER in('0028','0036') AND ACQUIRING_CHANNEL_ID='0001' AND PROC_CODE_FIRST_2='73'
	AND BIN NOT IN(SELECT BIN MDT_CARD_BIN FROM MDT_DB_CARD_BINS)
	 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="66">
		<name>ACQ_NAPS2_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRANS_AMOUNT AS TRA_AMT,RETR_REF_NO,TRAN_DATE AS TRA_DATE,TIME_IN_MILLIS,
'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' AS RESP_CODE,'' AS DEB_CRE_IND,CARD_NUMBER AS CARD_NO,
'QCB_STG' AS SOURCE_TARGET,TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT
 FROM QCB_STG WHERE ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='POS'
	AND TRAN_TYPE IN(10,98,99) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="67">
		<name>ACQ_NAPS2_CRDS_RECON_GL_1016</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1016' AS RECON_SIDE,SID,TRA_AMT,RETR_REF_NO,TRA_DATE,TIME_IN_MILLIS, 
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' AS RESP_CODE,DEB_CRE_IND,'' AS CARD_NO,
'GL_1016_STG' AS SOURCE_TARGET,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
FROM GL_1016_STG WHERE TELL_ID=9911 AND AUTHORIZING_CHANNEL =0028 AND ACQUIRING_CHANNEL_ID=0001
	AND PROC_CODE=73 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS_CREDIT_ATM
	
	<query id="68">
		<name>ACQ_NATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,DATE_LOC_TRAN AS TRA_DATE,AMT_SETT AS TRA_AMT,AMT_TRAN AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
	AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE,RESP_CODE,
	'' AS DEB_CRE_IND,'' AS TRAN_TYPE,'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
	 FROM IRIS_STG WHERE AUTHORIZER=0028 AND ACQUIRING_CHANNEL_ID=0001 
	AND PROC_CODE_FIRST_2=01 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="69">
		<name>ACQ_NATM_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRAN_DATE AS TRA_DATE,TRANS_AMOUNT AS TRA_AMT,0 AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
	'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' AS RESP_CODE,'' AS DEB_CRE_IND,TRAN_TYPE,
	'QCB_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT 
	FROM QCB_STG WHERE ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='ATM'
	AND TRAN_TYPE IN(10,98,99) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="70">
		<name>ACQ_NATM_CRDS_RECON_GL_1015</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1015' AS RECON_SIDE,SID,TRA_DATE,EQU_TRA_AMT AS TRA_AMT,TRA_AMT AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_CODE,'' AS RESP_CODE,DEB_CRE_IND,'' AS TRAN_TYPE,
	'GL_1015_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1015_STG WHERE AUTHORIZING_CHANNEL=0028 AND ACQUIRING_CHANNEL_ID=0001 
	AND PROC_CODE=01 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="71">
		<name>ACQ_NATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,TRA_DATE,EQU_TRA_AMT AS TRA_AMT,TRA_AMT AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
		AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_CODE,'' AS RESP_CODE,DEB_CRE_IND,
		'' AS TRAN_TYPE,'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
 FROM GL_1002_STG WHERE AUTHORIZING_CHANNEL=0028 AND ACQUIRING_CHANNEL_ID=0001 AND TELL_ID=9911
	AND PROC_CODE=01 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS_POS
	
	<query id="72">
		<name>ACQ_NPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I013_TRXN_DATE AS TRA_DATE,'' AS TRA_TIME,MISO.CHANNELSERNO,I004_AMT_TRXN AS TRA_AMT,I037_RET_REF_NUM AS RETR_REF_NO,
I002_NUMBER AS CARD_NO,MISO.SERNO,'' AS TELL_ID,I039_RESP_CD AS RESP_CODE,I000_MSG_TYPE,
'' as DEB_CRE_IND,'' AS TRAN_TYPE,'' as EDCFLAG,'' as PROCESS_NAME,
'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM BATCHES_STG BATCH
	INNER JOIN MTXNS_STG MTXN
	ON BATCH.SERNO=MTXN.INBATCHSERNO
	INNER JOIN MISO_STG MISO
	ON MTXN.SERNO=MISO.SERNO WHERE CHARINDEX('a2016',FILENAME)!=0  AND MISO.CHANNELSERNO=179 
	AND I018_MERCH_TYPE!=6011 AND STGENERAL IN('NEW','POST')	
	 AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!--Manualentries -->

<query id="333">
		<name>ONS_MAIN_INSERT_STG</name>
		<targetTables>FIN_ONS_CBS_STG</targetTables>
		<queryString>
			INSERT INTO FIN_ONS_CBS_STG(SID,TRAN_REF_NUM,INTERNAL_REF_NUM ,ACNT,TRAN_DATE,VALUE_DATE,AMOUNT,DRCR,CURRENCY,CARD_NUMBER,
	     	ACCT_BRANCH_ID,TRAN_PARTICULAR,TRAN_REMARKS,TRAN_ENTRY_USER,TRAN_POSTED_USER,COMMENTS,VERSION,
			ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,
			MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA,
			FREE_FIELD_1,FREE_FIELD_2,FREE_FIELD_3,FREE_FIELD_4,FREE_DATE_1,FREE_DATE_2)
			VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			SID@BIGINT,TRAN_REF_NUM@VARCHAR,INTERNAL_REF_NUM@VARCHAR,ACNT@VARCHAR,TRAN_DATE@DATE,VALUE_DATE@DATE,AMOUNT@VARCHAR, 
			DRCR@VARCHAR,CURRENCY@VARCHAR,CARD_NUMBER@VARCHAR,ACCT_BRANCH_ID@VARCHAR,TRAN_PARTICULAR@VARCHAR, 
			TRAN_REMARKS@VARCHAR,TRAN_ENTRY_USER@VARCHAR,TRAN_POSTED_USER@VARCHAR,COMMENTS@VARCHAR,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,
			ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR,
			FREE_FIELD_1@VARCHAR,FREE_FIELD_2@VARCHAR,FREE_FIELD_3@VARCHAR,FREE_FIELD_4@VARCHAR,FREE_DATE_1@DATE,FREE_DATE_2@DATE
	</queryParam>
	</query>
	
	<query id="2">
        <name>FIN_ONS_CBO_STG_INSERT</name>
		<targetTables>FIN_ONS_CBO_STG</targetTables>
        <queryString>
		INSERT INTO FIN_ONS_CBO_STG
           (SID,TRAN_REF_NUM,INTERNAL_REF_NUM,ACCOUNT,TRAN_DATE,VALUE_DATE,AMOUNT,DRCR,CURRENCY,PAN_NUMBER
           ,ORIGINATOR_BID,DESTINATION_BID,ACQUIRING_INSTITUTION_ID,CARD_ACCEPTOR_NAME,MERCHANT_CATEGORY_CODE,TRANSACTION_TYPE
           ,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS
           ,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA,FREE_TEXT_1,FREE_TEXT_2,FREE_TEXT_3,FREE_TEXT_4,FREE_TEXT_5
           ,FREE_TEXT_6,FREE_TEXT_7,FREE_TEXT_8,FREE_TEXT_9,FREE_TEXT_10,FREE_CODE_1,FREE_CODE_2,FREE_CODE_3,FREE_CODE_4,FREE_CODE_5
           ,FREE_DATE_1,FREE_DATE_2,FREE_DATE_3,FREE_DATE_4,FREE_DATE_5)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?)			
			 </queryString>
		<queryParam>
		   SID@BIGINT,TRAN_REF_NUM@VARCHAR,INTERNAL_REF_NUM@VARCHAR,ACCOUNT@VARCHAR,TRAN_DATE@DATE,VALUE_DATE@DATE,AMOUNT@DECIMAL,
		   DRCR@VARCHAR,CURRENCY@VARCHAR,PAN_NUMBER@VARCHAR ,ORIGINATOR_BID@VARCHAR,DESTINATION_BID@VARCHAR,ACQUIRING_INSTITUTION_ID@VARCHAR,
		   CARD_ACCEPTOR_NAME@VARCHAR,MERCHANT_CATEGORY_CODE@INTEGER,TRANSACTION_TYPE@VARCHAR ,COMMENTS@VARCHAR,VERSION@INTEGER,
		   ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP, RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR
           ,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR,FREE_TEXT_1@VARCHAR,FREE_TEXT_2@VARCHAR,
		   FREE_TEXT_3@VARCHAR,FREE_TEXT_4@VARCHAR,FREE_TEXT_5@VARCHAR ,FREE_TEXT_6@VARCHAR,FREE_TEXT_7@VARCHAR,FREE_TEXT_8@VARCHAR,
		   FREE_TEXT_9@VARCHAR,FREE_TEXT_10@VARCHAR,FREE_CODE_1@VARCHAR,FREE_CODE_2@VARCHAR,FREE_CODE_3@VARCHAR,FREE_CODE_4@VARCHAR,
		   FREE_CODE_5@VARCHAR,FREE_DATE_1@DATE,FREE_DATE_2@DATE,FREE_DATE_3@DATE,FREE_DATE_4@DATE,FREE_DATE_5@DATE
		</queryParam>
    </query>
	<!--Manualentries -->
	
	
	
	<query id="73">
		<name>ACQ_NPOS_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRAN_DATE AS TRA_DATE,TRAN_TIME AS TRA_TIME,'' AS CHANNELSERNO,TRANS_AMOUNT AS TRA_AMT,RETR_REF_NO,
	CARD_NUMBER AS CARD_NO,0 AS SERNO,'' AS TELL_ID,'' AS RESP_CODE,'' as I000_MSG_TYPE,'' as DEB_CRE_IND,TRAN_TYPE,'' as EDCFLAG,'' as PROCESS_NAME,
	'QCB_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT FROM QCB_STG 
	WHERE  ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 
		AND CHANNEL='POS' AND TRAN_TYPE IN('10','98','99') 
		 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="74">
		<name>ACQ_NPOS_CRDS_RECON_GL_1016</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1016' AS RECON_SIDE,SID,TRA_DATE, TRA_TIME,'' AS CHANNELSERNO,  TRA_AMT, RETR_REF_NO, ''  AS CARD_NO,'' AS SERNO,TELL_ID,'' AS RESP_CODE,'' as I000_MSG_TYPE,
	DEB_CRE_IND,'' AS TRAN_TYPE,'' as EDCFLAG,'' as PROCESS_NAME,'GL_1016_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
 FROM GL_1016_STG WHERE AUTHORIZING_CHANNEL IN('0028','0029','0031') 
			AND ACQUIRING_CHANNEL_ID IN('0050','0001') AND PROC_CODE IN('00','86') AND TELL_ID IN(9946)
			AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="75">
		<name>ACQ_NPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,LTIMESTAMP AS TRA_DATE,'' AS TRA_TIME,'' AS CHANNELSERNO,I004_AMT_TRXN AS TRA_AMT,I037_RET_REF_NUM AS RETR_REF_NO, 
I002_NUMBER AS CARD_NO,'' AS SERNO,'' AS TELL_ID,'' AS RESP_CODE,I000_MSG_TYPE,'' as DEB_CRE_IND,'' AS TRAN_TYPE,EDCFLAG,
PROCESS_NAME,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
  FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='CTIDHI2' AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') 
   AND (CARDBIN IN(SELECT PREFIX FROM MDT_NAPS_CARD_BINS) OR(CARDBIN LIKE '34%' OR CARDBIN LIKE '37%')) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//QPAY_POS
	
	<query id="76">
		<name>ACQ_QPAY_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRAN_DATE,TRANS_AMOUNT,RETR_REF_NO,ACQ_BANK_CODE,ISS_BANK_CODE,CHANNEL,TRAN_TYPE,CARD_NUMBER,
'' AS SOURCE_TYPE,'' AS DEST_TYPE,TIME_IN_MILLIS,'QCB_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT as LOCAL_AMT 
FROM QCB_STG
	 WHERE ACQ_BANK_CODE=3 AND ISS_BANK_CODE=3 OR (ISS_BANK_CODE=3 OR ISS_BANK_CODE!=3)  
	 AND CHANNEL='QPY' AND TRAN_TYPE IN(10,98,99) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="77">
		<name>ACQ_QPAY_CRDS_RECON_QPAY</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QPAY' AS RECON_SIDE,SID,TRAN_DATE,AMOUNT,RETR_REF_NO,
'' AS ACQ_BANK_CODE,'' AS ISS_BANK_CODE,'' AS CHANNEL,'' AS TRAN_TYPE,CARD_NUMBER,
SOURCE_TYPE,DEST_TYPE,TIME_IN_MILLIS,'QPAY_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMOUNT as LOCAL_AMT 
FROM QPAY_STG
 WHERE SOURCE_TYPE=5 AND DEST_TYPE=2 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//UNION_PAY_ATM
	
	<query id="78">
		<name>ACQ_UATM_CRDS_RECON_CUP</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CUP' AS RECON_SIDE,SID,trans_amount AS TRA_AMT,retr_ref_no AS RETR_REF_NO,sett_date as TRA_DATE,'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,
'' AS RESP_CODE,'' AS DEB_CRE_IND,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,
'CUP_STG' AS SOURCE_TARGET,trans_amount AS LOCAL_AMT  FROM CUP_STG where SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="79">
		<name>ACQ_UATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,AMT_C_HLDR_BILL AS TRA_AM,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,AUTHORIZER,ACQUIRING_CHANNEL_ID,RESP_CODE,'' AS DEB_CRE_IND,
'' AS TRAN_TYPE,'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT FROM IRIS_STG 
WHERE AUTHORIZER IN(0046) AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE_FIRST_2=01
	AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="80">
		<name>ACQ_UATM_CRDS_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1472' AS RECON_SIDE, SID,EQU_TRA_AMT AS TRA_AMT,RETR_REF_NO,TRA_DATE,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,'' AS RESP_CODE,DEB_CRE_IND,'' AS TRAN_TYPE,
		'GL_1472_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	 FROM GL_1472_STG WHERE SUB_ACCT_CODE=2 
	AND  AUTHORIZING_CHANNEL IN('0046') AND ACQUIRING_CHANNEL_ID=0001 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="81">
		<name>ACQ_UATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,EQU_TRA_AMT AS TRA_AMT,RETR_REF_NO,TRA_DATE,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,'' AS RESP_CODE,DEB_CRE_IND,'' AS TRAN_TYPE,
	'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	 FROM GL_1002_STG WHERE AUTHORIZING_CHANNEL IN('0046') AND ACQUIRING_CHANNEL_ID=0001 AND TELL_ID=9911
	 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//UNION_PAY_POS
	<query id="82">
		<name>ACQ_UPOS_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO AS I037_RET_REF_NUM,PAN AS I002_NUMBER ,'' AS CHANNELSERNO,AMT_SETT AS I004_AMT_TRXN,DATE_LOC_TRAN AS TRA_DATE,
	'' AS I000_MSG_TYPE,'' AS SERNO,RESP_CODE,PROC_CODE_FIRST_2 AS PROC_COD,AUTHORIZER,ACQUIRING_CHANNEL_ID,TIME_IN_MILLIS,'IRIS_STG' AS SOURCE_TARGET,
		MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
		 FROM IRIS_STG 
		WHERE AUTHORIZER IN('0046') 
		AND ACQUIRING_CHANNEL_ID IN('0050') AND PROC_CODE_FIRST_2=00 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="83">
		<name>ACQ_UPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I037_RET_REF_NUM, I002_NUMBER,MISO.CHANNELSERNO ,I004_AMT_TRXN, I013_TRXN_DATE AS TRA_DATE, I000_MSG_TYPE,MISO.SERNO,
	'' AS RESP_CODE,'' AS PROC_COD,'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,
	TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM BATCHES_STG BATCH
	INNER JOIN  MTXNS_STG MTXN
	ON MTXN.INBATCHSERNO=BATCH.SERNO
	INNER JOIN  MISO_STG MISO
	ON MISO.SERNO=MTXN.SERNO WHERE MISO.CHANNELSERNO=219  
	AND I018_MERCH_TYPE!=6011 AND (CHARINDEX('a2016',FILENAME)!=0 
	OR CHARINDEX('m2016',FILENAME)!=0) 
	AND STGENERAL IN('NEW','POST')
	AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="84">
		<name>ACQ_UPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,I037_RET_REF_NUM, I002_NUMBER,'' AS CHANNELSERNO, I004_AMT_TRXN, LTIMESTAMP AS TRA_DATE, I000_MSG_TYPE,
	'' AS SERNO,'' AS RESP_CODE,'' AS PROC_COD,'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,TIME_IN_MILLIS,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET, 
	MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM AUTH_ACQUIRER_STG 
	WHERE PROCESS_NAME='CTIDHI2' AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') 
	AND CARDBIN NOT IN (SELECT PREFIX FROM MDT_NAPS_CARD_BINS)
	AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//VISA_ATM
	
	
	<query id="85">
		<name>ACQ_VATM_CRDS_RECON_VISA</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'VISA' AS RECON_SIDE,SID,retrieval_ref_number AS RETR_REF_NO,transaction_amount AS TRA_AMT,report_date AS TRA_DATE,
	'' AS AUTHORIZER,'' AS  ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' as RESP_CODE,0 AS C_ACCEP_TERM_ID,0 AS TELL_ID,'' AS DEB_CRE_IND,tran_type AS TRAN_TYPE,
	'visa_stg' AS SOURCE_TARGET,MAIN_REV_IND,transaction_amount AS LOCAL_AMT
 FROM   visa_stg WHERE SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="86">
		<name>ACQ_VATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO,AMT_SETT AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,
		AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE,RESP_CODE,C_ACCEP_TERM_ID,0 AS TELL_ID,'' AS DEB_CRE_IND,'' AS TRAN_TYPE,
		'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
 FROM IRIS_STG 
		WHERE AUTHORIZER=0030 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE_FIRST_2=01 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="87">
		<name>ACQ_VATM_CRDS_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1472' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,C_ACCEP_TERM_ID,TELL_ID,DEB_CRE_IND,
'' AS TRAN_TYPE,'GL_1472_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
  FROM GL_1472_STG 

		WHERE AUTHORIZING_CHANNEL=0030 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE=01 AND 
		SUB_ACCT_CODE ='0' AND TELL_ID=9911 AND  SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="88">
		<name>ACQ_VATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE ,
	AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,
	C_ACCEP_TERM_ID,TELL_ID,DEB_CRE_IND,'' AS TRAN_TYPE,'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1002_STG 
		WHERE AUTHORIZING_CHANNEL=0030 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE =01 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	// VISA_POS
	
	<query id="89">
		<name>ACQ_VPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I037_RET_REF_NUM, I002_NUMBER,MISO.CHANNELSERNO,I004_AMT_TRXN, I013_TRXN_DATE AS TRA_DATE, I000_MSG_TYPE,MISO.SERNO,'' AS RESP_CODE,
TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM BATCHES_STG BATCH
	INNER JOIN  MTXNS_STG MTXN
	ON MTXN.INBATCHSERNO=BATCH.SERNO
	INNER JOIN  MISO_STG MISO
	ON MISO.SERNO=MTXN.SERNO WHERE (MISO.CHANNELSERNO=182 AND I018_MERCH_TYPE!=6011 AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0))
	OR (MISO.CHANNELSERNO=179  AND CHARINDEX('m2016',FILENAME)!=0 AND MISO.CHANNELSERNO=179 AND BIN LIKE '4%' ) AND STGENERAL IN('NEW','POST')
	AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="90">
		<name>ACQ_VPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,I037_RET_REF_NUM, I002_NUMBER,'' AS CHANNELSERNO, I004_AMT_TRXN, LTIMESTAMP AS TRA_DATE, I000_MSG_TYPE,
	'' AS SERNO,I039_RESP_CD AS RESP_CODE,TIME_IN_MILLIS,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='APIVISA' 
	AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="199">
		<name>PAYROL_RECON_GL_2279</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_2279' AS RECON_SIDE,SID,DOC_NUM AS COMP_CODE,TRA_AMT AS AMOUNT,ORIGT_TRA_DATE AS TRA_DATE,'GL_2279_STG' AS SOURCE_TARGET,
						WORKFLOW_STATUS,RECON_ID,VERSION FROM GL_2279_STG WHERE  SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	      <!-- REPORT  QUERIES -->
               
            <!-- GL_1002 REPORT QUERIES-->
            
         <query id="1000">
		<name>REPORT_GL_1002_STG_DEBIT</name>
		<queryType>GL_1002_STG_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1002_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1001">
		<name>REPORT_GL_1002_STG_CREDIT</name>
		<queryType>GL_1002_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1002_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1002">
		<name>REPORT_GL_1002_STG_EX_DEBIT</name>
		<queryType>GL_1002_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1002_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1003">
		<name>REPORT_GL_1002_STG_EX_CREDIT</name>
		<queryType>GL_1002_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1002_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1004">
		<name>REPORT_GL_1002_IRIS_STG_MAIN</name>
		<queryType>GL_1002_IRIS_STG_MAIN</queryType>
		<queryString>
          SELECT count(*),isnull(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('01','82') AND MAIN_REV_IND='MAIN'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1005">
		<name>REPORT_GL_1002_IRIS_STG_REVERSAL</name>
		<queryType>GL_1002_IRIS_STG_REVERSAL</queryType>
		<queryString>
          SELECT count(*),isnull(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('01','82') AND MAIN_REV_IND='REVERSAL'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
		<!-- GL_1006 REPORT QUERIES -->
	
	
	<query id="1006">
		<name>REPORT_GL_1006_STG_DEBIT</name>
		<queryType>GL_1006_STG_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) FROM GL_1006_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1007">
		<name>REPORT_GL_1006_STG_CREDIT</name>
		<queryType>GL_1006_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) FROM GL_1006_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1008">
		<name>REPORT_GL_1006_STG_EX_DEBIT</name>
		<queryType>GL_1006_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) FROM GL_1006_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1009">
		<name>REPORT_GL_1006_STG_EX_CREDIT</name>
		<queryType>GL_1006_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) FROM GL_1006_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1010">
		<name>REPORT_GL_1006_IRIS_STG_MAIN</name>
		<queryType>GL_1006_IRIS_STG_MAIN</queryType>
		<queryString>
          SELECT count(*),isnull(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('21','25') AND MAIN_REV_IND='MAIN'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1011">
		<name>REPORT_GL_1006_IRIS_STG_REVERSAL</name>
		<queryType>GL_1006_IRIS_STG_REVERSAL</queryType>
		<queryString>
          SELECT count(*),isnull(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('21','25') AND MAIN_REV_IND='REVERSAL'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- GL_1015 REPORT QUERIES -->
	
	<query id="1012">
		<name>REPORT_GL_1015_STG_DEBIT</name>
		<queryType>GL_1015_STG_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1015_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1013">
		<name>REPORT_GL_1015_STG_CREDIT</name>
		<queryType>GL_1015_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1015_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1014">
		<name>REPORT_GL_1015_STG_EX_DEBIT</name>
		<queryType>GL_1015_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1015_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1015">
		<name>REPORT_GL_1015_STG_EX_CREDIT</name>
		<queryType>GL_1015_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1015_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1016">
		<name>REPORT_GL_1015_QCB_STG_MAIN</name>
		<queryType>GL_1015_QCB_STG_MAIN</queryType>
		<queryString>
          SELECT COUNT(*),isnull(SUM(TRANS_AMOUNT),0.00)  FROM QCB_STG WHERE 
	    	CHANNEL='ATM' AND MAIN_REV_IND='MAIN' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1017">
		<name>REPORT_GL_1015_QCB_STG_REVERSAL</name>
		<queryType>GL_1015_QCB_STG_REVERSAL</queryType>
		<queryString>
          SELECT COUNT(*),isnull(SUM(TRANS_AMOUNT),0.00)  FROM QCB_STG WHERE 
	    	CHANNEL='ATM' AND MAIN_REV_IND='REVERSAL' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
               
               
               <!-- GL_1016 REPORT QUERIES -->
               
     <query id="1018">
     <name>REPORT_GL_1016_STG_DEBIT</name>
     <queryType>GL_1016_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1016_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1019">
		<name>REPORT_GL_1016_STG_CREDIT</name>
		<queryType>GL_1016_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1016_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1020">
		<name>REPORT_GL_1016_STG_EX_DEBIT</name>
		<queryType>GL_1016_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1016_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1021">
		<name>REPORT_GL_1016_STG_EX_CREDIT</name>
		<queryType>GL_1016_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1016_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1022">
		<name>REPORT_GL_1016_QCB_STG_MAIN</name>
		<queryType>GL_1016_QCB_STG_MAIN</queryType>
		<queryString>
          SELECT COUNT(*),isnull(SUM(TRANS_AMOUNT),0.00) FROM QCB_STG WHERE
		  ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='POS' AND MAIN_REV_IND='MAIN' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1023">
		<name>REPORT_GL_1016_QCB_STG_REVERSAL</name>
		<queryType>GL_1016_QCB_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),isnull(SUM(TRANS_AMOUNT),0.00) FROM QCB_STG WHERE
		ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='POS' AND MAIN_REV_IND='REVERSAL' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>  
	
	
	<query id="1024">
		<name>REPORT_GL_1016_ACQUIRER_STG_MAIN</name>
		<queryType>GL_1016_ACQUIRER_STG_MAIN</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,SUM(I004_AMT_TRXN) AS AMOUNT FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='CTIDHI2' 
		AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') 
		AND (CARDBIN IN(SELECT PREFIX FROM MDT_NAPS_CARD_BINS) OR (CARDBIN LIKE '34%' OR CARDBIN LIKE '37%'))
		AND MAIN_REV_IND='MAIN' AND LTIMESTAMP BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1025">
		<name>REPORT_GL_1016_ACQUIRER_STG_REVERSAL</name>
		<queryType>GL_1016_ACQUIRER_STG_REVERSAL</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,SUM(I004_AMT_TRXN) AS AMOUNT FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='CTIDHI2'
	   AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00')
	   AND (CARDBIN IN(SELECT PREFIX FROM MDT_NAPS_CARD_BINS) OR (CARDBIN LIKE '34%' OR CARDBIN LIKE '37%'))
	   AND MAIN_REV_IND='REVERSAL' AND LTIMESTAMP BETWEEN ? AND ?
       </queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>  
	
	
	<!-- GL_1472 REPORT QURIES -->
	
	 <query id="1026">
     <name>REPORT_GL_1472_STG_DEBIT</name>
     <queryType>GL_1472_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1472_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1027">
		<name>REPORT_GL_1472_STG_CREDIT</name>
		<queryType>GL_1472_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1472_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1028">
		<name>REPORT_GL_1472_STG_EX_DEBIT</name>
		<queryType>GL_1472_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1472_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1029">
		<name>REPORT_GL_1472_STG_EX_CREDIT</name>
		<queryType>GL_1472_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1472_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1030">
		<name>REPORT_GL_1472_CTL_STG_MAIN</name>
		<queryType>GL_1472_CTL_STG_MAIN</queryType>
		<queryString>
         SELECT count(*),isnull(SUM(I004_AMT_TRXN),0.00) FROM BATCHES_STG BATCH 
		 INNER JOIN CTXNS_STG CTXN ON CTXN.BATCHSERNO=BATCH.SERNO INNER JOIN CISO_STG CISO ON 
		 CISO.SERNO=CTXN.SERNO  WHERE charindex('Transfer',batch.FILENAME )!=0 
		 AND BIN in(select BIN from	MDT_DB_CARD_BINS) AND STGENERAL='POST' AND MAIN_REV_IND='MAIN'
		 AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1031">
		<name>REPORT_GL_1472_CTL_STG_REVERSAL</name>
		<queryType>GL_1472_CTL_STG_REVERSAL</queryType>
		<queryString>
         SELECT count(*),isnull(SUM(I004_AMT_TRXN),0.00) FROM BATCHES_STG BATCH 
		 INNER JOIN CTXNS_STG CTXN ON CTXN.BATCHSERNO=BATCH.SERNO INNER JOIN CISO_STG CISO ON 
		 CISO.SERNO=CTXN.SERNO  WHERE charindex('Transfer',batch.FILENAME )!=0 
		 AND BIN in(select BIN from	MDT_DB_CARD_BINS) AND STGENERAL='POST' AND MAIN_REV_IND='REVERSAL'
		 AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- GL_1482 REPORT QUERIES -->
	
	 <query id="1032">
     <name>REPORT_GL_1482_STG_DEBIT</name>
     <queryType>GL_1482_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1482_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1033">
		<name>REPORT_GL_1482_STG_CREDIT</name>
		<queryType>GL_1482_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1482_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1034">
		<name>REPORT_GL_1482_STG_EX_DEBIT</name>
		<queryType>GL_1482_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1482_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1035">
		<name>REPORT_GL_1482_STG_EX_CREDIT</name>
		<queryType>GL_1482_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1482_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1036">
		<name>REPORT_GL_1482_MASTER_STG_MAIN</name>
		<queryType>GL_1482_MASTER_STG_MAIN</queryType>
		<queryString>
         SELECT COUNT(*),isnull(SUM(trans_amount),0.00) FROM MASTER_STG WHERE  MAIN_REV_IND='MAIN'
		 AND tran_date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1037">
		<name>REPORT_GL_1482_MASTER_STG_REVERSAL</name>
		<queryType>GL_1482_MASTER_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),isnull(SUM(trans_amount),0.00) FROM MASTER_STG WHERE  MAIN_REV_IND='REVERSAL'
		AND tran_date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	
	<!-- GL-2247 REPORT QUERIES -->
	
	
	 <query id="1038">
     <name>REPORT_GL_2247_STG_DEBIT</name>
     <queryType>GL_2247_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2247_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1039">
		<name>REPORT_GL_2247_STG_CREDIT</name>
		<queryType>GL_2247_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2247_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1040">
		<name>REPORT_GL_2247_STG_EX_DEBIT</name>
		<queryType>GL_2247_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2247_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1041">
		<name>REPORT_GL_2247_STG_EX_CREDIT</name>
		<queryType>GL_2247_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2247_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1042">
		<name>REPORT_GL_2247_VISA_ISSUER_STG_MAIN</name>
		<queryType>GL_2247_VISA_ISSUER_STG_MAIN</queryType>
		<queryString>
         SELECT COUNT(*),isnull(SUM(Source_Amt),0.00)  FROM VISA_ISSUER_STG  WHERE   MAIN_REV_IND='MAIN'
		 AND Purchase_Date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1043">
		<name>REPORT_GL_2247_VISA_ISSUER_STG_REVERSAL</name>
		<queryType>GL_2247_VISA_ISSUER_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),isnull(SUM(Source_Amt),0.00)  FROM VISA_ISSUER_STG  WHERE   MAIN_REV_IND='REVERSAL'
		 AND Purchase_Date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
             
             
             <!-- GL_2279 REPORT QUERIES -->
             
             
             <query id="1044">
     <name>REPORT_GL_2279_STG_DEBIT</name>
     <queryType>GL_2279_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2279_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1045">
		<name>REPORT_GL_2279_STG_CREDIT</name>
		<queryType>GL_2279_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2279_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1046">
		<name>REPORT_GL_2279_STG_EX_DEBIT</name>
		<queryType>GL_2279_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2279_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1047">
		<name>REPORT_GL_2279_STG_EX_CREDIT</name>
		<queryType>GL_2279_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,isnull(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2279_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1048">
		<name>REPORT_GL_2279_DPAY_STG_MAIN</name>
		<queryType>GL_2279_DPAY_STG_MAIN</queryType>
		<queryString>
         SELECT COUNT(*),isnull(SUM(I004_AMT_TRXN),0.00)FROM DPAY_STG WHERE   MAIN_REV_IND='MAIN' AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1049">
		<name>REPORT_GL_2279_DPAY_STG_REVERSAL</name>
		<queryType>GL_2279_DPAY_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),isnull(SUM(I004_AMT_TRXN),0.00)FROM DPAY_STG WHERE   MAIN_REV_IND='REVERSAL' AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- GL_1015 BALANCING REPORT QUERIES -->
	
	<query id="1050">
		<name>BALANCE_REPORT_GL_1015_ISSUER_NATM_DEBIT_RECON</name>
		<queryType>ISSUER_NATM_DEBIT_RECON</queryType>
		<queryString>
          SELECT COUNT(*)AS COUNT, isnull(SUM(TRA_AMT),0.00)AS AMOUNT FROM ISSUER_NATM_DEBIT_RECON 
	       WHERE MATCH_TYPE IN('AU','MU') AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1051">
		<name>BALANCE_REPORT_GL_1015</name>
		<queryType>GL_1015_STG_ORPHANS</queryType>
		<queryString>
         SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_NATM_DEBIT_GL_1015_STG
		 WHERE RECON_ID IS NULL AND SID NOT IN (SELECT SID FROM ISSUER_NATM_DEBIT_RECON WHERE RECON_SIDE='GL_1015' AND ACTIVE_INDEX='Y')
		 AND ACTIVE_INDEX='Y' AND TRA_DATE  BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1052">
		<name>BALANCE_REPORT_GL_1015_ACQ_NATM_CRDS_RECON</name>
		<queryType>ACQ_NATM_CRDS_RECON</queryType>
		<queryString>
        SELECT COUNT(*),isnull(SUM(TRA_AMT),0.00)FROM ACQ_NATM_CRDS_RECON WHERE MATCH_TYPE IN('MU','AU')
		AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- GL_1016 BALANCING REPORT QURIES -->
               
               <query id="1053">
		<name>BALANCE_REPORT_GL_1016_ISSUER_NPOS_DEBIT_RECON</name>
		<queryType>ISSUER_NPOS_DEBIT_RECON</queryType>
		<queryString>
          SELECT COUNT(*)AS COUNT, isnull(SUM(TRA_AMT),0.00)AS AMOUNT FROM ISSUER_NPOS_DEBIT_RECON
		  WHERE MATCH_TYPE IN('AU','MU') AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1054">
		<name>BALANCE_REPORT_GL_1016</name>
		<queryType>GL_1016_STG_ORPHANS</queryType>
		<queryString>
         SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_NPOS_DEBIT_GL_1016_STG
		 WHERE RECON_ID IS NULL AND SID NOT IN (SELECT SID FROM ISSUER_NPOS_DEBIT_RECON WHERE RECON_SIDE='GL_1016' AND ACTIVE_INDEX='Y')
		 AND ACTIVE_INDEX='Y' AND TRA_DATE  BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1055">
		<name>BALANCE_REPORT_GL_1016_ACQ_NPOS_CRDS_RECON</name>
		<queryType>ACQ_NPOS_CRDS_RECON</queryType>
		<queryString>
        SELECT COUNT(*),isnull(SUM(TRA_AMT),0.00)FROM ACQ_NPOS_CRDS_RECON WHERE MATCH_TYPE IN('MU','AU')
		AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_1472/0 BALANCING REPORT QUERIES -->
	
	      <query id="1056">
		<name>BALANCE_REPORT_GL_14720_ACQ_VATM_CRDS_RECON_UNMATCH</name>
		<queryType>ACQ_VATM_CRDS_RECON_UNMATCH</queryType>
		<queryString>
          SELECT COUNT(*)AS COUNT, isnull(SUM(TRA_AMT),0.00)AS AMOUNT FROM ACQ_VATM_CRDS_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1057">
		<name>BALANCE_REPORT_GL_14720</name>
		<queryType>GL_14720_STG_ORPHANS</queryType>
		<queryString>
         SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ACQ_VATM_CRDS_GL_1472_STG_ORPHANS WHERE TRA_DATE  BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_1472/2 BALANCING REPORT QUERIES -->
	
	
	<query id="1058">
		<name>BALANCE_REPORT_GL_14722_ACQ_UATM_CRDS_RECON_UNMATCH</name>
		<queryType>ACQ_UATM_CRDS_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, isnull(SUM(TRA_AMT),0.00)AS AMOUNT FROM ACQ_UATM_CRDS_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1059">
		<name>BALANCE_REPORT_GL_14722</name>
		<queryType>GL_14722_STG_ORPHANS</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ACQ_UATM_CRDS_GL_1472_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- GL_1482 BALANCING REPORT QUERIES -->
	
	<query id="1060">
		<name>BALANCE_REPORT_GL_1482_ACQ_MATM_CRDS_RECON_UNMATCH</name>
		<queryType>ACQ_MATM_CRDS_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, isnull(SUM(TRA_AMT),0.00)AS AMOUNT FROM ACQ_MATM_CRDS_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1061">
		<name>BALANCE_REPORT_GL_1482</name>
		<queryType>GL_1482_STG_ORPHANS</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ACQ_MATM_CRDS_GL_1482_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_2247 BALANCE REPORT QUERIES -->
	
	<query id="1062">
		<name>BALANCE_REPORT_GL_2247_ISSUER_VATM_DEBIT_RECON_UNMATCH</name>
		<queryType>ISSUER_VATM_DEBIT_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VATM_DEBIT_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1063">
		<name>BALANCE_REPORT_GL_2247_VATM</name>
		<queryType>GL_2247_STG_ORPHANS_VATM</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VATM_DEBIT_GL_2247_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1064">
		<name>BALANCE_REPORT_GL_2247_ISSUER_VPOS_DEBIT_RECON_UNMATCH</name>
		<queryType>ISSUER_VPOS_DEBIT_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VPOS_DEBIT_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1065">
		<name>BALANCE_REPORT_GL_2247_VPOS</name>
		<queryType>GL_2247_STG_ORPHANS_VPOS</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,isnull(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VPOS_DEBIT_GL_2247_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<!-- GL_1002 TREMINAL BALANCING REPORT --> 

	<query id="1066">
		<name>GL_1002_TERMINALS</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM FROM MDT_ATM_SUSPENSE_ACCOUNTS WHERE
			LEDGER='1002' AND CUR='1' AND ACTIVE_INDEX='Y' ORDER BY ATM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1067">
		<name>GL_1002_ATM_LOCATION</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM_LOCATION,CUR FROM MDT_ATM_SUSPENSE_ACCOUNTS
			WHERE ATM=? AND LEDGER='1002' AND ACTIVE_INDEX='Y'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1068">
		<name>GL_1002_ADJMENTS_DISPUTES</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT (SELECT ISNULL(sum(TRA_AMT),0.00) FROM
			GL_1002_STG_EX WHERE TELL_ID
			NOT IN('9971','9953','9911') AND
			VAL_DATE
			BETWEEN ? AND ? AND CUR_CODE='1' AND concat(CUS_NUM,SUB_ACCT_CODE)=?
			AND REMARKS NOT LIKE 'RES ATM%' AND REMARKS NOT LIKE 'RPL ATM%' AND
			DEB_CRE_IND='2')-(SELECT ISNULL(sum(TRA_AMT),0.00) FROM
			GL_1002_STG_EX WHERE TELL_ID NOT IN('9971','9953','9911') AND
			VAL_DATE BETWEEN ? AND ? AND CUR_CODE='1' AND
			concat(CUS_NUM,SUB_ACCT_CODE)=? AND REMARKS NOT LIKE 'RES ATM%' AND
			REMARKS NOT LIKE 'RPL ATM%' AND DEB_CRE_IND='1') ADJ_DIS
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1069">
		<name>GL_1002_CASH_RECEIVED_FROM_ATM</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ISNULL(SUM(CONVERT(DECIMAL(15,2),TRA_AMT)),0.00)
			AS RSD_AMT FROM GL_1002_STG_EX WHERE CHARINDEX('RES ATM',REMARKS)!=0
			AND TELL_ID='9930' AND VAL_DATE BETWEEN ? AND ? AND CUR_CODE='1' AND
			concat(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1070">
		<name>GL_1002_CASH_LOADED_IN_ATM</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT
			ISNULL(SUM(CONVERT(DECIMAL(15,2),-TRA_AMT)+.00),0.00) AS RPL_AMT FROM
			GL_1002_STG_EX WHERE CHARINDEX('RPL ATM',REMARKS)!=0
			AND
			TELL_ID='9930' AND VAL_DATE BETWEEN ? AND ?
			AND CUR_CODE='1' AND
			concat(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1071">
		<name>GL_1002_CLOSING_BALANCE_AS_PER_FILE</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE
			LED_CODE='1002' AND BRA_CODE='201'
			AND concat(CUS_NUM,SUB_ACCT_CODE)=? AND CUR_CODE='1' AND PRE_BANK_DATE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1072">
		<name>GL_1002_OPENING_BALANCE</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE LED_CODE='1002' AND
			BRA_CODE='201'
			AND concat(CUS_NUM,SUB_ACCT_CODE)=? AND CUR_CODE='1' AND
			PRE_BANK_DATE=DATEADD(DD,-1,?)

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1073">
		<name>GL_1002_ADDED_TRANSACTIONS</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT((SELECT ISNULL(SUM(TRA_AMT),0.00) FROM GL_1002_STG WHERE PROC_CODE
			IN('01','82') AND MAIN_REV_IND='MAIN'
			AND VAL_DATE BETWEEN ? AND ? AND concat(CUS_NUM,SUB_ACCT_CODE)=? AND
			CUR_CODE='1')-
			(SELECT ISNULL(SUM(TRA_AMT),0.00) FROM GL_1002_STG WHERE PROC_CODE
			IN('01','82') AND MAIN_REV_IND='REVERSAL'
			AND VAL_DATE BETWEEN ? AND ? AND concat(CUS_NUM,SUB_ACCT_CODE)=? AND
			CUR_CODE='1')) RESULT

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1074">
		<name>GL_1002_GL_CUT_OFF</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM
			GL_1002_STG WHERE
			TRAN_DATE &gt;= (SELECT MAX(END_DATE) FROM BCS_ATM_REPL WHERE END_DATE=? AND
			DISPLAYID=?)
			AND TRA_TIME &gt;=(SELECT MAX(END_TIME) FROM BCS_ATM_REPL WHERE
			END_DATE=? AND DISPLAYID=?)
			AND TRA_DATE=? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND  RETR_REF_NO NOT IN (SELECT RETR_REF_NO  FROM GL_1002_STG WHERE MAIN_REV_IND='REVERSAL')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1075">
		<name>GL_1002_END_DATE_TIME</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT MAX(END_DATETIME) FROM BCS_ATM_REPL WHERE DISPLAYID=? AND END_DATE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_1006 TREMINAL BALANCING REPORT -->
	
  <query id="1076">
		<name>GL_1006_TERMINALS</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM FROM MDT_ATM_SUSPENSE_ACCOUNTS WHERE LEDGER='1006' AND CUR='1' AND ACTIVE_INDEX='Y' ORDER BY ATM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1077">
		<name>GL_1006_ATM_LOCATION</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM_LOCATION,CUR FROM MDT_ATM_SUSPENSE_ACCOUNTS WHERE ATM=? AND LEDGER='1006' AND ACTIVE_INDEX='Y'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1078">
		<name>GL_1006_ADJMENTS_DISPUTES</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT (-(SELECT ISNULL(SUM(TRA_AMT),0.0) AS ADJ FROM GL_1006_STG_EX
			WHERE TELL_ID NOT IN('9971','9953','9911') AND DEB_CRE_IND=1 
			AND REMARKS NOT LIKE 'DEP ATM%' AND VAL_DATE BETWEEN ? AND ?
			AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)+
			(SELECT ISNULL(SUM(TRA_AMT),0.0) AS ADJ FROM GL_1006_STG_EX
			WHERE TELL_ID NOT IN('9971','9953','9911') AND DEB_CRE_IND=2 AND VAL_DATE BETWEEN ? AND ?
			AND REMARKS NOT LIKE 'DEP ATM%' AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)) ADJ
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1079">
		<name>GL_1006_CASH_RECEIVED_FROM_ATM</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ISNULL(SUM(CONVERT(DECIMAL(20,2),TRA_AMT)),0.00) as TOTAL
			FROM GL_1006_STG_EX WHERE TELL_ID='9930' AND CHARINDEX('DEP ATM',REMARKS)!=0
			AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1080">
		<name>GL_1006_CLOSING_BALANCE_AS_PER_FILE</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE LED_CODE='1006' AND BRA_CODE='201'
			AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND PRE_BANK_DATE= ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1081">
		<name>GL_1006_OPENING_BALANCE</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE LED_CODE='1006' AND BRA_CODE='201'
			 AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND PRE_BANK_DATE=DATEADD(DD,-1,?)

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1082">
		<name>GL_1006_LESS_TRANSACTIONS</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT((SELECT ISNULL(SUM(-TRA_AMT),0.00) FROM GL_1006_STG WHERE   MAIN_REV_IND='MAIN'
			AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)-
		   (SELECT ISNULL(ISNULL(SUM(-TRA_AMT),0),0.00) FROM GL_1006_STG WHERE  MAIN_REV_IND='REVERSAL'
		    AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)) RESULT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<query id="1083">
		<name>GL_1006_GL_CUT_OFF</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1006_STG WHERE
			TRAN_DATE&gt;=(SELECT MAX(END_DATE) FROM BCS_ATM_REPL WHERE END_DATE=? AND DISPLAYID=?)
			AND TRA_TIME&gt;=(SELECT MAX(END_TIME) FROM BCS_ATM_REPL WHERE END_DATE=? AND DISPLAYID=?)
			AND TRA_DATE=? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1084">
		<name>GL_1006_END_DATE_TIME</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT MAX(END_DATETIME) FROM BCS_ATM_REPL WHERE DISPLAYID=? AND END_DATE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- password policy queries  start added by ankush 7/10/2017 4:20PM-->
	    <query id="19">
        <name>INSERT_PASS_POLICY</name>
        <queryType>INSERT</queryType>
        <queryString>
insert into password_policy(title,minLength,maxLength,pwdMaxAge,pwdExpiryWarning,pwdMaxFailure,allowUserToChangeOwnPwd,isSpecialCharsAllowed
,isUpperCaseAllowed,isNumbersAllowed,specialChars,created_on,updated_on,version,Active_Index
,status,workflowStatus,activity_comments,use_policy)
values(?,?,?,?,?,?,?,?,?,? ,?,?,?,?,?,?,?,?,?)
		
		</queryString>
        <queryParam>title@VARCHAR,minLength@BIGINT,maxLength@BIGINT,pwdMaxAge@BIGINT,
pwdExpiryWarning@BIGINT,pwdMaxFailure@BIGINT,allowUserToChangeOwnPwd@VARCHAR,
isSpecialCharsAllowed@VARCHAR,isUpperCaseAllowed@VARCHAR,isNumbersAllowed@VARCHAR,
specialChars@VARCHAR,created_on@TIMESTAMP,updated_on@TIMESTAMP,version@BIGINT,
Active_Index@VARCHAR,status@VARCHAR,workflowStatus@VARCHAR,activity_comments@VARCHAR,use_policy@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
	
	 <query id="19">
        <name>UPDATE_PASS_POLICY</name>
        <queryType>UPDATE</queryType>
        <queryString>
update  password_policy set title=?,minLength=?,maxLength=?,pwdMaxAge=?,pwdExpiryWarning=?,pwdMaxFailure=?,
allowUserToChangeOwnPwd=?,isSpecialCharsAllowed=?,isUpperCaseAllowed=?,isNumbersAllowed=?,specialChars=?,
updated_on=?,version=?,Active_Index=?,status=?,workflowStatus=?,activity_comments=?,use_policy=?
where (VERSION=(SELECT MAX(VERSION )FROM password_policy WHERE id=?)) and id=?
		
		</queryString>
        <queryParam>title@VARCHAR,minLength@BIGINT,maxLength@BIGINT,pwdMaxAge@BIGINT,
pwdExpiryWarning@BIGINT,pwdMaxFailure@BIGINT,allowUserToChangeOwnPwd@VARCHAR,
isSpecialCharsAllowed@VARCHAR,isUpperCaseAllowed@VARCHAR,isNumbersAllowed@VARCHAR,
specialChars@VARCHAR,updated_on@TIMESTAMP,version@BIGINT,
Active_Index@VARCHAR,status@VARCHAR,workflowStatus@VARCHAR,activity_comments@VARCHAR,use_policy@VARCHAR,id@BIGINT,id@BIGINT</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    
    	 <query id="19">
        <name>DELETE_PASS_POLICY</name>
        <queryType>DELETE</queryType>
        <queryString>
	  	  update password_policy set Active_Index='N' , status='DELETED' where (VERSION=(SELECT MAX(VERSION )FROM password_policy WHERE id=?)) and id=?  and use_policy!='Y'
		</queryString>
        <queryParam>id@BIGINT,id@BIGINT</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
     	 <query id="19">
        <name>DEACTIVATE_ALL_PASS_POLICY</name>
        <queryType>UPDATE</queryType>
        <queryString>
update password_policy set use_policy='N'  
		
		</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    
         	 <query id="19">
        <name>UPDATE_INACTIVE_USER_STATUS</name>
        <queryType>UPDATE</queryType>
        <queryString>
UPDATE users SET account_status='INACTIVE',updated_on=GETDATE()  WHERE 
 user_id IN(
SELECT distinct user_id FROM User_Audit  where  action='LOGOUT'
GROUP BY user_id
HAVING DATEDIFF(DAY,MAX(date_time),GETDATE())>=45) and account_status='ACTIVE';
insert into User_Audit(user_id,action,date_time,user_role,bussiness_area,recon_name) 
select 'SYSTEM' as userid,'disabled' +'"'+ USER_ID +'"' +'byAscent' as action,updated_on,system_role,'Support','Support' from users where account_status='INACTIVE'
and status='APPROVED' and active_index='Y';
update users set active_index='N' where account_status='INACTIVE';
update users set account_status='DISABLED' where account_status='INACTIVE';
		</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
         	 <query id="19">
        <name>VALIDATE_RESET_PASSWORD</name>
        <queryType>SELECT</queryType>
        <queryString>
			SELECT user_name,password,user_id FROM USERS WHERE user_id=? and status='APPROVED' and active_index='Y' and 
				account_status='ACTIVE' and isLdapUser='N' and version=(select max(version) from users where user_id=?)
		</queryString>
        <queryParam>user_Id@VARCHAR,user_Id@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
	<!--  -->
	
	
	
	
	
			<!-- tejaswi  reports-->
	<query id="7800">
		<name>RECONCILED_UTILITYPAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			RECON_SIDE,TRA_DATE,ACCOUNT_NUMBER,CHANNEL,TRA_REF_NO,TRA_CATEGORY,MAIN_REV_IND,TRA_AMT,
			CASE WHEN TRA_STATUS='00' THEN 'APPROVED' ELSE TRA_STATUS END AS
			TRA_STATUS
			FROM UTILITY_PAYMENTS_RECON WITH (NOLOCK)
			where (MATCH_TYPE IN ('AM','MM') ) AND ACTIVE_INDEX='Y' AND TRA_DATE
			BETWEEN ? AND ? ORDER BY RECON_SIDE

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>



	<query id="550">
		<name>UNRECONCILED_UTILITYPAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			RECON_SIDE,TRA_DATE,ACCOUNT_NUMBER,CHANNEL,TRA_REF_NO,TRA_CATEGORY,MAIN_REV_IND,TRA_AMT,CASE
			WHEN TRA_STATUS='00' THEN 'APPROVED' ELSE TRA_STATUS END AS
			TRA_STATUS,COMMENTS
			FROM UTILITY_PAYMENTS_RECON WITH (NOLOCK) where (MATCH_TYPE IN
			('AU','MU')) AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ? ORDER
			BY RECON_SIDE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="550">
		`
		<name>ORPHAN_UTILITYPAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			CONSUMER_NUM,ACCOUNT_NUM,CONNECTION_TYPE,TRA_ID,CHANNEL,TRA_DATE,TRA_AMT,REF_NUM,TRA_REF_NO
			FROM UBP_STG WITH (NOLOCK) WHERE (RECON_ID IS NULL OR RECON_STATUS IS
			NULL) AND TRA_AMT > 0 AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND
			?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="550">
		`
		<name>SUMMARY_UTILITYPAYMENT_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT TRA_CATEGORY,count(*) as COUNT ,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT
			FROM UTILITY_PAYMENTS_RECON WITH (NOLOCK) WHERE TRA_DATE BETWEEN ?
			AND ? group by TRA_CATEGORY


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="7800">
		<name>RECONCILED_TRADE_SUSPENS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			RECON_SIDE,BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NUM,TRA_AMT,TRA_DATE,SHORT_NAME_ENG
			AS NAME,MAIN_REV_IND
			FROM TRADE_SUSPENS_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AM','MM')
			AND ACTIVE_INDEX='Y' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="7800">
		<name>SUMMARY_BALNACE_HOME</name>
		<queryType>SELECT</queryType>
		<queryString>
		
select OL_SL_NO,sum(debit-credit) as BALANCE,OS_ACCOUNT_NAME  from 
(SELECT t1.GLCode,t1.debit,case when t2.credit is null
			then 0.0 else t2.credit end as credit,CASE WHEN t2.OD_TRANS_DATE IS
			NULL THEN T1.OD_TRANS_DATE ELSE T2.OD_TRANS_DATE END AS OD_TRANS_DATE
			,t1.FACILITY_NUMBER,t1.BRANCH_CODE
			, datediff( day ,CASE WHEN t2.OD_TRANS_DATE IS NULL THEN
			T1.OD_TRANS_DATE ELSE T2.OD_TRANS_DATE END , getDate()) as AGE,T1.OS_ACCOUNT_NAME,t1.OL_SL_NO FROM
			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE
			as varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))+
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount as debit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807' 
			AND ol_amount&gt;0 and FACILITY_NUMBER!='') T1
			Left Join
			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE as
			varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))+
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount*-1 as credit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807' 
						AND ol_amount&lt;0and FACILITY_NUMBER!='') T2 ON
			T1.BRANCH_CODE=t2.BRANCH_CODE and
			t1.FACILITY_NUMBER=t2.FACILITY_NUMBER
			and t1.GLCode=t2.GLCode
			WHERE (t1.debit!=t2.credit or T2.credit is null)

			union


			SELECT t2.GLCode,case when t1.debit is null then 0.0 else t1.debit end as
			credit, t2.credit,t2.OD_TRANS_DATE,t2.FACILITY_NUMBER,t2.BRANCH_CODE
			, datediff( day ,t2.OD_TRANS_DATE , getDate())AS AGE ,T2.OS_ACCOUNT_NAME,t2.OL_SL_NO FROM


			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE as
			varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))+
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount*-1 as credit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807' 
			AND ol_amount &lt; 0 and FACILITY_NUMBER!='') T2
			left join
			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE as
			varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount as debit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807'  
			AND ol_amount &gt;0 and FACILITY_NUMBER!='') T1

			ON T2.BRANCH_CODE=t1.BRANCH_CODE and
			t2.FACILITY_NUMBER=t1.FACILITY_NUMBER
			and t2.GLCode=t1.GLCode
			WHERE
			(t2.credit!=t1.debit or T1.debit is null)) a   group by OL_SL_NO,OS_ACCOUNT_NAME
		
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
		<query id="7800">
		<name>DISTINCT_SL_NO_HOME</name>
		<queryType>SELECT</queryType>
		<queryString>
			select distinct SL_NO,AREA  FROM TBL_RETAIL_ASSET_MDATA WHERE GL_CODE='110807'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="7800">
		<name>SUMMARY_HOME</name>
		<queryType>SELECT</queryType>
		<queryString>
		
		
select *,debit-credit as balance  from 
(SELECT t1.GLCode,t1.debit,case when t2.credit is null
			then 0.0 else t2.credit end as credit,CASE WHEN t2.OD_TRANS_DATE IS
			NULL THEN T1.OD_TRANS_DATE ELSE T2.OD_TRANS_DATE END AS OD_TRANS_DATE
			,t1.FACILITY_NUMBER,t1.BRANCH_CODE
			, datediff( day ,CASE WHEN t2.OD_TRANS_DATE IS NULL THEN
			T1.OD_TRANS_DATE ELSE T2.OD_TRANS_DATE END , getDate()) as AGE,T1.OS_ACCOUNT_NAME,t1.OL_SL_NO FROM
			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE
			as varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))+
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount as debit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807' 
			AND ol_amount&gt;0 and FACILITY_NUMBER!='') T1
			Left Join
			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE as
			varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))+
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount*-1 as credit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807' 
						AND ol_amount&lt;0and FACILITY_NUMBER!='') T2 ON
			T1.BRANCH_CODE=t2.BRANCH_CODE and
			t1.FACILITY_NUMBER=t2.FACILITY_NUMBER
			and t1.GLCode=t2.GLCode
			WHERE (t1.debit!=t2.credit or T2.credit is null)

			union


			SELECT t2.GLCode,case when t1.debit is null then 0.0 else t1.debit end as
			credit, t2.credit,t2.OD_TRANS_DATE,t2.FACILITY_NUMBER,t2.BRANCH_CODE
			, datediff( day ,t2.OD_TRANS_DATE , getDate())AS AGE ,T2.OS_ACCOUNT_NAME,t2.OL_SL_NO FROM


			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE as
			varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))+
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount*-1 as credit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807' 
			AND ol_amount &lt; 0 and FACILITY_NUMBER!='') T2
			left join
			(SELECT cast(t.OL_BRANCH_CODE as varchar(20))+'-'+cast(t.OL_CURRENCY_CODE as varchar(20))+ '-'+cast(t.OL_GL_CODE as
			varchar(20))
			+'-'+cast(t.OL_CIF_NO as varchar(20))
			+'-'+cast(t.OL_SL_NO as varchar(20)) AS GLCode,
			ol_amount as debit,
			OD_TRANS_DATE,FACILITY_NUMBER,BRANCH_CODE,OS_ACCOUNT_NAME,OL_SL_NO
			FROM RETAIL_ASSET_STG t WHERE ol_gl_code='110807'  
			AND ol_amount&gt;0 and FACILITY_NUMBER!='') T1

			ON T2.BRANCH_CODE=t1.BRANCH_CODE and
			t2.FACILITY_NUMBER=t1.FACILITY_NUMBER
			and t2.GLCode=t1.GLCode
			WHERE
			(t2.credit!=t1.debit or T1.debit is null)) a order by age  desc
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
			<query id="7800">
		<name>DISTINCT_SL_NO_HOME</name>
		<queryType>SELECT</queryType>
		<queryString>
			select distinct SL_NO,AREA  FROM TBL_RETAIL_ASSET_MDATA WHERE GL_CODE='110807'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
			<query id="7800">
		<name>SUMMARY_AUTOFINANACE_BALANCE</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SUM(BALANCE) as balance, OS_ACCOUNT_NAME FROM SUMMARY_AUTOFINANCE (?) GROUP BY OS_ACCOUNT_NAME
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="7800">
		<name>SUMMARY_AUTOFINANACE</name>
		<queryType>SELECT</queryType>
		<queryString>
	SELECT * FROM SUMMARY_AUTOFINANCE (?) ORDER BY AGE DESC
		
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>FINANCE_GL</name>
		<queryType>SELECT</queryType>
		<queryString>

		select distinct GL_CODE from TBL_RETAIL_ASSET_MDATA  where GL_CODE not in ('110807')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>DISTINCT_SL_NO_AUTOFINANCE</name>
		<queryType>SELECT</queryType>
		<queryString>
		select  SL_NO from TBL_RETAIL_ASSET_MDATA where GL_CODE =?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	

	<query id="7800">
		<name>UNRECONCILED_TRADE_SUSPENS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

			SELECT
			RECON_SIDE,BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NUM,TRA_AMT,TRA_DATE,SHORT_NAME_ENG
			AS NAME,MAIN_REV_IND,COMMENTS
			FROM TRADE_SUSPENS_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AU','MU')
			AND ACTIVE_INDEX='Y' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="7800">
		<name>ORPHAN_TRADE_SUSPENSE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

			SELECT
			BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NO,OP_NO,TRA_DATE,AMOUNT,MAIN_REV_IND
			FROM TRADE_SUSPENS_CSM_STG WITH (NOLOCK)
			WHERE (RECON_ID IS NULL OR RECON_STATUS IS NULL) AND AMOUNT>0 AND
			ACTIVE_INDEX='Y' 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
<query id="544">
		<name>ORPHAN_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>


			SELECT
			OL_BRANCH_CODE,OL_CURRENCY_CODE,OL_GL_CODE,OD_TRANS_DATE,OS_DESCRIPTION,MAIN_REV_IND,
			OL_AMOUNT,REF_NUM,BRANCH_CODE

			FROM RETAIL_ASSET_STG WITH (NOLOCK) WHERE (RECON_ID IS NULL OR
			RECON_STATUS IS NULL) AND OL_AMOUNT>0 AND ACTIVE_INDEX='Y' AND
			OD_TRANS_DATE BETWEEN ? AND ?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<!-- <query id="545">
		<name>DEBIT_RECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>


			SELECT
			TRA_DATE,TRA_AMT,BRANCH_CODE,TRA_CUR,DEB_CRE_IND,FACILITY_NUMBER,RECON_SIDE
			FROM RETAIL_ASSET_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AM','MM')
			AND DEB_CRE_IND='D' AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
			order by TRA_DATE,DEB_CRE_IND

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->

	<!-- <query id="545">
		<name>CREDIT_RECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			TRA_DATE,TRA_AMT,BRANCH_CODE,TRA_CUR,DEB_CRE_IND,FACILITY_NUMBER,RECON_SIDE
			FROM RETAIL_ASSET_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AM','MM')
			AND DEB_CRE_IND='C' AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
			order by TRA_DATE,DEB_CRE_IND

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->

	<!-- <query id="511">
		<name>BALANCED_RECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			(SELECT isnull(SUM(TRA_AMT),0.00) AS AMOUNT
			FROM RETAIL_ASSET_RECON WITH (NOLOCK) WHERE DEB_CRE_IND='C' AND (
			MATCH_TYPE IN ('AM','MM')) AND ACTIVE_INDEX='Y' and TRA_DATE BETWEEN
			? AND ?) -
			(SELECT isnull(SUM(TRA_AMT),0.00) AS AMOUNT
			FROM RETAIL_ASSET_RECON WITH (NOLOCK) WHERE DEB_CRE_IND='D' AND (
			MATCH_TYPE IN ('AM','MM')) AND ACTIVE_INDEX='Y' and TRA_DATE BETWEEN
			? AND ?)
			AS DIFF

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->


	<query id="7800">
		<name>ATM_DEBIT_CARD_RECONCILED_TERMINAL_ID</name>
		<queryType>SELECT</queryType>
		<queryString>
                SELECT DISTINCT   TREMINAL_ID FROM ONUS_ATM_DEBIT_RECON 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>
	  <query id="7800">
		<name>ATM_DEBIT_OUR_BANK</name>
		<queryType>SELECT</queryType>
		<queryString>
select SOURCE,ATM_ID,STAN,STAN,TRANSACTION_DATE,CARD_NO,TXN_TYPE,RRN,AMOUNT from
	(		SELECT 'ATM_GL'AS SOURCE,OS_TERM_ID AS ATM_ID, OS_STAN AS
	STAN,TRANSACTION_DATE,OS_CARD_NO AS CARD_NO,OS_TRAN_TYPE AS
	TXN_TYPE,RRN,AMOUNT FROM ATM_GL_STG WHERE SUBSTRING(OS_CARD_NO,1,6) IN
	('534417','539150','549184') AND OS_TRX_STATUS='APPROVED'AND ACTIVE_INDEX='Y' 
	UNION

	SELECT 'EJ' AS SOURCE,ATM_ID,STAN, TRA_DATE AS TRANSACTION_DATE,PAN AS
	CARD_NO ,TXN_TYPE,RRN,AMOUNT FROM EJ_STG WHERE SUBSTRING(PAN,1,6) IN (
	'534417','539150','549184') AND STATUS='APPROVED' AND ACTIVE_INDEX='Y'
	
	 )OUR_BANK  WHERE TRANSACTION_DATE BETWEEN ? AND ? AND  ATM_ID=? order by TXN_TYPE

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>
	  	  <query id="7800">
		<name>ATM_DEBIT_OTHER_BANK</name>
		<queryType>SELECT</queryType>
		<queryString>
	select SOURCE,ATM_ID,STAN,STAN,TRANSACTION_DATE,CARD_NO,TXN_TYPE,RRN,AMOUNT from
	(		SELECT 'ATM_GL'AS SOURCE,OS_TERM_ID AS ATM_ID, OS_STAN AS
	STAN,TRANSACTION_DATE,OS_CARD_NO AS CARD_NO,OS_TRAN_TYPE AS
	TXN_TYPE,RRN,AMOUNT FROM ATM_GL_STG WHERE SUBSTRING(OS_CARD_NO,1,6)  NOT IN
	('534417','539150','549184') AND OS_TRX_STATUS='APPROVED'AND ACTIVE_INDEX='Y' 
	UNION

	SELECT 'EJ' AS SOURCE,ATM_ID,STAN, TRA_DATE AS TRANSACTION_DATE,PAN AS
	CARD_NO ,TXN_TYPE,RRN,AMOUNT FROM EJ_STG WHERE SUBSTRING(PAN,1,6) NOT  IN (
	'534417','539150','549184') AND STATUS='APPROVED' AND ACTIVE_INDEX='Y'
	 )OTHER_BANK  WHERE TRANSACTION_DATE BETWEEN ? AND ? AND  ATM_ID=?   order by TXN_TYPE

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>
	  	  <query id="7800">
		<name>ATM_DEBIT_ERROR_OTHER_BANK</name>
		<queryType>SELECT</queryType>
		<queryString>
select SOURCE,ATM_ID,STAN,STAN,TRANSACTION_DATE,CARD_NO,TXN_TYPE,RRN,AMOUNT from
	(	SELECT    'ATM_GL'AS SOURCE, OS_TERM_ID AS ATM_ID, OS_STAN AS STAN,TRANSACTION_DATE,OS_CARD_NO AS CARD_NO,OS_TRAN_TYPE AS TXN_TYPE,RRN,AMOUNT FROM ATM_GL_STG WHERE  SUBSTRING(OS_CARD_NO,1,6) NOT  IN (
'534417','539150','549184') AND OS_TRX_STATUS!='APPROVED'AND ACTIVE_INDEX='Y' 
UNION
	SELECT   'EJ' AS SOURCE,ATM_ID,STAN, TRA_DATE AS TRANSACTION_DATE,PAN AS CARD_NO ,TXN_TYPE,RRN,AMOUNT FROM EJ_STG WHERE  SUBSTRING(PAN,1,6) NOT IN (
'534417','539150','549184') AND STATUS!='APPROVED'AND ACTIVE_INDEX='Y'  )ERROR_OTHER_BANK  WHERE TRANSACTION_DATE BETWEEN ? AND ? AND ATM_ID=? order by TXN_TYPE

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>
	    	  <query id="7800">
		<name>ATM_DEBIT_ERROR_OUR_BANK</name>
		<queryType>SELECT</queryType>
		<queryString>
select SOURCE,ATM_ID,STAN,STAN,TRANSACTION_DATE,CARD_NO,TXN_TYPE,RRN,AMOUNT from
	(	SELECT    'ATM_GL'AS SOURCE, OS_TERM_ID AS ATM_ID, OS_STAN AS STAN,TRANSACTION_DATE,OS_CARD_NO AS CARD_NO,OS_TRAN_TYPE AS TXN_TYPE,RRN,AMOUNT  FROM ATM_GL_STG WHERE  SUBSTRING(OS_CARD_NO,1,6)   IN (
'534417','539150','549184') AND OS_TRX_STATUS!='APPROVED'AND ACTIVE_INDEX='Y' 
UNION
	SELECT   'EJ' AS SOURCE,ATM_ID,STAN, TRA_DATE AS TRANSACTION_DATE,PAN AS CARD_NO ,TXN_TYPE,RRN,AMOUNT FROM EJ_STG WHERE  SUBSTRING(PAN,1,6) IN (
'534417','539150','549184') AND STATUS!='APPROVED'AND ACTIVE_INDEX='Y' 
	
	 ) ERROR_OUR_BANK  WHERE TRANSACTION_DATE BETWEEN ? AND ? AND  ATM_ID=?   order by TXN_TYPE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>
	  
	  <query id="7800">
		<name>ATM_DEBIT_CARD_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
          		    select RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,PAN,RRN
			   from ONUS_ATM_DEBIT_RECON  WITH (NOLOCK)
			  where tra_date between ? and ?
			    and TREMINAL_ID= ? and MATCH_TYPE IN ('AM','MM')   AND ACTIVE_INDEX='Y' AND ACTIVITY_STATUS='APPROVED'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>
	  
	  <query id="7801">
		<name>ATM_DEBIT_CARDRECON_NONFINANCIAL_TRX_SEGREGATION</name>
		<queryType>SELECT</queryType>
		<queryString>

	 select TXN_TYPE,AMOUNT,COUNT from
	(select TXN_TYPE,sum(AMOUNT) as AMOUNT,count(TXN_TYPE) as COUNT
	FROM EJ_STG WITH (NOLOCK) where TXN_TYPE NOT IN ('FUNDS TRANSFER - OPEN ENDED','FUNDS TRANSFER - INTER BANK',
	'OPEN ENDED CARD DEPOSIT','CASH DEPOSIT',
	'WITHDRAWAL','BILL PAYMENT','FUNDS TRANSFER- INTER BANK',
	'FUNDS TRANSFER - OWN ACCOUNT','ATM Deposit')   and  status='APPROVED' 
	and  TRA_DATE BETWEEN ? AND ? group by TXN_TYPE

	union

	SELECT  OS_TRAN_TYPE AS TXN_TYPE,sum(AMOUNT) as AMOUNT,count(OS_TRAN_TYPE) as COUNT
	FROM ATM_GL_STG WITH (NOLOCK)
	 where OS_TRAN_TYPE NOT IN ('FUNDS TRANSFER - OPEN ENDED','FUNDS TRANSFER - INTER BANK','OPEN ENDED CARD DEPOSIT',
	 'CASH DEPOSIT','WITHDRAWAL','BILL PAYMENT','FUNDS TRANSFER- INTER BANK',
	'FUNDS TRANSFER - OWN ACCOUNT','ATM Deposit') 
	 and  OS_TRX_STATUS='APPROVED' and  transaction_date BETWEEN ? AND ?
	  group by OS_TRAN_TYPE) Nonfinacial

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="7801">
		<name>ATM_DEBIT_CARDRECON_FINANCIAL_TRX_SEGREGATION</name>
		<queryType>SELECT</queryType>
		<queryString>

	select TXN_TYPE,AMOUNT,COUNT from
	(select TXN_TYPE,sum(AMOUNT) as AMOUNT,count(TXN_TYPE) as COUNT
	FROM EJ_STG WITH (NOLOCK) where TXN_TYPE IN ('OPEN ENDED CARD DEPOSIT','CASH DEPOSIT','WITHDRAWAL','BILL PAYMENT',
	'FUNDS TRANSFER- INTER BANK','FUNDS TRANSFER - OPEN ENDED',
	'FUNDS TRANSFER - OWN ACCOUNT','ATM Deposit')   and  status='APPROVED' 
	 and  TRA_DATE BETWEEN ? AND ? group by TXN_TYPE
	union
	SELECT  OS_TRAN_TYPE,sum(AMOUNT) as AMOUNT,count(OS_TRAN_TYPE) as COUNT
	FROM ATM_GL_STG WITH (NOLOCK)
	 where OS_TRAN_TYPE IN ('OPEN ENDED CARD DEPOSIT','CASH DEPOSIT','WITHDRAWAL','BILL PAYMENT','FUNDS TRANSFER- INTER BANK',
	 'FUNDS TRANSFER - OPEN ENDED',
	'FUNDS TRANSFER - OWN ACCOUNT','ATM Deposit') 
	 and  OS_TRX_STATUS='APPROVED' and  transaction_date BETWEEN ? AND ?
	  group by OS_TRAN_TYPE) finacial

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	  <query id="7800">
		<name>ATM_DEBIT_CARD_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
                select RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,PAN,RRN,COMMENTS
			   from ONUS_ATM_DEBIT_RECON WITH (NOLOCK) where tra_date between ? and ?
			    and TREMINAL_ID= ? and MATCH_TYPE IN ('AU','MU')   AND ACTIVE_INDEX='Y' AND ACTIVITY_STATUS='APPROVED'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	  </query>

	<query id="7801">
		<name>ATM_DEBIT_CARDRECON_ORPAHAN</name>
		<queryType>SELECT</queryType>
		<queryString>
			select CURRENCY,STAN,SEQ,TXN_TYPE,TRA_DATE,PAN,AMOUNT,RRN from
			ONUS_ATM_DEBIT_EJ_STG_ORPHANS_WITH_REVERSAL WITH (NOLOCK)
			WHERE TRA_DATE BETWEEN ? AND ? and ATM_ID=?  AND STATUS='APPROVED'

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
		<query id="7801">
		<name>ATM_DEBIT_CARDRECON_NONFINANCIAL</name>
		<queryType>SELECT</queryType>
		<queryString>	
	select SOURCE,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT from
	(	select 'EJ' AS SOURCE ,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT FROM EJ_STG WITH (NOLOCK) where
	TXN_TYPE IN ('PIN VALIDATION','PIN CHANGE','ACCT DETAIL','BALANCE INQUIRY','INVALID TRAN') 
	and  status='APPROVED'  
	UNION
	SELECT 'ATM_GL'AS SOURCE ,OS_TERM_ID AS ATM_ID,STAN,TRANSACTION_DATE AS TRA_DATE,OS_TRAN_TYPE AS TXN_TYPE, RRN, OS_CARD_NO AS PAN,AMOUNT
	FROM ATM_GL_STG WITH (NOLOCK)
	 where OS_TRAN_TYPE IN ('PIN VALIDATION','PIN CHANGE','ACCT DETAIL','BALANCE INQUIRY','INVALID TRAN') AND
	OS_TRX_STATUS='APPROVED'   )nonfinacial  where  TRA_DATE BETWEEN ? AND ?  AND ATM_ID=? order by TXN_TYPE
	</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	 <query id="7801">
		<name>ATM_DEBIT_CARDRECON_FINANCIAL</name>
		<queryType>SELECT</queryType>
		<queryString>

	select SOURCE,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT from
	(select 'EJ' AS SOURCE ,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT
	FROM EJ_STG WITH (NOLOCK) where TXN_TYPE IN ('CASH DEPOSIT','WITHDRAWAL','BILL PAYMENT','FUNDS TRANSFER- INTER BANK','FUNDS TRANSFER - OPEN ENDED',
	'FUNDS TRANSFER - OWN ACCOUNT')   and  status='APPROVED' 
	union
	SELECT  'ATM_GL'AS SOURCE ,OS_TERM_ID AS ATM_ID,OS_STAN AS STAN,TRANSACTION_DATE AS TRA_DATE,OS_TRAN_TYPE AS TXN_TYPE, RRN, OS_CARD_NO AS PAN,AMOUNT
	FROM ATM_GL_STG WITH (NOLOCK)
	 where OS_TRAN_TYPE IN ('ATM Deposit','CASH DEPOSIT','WITHDRAWAL','BILL PAYMENT','FUNDS TRANSFER- INTER BANK','FUNDS TRANSFER - OPEN ENDED',
	'FUNDS TRANSFER - OWN ACCOUNT')  and  OS_TRX_STATUS='APPROVED'   )finacial  where  TRA_DATE BETWEEN ? AND ? AND ATM_ID=? order by TXN_TYPE

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
		<query id="7801">
		<name>ATM_DEBIT_CARDRECON_WITHDRAWAL</name>
		<queryType>SELECT</queryType>
		<queryString>
	select SOURCE,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT,RECON_STATUS from
	(select 'EJ' AS SOURCE ,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT,RECON_STATUS
	FROM EJ_STG WITH (NOLOCK) where TXN_TYPE IN ('WITHDRAWAL','FUNDS TRANSFER- INTER BANK','FUNDS TRANSFER - OPEN ENDED',
	'FUNDS TRANSFER - OWN ACCOUNT')   and  status='APPROVED' 
	union
	SELECT  'ATM_GL'AS SOURCE ,OS_TERM_ID AS ATM_ID,OS_STAN AS STAN,TRANSACTION_DATE AS TRA_DATE,OS_TRAN_TYPE AS TXN_TYPE, RRN, OS_CARD_NO AS PAN,AMOUNT,RECON_STATUS
	FROM ATM_GL_STG WITH (NOLOCK)
	 where OS_TRAN_TYPE IN ('WITHDRAWAL','FUNDS TRANSFER- INTER BANK','FUNDS TRANSFER - OPEN ENDED','ATM WithDrawal',
	'FUNDS TRANSFER - OWN ACCOUNT')  and  OS_TRX_STATUS='APPROVED'   )WITHDRAWAL  where  TRA_DATE BETWEEN ? AND ? AND ATM_ID=? order by TXN_TYPE,RECON_STATUS

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="7801">
		<name>ATM_DEBIT_CARDRECON_DEPOSIT</name>
		<queryType>SELECT</queryType>
		<queryString>

	select SOURCE,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT,RECON_STATUS from
	(select 'EJ' AS SOURCE ,ATM_ID,STAN,TRA_DATE,TXN_TYPE,RRN,PAN,AMOUNT,RECON_STATUS
	FROM EJ_STG WITH (NOLOCK) where TXN_TYPE IN ('CASH DEPOSIT')   and  status='APPROVED' 
	union
	SELECT  'ATM_GL'AS SOURCE ,OS_TERM_ID AS ATM_ID,OS_STAN AS STAN,TRANSACTION_DATE AS TRA_DATE,OS_TRAN_TYPE AS TXN_TYPE, RRN, OS_CARD_NO AS PAN,AMOUNT,RECON_STATUS
	FROM ATM_GL_STG WITH (NOLOCK)
	 where OS_TRAN_TYPE IN ('ATM Deposit','CASH DEPOSIT')  and  OS_TRX_STATUS='APPROVED'   )DEPOSIT  where  TRA_DATE BETWEEN ? AND ? AND ATM_ID=? order by TXN_TYPE,RECON_STATUS


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="546">
		<name>UNRECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			TRA_DATE,TRA_AMT,BRANCH_CODE,TRA_CUR,DEB_CRE_IND,FACILITY_NUMBER,RECON_SIDE,COMMENTS
			FROM RETAIL_ASSET_RECON WITH (NOLOCK) WHERE  MATCH_TYPE IN ('AU','MU') AND ACTIVE_INDEX='Y' AND
			TRA_DATE BETWEEN ? AND ?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>



	<query id="563">
		<name>RECONCILED_TRADEMARGIN_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

			SELECT
			RECON_SIDE,BRANCH_CODE,CUR_CODE,GL_CODE,TRA_AMT,TRA_DATE,MAIN_REV_IND,CIF_NUM
			FROM TRADE_MARGIN_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>UNRECONCILED_TRADEMARGIN_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT
			RECON_SIDE,BRANCH_CODE,CUR_CODE,GL_CODE,TRA_AMT,TRA_DATE,MAIN_REV_IND,CIF_NUM,COMMENTS
			FROM TRADE_MARGIN_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AU','MU')
			AND ACTIVE_INDEX='Y' and (TRA_DATE BETWEEN ? AND ?) AND
			RECON_SIDE='TM_GL'

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ORPHAN_LG_TRADEMARGIN_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT REF_NUM,CIF_NUM,AMOUNT AS TRA_AMT,MAIN_REV_IND,AMENDED_FC_AMOUNT AS
			FC_AVL_BAL,TRA_STATUS,APPLICANT_NAME

			FROM LG_STG WITH (NOLOCK) WHERE (RECON_ID IS NULL OR RECON_STATUS IS NULL
			or RECON_STATUS in ('AU','MU')) AND AMOUNT>0 and ACTIVE_INDEX='Y' AND
			TRA_STATUS='APPROVED'

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="563">
		<name>ORPHAN_LC_TRADEMARGIN_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

			SELECT BRANCH,DYNAMIC_REF, CIF_NUM,TFSLC_LC_CURRENCY AS
			CUR_CODE,CURRENCY_BRIEF_DESC_ENG AS CURRENCY,MARGIN_AMT_CV AS
			TRA_AMT,AMENDED_FC_AMOUNT AS FC_AVL_BAL,MAIN_REV_IND
			FROM LC_STG WITH (NOLOCK) WHERE (RECON_ID IS NULL OR RECON_STATUS IS NULL
			or RECON_STATUS in ('AU','MU')) AND MARGIN_AMT_CV>0 AND
			TRA_STATUS='APPROVED'

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
 <query id="7800">
		<name>BALANCE_ECC_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT A.OL_BRANCH_CODE,A.OD_TRANS_DATE,A.OL_CURRENCY_CODE,A.CHEQUE_NO,
			B.BAMOUNT,A.AAMOUNT,ISNULL(B.BAMOUNT,0)-ISNULL(A.AAMOUNT,0) AS
			BALANCEAMOUNT FROM
		
			(SELECT OL_BRANCH_CODE,OD_TRANS_DATE,OL_CURRENCY_CODE,CHEQUE_NO,SUM(OL_AMOUNT)
			AS AAMOUNT
			FROM A_CBO_IMAL_STG
			GROUP BY OL_BRANCH_CODE,OD_TRANS_DATE,OL_CURRENCY_CODE,CHEQUE_NO) A 
			
			INNER JOIN
			
			(SELECT TRX_TYPE,CHEQUE_NO,CHQ_POSTING_DATE,MAIN_REV_IND,SUM(CHQ_AMOUNT) AS
			BAMOUNT
			FROM ECC_STG
			GROUP BY TRX_TYPE,CHEQUE_NO,CHQ_POSTING_DATE,MAIN_REV_IND) B ON
			A.CHEQUE_NO=B.CHEQUE_NO
			WHERE OD_TRANS_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
    
	
	<!-- CENTRAL -->
	<!-- VIJAYASRI -->

	<query id="213">
		<name>RECONCILED_CENTRALBANK_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select TRA_DATE,TRA_AMT,
			DEB_CRE_IND,
			TRA_CUR,REF_NUM,MAIN_REV_IND from
			CBO_FT_RECON_MATCH WITH (NOLOCK)
			WHERE MATCH_TYPE IN ('AM','MM') AND ACTIVE_INDEX='Y'
			AND TRA_DATE between ? and ? ORDER BY DEB_CRE_IND
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>UNRECONCILED_IMAL_CENTRALBANK_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT TRA_DATE,DEB_CRE_IND,
			REF_NUM,TRA_CUR,
			SOURCE_TARGET,COMMENTS,TRA_AMT
			FROM CBO_FT_RECON_UNMATCH WITH (NOLOCK)
			WHERE TRA_DATE BETWEEN ? AND ?
			and RECON_SIDE='IMAL_CBO_FT' ORDER BY DEB_CRE_IND 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	     </query>
<!-- vijayasri -->
	<!-- central -->
	
	<query id="213">
		<name>UNRECONCILED_CENTRALBANK_ACH</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT Debit_Credit,
			Batch_amount,Value_Date,REF_NUM,
			Batch_Reference,
			Instructionamount,Instruction_reference
			from ACH_STG WHERE
			(RECON_ID IS NULL OR RECON_STATUS IS NULL) and
			VALUE_DATE BETWEEN ? AND ?
			ORDER BY Debit_Credit
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="213">
		<name>UNREONCILED_CENTRALBANK_RTGS</name>
		<queryType>Select</queryType>
		<queryString>
			select Value_Date,
			MAIN_REV_IND,
			REF_NUM,Turnover,WORKFLOW_STATUS FROM
			RTGS_STG WITH (NOLOCK)
			WHERE (RECON_ID IS NULL OR RECON_STATUS IS NULL) AND
			Value_Date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="213">
		<name>BALANCE_CENTRALBANK_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT B.OD_TRANS_DATE,B.MAIN_REV_IND,B.OL_CURRENCY_CODE,
			B.BAMOUNT,A.AAMOUNT, ISNULL(B.BAMOUNT,0)-ISNULL(A.AAMOUNT,0) as
			BALANCEAMOUNT FROM
			(select Batch_amount,REF_NUM,VALUE_DATE,SUM(Instructionamount) AS AAMOUNT from
			CBO_FT_ACH_STG CB WITH (NOLOCK)
			group by REF_NUM,VALUE_DATE,Batch_amount) AS A inner join
		
			(select
			OD_TRANS_DATE,REF_NUM,OL_BRANCH_CODE,MAIN_REV_IND,OL_CURRENCY_CODE,SUM(OL_AMOUNT)
			AS BAMOUNT
			FROM CBO_FT_IMAL_CBO_FT_STG WITH (NOLOCK)
			GROUP BY REF_NUM,OD_TRANS_DATE,OL_BRANCH_CODE,MAIN_REV_IND,OL_CURRENCY_CODE)
			B ON A.REF_NUM=B.REF_NUM
			WHERE OD_TRANS_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>SUMMARY_BODY_CENTRALBANK_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			 SELECT REF_NUM,COUNT(Instructionamount) AS COUNT,SUM(Instructionamount) AS AMOUNT 
			 FROM CBO_FT_ACH_STG WITH (NOLOCK) where 
			 Value_Date  between ? and ? GROUP BY REF_NUM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>SUMMARY_HEADER_CENTRALBANK_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			 SELECT REF_NUM,COUNT(OL_AMOUNT) AS COUNT,SUM(OL_AMOUNT) AS AMOUNT
			  FROM CBO_FT_IMAL_CBO_FT_STG WITH (NOLOCK) 
			  GROUP BY REF_NUM 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	

	<!-- OMANET -->
	<!-- VIJAYASRI -->
	<query id="213">
		<name>UNRECONCILED_OMANET_IRIS_RECON</name>
		<queryType>Select</queryType>
			<queryString>
			select TRA_DATE,TRA_AMT,
			TRAN_CUR,RET_REF_NO,SOURCE_TARGET,COMMENTS
			FROM ISSUER_ONS_IMAL_RECON_UNMATCH WITH (NOLOCK)
			where TRA_DATE between ? and ?
			order by DEB_CRE_IND
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="213">
		<name>SUMMARY_OMANET_LOCAL_CBO_TRX_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
		select  TRANSACTION_TYPE, sum(TRANSACTION_AMOUNT) as TRANSACTION_AMOUNT from
        ISSUER_ONS_IMAL_CBO_STG  where  CURRENCY_CODE not in('512','414','634','682','048','784')
        and SYSTEM_OWNER_BUISINESS_DATE between ? and ?
        group by  TRANSACTION_TYPE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>UNRECONCILED_OMANET_CBO_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT TRANSACTION_TYPE,TRANSACTION_AMOUNT AS OL_AMOUNT,
			SYSTEM_OWNER_BUISINESS_DATE AS OD_TRANS_DATE,RECORD_TYPE
			FROM ISSUER_ONS_IMAL_CBO_STG_ORPHANS WITH (NOLOCK)
			where SYSTEM_OWNER_BUISINESS_DATE between ? and ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="213">
		<name>UNRECONCILED_OMANET_IMAL_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			 select
			OL_AMOUNT,COALESCE(NULLIF(OD_TRANS_DATE,''),'NA') AS OD_TRANS_DATE,
		    DEB_CRE_IND,RET_REF_NUM,pan,MAIN_REV_IND
			from ISSUER_ONS_IMAL_ONS_IMAL_STG_ORPHANS WITH (NOLOCK)
			WHERE OD_TRANS_DATE BETWEEN ? AND ?
			order by OD_VALUE_DATE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>RECONCILED_OMANET_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select TRA_DATE,TRA_AMT,DEB_CRE_IND,
			TRAN_CUR,LOCAL_AMT
			FROM ISSUER_ONS_IMAL_RECON_MATCH WITH (NOLOCK)
			where TRA_DATE between ? and ? order by DEB_CRE_IND
		</queryString>
		<queryParam></queryParam>
	</query>
	
	<query id="213">
		<name>SUMMARY_OMANET_GCC_CBO_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT CURRENCY_CODE, COUNT(TRANSACTION_AMOUNT) AS
			COUNT,SUM(TRANSACTION_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_CBO_STG
			where CURRENCY_CODE in('512','414','634','682','048','784')
			and SYSTEM_OWNER_BUISINESS_DATE BETWEEN ? AND ?
			GROUP BY CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IRIS_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT LOCAL_CURRENCY, COUNT(TRX_AMOUNT) AS
			COUNT,SUM(TRX_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_IRIS_STG
			where LOCAL_CURRENCY  in('512','414','634','682','048','784')
			and TRA_DATE BETWEEN ? AND ?
			GROUP BY LOCAL_CURRENCY
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IMAL_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT OL_CURRENCY_CODE, COUNT(OL_AMOUNT) AS
			COUNT,SUM(OL_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_ONS_IMAL_STG
			where OL_CURRENCY_CODE  in('512','414','634','682','048','784')
			 and OD_TRANS_DATE BETWEEN ? AND ?
			GROUP BY OL_CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_CBO_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT CURRENCY_CODE, COUNT(TRANSACTION_AMOUNT) AS
			COUNT,SUM(TRANSACTION_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_CBO_STG
			where CURRENCY_CODE not in('512','414','634','682','048','784')
			 and SYSTEM_OWNER_BUISINESS_DATE BETWEEN ? AND ?
			GROUP BY CURRENCY_CODE 
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IRIS_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT LOCAL_CURRENCY, COUNT(TRX_AMOUNT) AS
			COUNT,SUM(TRX_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_IRIS_STG
			where LOCAL_CURRENCY not in('512','414','634','682','048','784')
			 and TRA_DATE BETWEEN ? AND ?
			GROUP BY LOCAL_CURRENCY
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IMAL_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT OL_CURRENCY_CODE, COUNT(OL_AMOUNT) AS
			COUNT,SUM(OL_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_ONS_IMAL_STG
			where OL_CURRENCY_CODE not in('512','414','634','682','048','784')
			 and OD_TRANS_DATE BETWEEN ? AND ?
			GROUP BY OL_CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
	</query>

	
	<query id="213">
		<name>RECONCILED_MASTERCREDITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			 SELECT TRA_AMT,TRA_DATE,DEB_CRE_IND,SOURCE_TARGET,
			 MAIN_REV_IND,CARD_NUM FROM CC_RECON WITH (NOLOCK) where (MATCH_TYPE IN ('AM','MM') )  AND ACTIVE_INDEX='Y'
			 and TRA_DATE BETWEEN ? AND ?
			 ORDER BY DEB_CRE_IND
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>UNRECONCILED_MASTERCREDITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT TRA_AMT,TRA_DATE,DEB_CRE_IND,SOURCE_TARGET,COMMENTS,
			MAIN_REV_IND,CARD_NUM FROM CC_RECON WITH (NOLOCK)
			where (MATCH_TYPE IN ('AU','MU') ) AND ACTIVE_INDEX='Y'
			and TRA_DATE BETWEEN ? AND ?
			ORDER BY DEB_CRE_IND
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>ORPHAN_WITH_REVERSAL_MASTERCREDITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select TRA_AMT,TRA_DATE,SEQ_NUM,BATCH_NUM,
			TRA_CODE,REC_NUM,MAIN_REV_IND from 
			MCC_PAYMENTS_POOL_STG_ORPHANS_WITH_REVERSAL WITH (NOLOCK)
			where TRA_DATE between ? and ?
			order by SEQ_NUM
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>MASTERCREDITCARD_RECON_STG</name>
		<queryType>Select</queryType>
		<queryString>
	 SELECT * FROM (Select D.CARD_NUMBER,D.BANK_IDENTIFICATION,D.BANK_ACCOUNT,D.ORG_MSG_TYPE,D.MSG_TYPE,D.PROC_CDOE,D.BILL_CUR,
	D.BILL_CUR_DECIMAL,D.BILL_AMOUNT,D.DB_CR_IND,D.post_date,M.DECRIPTION from DAILY_GL_TRXN_STG D left JOIN MDBDES M
	ON D.PROC_CDOE=M.PROCODE)AS A
	where  A.post_date between ? and ?
		</queryString>
		<queryParam></queryParam>
		
	</query>
	
		<query id="213">
		<name>BALANCE_MASTERCREDITCARD</name>
		<queryType>Select</queryType>
		<queryString>
	SELECT A.amount,A.PROC_CDOE,A.DB_CR_IND,B.DECRIPTION FROM (
				select 

			 CASE WHEN DB_CR_IND='DB' THEN SUM(bill_amount)
					ELSE -SUM(bill_amount) END as amount ,PROC_CDOE,DB_CR_IND
			 from DAILY_GL_TRXN_STG WITH (NOLOCK) where  post_date between ? and ? group by PROC_CDOE,DB_CR_IND  ) A 
			  left JOIN MDBDES B ON A.PROC_CDOE = B.PROCODE  order by  a.PROC_CDOE
		</queryString>
		<queryParam></queryParam>
	</query>

	<query id="213">
		<name>BALANCING_MASTERCREDITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select a.POST_date AS AFS_POST_DATE,a.BILL_CUR AS AFS_BILL_CUR,a.PROC_CDOE as AFS_PROC_CODE,
				A.amount AS AFS_BILL_AMOUNT,A.DB_CR_IND AS AFS_DEB_CRE_IND,
				B.TRA_DATE AS PRODUCT_IMAL_TRA_DATE,B.TRA_CUR AS PRODUCT_IMAL_CUR,B.TRA_CODE AS PRODUCT_IMAL_TRA_CODE,
				B.amount AS PRODUCT_IMAL_TRA_AMT,B.DEB_CRE_IND AS PRODUCT_IMAL_DEB_CRE_IND,
				B.TRA_DESCRIPTION AS PRODUCT_IMAL_TRA_DESC,
				ISNULL(A.amount,0)-ISNULL(B.amount,0.0) AS BALANCING_AMOUNT
				from(
				(select POST_date,proc_cdoe,bill_cur,sum(bill_amount) as amount,db_cr_ind from DAILY_GL_TRXN_STG
				group by POST_date,proc_cdoe,bill_cur,db_cr_ind) as a LEFT OUTER join
				 
				(select tra_date,tra_code,sum(tra_amt) as amount,tra_cur,deb_cre_ind,TRA_DESCRIPTION from cc_gl_stg group by
				tra_date,tra_code,tra_cur,deb_cre_ind,TRA_DESCRIPTION) as b
				on a.PROC_CDOE=b.TRA_CODE and substring(a.DB_CR_IND,1,1)!=b.DEB_CRE_IND
				) where POST_date between ? and ?
		</queryString>
		<queryParam></queryParam>
	</query>
	
		
	    <query id="213">
		<name>UNRECONCILED_IMAL_MASTERDEBITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
		 select CARD_NO,AMOUNT,RRN,STAN,TRX_TYPE_DESC from IMAL_STG with (nolock)
             where (RECON_ID is null or RECON_STATUS is null)  
  and CREATED_DATE between ? and ? order by TRX_TYPE_DESC
		</queryString>
		<queryParam></queryParam>
	    </query>
		
	    <query id="213">
		<name>UNRECONCILED_IRIS_MASTERDEBITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT RET_REF_NO,MAIN_REV_IND,TRA_DATE,TRX_AMOUNT,TRX_TYPE,TRX_CURRENCY
 			FROM IRIS_STG WITH (NOLOCK) WHERE (RECON_ID IS NULL or RECON_STATUS IS NULL)
  			AND TRA_DATE BETWEEN ? AND ? and NETWORK='MASTERCARD'
		</queryString>
		<queryParam></queryParam>
	    </query>
	
	<!-- end query for report -->
	  
	  <!-- CENTRAL BALANCE REPORT QUERIES -->
	
              <query id="213">
                <name>BALANCE_CENTRAL_IMAL_COL_RECON</name>
                <queryType>Select</queryType>
			    <queryString>
				select OS_DESCRIPTION,OL_AMOUNT,OD_TRANS_DATE from IMAL_CBO_FT_STG 
                where  OD_TRANS_DATE between ? and ?
                </queryString>
                <queryParam></queryParam>
        </query>
        <query id="213">
                <name>BALANCE_CENTRAL_IMAL_OPENINGBAL_RECON</name>
                <queryType>Select</queryType>
                <queryString>
                  select OL_AMOUNT from IMAL_CBO_FT_STG where OS_DESCRIPTION like '**B/F Balance'
                  and OD_VALUE_DATE=?  
                </queryString>
                <queryParam></queryParam>
        </query>
        <query id="213">
                <name>BALANCE_CENTRAL_IMAL_CLOSINGBAL_RECON</name>
                <queryType>Select</queryType>
			    <queryString>
				select sum(OL_AMOUNT) as ImalAmount from IMAL_CBO_FT_STG
				where OS_DESCRIPTION not like '**B/F Balance' and OD_VALUE_DATE between ?
				and ?
                </queryString>
                <queryParam></queryParam>
        </query>
        <query id="213">
                <name>BALANCE_CENTRAL_ACHRTGS_COL_RECON</name>
                <queryType>Select</queryType>
			     <queryString>
				select participant,Debit_Credit,Instructionamount,SID from ACH_STG where 
				Value_Date between ? and ?
				union  
				select Account_Participant_Name,DEB_CRE_IND,Deponent_Name,SID from RTGS_STG 
				where VALUE_DATE between ? and ?
                </queryString>
                <queryParam></queryParam>
        </query>
        <query id="213">
                <name>BALANCE_CENTRAL_RTGS_OPENINGBAL_RECON</name>
                <queryType>Select</queryType>
                <queryString>
                   select Deponent_Name from RTGS_STG where TRN like '%CLOSING_BALANCE%' and 
                    VALUE_DATE=?
                </queryString>
                <queryParam></queryParam>
        </query>
        <query id="213">
                <name>BALANCE_CENTRAL_RTGS_CLOSINGBAL_RECON</name>
                <queryType>Select</queryType>
			     <queryString>
					SELECT SUM(A.AMOUNT ) AS AMOUNT FROM 
					 (
					 SELECT Deponent_Name AS AMOUNT FROM RTGS_STG   where trn = '**BALANCE' AND Value_Date =?
					 UNION
					 select  SUM(Instructionamount)AS AMOUNT
					 
					  from ACH_STG  where Value_Date BETWEEN ? AND ?
					  UNION
					
					 select  SUM(case when DEB_CRE_IND LIKE'D%' THEN (Deponent_Name*-1)  WHEN DEB_CRE_IND LIKE 'C%' THEN Note 
					 ELSE 0.0 END)AS AMOUNT
					 
					  from RTGS_STG where trn  not like '**BALANCE %' and  Value_Date BETWEEN ? AND ?
					 ) A 
                </queryString>
                <queryParam></queryParam>
        </query>
        <query id="213">
                <name>BALANCE_CENTRAL_ACH_COL_RECON</name>
                <queryType>Select</queryType>
                <queryString>        
				select Participant,Instructionamount,Debit_Credit from CBO_FT_ACH_STG
				where Value_Date between ? and ?
                </queryString>
                <queryParam></queryParam>
        </query> 
        <!-- OMANET -->
	<!-- ModifiedSummaryReport -->
	
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IMAL_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT OL_CURRENCY_CODE, COUNT(OL_AMOUNT) AS
			COUNT,SUM(OL_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_ONS_IMAL_STG
			where OL_CURRENCY_CODE  in('512','414','634','682','048','784')
			 and OD_TRANS_DATE BETWEEN ? AND ?
			GROUP BY OL_CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IRIS_RECON</name>
		<queryType>Select</queryType>
					<queryString>
					select LOCAL_CURRENCY,TRX_TYPE,COUNT(TRX_AMOUNT) as
				COUNT,SUM(TRX_AMOUNT) AS AMOUNT
				from ISSUER_ONS_IMAL_IRIS_STG where TRX_TYPE='PURCHASE'
				and LOCAL_CURRENCY in('512','414','634','682','048','784')
				and TRA_DATE BETWEEN ? AND ?
				GROUP BY LOCAL_CURRENCY,TRX_TYPE
			
				union
			
				select LOCAL_CURRENCY,TRX_TYPE,COUNT(TRX_AMOUNT) as COUNT,SUM(TRX_AMOUNT) AS
				AMOUNT
				from ISSUER_ONS_IMAL_IRIS_STG where TRX_TYPE='WITHDRAWAL'
				and LOCAL_CURRENCY in('512','414','634','682','048','784')
				and TRA_DATE BETWEEN ? AND ?
				GROUP BY LOCAL_CURRENCY,TRX_TYPE
			
				union
			
				select LOCAL_CURRENCY,TRX_TYPE,COUNT(TRX_AMOUNT) as COUNT,SUM(TRX_AMOUNT) AS
				AMOUNT
				from ISSUER_ONS_IMAL_IRIS_STG where TRX_TYPE='BI'
				and LOCAL_CURRENCY in('512','414','634','682','048','784')
				and TRA_DATE BETWEEN ? AND ?
				GROUP BY LOCAL_CURRENCY,TRX_TYPE
					
					
					
					</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_CBO_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select TRANSACTION_TYPE,CURRENCY_CODE,sid, COUNT(TRANSACTION_AMOUNT) AS
			COUNT,SUM(TRANSACTION_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_CBO_STG                                      
			where CURRENCY_CODE in('512','414','634','682','048','784')
			and SYSTEM_OWNER_BUISINESS_DATE BETWEEN ? AND ?
			GROUP BY TRANSACTION_TYPE,CURRENCY_CODE,sid
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IMAL_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			 SELECT OL_CURRENCY_CODE, COUNT(OL_AMOUNT) AS
			COUNT,SUM(OL_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_ONS_IMAL_STG
			where OL_CURRENCY_CODE not in('512','414','634','682','048','784')       
			 and OD_TRANS_DATE BETWEEN ? AND ?
			GROUP BY OL_CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IRIS_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select LOCAL_CURRENCY,TRX_TYPE,COUNT(TRX_AMOUNT) as
			COUNT,SUM(TRX_AMOUNT) AS AMOUNT
			from ISSUER_ONS_IMAL_IRIS_STG where TRX_TYPE='PURCHASE'
			and LOCAL_CURRENCY not in('512','414','634','682','048','784')
			and TRA_DATE BETWEEN ? AND ?
			GROUP BY LOCAL_CURRENCY,TRX_TYPE
		
			union
		
			select LOCAL_CURRENCY,TRX_TYPE,COUNT(TRX_AMOUNT) as COUNT,SUM(TRX_AMOUNT) AS
			AMOUNT
			from ISSUER_ONS_IMAL_IRIS_STG where TRX_TYPE='WITHDRAWAL'
			and LOCAL_CURRENCY not in('512','414','634','682','048','784')
			and TRA_DATE BETWEEN ? AND ?
			GROUP BY LOCAL_CURRENCY,TRX_TYPE
		
			union
		
			select LOCAL_CURRENCY,TRX_TYPE,COUNT(TRX_AMOUNT) as COUNT,SUM(TRX_AMOUNT) AS
			AMOUNT
			from ISSUER_ONS_IMAL_IRIS_STG where TRX_TYPE='BI'
			and LOCAL_CURRENCY not in('512','414','634','682','048','784')
			and TRA_DATE BETWEEN ? AND ?
			GROUP BY LOCAL_CURRENCY,TRX_TYPE
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_CBO_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select TRANSACTION_TYPE,CURRENCY_CODE,sid, COUNT(TRANSACTION_AMOUNT) AS
			COUNT,SUM(TRANSACTION_AMOUNT) AS AMOUNT
			FROM ISSUER_ONS_IMAL_CBO_STG                                      
			where CURRENCY_CODE not in('512','414','634','682','048','784')
			and SYSTEM_OWNER_BUISINESS_DATE BETWEEN ? AND ?
			GROUP BY TRANSACTION_TYPE,CURRENCY_CODE,sid
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IRIS_PURCHASE_SEGREGATION</name>
		<queryType>Select</queryType>
		 <queryString>
			select sum(TRX_AMOUNT) as PurchaseAmount from ISSUER_ONS_IMAL_IRIS_STG
			where TRX_TYPE='PURCHASE' and LOCAL_CURRENCY
			in('512','414','634','682','048','784')
			and TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IRIS_WITHDRAWAL_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
		select sum(TRX_AMOUNT) as WithdrawalAmount from
		ISSUER_ONS_IMAL_IRIS_STG
		where TRX_TYPE='WITHDRAWAL' and LOCAL_CURRENCY
		in('512','414','634','682','048','784')
		and TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_GCC_IRIS_BI_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
			select sum(TRX_AMOUNT) as BIAmount from ISSUER_ONS_IMAL_IRIS_STG
			where TRX_TYPE='BI' and LOCAL_CURRENCY
			in('512','414','634','682','048','784')
			and TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IRIS_PURCHASE_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
		select sum(TRX_AMOUNT) as PurchaseAmount from ISSUER_ONS_IMAL_IRIS_STG
		where TRX_TYPE='PURCHASE' and LOCAL_CURRENCY not
		in('512','414','634','682','048','784')
		and TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IRIS_WITHDRAWAL_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
		select sum(TRX_AMOUNT) as WithdrawalAmount from
		ISSUER_ONS_IMAL_IRIS_STG
		where TRX_TYPE='WITHDRAWAL' and LOCAL_CURRENCY not
		in('512','414','634','682','048','784')
		and TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	</query>
	<query id="213">
		<name>SUMMARY_OMANET_LOCAL_IRIS_BI_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
		select sum(TRX_AMOUNT) as BIAmount from ISSUER_ONS_IMAL_IRIS_STG
		where TRX_TYPE='BI' and LOCAL_CURRENCY not
		in('512','414','634','682','048','784')
		and TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	</query>
	  
	  
	  
	  <!-- MASTERDEBITCARD -->
	<!-- VIJAYASRI -->
	<query id="213">
		<name>RECONCILED_MASTERDEBITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			select TRA_AMT,TRA_DATE,SOURCE_TARGET,MAIN_REV_IND,RET_REF_NO
			from MATM_RECON WITH (NOLOCK)
			WHERE MATCH_TYPE IN ('AM','MM') AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ?
			AND ?
		</queryString>
		<queryParam></queryParam>
	    </query>
	<query id="213">
		<name>UNRECONCILED_IPM_MASTERDEBITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
			   select RMR_COMP_TRANS_AMT,RMR_REC_AMT,RMR_COMP_CODE,
			   MAIN_REV_IND,RMR_CURR_CODE,RMR_REC_CURR
			   from IPM_STG WITH (NOLOCK) where (RECON_ID is null 
			   or RECON_STATUS is null) and RMR_TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
	    </query>
	    <query id="213">
		<name>UNRECONCILED_IMAL_MASTERDEBITCARD_RECON</name>
		<queryType>Select</queryType>
		<queryString>
		 select CARD_NO,AMOUNT,RRN,STAN,TRX_TYPE_DESC from IMAL_STG with (nolock)
             where (RECON_ID is null or RECON_STATUS is null)  
         and CREATED_DATE between ? and ? order by TRX_TYPE_DESC
		</queryString>
		<queryParam></queryParam>
	    </query> 
	     <query id="213">
		<name>UNRECONCILED_IMAL_MASTERDEBITCARD_RECON_TRX_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
			  select TRX_TYPE_DESC,sum(AMOUNT) as AMOUNT,count(TRX_TYPE_DESC) as COUNT from IMAL_STG with (nolock)
			  where (RECON_ID is null or RECON_STATUS is null) 
			and CREATED_DATE between ? and ?
			group by TRX_TYPE_DESC
		</queryString>
		<queryParam></queryParam>
	    </query> 
	    <query id="213">
		<name>UNRECONCILED_IRIS_MASTERDEBITCARD_RECON_TRX_SEGREGATION</name>
		<queryType>Select</queryType>
		<queryString>
            select TRX_TYPE,sum(TRX_AMOUNT) TRX_AMOUNT,count(TRX_TYPE) as COUNT
			from IRIS_STG
			where (RECON_ID is null or RECON_STATUS is null)  and
			NETWORK='MASTERCARD' and TRA_DATE between ? and ?
			group by TRX_TYPE
		</queryString>
		<queryParam></queryParam>
	    </query>
	
	
	    <query id="213">
		<name>MDB_CODES</name>
		<queryType>Select</queryType>
		<queryString>
	select Trx_code,RevTrx_code from MDB_CODES
		</queryString>
		<queryParam></queryParam>
	    </query>
	
	 <!-- SOURCEWISE CBO REPORTS END -->
	
	
	<query id="7800">
		<name>RECONCILED_ECC_IMAL_SOURCE_CREDITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
		
	
			SELECT * FROM ECC_CREDITS_REPORT (?,?) order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>RECONCILED_ECC_IMAL_SOURCE_DEBITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
    
 			SELECT * FROM ECC_DEBITS_REPORT (?,?) order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>RECONCILED_ECC_CBO_IMAL_SOURCE_CREDITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	

 			SELECT * FROM ECC_IMAL_CREDITS_REPORT (?,?) order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>RECONCILED_ECC_CBO_IMAL_SOURCE_DEBITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
   
 			SELECT * FROM ECC_IMAL_DEBITS_REPORT (?,?) order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- <query id="7800">
		<name>UNRECONCILED_ECC_IMAL_SOURCE_CREDITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	

  select CHQ_POSTING_DATE AS TRA_DATE,case when CHQ_AMOUNT &lt;0 THEN -CHQ_AMOUNT ELSE CHQ_AMOUNT END AS TRA_AMT,CHEQUE_NO ,BENEF_BANK from
 ECC_STG where (RECON_STATUS is null or RECON_STATUS IN ('AU','MU'))  and TRX_TYPE = 'OUTWARD' and CHQ_POSTING_DATE between ? and ? AND  REASON NOT IN ('001-Insufficient Fund','Deleted Cheque')
  order by TRA_AMT,CHEQUE_NO

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<!-- <query id="7800">
		<name>UNRECONCILED_ECC_IMAL_SOURCE_DEBITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	

 select CHQ_POSTING_DATE AS TRA_DATE,case when CHQ_AMOUNT &lt;0 THEN -CHQ_AMOUNT ELSE CHQ_AMOUNT END AS TRA_AMT,CHEQUE_NO ,BENEF_BANK from
	ECC_STG where (RECON_STATUS is null or RECON_STATUS IN ('AU','MU')) and TRX_TYPE = 'INWARD' and
	CHQ_POSTING_DATE between ? and ? AND  REASON NOT IN ('001-Insufficient Fund','Deleted Cheque')
	order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<!-- <query id="7800">
		<name>UNRECONCILED_ECC_CBO_IMAL_SOURCE_CREDITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
	
		 select OD_TRANS_DATE AS TRA_DATE,case when OL_AMOUNT &lt;0 THEN -OL_AMOUNT ELSE OL_AMOUNT END AS TRA_AMT,CHEQUE_NO AS
		CHEQUE_NO,OS_DESCRIPTION AS DESCRIPTION from
		CBO_IMAL_STG where RECON_STATUS is null and OL_LINE_NO = '2' and OD_TRANS_DATE
		between ? and ?
		order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<!-- <query id="7800">
		<name>UNRECONCILED_ECC_CBO_IMAL_SOURCE_DEBITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
		
		 select OD_TRANS_DATE AS TRA_DATE,case when OL_AMOUNT &lt;0 THEN -OL_AMOUNT ELSE OL_AMOUNT END AS TRA_AMT,CHEQUE_NO AS
			CHEQUE_NO,OS_DESCRIPTION AS DESCRIPTION from
			CBO_IMAL_STG where RECON_STATUS is null and OL_LINE_NO = '1' and OD_TRANS_DATE
			between ? and ?
			order by TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
    
    <!-- CBO -->
     <query id="213">
        <name>RECONCILED_CBO_IMAL_CREDITS_RECON</name>
                <queryType>Select</queryType>
				<queryString>
				select TRA_DATE,TRA_AMT,DEB_CRE_IND,case when
				SOURCE_TARGET='IMAL_CBO_FT_STG' then 'IMAL' end as SOURCE_TARGET,
				CAST(UPDATED_ON AS DATE) AS RECONCILED_DATE,
				MAIN_REV_IND from CBO_FT_RECON with (NOLOCK)
				WHERE MATCH_TYPE IN ('AM','MM') AND DEB_CRE_IND IN ('C')
				and SOURCE_TARGET='IMAL_CBO_FT_STG'
				AND TRA_DATE BETWEEN ? AND ? order by TRA_DATE,TRA_AMT
                </queryString>
                <queryParam></queryParam>
        </query> 
        <query id="213">
                <name>RECONCILED_CBO_IMAL_DEBITS_RECON</name>
                <queryType>Select</queryType>
				<queryString>
					select TRA_DATE,TRA_AMT,DEB_CRE_IND,case when
					SOURCE_TARGET='IMAL_CBO_FT_STG' then 'IMAL' end as SOURCE_TARGET,
					CAST(UPDATED_ON AS DATE) AS RECONCILED_DATE,
					MAIN_REV_IND from CBO_FT_RECON with (NOLOCK)
					WHERE MATCH_TYPE IN ('AM','MM') AND DEB_CRE_IND IN ('D')
					and SOURCE_TARGET='IMAL_CBO_FT_STG'
					AND TRA_DATE BETWEEN ? AND ? order by TRA_DATE,TRA_AMT
                </queryString>
                <queryParam></queryParam>
        </query>        
        <query id="213">
                <name>RECONCILED_CBO_RTGS_CREDITS_RECON</name>
                <queryType>Select</queryType>
				<queryString>
				   select TRA_DATE,TRA_AMT,DEB_CRE_IND,case when  SOURCE_TARGET='RTGS_STG' then 'RTGS' end as SOURCE_TARGET,
               CAST(UPDATED_ON AS DATE) AS RECONCILED_DATE,
				MAIN_REV_IND from CBO_FT_RECON with (nolock)
				WHERE MATCH_TYPE  IN ('AM','MM') AND DEB_CRE_IND IN ('C')
				and SOURCE_TARGET='RTGS_STG'
				AND TRA_DATE BETWEEN ? AND ? order by TRA_DATE,TRA_AMT

                </queryString>
                <queryParam></queryParam>
        </query> 
        <query id="213">
                <name>RECONCILED_CBO_RTGS_DEBITS_RECON</name>
                <queryType>Select</queryType>
				<queryString>
				 select TRA_DATE,TRA_AMT,DEB_CRE_IND,case when  SOURCE_TARGET='RTGS_STG' then 'RTGS' end as SOURCE_TARGET,
                CAST(UPDATED_ON AS DATE) AS RECONCILED_DATE,
				MAIN_REV_IND from CBO_FT_RECON   with (nolock)                                                
				WHERE MATCH_TYPE  IN ('AM','MM') AND DEB_CRE_IND IN ('D')
				and SOURCE_TARGET='RTGS_STG'
				AND TRA_DATE BETWEEN ? AND ? order by TRA_DATE,TRA_AMT
                </queryString>
                <queryParam></queryParam>
        </query> 
	
	
	
<!-- 	TEJASWI ADDED -->
	 <query id="24">
		<name>PAYMENT_ORDER_RECONCILED_IMLO_PAYMENTS_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>
	select TRANS_DATE,  case when AMOUNT &lt;0 then -amount end as AMOUNT, OP_NO, CHQ_NO,CAST(UPDATED_ON AS DATE) AS RECONCILED_DATE,
DESCRIPTION
 from IMLO_PAYMENTS_STG with (nolock) where description not like '**B/F Balance'
 and RECON_STATUS in ('AM','MM')  and amount &lt; 0
 and TRANS_DATE between ? and ? order by  AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="24">
		<name>PAYMENT_ORDER_RECONCILED_IMLO_PAYMENTS_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>
		 select TRANS_DATE, AMOUNT, OP_NO, CHQ_NO, CAST(UPDATED_ON AS DATE) AS RECONCILED_DATE,DESCRIPTION
 from IMLO_PAYMENTS_STG with (nolock) where DESCRIPTION not like '**B/F Balance'
  and RECON_STATUS in ('AM','MM')and amount &gt; 0
 and TRANS_DATE between ? and ? order by  AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<!-- 
<query id="24">
		<name>PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>

			select TRANS_DATE, case when amount &lt; 0 THEN -AMOUNT ELSE AMOUNT
			END AS AMOUNT , OP_NO, CHQ_NO,DESCRIPTION
			from IMLO_PAYMENTS_STG with (nolock) where description not like '**B/F Balance'
			and (RECON_STATUS not in ('AM','MM') or RECON_STATUS is null) and amount
			&gt; 0
			and TRANS_DATE between ? and ? order by  AMOUNT
		
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<!-- <query id="24">
		<name>PAYMENT_ORDER_OPENINGBAL_IMLO_PAYMENTS</name>
		<queryType>Select</queryType>
		<queryString>
			select  top 1 AMOUNT from IMLO_PAYMENTS_STG with (nolock) where DESCRIPTION like '**B/F Balance'
 and TRANS_DATE between ? and ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<!-- <query id="24">
		<name>BALANCE_UNRECONCIED</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT (A.OPENING_BALANCE)+B.AMOUNT as AMOUNT FROM 
			(select top 1 AMOUNT AS OPENING_BALANCE from IMLO_PAYMENTS_STG with (nolock) where DESCRIPTION like
			'**B/F Balance'  and TRANS_DATE between ? and ?)A,
				
			(select sum(AMOUNT)AS AMOUNT 
			from IMLO_PAYMENTS_STG with (nolock) where description not like '**B/F Balance'
			and (RECON_STATUS not in ('AM','MM') or RECON_STATUS is null) and TRANS_DATE between ? and ?)B
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<!-- <query id="24">
		<name>PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>

			select TRANS_DATE, case when amount &lt; 0 THEN -AMOUNT ELSE AMOUNT END AS
			AMOUNT , OP_NO, CHQ_NO,DESCRIPTION
			from IMLO_PAYMENTS_STG with (nolock) where description not like '**B/F Balance'
			and (RECON_STATUS not in ('AM','MM') or RECON_STATUS is null) and amount &lt; 0
			and TRANS_DATE between ? and ? order by  AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	<query id="543">
		<name>BALANCED_PAYMENTORDER_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

			SELECT A.TRA_AMT AS DEBIT_AMONT,A.CHQ_NO,B.TRA_AMT
			CREDIT_AMOUNT,A.TRA_DATE AS DEBIT_DATE,B.TRA_DATE AS CREDIT_DATE
			,A.TRA_CUR,A.TRA_AMT-B.TRA_AMT AS BALANCE
			FROM
			(SELECT
			TRA_AMT,RECON_SIDE,DEB_CRE_IND,TRA_CUR,CHQ_NO,TRA_DATE FROM
			PAYMENT_ORDER_RECON WITH (NOLOCK) WHERE DEB_CRE_IND='D') A
			INNER JOIN
			(SELECT TRA_AMT,RECON_SIDE,DEB_CRE_IND,TRA_CUR,CHQ_NO,TRA_DATE FROM
			PAYMENT_ORDER_RECON WITH (NOLOCK) WHERE DEB_CRE_IND='C') B
			ON
			A.CHQ_NO=B.CHQ_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		
		<query id="24">
		<name>ATM_GL_OURBANK_CARDNO</name>
		<queryType>Select</queryType>
		<queryString>
		select TRANSACTION_DATE AS  TRA_DATE,OS_CARD_NO AS CARD_NO,AMOUNT,OS_TRAN_TYPE  AS TXN_TYPE from ATM_GL_STG 
		 where SUBSTRING(OS_CARD_NO,1,6) in ('534417','539150','549184') 
		 AND TRANSACTION_DATE BETWEEN ? AND ?  AND OS_TERM_ID=? AND OS_TRX_STATUS='APPROVED' AND ACTIVE_INDEX='Y' ORDER BY AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="24">
		<name>ATM_GL_OTHERBANK_CARDNO</name>
		<queryType>Select</queryType>
		<queryString>
	select TRANSACTION_DATE AS  TRA_DATE,OS_CARD_NO AS CARD_NO,AMOUNT,OS_TRAN_TYPE  AS TXN_TYPE from ATM_GL_STG 
		 where SUBSTRING(OS_CARD_NO,1,6)  not in ('534417','539150','549184') 
		 AND TRANSACTION_DATE BETWEEN ? AND ?  AND OS_TERM_ID=? AND OS_TRX_STATUS='APPROVED' AND ACTIVE_INDEX='Y'  ORDER BY AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="24">
		<name>EJ_OURBANK_CARDNO</name>
		<queryType>Select</queryType>
		<queryString>
select TRA_DATE,PAN AS CARD_NO,AMOUNT,TXN_TYPE from EJ_STG  WITH (NOLOCK) where  SUBSTRING(PAN,1,6)   in ('534417','539150','549184')
 AND TRA_DATE BETWEEN ? AND ? AND ATM_ID=? 
AND	STATUS='APPROVED' and amount &gt;0  AND ACTIVE_INDEX='Y' ORDER BY AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="24">
		<name>EJ_OTHERBANK_CARDNO</name>
		<queryType>Select</queryType>
		<queryString>
		
 select TRA_DATE,PAN AS CARD_NO,AMOUNT,TXN_TYPE from EJ_STG where  SUBSTRING(PAN,1,6)  not in ('534417','539150','549184')
 AND TRA_DATE BETWEEN ? AND ? AND ATM_ID=? 
AND	STATUS='APPROVED' and amount &gt;0   AND ACTIVE_INDEX='Y' ORDER BY AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
<!-- 	Payemnt Reports -->
 
	
          

	<query id="24">
		<name>CLOSING_BALANCE</name>
		<queryType>Select</queryType>
		<queryString>

			SELECT ISNULL((SELECT CLOSE_BAL from OPENING_CLOSING_BALANCES WHERE
			BAL_DATE=? and module_name='CBO'),0.0) AS IMAL_CLOSING_BALANCE,
			ISNULL((SELECT Deponent_Name FROM RTGS_STG WHERE TRN LIKE
			'%CLOSING_BALANCE%' and VALUE_DATE=?),0.0) AS RTGS_CLOSING_BALANCE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>




	<query id="24">
		<name>IMAL_CBO_FT_STG_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM UNRECONCILED_CBO_RECON_IMAL_CREDITS(?) ORDER BY OD_TRANS_DATE,AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	


	<query id="24">
		<name>IMAL_CBO_FT_STG_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>

			SELECT * FROM UNRECONCILED_CBO_RECON_IMAL_DEBITS(?) ORDER BY OD_TRANS_DATE,AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="28">
		<name>RTGS_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM UNRECONCILED_CBO_RECON_RTGS_CREDITS(?) ORDER BY VALUE_DATE,AMOUNT		 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="29">
		<name>RTGS_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM UNRECONCILED_CBO_RECON_RTGS_DEBITS(?) ORDER BY VALUE_DATE, AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="29">
		<name>CLOSING_BAL_CBO_LEFT</name>
		<queryType>Select</queryType>
		<queryString>
			select sum(isnull(c.CBO_CLOSING_BALANCE,0.00)) as LEFT_CLOSING_BALANCE from (
			select CLOSE_BAL AS CBO_CLOSING_BALANCE from OPENING_CLOSING_BALANCES
			WHERE BAL_DATE=? and module_name='CBO'
			union all
			SELECT SUM(AMOUNT) AS AMOUNT FROM UNRECONCILED_CBO_RECON_RTGS_DEBITS(?) 
			union all
			SELECT SUM(AMOUNT) FROM UNRECONCILED_CBO_RECON_IMAL_DEBITS(?) 
			) c
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="29">
		<name>CLOSING_BAL_CBO_RIGHT</name>
		<queryType>Select</queryType>
		<queryString>
			

			select sum(isnull(c.AMOUNT,0.00)) as RIGTH_CLOSING_BALANCE from (
			SELECT Deponent_Name AS AMOUNT FROM RTGS_STG WHERE TRN LIKE '%CLOSING_BALANCE%'
			and VALUE_DATE=?
			union all
			SELECT SUM(AMOUNT) AS AMOUNT FROM UNRECONCILED_CBO_RECON_RTGS_CREDITS(?) 
			union all
			SELECT SUM(AMOUNT) FROM UNRECONCILED_CBO_RECON_IMAL_DEBITS(?)
			) c

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="29">
		<name>SUPRESS_RTGS</name>
		<queryType></queryType>
		<queryString>

			SELECT * FROM UNRECONCILED_CBO_RECON_SUPPRESS_RTGS(?) ORDER BY AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


	<query id="29">
		<name>CLOSING_BAL_CBO</name>
		<queryType></queryType>
		<queryString>
	select CLOSE_BAL from OPENING_CLOSING_BALANCES WHERE BAL_DATE=? and
			module_name='CBO'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<query id="213">
		<name>RECONCILED_IMAL_CBO_FT_STG_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM RECONCILED_CBO_RECON_IMAL_CREDITS (?,?) order by AMOUNT,REF_NUM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>RECONCILED_IMAL_CBO_FT_STG_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM RECONCILED_CBO_RECON_IMAL_DEBITS (?,?) order by AMOUNT,REF_NUM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>RECONCILED_RTGS_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>
		SELECT * FROM RECONCILED_CBO_RECON_RTGS_CREDITS (?,?) order by AMOUNT,REF_NUM
				</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="213">
		<name>RECONCILED_RTGS_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM RECONCILED_CBO_RECON_RTGS_DEBITS (?,?) order by AMOUNT,REF_NUM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<!-- <query id="24">
		<name>SUPRESS_PAYMENT</name>
		<queryType>Select</queryType>
		<queryString>
	select TRANS_DATE, case when AMOUNT &lt;0 then -AMOUNT ELSE AMOUNT end as
	AMOUNT, OP_NO, CHQ_NO,DESCRIPTION from IMLO_PAYMENTS_STG
	WHERE   ACTIVE_INDEX='N' AND  CAST(UPDATED_ON AS DATE)  BETWEEN ? AND ?
		
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	
	<!-- vijayapaymentorderunreconcilereportqueries -->
	
	<query id="24">
		<name>PAYMENT_ORDER_CLOSING_BAL_IMLO_PAYMENTS</name>
		<queryType>Select</queryType>
		<queryString>
				select -CLOSE_BAL as CLOSING_BALANCE from OPENING_CLOSING_BALANCES 
				where  BAL_DATE=? and  module_name='PAYMENT ORDER' 
		</queryString>
		
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>BALANCE_UNRECONCIED</name>
		<queryType>Select</queryType>
		<queryString>	
 select sum(a.amount) as DEB_CREDIT_AMOUNT from 
(select AMOUNT
			from IMLO_PAYMENTS_STG where cast( CREATED_ON as date)&lt;=	? and cast( updated_on as date)&gt;?
and RECON_STATUS not in ('AM','MM')and ACTIVE_INDEX='Y'
union 
select AMOUNT from IMLO_PAYMENTS_STG where cast( CREATED_ON as date)&lt;=?
and RECON_STATUS is null and ACTIVE_INDEX='Y'

union
select AMOUNT from IMLO_PAYMENTS_STG where cast( CREATED_ON as date)&lt;=? and cast( updated_on as date)&gt;? 
and RECON_STATUS not in ('AM','MM') and ACTIVE_INDEX='Y'
union 
select AMOUNT from IMLO_PAYMENTS_STG where cast( CREATED_ON as date)&lt;=?
and RECON_STATUS  in ('AU','MU') and ACTIVE_INDEX='Y')a 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>PAYMENT_ORDER_OPENINGBAL_IMLO_PAYMENTS</name>
		<queryType>Select</queryType>
		<queryString>
				select -OPEN_BAL as OPENING_BALANCE from OPENING_CLOSING_BALANCES where  BAL_DATE=? and  module_name='PAYMENT ORDER'
		</queryString>
		
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	<query id="24">
		<name>PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_CREDITS</name>
		<queryType>Select</queryType>
		<queryString>

			SELECT * FROM PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_CREDITS (?) ORDER BY TRANS_DATE, AMOUNT

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	

<query id="24">
		<name>PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_DEBITS</name>
		<queryType>Select</queryType>
		<queryString>
			SELECT * FROM PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_DEBITS (?) ORDER BY TRANS_DATE, AMOUNT
		
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	
<query id="24">
		<name>SUPRESS_PAYMENT</name>
		<queryType>Select</queryType>
		<queryString>
		select * from SUPRESS_PAYMENT(?) ORDER BY TRANS_DATE, AMOUNT  
		
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="24">
		<name>SUM_OF_CREDIT_DEBIT_OENBAL</name>
		<queryType>Select</queryType>
		<queryString>
	
		SELECT a.CR-B.DR as cal_amount from 
		(SELECT isnull(sum(amount),0.00) as CR FROM PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_CREDITS (?)) a,
		(SELECT isnull(sum(amount),0.00) as DR FROM PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_DEBITS (?))  b
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="24">
		<name>DIFFERENCEOF_CREDIT_DEBIT_OPEN_CLOSINGBAL</name>
		<queryType>Select</queryType>
		<queryString>
					
			SELECT a.CR-B.DR - C.AMOUNT as dif_amount from 
   (SELECT isnull(sum(amount),0.00) as CR FROM PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_CREDITS (?)) a,
   (SELECT isnull(sum(amount),0.00) as DR FROM PAYMENT_ORDER_UNRECONCILED_IMLO_PAYMENTS_DEBITS (?))  b,
   (select COALESCE((select -CLOSE_BAL from OPENING_CLOSING_BALANCES Where  BAL_DATE=? and  module_name='PAYMENT ORDER'), 0.000) as AMOUNT ) C


 		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- VIJAYASRISUSPENCEGLREPORTQUERIES -->
	<query id="7800">
		<name>RECONCILED_TRADE_SUSPENS_RECON_DROPDOWN</name>
		<queryType>SELECT</queryType>
		<queryString>
			select distinct gl_code from TRADE_FINANCE_SUMMARY_STG where MODULE='SUSPENSE'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>RECONCILED_TRADE_SUSPENS_RECON_CREDITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			<!-- SELECT
			RECON_SIDE,BRANCH_CODE,CUR_CODE,GL_CODE,CIF_NUM,TRA_AMT,TRA_DATE,SHORT_NAME_ENG
			AS NAME,MAIN_REV_IND
			FROM TRADE_SUSPENS_RECON WITH (NOLOCK) WHERE MATCH_TYPE IN ('AM','MM')
			AND ACTIVE_INDEX='Y'  -->
			select CIF_SUB_NO,CURRENCY_CODE,case when FC_AMOUNT&lt;0 then -FC_AMOUNT else FC_AMOUNT end as FC_AMOUNT,
			case when CV_AMOUNT&lt;0 then -CV_AMOUNT else CV_AMOUNT end as CV_AMOUNT,GL_CODE,
			case when AMOUNT &lt; 0 then 'C' end as DEB_CRE_IND from
			TRADE_FINANCE_SUMMARY_STG
			where MODULE='SUSPENSE' and TRANS_DATE=? and RECON_STATUS in ('AM','MM') and
			GL_CODE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>RECONCILED_TRADE_SUSPENS_RECON_DEBITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			select CIF_SUB_NO,CURRENCY_CODE,case when FC_AMOUNT&lt;0 then -FC_AMOUNT else FC_AMOUNT end as FC_AMOUNT,
			case when CV_AMOUNT&lt;0 then -CV_AMOUNT else CV_AMOUNT end as CV_AMOUNT,GL_CODE,
			case when AMOUNT &gt; 0 then 'D' end as DEB_CRE_IND from
			TRADE_FINANCE_SUMMARY_STG
			where MODULE='SUSPENSE' and TRANS_DATE=? and RECON_STATUS in ('AM','MM') and
			GL_CODE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>UNRECONCILED_TRADE_SUSPENS_RECON_CREDITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			select CIF_SUB_NO,CURRENCY_CODE,FC_AMOUNT,CV_AMOUNT,GL_CODE,
			case when AMOUNT &lt; 0 then 'C' end as DEB_CRE_IND from
			TRADE_FINANCE_SUMMARY_STG
			where MODULE='SUSPENSE' and TRANS_DATE=? and RECON_STATUS IS NULL and
			GL_CODE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>UNRECONCILED_TRADE_SUSPENS_RECON_DEBITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			select CIF_SUB_NO,CURRENCY_CODE,FC_AMOUNT,CV_AMOUNT,GL_CODE,
			case when AMOUNT &gt; 0 then 'D' end as DEB_CRE_IND from
			TRADE_FINANCE_SUMMARY_STG
			where MODULE='SUSPENSE' and TRANS_DATE=? and RECON_STATUS IS NULL and
			GL_CODE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- RETAILASSERTQUERIESBYVIJAYASRI -->

	<query id="545">
		<name>CREDIT_RECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CREDIT_RECONCILED_LOAN_AND_INSURANCE_RECON (?, ?) order by TRA_DATE,DEB_CRE_IND

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="545">
		<name>DEBIT_RECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>

		SELECT * FROM DEBIT_RECONCILED_LOAN_AND_INSURANCE_RECON(?,?) order by TRA_DATE,DEB_CRE_IND
			

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="511">
		<name>BALANCED_RECONCILED_LOAN_AND_INSURANCE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT a.CR-B.DR as cal_amount from 
		(SELECT sum(TRA_AMT) as CR FROM CREDIT_RECONCILED_LOAN_AND_INSURANCE_RECON(?,?)) a,
		(SELECT sum(TRA_AMT) as DR FROM DEBIT_RECONCILED_LOAN_AND_INSURANCE_RECON(?,?))  b
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<query id="546">
		<name>UNRECONCILED_TRADE_SUMMARY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM UNRECONCILED_TRADE_SUMMARY (?) ORDER BY OL_GL_CODE, TRA_DATE, TRA_AMT

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="544">
		<name>UNRECONCILED_PO_SUMMARYYY</name>
		<queryType>SELECT</queryType>
		<queryString>


		SELECT * FROM UNRECONCILED_PO_SUMMARY (?) ORDER BY OL_GL_CODE, OD_TRANS_DATE, OL_AMOUNT


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>	
	
	<query id="7800">
		<name>UNRECONCILED_ECC_CREDITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	

 		SELECT * FROM UNRECONCILED_ECC_CREDITS_RECON(?,?) order by TRA_DATE,TRA_AMT,CHEQUE_NO

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>UNRECONCILED_ECC_DEBITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	

           SELECT * FROM UNRECONCILED_ECC_DEBITS_RECON(?,?) order by TRA_DATE,TRA_AMT,CHEQUE_NO

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>UNRECONCILED_ECC_IMAL_CREDITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
	
		SELECT * FROM UNRECONCILED_ECC_IMAL_CREDITS_RECON(?,?) order by TRA_DATE,TRA_AMT,CHEQUE_NO

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>UNRECONCILED_ECC_IMAL_DEBITS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
		
		SELECT * FROM UNRECONCILED_ECC_IMAL_DEBITS_RECON(?,?) order by TRA_DATE,TRA_AMT,CHEQUE_NO
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>UNRECONCILED_ECC_CBO_IMAL_SOURCE_SUPPRESS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	
		
		 SELECT * FROM UNRECONCILED_ECC_CBO_IMAL_SOURCE_SUPPRESS_RECON(?,?) order by TRA_DATE,TRA_AMT,CHEQUE_NO

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <!-- VIJAYASRISUSPENCEGLREPORTQUERIES -->
	
	<!-- <query id="7800">
		<name>SUSPENCE_GL_RECONCILED_CREDITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_RECONCILED_CREDITS (?,?) order by CIF_SUB_NO,CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENCE_GL_RECONCILED_DEBITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_RECONCILED_DEBITS (?,?)order by CIF_SUB_NO,CURRENCY_CODE 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENCE_GL_UNRECONCILED_CREDITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_UNRECONCILED_CREDITS (?,?)order by CIF_SUB_NO,CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENCE_GL_UNRECONCILED_DEBITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_UNRECONCILED_DEBITS (?,?)order by CIF_SUB_NO,CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENS_GL_DROPDOWN</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM TRADE_FINANCE_SUMMARY_STG
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>  -->
    <!-- VIJAYASRISUSPENCEGLREPORTQUERIES -->
	
	<query id="7800">
		<name>SUSPENCE_GL_RECONCILED_CREDITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_RECONCILED_CREDITS (?,?) order by CURRENCY_CODE ASC, CIF_SUB_NO,-AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENCE_GL_RECONCILED_DEBITS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_RECONCILED_DEBITS (?,?) order by CURRENCY_CODE ASC, CIF_SUB_NO,AMOUNT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENCE_GL_AGGREGATED_UNRECONCILED_DATA</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_AGGREGATED_UNRECONCILED_DATA (?,?)order by CIF_SUB_NO,CURRENCY_CODE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENCE_GL_UNRECONCILED_API_DATA</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_GL_UNRECONCILED_API_DATA (?,?)order by CIF_NUM,CUR_CODE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="7800">
		<name>SUSPENS_GL_DROPDOWN</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENS_GL_DROPDOWN
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="18">
        <name>GET_CASEID</name>
        <queryType>SELECT</queryType>
        <queryString>
        	<!-- SELECT ISNULL(MAX(CASE_ID)+1,1) AS "CASE_ID" FROM RECON_CASE_MANAGEMENT -->
        	<!-- SELECT 'SR'+ CAST(NEXT VALUE FOR CASE_ID_SEQ AS VARCHAR) AS CASE_ID -->
        	SELECT COUNT(*)+1 AS CASE_ID FROM RECON_CASE_MANAGEMENT
        </queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="18">
        <name>GET_ALL_REASONS</name>
        <queryType>SELECT</queryType>
        <queryString>SELECT REASON FROM CASE_MANAGEMENT_REASON ORDER BY ID</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
        <name>SAVE_CASE_MANAGEMENT_REASON</name>
        <queryType>INSERT</queryType>
        <queryString>INSERT INTO CASE_MANAGEMENT_REASON(REASON) VALUES (?)</queryString>
        <queryParam>REASON@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
	<query id="18">
        <name>GET_ESCALATION_DEPARTMENTS</name>
        <queryType>SELECT</queryType>
        <queryString>SELECT DEPARTMENT FROM ESCALATION_DEPARTMENTS</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
     <query id="18">
        <name>GET_USERS_FOR_DEPARTMENT</name>
        <queryType>SELECT</queryType>
        <queryString>SELECT * FROM USERS WHERE DEPT_NAME=?</queryString>
        <queryParam>DEPT_NAME@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="18">
        <name>GET_EMAIL_ID_BY_USER</name>
        <queryType>SELECT</queryType>
        <queryString>
        	SELECT EMAIL_ID FROM USERS WHERE USER_NAME=?
        </queryString>
        <queryParam>USER_NAME@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
	<query id="9">
		<name>INSERT_FINANCE_ONS_RECON_FIN_ONS_CBO</name>
		<queryType>INSERT_FINANCE_ONS_RECON_FIN_ONS_CBO</queryType>
		<queryString>
			INSERT INTO FINANCE_ONS_RECON(SID,RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,TRA_CUR,REF_NUM,WORKFLOW_STATUS,SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,MATCH_TYPE,ACTIVE_INDEX,USER_ID,UPDATED_ON,CREATED_ON,COMMENTS,SUPPORTING_DOC_ID,RULE_NAME,ACTIVITY_STATUS,OPERATION,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS,PAN_CARD,ACCOUNT_NUMBER,ID)
			VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TRA_CUR@VARCHAR,REF_NUM@VARCHAR,
		WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,MATCH_TYPE@VARCHAR,
		ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,SUPPORTING_DOC_ID@VARCHAR,
		RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,
		PAN_CARD@VARCHAR,ACCOUNT_NUMBER@VARCHAR,ID@BIGINT
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="9">
		<name>FINANCE_ONS_RECON_FIN_ONS_CBO</name>
		<queryType>INSERT_FINANCE_ONS_RECON_FIN_ONS_CBO</queryType>
		<queryString>
			 SELECT SID,TRAN_REF_NUM,INTERNAL_REF_NUM,ACCOUNT,TRAN_DATE,VALUE_DATE,AMOUNT,DRCR,CURRENCY,PAN_NUMBER,ORIGINATOR_BID,DESTINATION_BID,
					ACQUIRING_INSTITUTION_ID,CARD_ACCEPTOR_NAME,MERCHANT_CATEGORY_CODE,TRANSACTION_TYPE,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,
					UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA,FREE_TEXT_1,
					FREE_TEXT_2,FREE_TEXT_3,FREE_TEXT_4,FREE_TEXT_5,FREE_TEXT_6,FREE_TEXT_7,FREE_TEXT_8,FREE_TEXT_9,FREE_TEXT_10,FREE_CODE_1,
					FREE_CODE_2,FREE_CODE_3,FREE_CODE_4,FREE_CODE_5,FREE_DATE_1,FREE_DATE_2,FREE_DATE_3,FREE_DATE_4,FREE_DATE_5
  			FROM FIN_ONS_CBO_STG WHERE SID=?
		</queryString>
		<queryParam>SID@BIGINT
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
    <query id="9">
		<name>INSERT_RECON_CASE_MANAGEMENT</name>
		<queryType>INSERT</queryType>
		<queryString>
			insert into RECON_CASE_MANAGEMENT
			(
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,
			active_index,created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			)
			values
			(
			?,?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,?,?
			)

		</queryString>
		<queryParam>case_id@VARCHAR,sid@BIGINT,activity_id@BIGINT,activity_name@VARCHAR,activity_type@VARCHAR,activity_level@INTEGER,
			allowed_approvers@VARCHAR,activity_owner@VARCHAR,created_by@VARCHAR,activity_data@LONGVARBINARY,status@VARCHAR,
			active_index@VARCHAR,created_on@TIMESTAMP,recently_updated_on@TIMESTAMP,version@INTEGER,
			business_area@VARCHAR,recon@VARCHAR,transaction_amount@DECIMAL,reason@VARCHAR,department@VARCHAR,assignedTO@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="9">
        <name>INSERT_RECON_CASE_OPERATION</name>
        <queryType>INSERT</queryType>
        <queryString>insert into recon_case_operation(sid,case_id,operation_name,operation_date,comment,user_id) 
        				values(?,?,?,?,?,?)
        </queryString>
        <queryParam>sid@BIGINT,case_id@VARCHAR,operation_name@VARCHAR,operation_date@TIMESTAMP,comment@VARCHAR,user_id@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
	
    <!-- <query id="7">
		<name>GET_APPROVED_CASES_BY_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and activity_owner=? and status like 'Appro%'and
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>activity_owner@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	
	<!-- <query id="7">
		<name>GET_PENDING_CASES_BY_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and activity_owner=? and status like 'Pend%'and
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>activity_owner@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	
	<query id="18">
        <name>CHECK_CASE_BY_SID</name>
        <queryType>SELECT</queryType>
        <queryString>select count(*) as COUNT from RECON_CASE_MANAGEMENT where sid=?</queryString>
        <queryParam>sid@BIGINT</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="18">
        <name>UPDATE_USER_IN_RECON_CASE_MANAGEMENT</name>
        <queryType>UPDATE</queryType>
        <queryString>update RECON_CASE_MANAGEMENT set recently_updated_on=?,department=?,assigned_to=? where case_id=? and sid=?</queryString>
        <queryParam>recently_updated_on@TIMESTAMP,department@VARCHAR,assigned_to@VARCHAR,case_id@VARCHAR,sid@BIGINT</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="18">
        <name>UPDATE_STATUS_IN_RECON_CASE_MANAGEMENT</name>
        <queryType>UPDATE</queryType>
        <queryString>update RECON_CASE_MANAGEMENT set recently_updated_on=?,status=? where case_id=? and sid=?</queryString>
        <queryParam>recently_updated_on@TIMESTAMP,status@VARCHAR,case_id@VARCHAR,sid@BIGINT</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="18">
        <name>GET_CASE_OPERATIONS_BY_CASE_ID</name>
        <queryType>SELECT</queryType>
        <queryString>select sno,case_id,operation_date,comment,user_id,operation_name from RECON_CASE_OPERATION where case_id=? and sid=? and operation_name='CaseComments'</queryString>
        <queryParam>case_id@VARCHAR,sid@BIGINT</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
   <query id="7">
		<name>GET_CREATED_CASES_BY_MAKER_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and status='PENDING' and
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id) 
			and created_by=?
			order by case_id
		</queryString>
		<queryParam>created_by@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="7">
		<name>GET_CREATED_CASES_BY_CHECKER_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and status='PENDING' and
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id) 
			and created_by in(select user_id from users where reporting=(select user_name from users where user_id=?))
			order by case_id
		</queryString>
		<queryParam>created_by@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="7">
		<name>GET_CLOSED_CASES_BY_MAKER_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and status like 'Close%'and
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id) and created_by=?
			order by case_id
		</queryString>
		<queryParam>created_by@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="7">
		<name>GET_CLOSED_CASES_BY_CHECKER_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and status like 'Close%'and
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id) 
			and created_by in(select user_id from users where reporting=(select user_name from users where user_id=?))
			order by case_id
		</queryString>
		<queryParam>created_by@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- <query id="7">
		<name>GET_ASSIGNED_CASES_BY_USER</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			case_id,sid,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,created_by,activity_data,status,active_index,
			created_on,recently_updated_on,version,business_area,recon,transaction_amount,reason,department,assigned_to
			from
			RECON_CASE_MANAGEMENT a where active_index='Y' and assigned_to=? and 
			activity_level=(
			select max(activity_level) from RECON_CASE_MANAGEMENT b
			where b.activity_id=a.activity_id) order by case_id
		</queryString>
		<queryParam>assigned_to@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	
	
	<query id="24">
		<name>FINANCE_MP_CLEAR_RECON_FIN_MP_CLEAR_EXT_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'EXT' AS RECON_SIDE,SID,ID AS REFERENCE,AMOUNT AS TRA_AMT,
		SETTLEMENT_DATE AS TRA_DATE,'' AS DEB_CRE_IND,'FIN_MP_CLEAR_EXT_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
	 	FROM FIN_MP_CLEAR_EXT_STG WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CO_CDM_RECON_CO_CDM_JOURNAL_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'CO_CDM_JOURNAL' AS RECON_SIDE,SID,TERMINALID AS TERMINAL_ID,ACCOUNTNO1 AS ACCOUNT_NUM,CARDNUMBER AS REF_NUM,AMOUNT AS TRA_AMT,
		TXNDATETIME AS TRA_DATE,SEQUENCENUMBER AS TRAN_SEQ ,'' AS DEB_CRE_IND,'CO_CDM_JOURNAL_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
	 	FROM CO_CDM_JOURNAL_STG WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CO_ATM_RECON_CO_ATM_JOURNAL_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'CO_ATM_JOURNAL' AS RECON_SIDE,SID,TXNDATETIME AS TRA_DATE,AMOUNT AS
			TRA_AMT ,TERMINALID AS TREMINAL_ID
			,SEQUENCENUMBER,CARDNUMBER ,
			'CO_ATM_JOURNAL_STG' AS
			SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
			FROM CO_ATM_JOURNAL_STG IJ WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CO_ACH_RECON_CO_ACH_CBO_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'CO_ACH_CBO' AS RECON_SIDE,SID, AMOUNT AS TRA_AMT,SETTLEMENT_DATE
			AS TRA_DATE,TRANSACTION_ID AS REFERENCE,SUBSTRING(TYPE1,1,1) AS DEB_CRD_IND,
			'CO_ACH_CBO_STG' AS
			SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
			FROM CO_ACH_CBO_STG IJ WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CARDS_ATM_MASTERCARD_ACQ_RECON_CARD_ATM_MC_ACQ_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
			SELECT 'ACQ' AS RECON_SIDE,SID, TRN_DATE AS  TRA_DATE,REQ_AMT_TRN_LOCAL AS TRA_AMT ,RIGHT(REF_NUMBER,6) AS REF_NUM,
			COMPTD_AMT_TRN_LOCAL_DR_CR_IND AS DEB_CRE_IND,'' AS TRAN_PARTICULAR,FILE_NAME,'CARD_ATM_MC_ACQ_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
			BUSINESS_AREA,PRIMARY_ACC_NUM as CARD_NUM FROM CARD_ATM_MC_ACQ_STG WHERE  SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CARD_ATM_VISA_ACQ_RECON_CARD_ATM_VISA_ACQ_EXT_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
			SELECT 'CARD_ATM_VISA_ACQ_EXT' AS
				RECON_SIDE,SID,TRANSACTION_AMOUNT AS
				TRA_AMT,REPORT_DATE AS TRA_DATE,
				(LEFT(CARD_NUMBER,6) + REPLACE(SPACE(LEN(CARD_NUMBER)-10),' ','*') + RIGHT(CARD_NUMBER,4)) AS
				CARD_NUMBER,'' AS TRAN_PARTICULAR,RETRIEVAL_REF_NUMBER AS REFERENCE_NUMBER,CR_CURRENCY AS
				DEB_CR_IND
				,TERMINAL_ID ,'' AS CUSTOMER_ACCT,
				'CARD_ATM_VISA_ACQ_EXT_STG' AS
				SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS
			FROM CARD_ATM_VISA_ACQ_EXT_STG IJ WHERE  SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CARD_POSNI_ACQUIRER_RECON_CARD_POSNI_TRAN_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'CARD_POSNI_TRAN' AS RECON_SIDE,SID,VALUE_DATE AS TRA_DATE,TRAN_AMT AS
			TRA_AMT ,ACCT_NUM AS ACCT_NUM,CARD_NO AS CARDNUMBER,REF_NUM,
			'CARD_POSNI_TRAN_STG' AS
			SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA,'' AS TRAN_PARTICULAR
			FROM CARD_POSNI_TRAN_STG IJ WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CREDIT_CARD_STATEMENT_RECON_CREDIT_CARD_STATEMENT_TRAN_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'CREDIT_CARD_STATEMENT_TRAN' AS RECON_SIDE,SID,TXN_AMT AS TRA_AMT,TXN_DATE  AS TRA_DATE,
				CARD_NUMBER AS CARD_NUMBER ,TXN_TYP_AFS AS DEB_CRE_IND,TXN_DESC AS TRAN_DESC,
				'CREDIT_CARD_STATEMENT_TRAN_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,
				VERSION,WORKFLOW_STATUS 
				FROM CREDIT_CARD_STATEMENT_TRAN_STG IJ WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>CARD_ATM_VISA_ISS_RECON_CARD_ATM_VISA_ISS_PST_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'CARD_ATM_VISA_ISS_PST' AS RECON_SIDE,SID,AMOUNT1 AS TRA_AMT,LIEN_DATE  AS TRA_DATE,
					CARD_NUM AS CARD_NUMBER ,'' AS REF_NUM,'' AS DEB_CRE_IND,'CARD_ATM_VISA_ISS_PST_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,
					VERSION,WORKFLOW_STATUS FROM CARD_ATM_VISA_ISS_PST_STG IJ WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>FINANCE_SUSPENSE_RECON_FIN_SUSPENSE_STG</name>
		<queryType>OrphanSameSideReversalFrom</queryType>
		<queryString>
		SELECT 'FIN_SUSPENSE_DEBIT' AS RECON_SIDE,SID, TRAN_DATE AS  TRA_DATE,replace(AMOUNT,'-','') AS TRA_AMT ,REFERENCE_NUMBER,REF_CRNCY_CODE,
			DRCR AS DEB_CRE_IND,'FIN_SUSPENSE_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
			FROM FIN_SUSPENSE_STG IJ WHERE  SID=?
				
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>ETL_STATUS</name>
		<queryType>ETL_STATUS</queryType>
		<queryString>
			SELECT ETL_STATUS,CONCAT((CONVERT(DATE,ETL_LAST_RUN)),' ',(CONVERT(TIME,ETL_LAST_RUN))) AS ETL_LAST_RUN FROM STATUS_ETL_RECON WHERE MODULE = ?
		</queryString>
		<queryParam>MODULE@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>RECON_STATUS</name>
		<queryType>RECON_STATUS</queryType>
		<queryString>
			SELECT RECON_STATUS,CONCAT((CONVERT(DATE,RECON_LAST_RUN)),' ',(CONVERT(TIME,RECON_LAST_RUN))) AS RECON_LAST_RUN FROM STATUS_ETL_RECON WHERE MODULE = ?
		</queryString>
		<queryParam>MODULE@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>GET_PENDING_CASES</name>
		<queryType>RECON_STATUS</queryType>
		<queryString>
			SELECT C.*,U.EMAIL_ID FROM RECON_CASE_MANAGEMENT C JOIN USERS U ON C.CREATED_BY=U.USER_ID WHERE C.STATUS='PENDING';
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- ESCALATION_MATRIX -->
	
	<query id="18">
        <name>CHECK_ESCALATION_MATRIX</name>
        <queryType>SELECT</queryType>
        <queryString>SELECT COUNT(*) AS "COUNT" FROM ESCALATION_MATRIX_VIEW WHERE DEPARTMENT=? AND ESCALATED_PERSON=?</queryString>
        <queryParam>DEPARTMENT@VARCHAR,ESCALATED_PERSON@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
	<query id="2">
        <name>SAVE_ESCALATION_MATRIX</name>
        <queryType>INSERT</queryType>
        <queryString>INSERT INTO ESCALATION_MATRIX(DEPARTMENT,ESCALATED_PERSON,MAIL_ID,STATUS,ACTIVE_INDEX,VERSION) VALUES (?,?,?,?,?,?)</queryString>
        <queryParam>DEPARTMENT@VARCHAR,ESCALATED_PERSON@VARCHAR,MAIL_ID@VARCHAR,STATUS@VARCHAR,ACTIVE_INDEX@VARCHAR,VERSION@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="121">
		<name>UPDATE_ESCALATION_MATRIX</name>
		<queryType>Update</queryType>
		<queryString>UPDATE ESCALATION_MATRIX SET MAIL_ID= ?,STATUS=? WHERE ID=?</queryString>
		<queryParam>MAIL_ID@VARCHAR,STATUS@VARCHAR,ID@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="121">
		<name>DELETE_ESCALATION_MATRIX</name>
		<queryType>Delete</queryType>
		<queryString>UPDATE ESCALATION_MATRIX SET ACTIVE_INDEX= ? WHERE ID=?</queryString>
		<queryParam>ACTIVE_INDEX@VARCHAR,ID@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="24">
		<name>GET_ALL_ESCALATION_MATRIX</name>
		<queryType>ESCALATION_MATRIX</queryType>
		<queryString>
			SELECT DEPARTMENT,RECON,ESCALATED_PERSON,MAIL_ID,MIN_DAYS,MAX_DAYS FROM ESCALATION_MATRIX;
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- ESCALATION_MATRIX end-->
	
	<!-- Reports -->
	
	<!-- ONS -->
	<query id="563">
		<name>ONS_INTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT TRAN_REF_NUM AS 'REFERENCE NUMBER',INTERNAL_REF_NUM AS 'INTERNAL REFERENCE NUMBER',AMOUNT AS 'TRANSACTION AMOUNT',
				VALUE_DATE AS 'TRANSACTION DATE',TRAN_PARTICULAR AS 'TRAN PARTICULAR',TRAN_REMARKS AS 'TRAN REMARKS',DRCR AS 'DEB/CR INDICATOR',
				ACNT AS 'ACCOUNT NUMBER',CARD_NUMBER AS 'CARD NUMBER',ACCT_BRANCH_ID AS 'ACCOUNT BRANCH ID' 
			FROM FIN_ONS_CBS_STG WITH (NOLOCK) WHERE RECON_STATUS IN ('AM','MM') AND ACTIVE_INDEX='Y' AND VALUE_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ONS_EXTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT TRAN_REF_NUM AS 'REFERENCE NUMBER',INTERNAL_REF_NUM AS 'INTERNAL REFERENCE NUMBER',AMOUNT AS 'TRANSACTION AMOUNT',
				VALUE_DATE AS 'TRANSACTION DATE','-' AS 'TRAN PARTICULAR','-' as 'TRAN REMARKS',DRCR AS 'DEB/CR INDICATOR',
				ACCOUNT AS 'ACCOUNT NUMBER',PAN_NUMBER AS 'PAN NUMBER',ORIGINATOR_BID AS 'ORIGINATOR BID',DESTINATION_BID AS 'DESTINATION BID',
				ACQUIRING_INSTITUTION_ID AS 'ACQUIRING INSTITUTION ID',CARD_ACCEPTOR_NAME AS 'CARD_ACCEPTOR_NAME',
				MERCHANT_CATEGORY_CODE AS 'MERCHANT_CATEGORY_CODE',TRANSACTION_TYPE AS 'TRANSACTION_TYPE'
			FROM FIN_ONS_CBO_STG WITH (NOLOCK) WHERE RECON_STATUS IN ('AM','MM') AND ACTIVE_INDEX='Y' AND VALUE_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_INTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT TRAN_REF_NUM AS 'REFERENCE NUMBER',INTERNAL_REF_NUM AS 'INTERNAL REFERENCE NUMBER',AMOUNT AS 'TRANSACTION AMOUNT',
				VALUE_DATE AS 'TRANSACTION DATE',TRAN_PARTICULAR AS 'TRAN PARTICULAR',TRAN_REMARKS AS 'TRAN REMARKS',DRCR AS 'DEB/CR INDICATOR',
				ACNT AS 'ACCOUNT NUMBER',CARD_NUMBER AS 'CARD NUMBER',ACCT_BRANCH_ID AS 'ACCOUNT BRANCH ID' 
			FROM FIN_ONS_CBS_STG WITH (NOLOCK) WHERE RECON_STATUS IN ('AU','MU') AND ACTIVE_INDEX='Y' AND VALUE_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ONS_EXTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT TRAN_REF_NUM AS 'REFERENCE NUMBER',INTERNAL_REF_NUM AS 'INTERNAL REFERENCE NUMBER',AMOUNT AS 'TRANSACTION AMOUNT',
				VALUE_DATE AS 'TRANSACTION DATE','-' AS 'TRAN PARTICULAR','-' as 'TRAN REMARKS',DRCR AS 'DEB/CR INDICATOR',
				ACCOUNT AS 'ACCOUNT NUMBER',PAN_NUMBER AS 'PAN NUMBER',ORIGINATOR_BID AS 'ORIGINATOR BID',DESTINATION_BID AS 'DESTINATION BID',
				ACQUIRING_INSTITUTION_ID AS 'ACQUIRING INSTITUTION ID',CARD_ACCEPTOR_NAME AS 'CARD_ACCEPTOR_NAME',
				MERCHANT_CATEGORY_CODE AS 'MERCHANT_CATEGORY_CODE',TRANSACTION_TYPE AS 'TRANSACTION_TYPE'
			FROM FIN_ONS_CBO_STG WITH (NOLOCK) WHERE RECON_STATUS IS NULL AND ACTIVE_INDEX='Y' AND VALUE_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- ONS End-->
	
	
	<!-- MPCLEAR -->
	<query id="563">
		<name>MPCLEAR_INTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT PAYMENT_REFERENCE AS 'REFERENCE',TRAN_AMT AS 'TRANSACTION AMOUNT',TRAN_DATE  AS 'TRANSACTION DATE',
				BD_STATUS AS 'BD STATUS',CUST_GSM_NUM AS 'CUSTOMER GSM NUM', SENDER_BANK AS 'SENDER BANK',
				DEBIT_ACCT_NUMBER AS 'DEBIT ACCOUNT NUMBER',DEBIT_ACCT_NAME AS 'DEBIT ACCOUNT NAME',
				CREDIT_ACCT_NUMBER AS 'CREDIT ACCOUTN NUMBER',CREDIT_ACCT_NAME AS 'CREDIT ACCOUNT NAME',STAFF_FLAG AS 'STAFF FLAG',
				PYMNT_TYPE AS 'PAYMENT TYPE',REVERSED_FLAG AS 'REVERSED FLAG',TRAN_ID AS 'TRANSACTION ID',VALUE_DATE AS 'VALUE DATE',
				TRAN_POST_FLG AS 'TRAN POST FLG',WALLET_TRANSFER_WITHIN_BD AS 'WALLET TRANSFER WITHIN BD',CUST_TYPE_CHRG
			 FROM FIN_MP_CLEAR_CBS_STG WITH (NOLOCK) WHERE RECON_STATUS IN ('AM','MM') AND ACTIVE_INDEX='Y' AND TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>MPCLEAR_EXTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT ID AS 'REFERENCE',AMOUNT AS 'TRANSACTION AMOUNT',SETTLEMENT_DATE AS 'TRANSACTION DATE',
				SESSION_SEQ AS 'SESSION SEQ',CURRENCY AS 'CURRENCY',PARTICIPANT AS 'PARTICIPANT',SETTLEMENTRETRY AS 'SETTLEMENT RETRY',
				TYPE AS 'SETTLEMENTRETRY',STATE AS 'STATE',REASON AS 'REASON',ADDITIONAL_INFO AS 'ADDITIONAL INFO'
		 	FROM FIN_MP_CLEAR_EXT_STG WITH (NOLOCK) WHERE RECON_STATUS IN ('AM','MM') AND ACTIVE_INDEX='Y' AND SETTLEMENT_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>MPCLEAR_INTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT PAYMENT_REFERENCE AS 'REFERENCE',TRAN_AMT AS 'TRANSACTION AMOUNT',TRAN_DATE  AS 'TRANSACTION DATE',
				BD_STATUS AS 'BD STATUS',CUST_GSM_NUM AS 'CUSTOMER GSM NUM', SENDER_BANK AS 'SENDER BANK',
				DEBIT_ACCT_NUMBER AS 'DEBIT ACCOUNT NUMBER',DEBIT_ACCT_NAME AS 'DEBIT ACCOUNT NAME',
				CREDIT_ACCT_NUMBER AS 'CREDIT ACCOUTN NUMBER',CREDIT_ACCT_NAME AS 'CREDIT ACCOUNT NAME',STAFF_FLAG AS 'STAFF FLAG',
				PYMNT_TYPE AS 'PAYMENT TYPE',REVERSED_FLAG AS 'REVERSED FLAG',TRAN_ID AS 'TRANSACTION ID',VALUE_DATE AS 'VALUE DATE',
				TRAN_POST_FLG AS 'TRAN POST FLG',WALLET_TRANSFER_WITHIN_BD AS 'WALLET TRANSFER WITHIN BD',CUST_TYPE_CHRG
			 FROM FIN_MP_CLEAR_CBS_STG WITH (NOLOCK) WHERE RECON_STATUS IN ('AU','MU') AND ACTIVE_INDEX='Y' AND TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>MPCLEAR_EXTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT ID AS 'REFERENCE',AMOUNT AS 'TRANSACTION AMOUNT',SETTLEMENT_DATE AS 'TRANSACTION DATE',
				SESSION_SEQ AS 'SESSION SEQ',CURRENCY,PARTICIPANT,SETTLEMENTRETRY AS 'SETTLEMENT RETRY',
				TYPE,STATE,REASON,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
		 	FROM FIN_MP_CLEAR_EXT_STG WITH (NOLOCK) WHERE RECON_STATUS IS NULL AND ACTIVE_INDEX='Y' AND SETTLEMENT_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- MPCLEAR End-->
	
	<!-- ATM RECONCILITION  -->
<!-- ATM RECONCILITION  INTERNAL RECON -->
	<query id="563">
		<name>ATM_RECONCILATION_INTERNAL_RECON</name>
		<queryType>SELECT</queryType>
		<queryString> 
         SELECT SID AS 'SID' 
              ,CUSTOMER_ACCT AS 'CUSTOMER ACCT'
              ,ATM_ACCOUNT AS 'ATM ACCOUNT'
              ,TRAN_ID AS 'TTRAN ID'
              ,TRAN_DATE AS 'TRAN DATE'
              ,VALUE_DATE AS 'VALUE DATE'
              ,CUSTOMER_ACCT1 AS 'CUSTOMER ACCT1'
              ,ATM_ACCOUNT1 AS 'ATM ACCOUNT1'
              ,DRCR AS 'DRCR'
              ,AMOUNT AS 'AMOUNT'
              ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
              ,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
              ,TRAN_REMARKS AS 'TRAN REMARKS'
              ,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
              ,REF_CRNCY_CODE AS 'REF CRNCY CODE'
              ,REF_AMT AS 'REF AMT'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
              ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID' 
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA'
              ,FILE_NAME AS 'FILE NAME' FROM CO_ATM_CBS_STG WITH
	          (NOLOCK) WHERE RECON_STATUS IN ('AM','MM')
		       AND ACTIVE_INDEX='Y' AND
		       TRAN_DATE BETWEEN ? AND ?
	 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>
	
<!-- ATM RECONCILITION  INTERNAL UNRECON -->

 <query id="563">
		<name>ATM_RECONCILATION_INTERNAL_UNRECON</name>
		<queryType>SELECT</queryType>
		<queryString>   
         SELECT SID AS 'SID' 
              ,CUSTOMER_ACCT AS 'CUSTOMER ACCT'
              ,ATM_ACCOUNT AS 'ATM ACCOUNT'
              ,TRAN_ID AS 'TTRAN ID'
              ,TRAN_DATE AS 'TRAN DATE'
              ,VALUE_DATE AS 'VALUE DATE'
              ,CUSTOMER_ACCT1 AS 'CUSTOMER ACCT1'
              ,ATM_ACCOUNT1 AS 'ATM ACCOUNT1'
              ,DRCR AS 'DRCR'
              ,AMOUNT AS 'AMOUNT'
              ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
              ,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
              ,TRAN_REMARKS AS 'TRAN REMARKS'
              ,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
              ,REF_CRNCY_CODE AS 'REF CRNCY CODE'
              ,REF_AMT AS 'REF AMT'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
              ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID' 
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA'
              ,FILE_NAME AS 'FILE NAME', DATEDIFF(day,VALUE_DATE,GETDATE()) AS 'AGE' 
              FROM CO_ATM_CBS_STG WITH
	           (NOLOCK) WHERE RECON_STATUS IN ('AU','MU')
		       AND ACTIVE_INDEX='Y' AND
		        TRAN_DATE BETWEEN ? AND ?
		         </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ATM RECONCILITION EXTERNAL RECON -->

<query id="563">
		<name>ATM_RECONCILATION_EXTERNAL_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>   
        SELECT SID AS 'SID'
              ,TXNMESSAGES_ID AS 'TXNMESSAGES ID'
              ,CREATEDDATE AS 'CREATEDDATE' 
              ,TXNDATETIME AS 'XNDATETIME'
              ,TXNDATE AS 'TXNDATE'
              ,TXNTIME AS 'TXNTIME'
              ,TERMINALID 'TERMINALID'
              ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
              ,TXNTYPE_ID AS 'TXNTYPE ID'
              ,TXNTYPE AS 'TXNTYPE'
              ,CHIPCARD AS 'CHIPCARD'
              ,CARDNUMBER AS 'CARDNUMBER'
              ,ACCOUNTNO1 AS 'ACCOUNTNO1'
              ,ACCOUNTNO2 AS 'ACCOUNTNO2'
              ,AMOUNT AS 'AMOUNT'
              ,NOTEDETAILS AS 'NOTEDETAILS'
              ,CARDTAKEN AS 'CARDTAKEN'
              ,CARDCAPTURE AS 'CARDCAPTURE'
              ,NOTESPRESENTED AS 'NOTESPRESENTED'
              ,NOTESTAKEN AS 'NOTESTAKEN'
              ,NOTESRETRACT AS 'NOTESRETRACT'
              ,RESPONSECODE AS 'RESPONSECODE'
               ,RESPONSEDESC AS 'RESPONSEDESC'
              ,HARDWARESTATUS AS 'HARDWARESTATUS'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
               ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID'
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA'
              ,FILE_NAME AS 'FILE NAME'
               FROM CO_ATM_JOURNAL_STG  WITH
			  (NOLOCK) WHERE RECON_STATUS IN ('AM','MM')
               AND ACTIVE_INDEX='Y' AND
			   TXNDATETIME BETWEEN ? AND ?
		         </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ATM RECONCILITION EXTERNAL RECON -->

<query id="563">
		<name>ATM_RECONCILATION_EXTERNAL_UNRECON</name>
		<queryType>SELECT</queryType>
		<queryString>   
        SELECT SID AS 'SID'
              ,TXNMESSAGES_ID AS 'TXNMESSAGES ID'
              ,CREATEDDATE AS 'CREATEDDATE' 
              ,TXNDATETIME AS 'XNDATETIME'
              ,TXNDATE AS 'TXNDATE'
              ,TXNTIME AS 'TXNTIME'
              ,TERMINALID 'TERMINALID'
              ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
              ,TXNTYPE_ID AS 'TXNTYPE ID'
              ,TXNTYPE AS 'TXNTYPE'
              ,CHIPCARD AS 'CHIPCARD'
              ,CARDNUMBER AS 'CARDNUMBER'
              ,ACCOUNTNO1 AS 'ACCOUNTNO1'
              ,ACCOUNTNO2 AS 'ACCOUNTNO2'
              ,AMOUNT AS 'AMOUNT'
              ,NOTEDETAILS AS 'NOTEDETAILS'
              ,CARDTAKEN AS 'CARDTAKEN'
              ,CARDCAPTURE AS 'CARDCAPTURE'
              ,NOTESPRESENTED AS 'NOTESPRESENTED'
              ,NOTESTAKEN AS 'NOTESTAKEN'
              ,NOTESRETRACT AS 'NOTESRETRACT'
              ,RESPONSECODE AS 'RESPONSECODE'
               ,RESPONSEDESC AS 'RESPONSEDESC'
              ,HARDWARESTATUS AS 'HARDWARESTATUS'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
               ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID'
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA'
              ,FILE_NAME AS 'FILE NAME',DATEDIFF(day,TXNDATETIME,GETDATE()) AS 'AGE'
               FROM CO_ATM_JOURNAL_STG  WITH 
			  (NOLOCK) WHERE RECON_STATUS IS NULL
               AND ACTIVE_INDEX='Y' AND
			   TXNDATETIME BETWEEN ? AND ?
		         </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ATM INTERNAL SUPPRESS-->

<query id="563">
		<name>ATM_RECONCILATION_SUPPRESS_INTERNAL_BK</name>
		<queryType>SELECT</queryType>
		<queryString> 
         SELECT SID AS 'SID' 
              ,CUSTOMER_ACCT AS 'CUSTOMER ACCT'
              ,ATM_ACCOUNT AS 'ATM ACCOUNT'
              ,TRAN_ID AS 'TTRAN ID'
              ,TRAN_DATE AS 'TRAN DATE'
              ,VALUE_DATE AS 'VALUE DATE'
              ,CUSTOMER_ACCT1 AS 'CUSTOMER ACCT1'
              ,ATM_ACCOUNT1 AS 'ATM ACCOUNT1'
              ,DRCR AS 'DRCR'
              ,AMOUNT AS 'AMOUNT'
              ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
              ,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
              ,TRAN_REMARKS AS 'TRAN REMARKS'
              ,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
              ,REF_CRNCY_CODE AS 'REF CRNCY CODE'
              ,REF_AMT AS 'REF AMT'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
              ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID' 
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA'
              ,FILE_NAME AS 'FILE NAME' FROM CO_ATM_CBS_STG WITH
	          (NOLOCK) WHERE ACTIVE_INDEX='N' AND
		       TRAN_DATE BETWEEN ? AND ?
	 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ATM INTERNAL SUPPRESS -->

<query id="563">
		<name>ATM_RECONCILATION_SUPPRESS_EXTERNAL_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
            SELECT SID AS 'SID'
              ,TXNMESSAGES_ID AS 'TXNMESSAGES ID'
              ,CREATEDDATE AS 'CREATEDDATE' 
              ,TXNDATETIME AS 'XNDATETIME'
              ,TXNDATE AS 'TXNDATE'
              ,TXNTIME AS 'TXNTIME'
              ,TERMINALID 'TERMINALID'
              ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
              ,TXNTYPE_ID AS 'TXNTYPE ID'
              ,TXNTYPE AS 'TXNTYPE'
              ,CHIPCARD AS 'CHIPCARD'
              ,CARDNUMBER AS 'CARDNUMBER'
              ,ACCOUNTNO1 AS 'ACCOUNTNO1'
              ,ACCOUNTNO2 AS 'ACCOUNTNO2'
              ,AMOUNT AS 'AMOUNT'
              ,NOTEDETAILS AS 'NOTEDETAILS'
              ,CARDTAKEN AS 'CARDTAKEN'
              ,CARDCAPTURE AS 'CARDCAPTURE'
              ,NOTESPRESENTED AS 'NOTESPRESENTED'
              ,NOTESTAKEN AS 'NOTESTAKEN'
              ,NOTESRETRACT AS 'NOTESRETRACT'
              ,RESPONSECODE AS 'RESPONSECODE'
               ,RESPONSEDESC AS 'RESPONSEDESC'
              ,HARDWARESTATUS AS 'HARDWARESTATUS'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
               ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID'
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA'
              ,FILE_NAME AS 'FILE NAME'
               FROM CO_ATM_JOURNAL_STG  WITH
			  (NOLOCK) WHERE ACTIVE_INDEX='N' AND
			   TXNDATETIME BETWEEN ? AND ?
		  </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>ATM_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<!-- CDM TECONCILIATION -->
<!-- CDM RECONCILIATION INTERNAL RECON  -->
<query id="563">
		<name>CDM_INTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
            SELECT SID AS 'SID'
                  ,CDM_ID AS 'CDM ID'
                  ,CDM_BRANCH AS 'CDM BRANCH'
                 ,TRAN_ID AS 'TRAN ID'
                 ,TRAN_DATE AS 'TRAN DATE'
                 ,VALUE_DATE AS 'VALUE DATE'
                 ,CUSTOMER_ACCT AS 'CUSTOMER ACCT'
                 ,CDM_ACCOUNT AS 'CDM ACCOUNT'
                 ,DRCR AS 'DRCR'
                 ,AMOUNT AS 'AMOUNT'
                 ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
                 ,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
                 ,TRAN_REMARKS AS 'TRAN REMARKS'
                 ,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
                 ,REF_CRNCY_CODE AS 'REF CRNCY CODE'
                 ,REF_AMT AS 'REF AMT'
                 ,COMMENTS AS 'COMMENTS'
                 ,VERSION AS 'VERSION'
                 ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                 ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                 ,UPDATED_ON AS 'UPDATED ON'
                 ,CREATED_ON AS 'CREATED ON'
                 ,RECON_STATUS AS 'RECON STATUS'
                 ,RECON_ID AS 'RECON ID'
                 ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                 ,MAIN_REV_IND AS 'MAIN REV IND'
                 ,OPERATION AS 'OPERATION'
                 ,FILE_NAME AS 'FILE NAME'
                 ,BUSINESS_AREA AS 'BUSINESS AREA'
                  FROM CO_CDM_CBS_STG WITH
			      (NOLOCK) WHERE RECON_STATUS IN ('AM','MM')
			      AND ACTIVE_INDEX='Y' AND
			      TRAN_DATE BETWEEN ? AND ?
		  </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- CDM RECONCILIATION INTERNAL RECON  -->

<query id="563">
		<name>CDM_INTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
            SELECT SID AS 'SID'
                  ,CDM_ID AS 'CDM ID'
                  ,CDM_BRANCH AS 'CDM BRANCH'
                 ,TRAN_ID AS 'TRAN ID'
                 ,TRAN_DATE AS 'TRAN DATE'
                 ,VALUE_DATE AS 'VALUE DATE'
                 ,CUSTOMER_ACCT AS 'CUSTOMER ACCT'
                 ,CDM_ACCOUNT AS 'CDM ACCOUNT'
                 ,DRCR AS 'DRCR'
                 ,AMOUNT AS 'AMOUNT'
                 ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
                 ,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
                 ,TRAN_REMARKS AS 'TRAN REMARKS'
                 ,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
                 ,REF_CRNCY_CODE AS 'REF CRNCY CODE'
                 ,REF_AMT AS 'REF AMT'
                 ,COMMENTS AS 'COMMENTS'
                 ,VERSION AS 'VERSION'
                 ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                 ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                 ,UPDATED_ON AS 'UPDATED ON'
                 ,CREATED_ON AS 'CREATED ON'
                 ,RECON_STATUS AS 'RECON STATUS'
                 ,RECON_ID AS 'RECON ID'
                 ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                 ,MAIN_REV_IND AS 'MAIN REV IND'
                 ,OPERATION AS 'OPERATION'
                 ,FILE_NAME AS 'FILE NAME'
                 ,BUSINESS_AREA AS 'BUSINESS AREA',DATEDIFF(day,TRAN_DATE,GETDATE()) AS 'AGE'
                  FROM CO_CDM_CBS_STG WITH
			      (NOLOCK) WHERE RECON_STATUS IN ('AU','MU')
			      AND ACTIVE_INDEX='Y' AND
			      TRAN_DATE BETWEEN ? AND ?
		  </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- CDM RECONCILIATION EXTERNAL RECONRECNO  -->

<query id="563">
		<name>CDM_EXTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
       SELECT SID AS 'SID'
                ,TXNMESSAGES_ID AS 'TXNMESSAGES ID'
                ,CREATEDDATE AS 'CREATEDDATE'
                ,TXNDATETIME AS 'TXNDATETIME'
                ,TXNDATE AS 'TXNDATE'
                ,TXNTIME AS 'TXNTIME'
                ,TERMINALID AS 'TERMINALID'
                ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
                ,TXNTYPE_ID AS 'TXNTYPE ID'
                ,TXNTYPE AS 'TXNTYPE'
                ,CARDNUMBER AS 'CARDNUMBER'
                ,ACCOUNTNO1 AS 'ACCOUNTNO1'
                ,ACCOUNTNAME AS 'ACCOUNTNAME'
                ,AMOUNT AS 'AMOUNT'
                ,NOTEDETAILS AS 'NOTEDETAILS'
                ,CARDTAKEN AS 'CARDTAKEN'
                ,CARDCAPTURE AS 'CARDCAPTURE'
                ,NOTESENCASHED AS 'NOTESENCASHED'
                ,CASHRETRACT AS 'CASHRETRACT'
                ,RESPONSECODE AS 'RESPONSECODE'
                ,RESPONSEDESC AS 'RESPONSEDESC'
                ,HARDWARESTATUS AS 'HARDWARESTATUS'
                ,COMMENTS AS 'COMMENTS'
                ,VERSION AS 'VERSION'
                ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                ,UPDATED_ON AS 'UPDATED ON'
                ,CREATED_ON AS 'CREATED ON'
                ,RECON_STATUS AS 'RECON STATUS'
                ,RECON_ID AS 'RECON ID'
                ,ACTIVITY_COMMENTS AS 'ACTIVITY_COMMENTS'
                ,MAIN_REV_IND AS 'MAIN_REV_IND'
                ,OPERATION AS 'OPERATION'
                ,FILE_NAME AS 'FILE_NAME'
                ,BUSINESS_AREA AS 'BUSINESS_AREA'
                 FROM CO_CDM_JOURNAL_STG WITH
			     (NOLOCK) WHERE RECON_STATUS
			     IN ('AM','MM') AND ACTIVE_INDEX='Y' AND
			     TXNDATETIME BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- CDM RECONCILIATION EXTERNAL UNRECONRECNO  -->
<query id="563">
	<name>CDM_EXTERNAL_UNRECONCILE_REPORT</name>
	 <queryType>SELECT</queryType>
		<queryString> 
            SELECT SID AS 'SID'
            ,TXNMESSAGES_ID AS 'TXNMESSAGES ID'
            ,CREATEDDATE AS 'CREATEDDATE'
            ,TXNDATETIME AS 'TXNDATETIME'
            ,TXNDATE AS 'TXNDATE'
            ,TXNTIME AS 'TXNTIME'
            ,TERMINALID AS 'TERMINALID'
            ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
            ,TXNTYPE_ID AS 'TXNTYPE ID'
            ,TXNTYPE AS 'TXNTYPE'
            ,CARDNUMBER AS 'CARDNUMBER'
            ,ACCOUNTNO1 AS 'ACCOUNTNO1'
            ,ACCOUNTNAME AS 'ACCOUNTNAME'
            ,AMOUNT AS 'AMOUNT'
            ,NOTEDETAILS AS 'NOTEDETAILS'
            ,CARDTAKEN AS 'CARDTAKEN'
            ,CARDCAPTURE AS 'CARDCAPTURE'
            ,NOTESENCASHED AS 'NOTESENCASHED'
            ,CASHRETRACT AS 'CASHRETRACT'
            ,RESPONSECODE AS 'RESPONSECODE'
            ,RESPONSEDESC AS 'RESPONSEDESC'
            ,HARDWARESTATUS AS 'HARDWARESTATUS'
            ,COMMENTS AS 'COMMENTS'
            ,VERSION AS 'VERSION'
            ,ACTIVE_INDEX AS 'ACTIVE INDEX'
            ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
            ,UPDATED_ON AS 'UPDATED ON'
            ,CREATED_ON AS 'CREATED ON'
            ,RECON_STATUS AS 'RECON STATUS'
            ,RECON_ID AS 'RECON ID'
            ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
            ,MAIN_REV_IND AS 'MAIN REV IND'
            ,OPERATION AS 'OPERATION'
            ,FILE_NAME AS 'FILE NAME'
            ,BUSINESS_AREA AS 'BUSINESS AREA',DATEDIFF(day,TXNDATETIME,GETDATE()) AS 'AGE'
             FROM CO_CDM_JOURNAL_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IS NULL
			AND ACTIVE_INDEX='Y' AND
			TXNDATETIME BETWEEN ? AND ?
			</queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- CDM RECONCILILATION SUPPRESS INTERNAL -->

<query id="563">
		<name>CDM_SUPPRESS_INTERNAL_REPORT_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
            SELECT SID AS 'SID'
                  ,CDM_ID AS 'CDM ID'
                  ,CDM_BRANCH AS 'CDM BRANCH'
                 ,TRAN_ID AS 'TRAN ID'
                 ,TRAN_DATE AS 'TRAN DATE'
                 ,VALUE_DATE AS 'VALUE DATE'
                 ,CUSTOMER_ACCT AS 'CUSTOMER ACCT'
                 ,CDM_ACCOUNT AS 'CDM ACCOUNT'
                 ,DRCR AS 'DRCR'
                 ,AMOUNT AS 'AMOUNT'
                 ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
                 ,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
                 ,TRAN_REMARKS AS 'TRAN REMARKS'
                 ,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
                 ,REF_CRNCY_CODE AS 'REF CRNCY CODE'
                 ,REF_AMT AS 'REF AMT'
                 ,COMMENTS AS 'COMMENTS'
                 ,VERSION AS 'VERSION'
                 ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                 ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                 ,UPDATED_ON AS 'UPDATED ON'
                 ,CREATED_ON AS 'CREATED ON'
                 ,RECON_STATUS AS 'RECON STATUS'
                 ,RECON_ID AS 'RECON ID'
                 ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                 ,MAIN_REV_IND AS 'MAIN REV IND'
                 ,OPERATION AS 'OPERATION'
                 ,FILE_NAME AS 'FILE NAME'
                 ,BUSINESS_AREA AS 'BUSINESS AREA'
                  FROM CO_CDM_CBS_STG WITH
			      (NOLOCK) WHERE ACTIVE_INDEX='N' AND
			      TRAN_DATE BETWEEN ? AND ?
		  </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- CDM RECONCILILATION SUPPRESS EXTERNAL -->
<query id="563">
		<name>CDM_SUPPRESS_EXTERNAL_REPORT_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
       SELECT SID AS 'SID'
                ,TXNMESSAGES_ID AS 'TXNMESSAGES ID'
                ,CREATEDDATE AS 'CREATEDDATE'
                ,TXNDATETIME AS 'TXNDATETIME'
                ,TXNDATE AS 'TXNDATE'
                ,TXNTIME AS 'TXNTIME'
                ,TERMINALID AS 'TERMINALID'
                ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
                ,TXNTYPE_ID AS 'TXNTYPE ID'
                ,TXNTYPE AS 'TXNTYPE'
                ,CARDNUMBER AS 'CARDNUMBER'
                ,ACCOUNTNO1 AS 'ACCOUNTNO1'
                ,ACCOUNTNAME AS 'ACCOUNTNAME'
                ,AMOUNT AS 'AMOUNT'
                ,NOTEDETAILS AS 'NOTEDETAILS'
                ,CARDTAKEN AS 'CARDTAKEN'
                ,CARDCAPTURE AS 'CARDCAPTURE'
                ,NOTESENCASHED AS 'NOTESENCASHED'
                ,CASHRETRACT AS 'CASHRETRACT'
                ,RESPONSECODE AS 'RESPONSECODE'
                ,RESPONSEDESC AS 'RESPONSEDESC'
                ,HARDWARESTATUS AS 'HARDWARESTATUS'
                ,COMMENTS AS 'COMMENTS'
                ,VERSION AS 'VERSION'
                ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                ,UPDATED_ON AS 'UPDATED ON'
                ,CREATED_ON AS 'CREATED ON'
                ,RECON_STATUS AS 'RECON STATUS'
                ,RECON_ID AS 'RECON ID'
                ,ACTIVITY_COMMENTS AS 'ACTIVITY_COMMENTS'
                ,MAIN_REV_IND AS 'MAIN_REV_IND'
                ,OPERATION AS 'OPERATION'
                ,FILE_NAME AS 'FILE_NAME'
                ,BUSINESS_AREA AS 'BUSINESS_AREA'
                 FROM CO_CDM_JOURNAL_STG WITH
			    (NOLOCK) WHERE ACTIVE_INDEX='N' AND
			     TXNDATETIME BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>CDM_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CDM_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<!-- ACH Reconciliation INTERNAL -->
<!-- ACH INTERNAL  RECON-->
<query id="563">
		<name>ACH_INTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
        SELECT SID AS 'SID' 
               ,ACCT_NUM AS 'ACCT NUM'
               ,DRCR AS 'DRCR'
               ,EXTRACTION_DATE AS 'EXTRACTION DATE'
               ,CHNL AS 'CHANNEL'
               ,END_TO_END_ID 'END TO END ID'
               ,TRANID AS 'TRANID'
               ,TRAN_DT AS 'TRAN DATE'
                ,VALUE_DATE AS 'VALUE DATE'
                ,TRAN_AMOUNT AS 'TRAN AMOUNT'
                ,FILENAME AS 'FILENAME'
                ,TAG_20 AS 'TAG 20'
                ,TAG_21 AS 'TAG 21'
                ,BANK AS 'BANK'
                ,MSG_GENERATED AS 'MSG GENERATED'
                ,COMMENTS AS 'COMMENTS'
                ,VERSION AS 'VERSION'
                ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                ,UPDATED_ON AS 'UPDATED ON'
                ,CREATED_ON AS 'CREATED ON'
                ,RECON_STATUS AS 'RECON STATUS'
                ,RECON_ID AS 'RECON ID'
                ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                ,MAIN_REV_IND AS 'MAIN REV IND'
                ,OPERATION AS 'OPERATION'
                ,BUSINESS_AREA AS 'BUSINESS AREA'
                FROM CO_ACH_CBS_STG WITH
			     (NOLOCK) WHERE RECON_STATUS
			     IN ('AM','MM')
			     AND ACTIVE_INDEX='Y' AND
			     TRAN_DT BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ACH INTERNAL UNRECON -->

<query id="563">
		<name>ACH_INTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
       	SELECT SID AS 'SID'  
              ,ACCT_NUM AS 'ACCT NUM'
              ,DRCR AS 'DRCR'
              ,EXTRACTION_DATE AS 'EXTRACTION DATE'
              ,CHNL AS 'CHANNEL'
              ,END_TO_END_ID 'END TO END ID'
              ,TRANID AS 'TRANID'
              ,TRAN_DT AS 'TRAN DATE'
              ,VALUE_DATE AS 'VALUE DATE'
              ,TRAN_AMOUNT AS 'TRAN AMOUNT'
              ,FILENAME AS 'FILENAME'
              ,TAG_20 AS 'TAG 20'
              ,TAG_21 AS 'TAG 21'
              ,BANK AS 'BANK'
              ,MSG_GENERATED AS 'MSG GENERATED'
              ,COMMENTS AS 'COMMENTS'
              ,VERSION AS 'VERSION'
              ,ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,UPDATED_ON AS 'UPDATED ON'
              ,CREATED_ON AS 'CREATED ON'
              ,RECON_STATUS AS 'RECON STATUS'
              ,RECON_ID AS 'RECON ID'
              ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,MAIN_REV_IND AS 'MAIN REV IND'
              ,OPERATION AS 'OPERATION'
              ,BUSINESS_AREA AS 'BUSINESS AREA',DATEDIFF(day,TRAN_DT,GETDATE()) AS 'AGE'
               FROM CO_ACH_CBS_STG  WITH
			   (NOLOCK) WHERE RECON_STATUS IN ('AU','MU')
			   AND ACTIVE_INDEX='Y' AND
			   TRAN_DT BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>


<query id="563">
		<name>ACH_EXTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
       	          SELECT SID AS 'SID'
                       ,STATUS AS 'STATUS'
                       ,DIRECTION AS 'DIRECTION'
                       ,TYPE AS 'TYPE'
                       ,TRANSACTION_ID AS 'TRANSACTION ID'
                       ,END_TO_END_ID AS 'END TO END ID'
                       ,INSTRUCTION_ID AS 'INSTRUCTION ID'
                       ,BATCH AS 'BATCH'
                       ,CURRENCY AS 'CURRENCY'
                       ,AMOUNT AS 'AMOUNT'
                       ,INSTRUCTING_PARTICIPANT AS 'INSTRUCTING PARTICIPANT'
                       ,INSTRUCTING_BRANCH AS 'INSTRUCTING BRANCH'
                       ,INSTRUCTED_PARTICIPANT AS 'INSTRUCTED PARTICIPANT'
                       ,INSTRUCTED_BRANCH AS 'INSTRUCTED BRANCH'
                       ,DEBTOR_BANK AS 'DEBTOR BANK'
                       ,DEBTOR_BRANCH AS 'DEBTOR BRANCH'
                       ,DEBTOR_NAME AS 'DEBTOR NAME'
                       ,DEBTOR_ACCOUNT AS 'DEBTOR ACCOUNT'
                       ,DEBTOR_IBAN AS 'DEBTOR IBAN'
                       ,CREDITOR_BANK AS 'CREDITOR BANK'
                       ,CREDITOR_BRANCH AS 'CREDITOR BRANCH'
                       ,CREATED_ON AS 'CREATED ON'
                       ,UPDATED_ON AS 'UPDATED ON'
                       ,DUE_ON AS 'DUE ON'
                       ,DELETED_ON AS 'DELETED ON'
                       ,CREDITOR_NAME AS 'CREDITOR NAME'
                       ,CREATED_BY AS 'CREATED BY'
                       ,UPDATED_BY AS 'UPDATED BY'
                       ,LOCKED_BY AS 'LOCKED BY'
                       ,DELETED_BY AS 'DELETED BY'
                       ,DELETED AS 'DELETED'
                       ,CREDITOR_ACCOUNT AS 'DELETED'
                       ,CREDITOR_IBAN AS 'CREDITOR IBAN'
                       ,TRANSACTION_PURPOSE AS 'TRANSACTION PURPOSE'
                       ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'
                       ,SESSION_NO AS 'SESSION NO'
                       ,REASON AS 'REASON'
                       ,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
                       ,RELATED_REASON AS 'RELATED REASON'
                       ,RELATED_ADDITIONAL_INFO AS 'RELATED ADDITIONAL INFO'
                       ,MANDATE AS 'MANDATE'
                       ,CANDIDATE_PAYMENT AS 'CANDIDATE PAYMENT'
                       ,COMMENTS AS 'COMMENTS'
                       ,VERSION AS 'VERSION'
                       ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                       ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                       ,RECON_STATUS AS 'RECON STATUS'
                       ,RECON_ID AS 'RECON ID'
                       ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                       ,MAIN_REV_IND AS 'MAIN REV IND'
                       ,OPERATION AS 'OPERATION'
                       ,FILE_NAME AS 'FILE NAME'
                       ,BUSINESS_AREA AS 'BUSINESS AREA'
                       FROM CO_ACH_CBO_STG  WITH
			           (NOLOCK) WHERE RECON_STATUS IN ('AM','MM')
			           AND ACTIVE_INDEX='Y' AND SETTLEMENT_DATE BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ACH EXTERNAL UNRECON  -->

<query id="563">
		<name>ACH_EXTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
       	          SELECT SID AS 'SID'
                       ,STATUS AS 'STATUS'
                       ,DIRECTION AS 'DIRECTION'
                       ,TYPE AS 'TYPE'
                       ,TRANSACTION_ID AS 'TRANSACTION ID'
                       ,END_TO_END_ID AS 'END TO END ID'
                       ,INSTRUCTION_ID AS 'INSTRUCTION ID'
                       ,BATCH AS 'BATCH'
                       ,CURRENCY AS 'CURRENCY'
                       ,AMOUNT AS 'AMOUNT'
                       ,INSTRUCTING_PARTICIPANT AS 'INSTRUCTING PARTICIPANT'
                       ,INSTRUCTING_BRANCH AS 'INSTRUCTING BRANCH'
                       ,INSTRUCTED_PARTICIPANT AS 'INSTRUCTED PARTICIPANT'
                       ,INSTRUCTED_BRANCH AS 'INSTRUCTED BRANCH'
                       ,DEBTOR_BANK AS 'DEBTOR BANK'
                       ,DEBTOR_BRANCH AS 'DEBTOR BRANCH'
                       ,DEBTOR_NAME AS 'DEBTOR NAME'
                       ,DEBTOR_ACCOUNT AS 'DEBTOR ACCOUNT'
                       ,DEBTOR_IBAN AS 'DEBTOR IBAN'
                       ,CREDITOR_BANK AS 'CREDITOR BANK'
                       ,CREDITOR_BRANCH AS 'CREDITOR BRANCH'
                       ,CREATED_ON AS 'CREATED ON'
                       ,UPDATED_ON AS 'UPDATED ON'
                       ,DUE_ON AS 'DUE ON'
                       ,DELETED_ON AS 'DELETED ON'
                       ,CREDITOR_NAME AS 'CREDITOR NAME'
                       ,CREATED_BY AS 'CREATED BY'
                       ,UPDATED_BY AS 'UPDATED BY'
                       ,LOCKED_BY AS 'LOCKED BY'
                       ,DELETED_BY AS 'DELETED BY'
                       ,DELETED AS 'DELETED'
                       ,CREDITOR_ACCOUNT AS 'DELETED'
                       ,CREDITOR_IBAN AS 'CREDITOR IBAN'
                       ,TRANSACTION_PURPOSE AS 'TRANSACTION PURPOSE'
                       ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'
                       ,SESSION_NO AS 'SESSION NO'
                       ,REASON AS 'REASON'
                       ,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
                       ,RELATED_REASON AS 'RELATED REASON'
                       ,RELATED_ADDITIONAL_INFO AS 'RELATED ADDITIONAL INFO'
                       ,MANDATE AS 'MANDATE'
                       ,CANDIDATE_PAYMENT AS 'CANDIDATE PAYMENT'
                       ,COMMENTS AS 'COMMENTS'
                       ,VERSION AS 'VERSION'
                       ,ACTIVE_INDEX AS 'ACTIVE INDEX'
                       ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                       ,RECON_STATUS AS 'RECON STATUS'
                       ,RECON_ID AS 'RECON ID'
                       ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                       ,MAIN_REV_IND AS 'MAIN REV IND'
                       ,OPERATION AS 'OPERATION'
                       ,FILE_NAME AS 'FILE NAME'
                       ,BUSINESS_AREA AS 'BUSINESS AREA',DATEDIFF(day,SETTLEMENT_DATE,GETDATE()) AS 'AGE'
                       FROM CO_ACH_CBO_STG  WITH
			           (NOLOCK) WHERE RECON_STATUS IS NULL
			           AND ACTIVE_INDEX='Y' AND SETTLEMENT_DATE BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ACH INTERNAL SUPPRESS  -->

<query id="563">
		<name>ACH_SUPPRESS_INTERNAL_REPORT_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
       	          SELECT SID AS 'SID' 
      ,ACCT_NUM AS 'ACCT NUM'
      ,DRCR AS 'DRCR'
      ,EXTRACTION_DATE AS 'EXTRACTION DATE'
      ,CHNL AS 'CHANNEL'
      ,END_TO_END_ID 'END TO END ID'
      ,TRANID AS 'TRANID'
      ,TRAN_DT AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,FILENAME AS 'FILENAME'
      ,TAG_20 AS 'TAG 20'
      ,TAG_21 AS 'TAG 21'
      ,BANK AS 'BANK'
      ,MSG_GENERATED AS 'MSG GENERATED'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,BUSINESS_AREA AS 'BUSINESS_AREA'
  FROM CO_ACH_CBS_STG  WITH
			(NOLOCK) WHERE
			 ACTIVE_INDEX='N' AND
			TRAN_DT BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ACH EXTERNAL SUPPRESS  -->

<query id="563">
		<name>ACH_SUPPRESS_EXTERNAL_REPORT_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
     SELECT OP 1000 SID AS 'SID'
      ,STATUS AS 'STATUS'
      ,DIRECTION AS 'DIRECTION'
      ,TYPE AS 'TYPE'
      ,TRANSACTION_ID AS 'TRANSACTION ID'
      ,END_TO_END_ID AS 'END TO END ID'
      ,INSTRUCTION_ID AS 'INSTRUCTION ID'
      ,BATCH AS 'BATCH'
      ,CURRENCY AS 'CURRENCY'
      ,AMOUNT AS 'AMOUNT'
      ,INSTRUCTING_PARTICIPANT AS 'INSTRUCTING PARTICIPANT'
      ,INSTRUCTING_BRANCH AS 'INSTRUCTING BRANCH'
      ,INSTRUCTED_PARTICIPANT AS 'INSTRUCTED PARTICIPANT'
      ,INSTRUCTED_BRANCH AS 'INSTRUCTED BRANCH'
      ,DEBTOR_BANK AS 'DEBTOR BANK'
      ,DEBTOR_BRANCH AS 'DEBTOR BRANCH'
      ,DEBTOR_NAME AS 'DEBTOR NAME'
      ,DEBTOR_ACCOUNT AS 'DEBTOR ACCOUNT'
      ,DEBTOR_IBAN AS 'DEBTOR IBAN'
      ,CREDITOR_BANK AS 'CREDITOR BANK'
      ,CREDITOR_BRANCH AS 'CREDITOR BRANCH'
      ,CREATED_ON AS 'CREATED ON'
      ,UPDATED_ON AS 'UPDATED ON'
      ,DUE_ON AS 'DUE ON'
      ,DELETED_ON AS 'DELETED ON'
      ,CREDITOR_NAME AS 'CREDITOR NAME'
      ,CREATED_BY AS 'CREATED BY'
      ,UPDATED_BY AS 'UPDATED BY'
      ,LOCKED_BY AS 'LOCKED BY'
      ,DELETED_BY AS 'DELETED BY'
      ,DELETED AS 'DELETED'
      ,CREDITOR_ACCOUNT AS 'CREDITOR ACCOUNT'
      ,CREDITOR_IBAN AS 'CREDITOR IBAN'
      ,TRANSACTION_PURPOSE AS 'TRANSACTION PURPOSE'
      ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'
      ,SESSION_NO AS 'SESSION NO'
      ,REASON AS 'REASON'
      ,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
      ,RELATED_REASON AS 'RELATED REASON'
      ,RELATED_ADDITIONAL_INFO AS 'RELATED ADDITIONAL INFO'
      ,MANDATE AS 'MANDATE'
      ,CANDIDATE_PAYMENT AS 'CANDIDATE PAYMENT'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      FROM CO_ACH_CBO_STG  WITH
			(NOLOCK) WHERE ACTIVE_INDEX='N' AND
			SETTLEMENT_DATE BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

	
	<query id="563">
		<name>ACH_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ACH_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ACH_EXTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ACH_EXTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<!-- POSNI ACQUIRER -->
	<!-- POSNI ACQUIRER INTERNAL RECONCILE -->

	<query id="563">
		<name>POSNI_ACQUIRER_INTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT SID As 'SID'
			,ACCT_NUM AS 'ACCT NUM'
			,CARD_TYPE AS
			'CARD TYPE'
			,VALUE_DATE AS 'VALUE DATE'
			,TRAN_DATE AS 'TRAN DATE'
			,REF_NUM AS 'REF NO'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_AMT AS 'TRAN AMT'
			,PART_TRAN_TYPE AS 'PART TRAN TYPE'
			,TRAN_RMKS AS 'TRAN RMKS'
			,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,PSTD_FLG AS 'PSTD FLG'
			,PSTD_DATE AS 'PSTD DATE'
			,ACTIVE_INDEX AS 'ACTIVE INDEX'
			,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,UPDATED_ON AS 'UPDATED ON'
			,CREATED_ON AS 'CREATED ON'
			,RECON_STATUS AS 'RECON STATUS'
			,RECON_ID
			AS 'RECON ID'
			,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,MAIN_REV_IND
			AS 'MAIN REV IND'
			,OPERATION AS 'OPERATION'
			,BUSINESS_AREA AS 'BUSINESS
			AREA'
			,VERSION AS 'VERSION'
			FROM CARD_POSNI_CBS_STG WITH
			(NOLOCK) WHERE
			RECON_STATUS IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN
			? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- POSNI ACQUIRER INTERNAL UNRECONCILE -->
	<query id="563">
		<name>POSNI_ACQUIRER_INTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT 'SID'
			,ACCT_NUM AS 'ACCT NUM'
			,CARD_TYPE AS 'CARD
			TYPE'
			,VALUE_DATE AS 'VALUE DATE'
			,TRAN_DATE AS 'TRAN DATE'
			,REF_NUM AS
			'REF NUM'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_AMT AS 'TRAN AMT'
			,PART_TRAN_TYPE
			AS 'PART TRAN TYPE'
			,TRAN_RMKS AS 'TRAN RMKS'
			,TRAN_PARTICULAR AS 'TRAN
			PARTICULAR'
			,PSTD_FLG AS 'PSTD FLG'
			,PSTD_DATE AS 'PSTD DATE'
			,ACTIVE_INDEX AS 'ACTIVE INDEX'
			,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,UPDATED_ON AS 'UPDATED ON'
			,CREATED_ON AS 'CREATED ON'
			,RECON_STATUS
			AS 'RECON STATUS'
			,RECON_ID AS 'RECON ID'
			,ACTIVITY_COMMENTS AS
			'ACTIVITY COMMENTS'
			,MAIN_REV_IND AS 'MAIN REV IND'
			,OPERATION AS
			'OPERATION'
			,BUSINESS_AREA AS 'BUSINESS AREA'
			,VERSION AS 'VERSION',
			DATEDIFF(day,TRAN_DATE,GETDATE()) AS 'AGE'
			FROM CARD_POSNI_CBS_STG
			WITH
			(NOLOCK) WHERE RECON_STATUS IN ('AU','MU')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- POSNI ACQUIRER EXTERNAL RECONCILE -->
	<query id="563">
		<name>POSNI_ACQUIRER_EXTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT SID AS 'SID'
			,ACCT_NUM AS 'ACCT NUM'
			,CARD_TYPE AS
			'CARD TYPE'
			,FILE_NAME AS 'FILE NAME'
			,VALUE_DATE AS 'VALUE DATE'
			,UPLOAD_DATE AS 'UPLOAD DATE'
			,REF_NUM AS 'REF NUM'
			,CARD_NO AS 'CARD
			NO'
			,TRAN_AMT AS 'TRAN AMT'
			,MERCHANT_ID AS'MERCHANT ID'
			,MERCHANT_NAME
			AS 'MERCHANT NAME'
			,LOCATION AS 'LOCATION'
			,TRAN_CRNCY AS 'TRAN CRNCY'
			,TRAN_TYPE AS 'TRAN TYPE'
			,ACTIVE_INDEX AS 'ACTIVE INDEX'
			,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,UPDATED_ON AS 'UPDATED ON'
			,CREATED_ON AS 'CREATED ON'
			,RECON_STATUS AS 'RECON STATUS'
			,RECON_ID
			AS 'RECON ID'
			,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,MAIN_REV_IND
			AS 'MAIN REV IND'
			,OPERATION AS 'OPERATION'
			,BUSINESS_AREA AS 'BUSINESS
			AREA'
			,VERSION AS 'VERSION'
			FROM CARD_POSNI_TRAN_STG WITH
			(NOLOCK) WHERE
			RECON_STATUS IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			VALUE_DATE BETWEEN
			? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- POSNI ACQUIRER EXTERNAL UNRECONCILE -->
	<query id="563">
		<name>POSNI_ACQUIRER_EXTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT SID AS 'SID'
			,ACCT_NUM AS 'ACCT NUM'
			,CARD_TYPE AS
			'CARD TYPE'
			,FILE_NAME AS 'FILE NAME'
			,VALUE_DATE AS 'VALUE DATE'
			,UPLOAD_DATE AS 'UPLOAD DATE'
			,REF_NUM AS 'REF NUM'
			,CARD_NO AS 'CARD
			NO'
			,TRAN_AMT AS 'TRAN AMT'
			,MERCHANT_ID AS'MERCHANT ID'
			,MERCHANT_NAME
			AS 'MERCHANT NAME'
			,LOCATION AS 'LOCATION'
			,TRAN_CRNCY AS 'TRAN CRNCY'
			,TRAN_TYPE AS 'TRAN TYPE'
			,ACTIVE_INDEX AS 'ACTIVE INDEX'
			,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,UPDATED_ON AS 'UPDATED ON'
			,CREATED_ON AS 'CREATED ON'
			,RECON_STATUS AS 'RECON STATUS'
			,RECON_ID
			AS 'RECON ID'
			,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,MAIN_REV_IND
			AS 'MAIN REV IND'
			,OPERATION AS 'OPERATION'
			,BUSINESS_AREA AS 'BUSINESS
			AREA'
			,VERSION AS 'VERSION',DATEDIFF(day,VALUE_DATE,GETDATE()) AS
			'AGE'
			FROM CARD_POSNI_TRAN_STG WITH
			(NOLOCK) WHERE RECON_STATUS IS NULL
			AND ACTIVE_INDEX='Y' AND
			VALUE_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- POSNI ACQUIRER SUPPRESS INTERNAL -->

	<query id="563">
		<name>POSNI_ACQUIRER_SUPPRESS_INTERNAL_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT SID AS 'SID'
			,ACCT_NUM AS 'ACCT NUM'
			,CARD_TYPE AS 'CARD TYPE'
			,VALUE_DATE AS 'VALUE DATE'
			,TRAN_DATE AS 'TRAN DATE'
			,REF_NUM AS 'REF NO'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_AMT AS 'TRAN AMT'
			,PART_TRAN_TYPE AS 'PART TRAN TYPE'
			,TRAN_RMKS AS 'TRAN RMKS'
			,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,PSTD_FLG AS 'PSTD FLG'
			,PSTD_DATE AS 'PSTD DATE'
			,ACTIVE_INDEX AS 'ACTIVE INDEX'
			,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,UPDATED_ON AS 'UPDATED ON'
			,CREATED_ON AS 'CREATED ON'
			,RECON_STATUS AS 'RECON STATUS'
			,RECON_ID AS 'RECON ID'
			,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,MAIN_REV_IND AS 'MAIN REV IND'
			,OPERATION AS 'OPERATION'
			,BUSINESS_AREA AS 'BUSINESS AREA'
			,VERSION AS 'VERSION'
			FROM CARD_POSNI_CBS_STG WITH
			(NOLOCK) WHERE ACTIVE_INDEX='N' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- POSNI ACQUIRER SUPPRESS EXTERNAL -->

	<query id="563">
		<name>POSNI_ACQUIRER_SUPPRESS_EXTERNAL_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT SID AS 'SID'
			,ACCT_NUM AS 'ACCT NUM'
			,CARD_TYPE AS 'CARD TYPE'
			,FILE_NAME AS 'FILE NAME'
			,VALUE_DATE AS 'VALUE DATE'
			,UPLOAD_DATE AS 'UPLOAD DATE'
			,REF_NUM AS 'REF NUM'
			,CARD_NO AS 'CARD NO'
			,TRAN_AMT AS 'TRAN AMT'
			,MERCHANT_ID AS'MERCHANT ID'
			,MERCHANT_NAME AS 'MERCHANT NAME'
			,LOCATION AS 'LOCATION'
			,TRAN_CRNCY AS 'TRAN CRNCY'
			,TRAN_TYPE AS 'TRAN TYPE'
			,ACTIVE_INDEX AS 'ACTIVE INDEX'
			,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,UPDATED_ON AS 'UPDATED ON'
			,CREATED_ON AS 'CREATED ON'
			,RECON_STATUS AS 'RECON STATUS'
			,RECON_ID AS 'RECON ID'
			,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,MAIN_REV_IND AS 'MAIN REV IND'
			,OPERATION AS 'OPERATION'
			,BUSINESS_AREA AS 'BUSINESS AREA'
			,VERSION AS 'VERSION'
			FROM CARD_POSNI_TRAN_STG WITH (NOLOCK)
			WHERE ACTIVE_INDEX='N' AND VALUE_DATE BETWEEN
			? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>POSNI_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM POSNI_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>POSNI_RECONCILE_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM POSNI_RECONCILE_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>POSNI_UNRECONCILE_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM POSNI_UNRECONCILE_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- CREDIT CARD SUPPRESS INTERNAL  -->
	<query id="563">
		<name>CREDIT_SUPRESS_INTERNAL_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,FORACID AS 'FORACID'
      ,CIF_ID As 'CIF ID'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,PART_TRAN_TYPE AS 'PART TRAN TYPE'
      ,TRAN_AMT AS 'TRAN AMT'
      ,CARD_NUM AS 'CARD NUM'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_NOTES AS 'TRAN NOTES'
      ,TXN_CAT AS 'TXN CAT'
      ,TXN_ID AS 'TXN ID'
      ,PSTD_FLG AS 'PSTD FLG'
      ,PSTD_DATE AS 'PSTD DATE'
      ,RECON_STATUS AS 'RECON STATUS'
      ,VERSION AS 'VERSION'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_ID AS 'ACTIVITY ID'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_REMARKS AS 'RECON REMARKS'
      ,CREATIONTIME AS 'CREATIONTIME'
      ,UPDATETIME AS 'UPDATETIME'
      ,UPDATEDBY AS 'UPDATEDBY '
      ,OPSOURCEID AS 'OPSOURCEID'
      ,OPIPADDRESS AS 'OPIPADDRESS'
      ,SOURCE_NAME AS 'SOURCE NAME'
      ,SOURCE_TYPE AS 'SOURCE TYPE'
      ,TTUM_STATUS AS 'TTUM STATUS'
      ,RECON_DATE AS 'RECON DATE'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,UPDATED_ON AS 'UPDATED ON'
      ,OPERATION AS 'OPERATION'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
  FROM CREDIT_CARD_STATEMENT_CBS_STG WITH
			(NOLOCK) WHERE  ACTIVE_INDEX='N' AND
			TRAN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- CREDIT CARD SUPPRESS EXTERNAL  -->
	<query id="563">
		<name>CREDIT_SUPRESS_EXTERNAL_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,DB_TS AS 'DB TS'
      ,TXN_ID AS 'TXN ID'
      ,TXN_DATE AS 'TXN DATE'
      ,TXN_SRL_NO AS 'TXN SRL NO'
      ,TXN_AMT AS 'TXN AMT'
      ,ORG_AMT AS 'ORG AMT'
      ,ORG_CUR AS 'ORG CUR'
      ,TXN_TYP AS 'TXN TYP'
      ,TXN_CAT AS 'TXN CAT'
      ,TXN_ORGN AS 'TXN ORGN'
      ,TXN_DESC AS 'TXN DESC'
      ,BANK_ID AS 'BANK ID'
      ,BRANCH_ID AS 'BRANCH ID'
      ,AC_ID AS 'AC ID'
      ,PRIMARYACID AS 'PRIMARYACID'
      ,CARD_NUMBER AS 'CARD NUMBER'
      ,PST_DATE AS 'PST DATE'
      ,FREE_AMOUNT AS 'FREE AMOUNT'
      ,VALUE_DATE AS 'VALUE DATE'
      ,DEL_FLG AS 'DEL FLG'
      ,R_MOD_ID AS 'R MOD ID'
      ,R_MOD_TIME AS 'R MOD TIME'
      ,R_CRE_ID AS 'R CRE ID'
      ,R_CRE_TIME AS 'R CRE TIME'
      ,RECON_STATUS AS 'RECON STATUS'
      ,VERSION AS 'VERSION'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_ID AS 'ACTIVITY ID'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_REMARKS AS 'RECON REMARKS'
      ,CREATED_ON AS 'CREATIONTIME'
      ,UPDATED_ON AS 'UPDATETIME' 
      ,UPDATEDBY AS 'UPDATEDBY'
      ,OPSOURCEID AS 'OPSOURCEID'
      ,OPIPADDRESS AS 'OPIPADDRESS'
      ,SOURCE_NAME AS 'SOURCE NAME' 
      ,SOURCE_TYPE AS 'SOURCE TYPE'
      ,TTUM_STATUS AS 'TTUM STATUS'
      ,RECON_DATE AS 'RECON DATE'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,UPDATED_ON AS 'UPDATED ON'
      ,OPERATION AS 'OPERATION'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS' 
  FROM CREDIT_CARD_STATEMENT_TRAN_STG WITH
			(NOLOCK) WHERE  ACTIVE_INDEX='N' AND
			TXN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- CREDIT CARD INTERNAL RECON -->
	<query id="563">
		<name>CREDIT_INTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,FORACID AS 'FORACID'
      ,CIF_ID As 'CIF ID'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,PART_TRAN_TYPE AS 'PART TRAN TYPE'
      ,TRAN_AMT AS 'TRAN AMT'
      ,CARD_NUM AS 'CARD NUM'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_NOTES AS 'TRAN NOTES'
      ,TXN_CAT AS 'TXN CAT'
      ,TXN_ID AS 'TXN ID'
      ,PSTD_FLG AS 'PSTD FLG'
      ,PSTD_DATE AS 'PSTD DATE'
      ,RECON_STATUS AS 'RECON STATUS'
      ,VERSION AS 'VERSION'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_ID AS 'ACTIVITY ID'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_REMARKS AS 'RECON REMARKS'
      ,CREATED_ON AS 'CREATIONTIME'
      ,UPDATED_ON AS 'UPDATETIME'
      ,UPDATEDBY AS 'UPDATEDBY '
      ,OPSOURCEID AS 'OPSOURCEID'
      ,OPIPADDRESS AS 'OPIPADDRESS'
      ,SOURCE_NAME AS 'SOURCE NAME'
      ,SOURCE_TYPE AS 'SOURCE TYPE'
      ,TTUM_STATUS AS 'TTUM STATUS'
      ,RECON_DATE AS 'RECON DATE'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,UPDATED_ON AS 'UPDATED ON'
      ,OPERATION AS 'OPERATION'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
  FROM CREDIT_CARD_STATEMENT_CBS_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- CREDIT CARD INTERNAL UNRECON -->
	<query id="563">
		<name>CREDIT_INTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,FORACID AS 'FORACID'
      ,CIF_ID As 'CIF ID'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,PART_TRAN_TYPE AS 'PART TRAN TYPE'
      ,TRAN_AMT AS 'TRAN AMT'
      ,CARD_NUM AS 'CARD NUM'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_NOTES AS 'TRAN NOTES'
      ,TXN_CAT AS 'TXN CAT'
      ,TXN_ID AS 'TXN ID'
      ,PSTD_FLG AS 'PSTD FLG'
      ,PSTD_DATE AS 'PSTD DATE'
      ,RECON_STATUS AS 'RECON STATUS'
      ,VERSION AS 'VERSION'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_ID AS 'ACTIVITY ID'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_REMARKS AS 'RECON REMARKS'
      ,CREATED_ON AS 'CREATIONTIME'
      ,UPDATED_ON AS 'UPDATETIME'
      ,UPDATEDBY AS 'UPDATEDBY '
      ,OPSOURCEID AS 'OPSOURCEID'
      ,OPIPADDRESS AS 'OPIPADDRESS'
      ,SOURCE_NAME AS 'SOURCE NAME'
      ,SOURCE_TYPE AS 'SOURCE TYPE'
      ,TTUM_STATUS AS 'TTUM STATUS'
      ,RECON_DATE AS 'RECON DATE'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,UPDATED_ON AS 'UPDATED ON'
      ,OPERATION AS 'OPERATION'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
     ,DATEDIFF(day,TRAN_DATE,GETDATE() ) AS 'AGE'
  FROM CREDIT_CARD_STATEMENT_CBS_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AU','MU')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- CREDIT CARD EXTERNAL RECON -->
	<query id="563">
		<name>CREDIT_EXTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,DB_TS AS 'DB TS'
      ,TXN_ID AS 'TXN ID'
      ,TXN_DATE AS 'TXN DATE'
      ,TXN_SRL_NO AS 'TXN SRL NO'
      ,TXN_AMT AS 'TXN AMT'
      ,ORG_AMT AS 'ORG AMT'
      ,ORG_CUR AS 'ORG CUR'
      ,TXN_TYP AS 'TXN TYP'
      ,TXN_CAT AS 'TXN CAT'
      ,TXN_ORGN AS 'TXN ORGN'
      ,TXN_DESC AS 'TXN DESC'
      ,BANK_ID AS 'BANK ID'
      ,BRANCH_ID AS 'BRANCH ID'
      ,AC_ID AS 'AC ID'
      ,PRIMARYACID AS 'PRIMARYACID'
      ,CARD_NUMBER AS 'CARD NUMBER'
      ,PST_DATE AS 'PST DATE'
      ,FREE_AMOUNT AS 'FREE AMOUNT'
      ,VALUE_DATE AS 'VALUE DATE'
      ,DEL_FLG AS 'DEL FLG'
      ,R_MOD_ID AS 'R MOD ID'
      ,R_MOD_TIME AS 'R MOD TIME'
      ,R_CRE_ID AS 'R CRE ID'
      ,R_CRE_TIME AS 'R CRE TIME'
      ,RECON_STATUS AS 'RECON STATUS'
      ,VERSION AS 'VERSION'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_ID AS 'ACTIVITY ID'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_REMARKS AS 'RECON REMARKS'
      ,CREATED_ON AS 'CREATIONTIME'
      ,UPDATED_ON AS 'UPDATETIME' 
      ,UPDATEDBY AS 'UPDATEDBY'
      ,OPSOURCEID AS 'OPSOURCEID'
      ,OPIPADDRESS AS 'OPIPADDRESS'
      ,SOURCE_NAME AS 'SOURCE NAME' 
      ,SOURCE_TYPE AS 'SOURCE TYPE'
      ,TTUM_STATUS AS 'TTUM STATUS'
      ,RECON_DATE AS 'RECON DATE'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,UPDATED_ON AS 'UPDATED ON'
      ,OPERATION AS 'OPERATION'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS' 
  FROM CREDIT_CARD_STATEMENT_TRAN_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			TXN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- CREDIT CARD EXTERNAL UNRECON -->
	<query id="563">
		<name>CREDIT_EXTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,DB_TS AS 'DB TS'
      ,TXN_ID AS 'TXN ID'
      ,TXN_DATE AS 'TXN DATE'
      ,TXN_SRL_NO AS 'TXN SRL NO'
      ,TXN_AMT AS 'TXN AMT'
      ,ORG_AMT AS 'ORG AMT'
      ,ORG_CUR AS 'ORG CUR'
      ,TXN_TYP AS 'TXN TYP'
      ,TXN_CAT AS 'TXN CAT'
      ,TXN_ORGN AS 'TXN ORGN'
      ,TXN_DESC AS 'TXN DESC'
      ,BANK_ID AS 'BANK ID'
      ,BRANCH_ID AS 'BRANCH ID'
      ,AC_ID AS 'AC ID'
      ,PRIMARYACID AS 'PRIMARYACID'
      ,CARD_NUMBER AS 'CARD NUMBER'
      ,PST_DATE AS 'PST DATE'
      ,FREE_AMOUNT AS 'FREE AMOUNT'
      ,VALUE_DATE AS 'VALUE DATE'
      ,DEL_FLG AS 'DEL FLG'
      ,R_MOD_ID AS 'R MOD ID'
      ,R_MOD_TIME AS 'R MOD TIME'
      ,R_CRE_ID AS 'R CRE ID'
      ,R_CRE_TIME AS 'R CRE TIME'
      ,RECON_STATUS AS 'RECON STATUS'
      ,VERSION AS 'VERSION'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_ID AS 'ACTIVITY ID'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,RECON_REMARKS AS 'RECON REMARKS'
      ,CREATED_ON AS 'CREATIONTIME'
      ,UPDATED_ON AS 'UPDATETIME' 
      ,UPDATEDBY AS 'UPDATEDBY'
      ,OPSOURCEID AS 'OPSOURCEID'
      ,OPIPADDRESS AS 'OPIPADDRESS'
      ,SOURCE_NAME AS 'SOURCE NAME' 
      ,SOURCE_TYPE AS 'SOURCE TYPE'
      ,TTUM_STATUS AS 'TTUM STATUS'
      ,RECON_DATE AS 'RECON DATE'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,UPDATED_ON AS 'UPDATED ON'
      ,OPERATION AS 'OPERATION'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,DATEDIFF(day,TXN_DATE,GETDATE()) AS 'AGE'
  FROM CREDIT_CARD_STATEMENT_TRAN_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IS NULL
			AND ACTIVE_INDEX='Y' AND
			TXN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
		<!-- ATM MASTER CARD INTERNAL RECON -->
	<query id="563">
		<name>ATMMASTERCARD_INTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,CARD_NUM AS 'CARD NUM'
      ,ACTUAL_REF_NUM AS 'ACTUAL REF NUM'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TXN_REF_NUM AS 'TXN REF NUM'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,DRCR_IND AS 'DRCR IND'
      ,TRAN_RMKS AS 'TRAN RMKS'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'ACTIVITY COMMENTS'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
  FROM CARD_ATM_MC_CBS_STG
  WHERE RECON_STATUS
			IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- ATM MASTER CARD INTERNAL UNRECON -->
	<query id="563">
		<name>ATMMASTERCARD_INTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,CARD_NUM AS 'CARD NUM'
      ,ACTUAL_REF_NUM AS 'ACTUAL REF NUM'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TXN_REF_NUM AS 'TXN REF NUM'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,DRCR_IND AS 'DRCR IND'
      ,TRAN_RMKS AS 'TRAN RMKS'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'ACTIVITY COMMENTS'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      ,DATEDIFF(day,TRAN_DATE,GETDATE()) AS 'AGE'
  FROM CARD_ATM_MC_CBS_STG
  WHERE RECON_STATUS
			IN ('AU','MU')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- ATM MASTER CARD EXTERNAL RECON -->
	<query id="563">
		<name>ATMMASTERCARD_EXTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,MSG_TYPE_IND AS 'MSG TYPE IND'
      ,SWITCH_SER_NUM AS 'SWITCH SER NUM'
      ,PROCESSOR_ACQ AS 'PROCESSOR ACQ'
      ,PROCESSOR_ID AS 'PROCESSOR ID'
      ,TRN_DATE AS 'TRN DATE'
      ,TRN_TIME AS 'TRN TIME'
      ,PAN_LENGTH AS 'PAN LENGTH'
      ,PRIMARY_ACC_NUM AS 'PRIMARY ACC NUM'
      ,PROCESSING_CODE AS 'PROCESSING CODE'
      ,TRACE_NUM1 AS 'TRACE NUM1'
      ,MERCHANT_TYPE AS 'MERCHANT TYPE'
      ,POS_ENTRY AS 'POS ENTRY'
      ,REF_NUMBER AS 'REF NUMBER'
      ,ACQ_INST_ID AS 'ACQ INST ID'
      ,TERMINAL_ID AS 'TERMINAL ID'
      ,BRAND AS 'BRAND'
      ,ADVICE_REASON_CODE AS 'ADVICE REASON CODE'
      ,INTRACUR_AGRMNT_CODE AS 'INTRACUR AGRMNT CODE'
      ,AUTHORIZATION_ID AS 'AUTHORIZATION ID'
      ,CUR_CODE_TRN AS 'CUR CODE TRN'
      ,IMP_DEC_TRN AS 'IMP DEC TRN'
      ,COMPTD_AMT_TRN_LOCAL AS 'COMPTD AMT TRN LOCAL'
      ,COMPTD_AMT_TRN_LOCAL_DR_CR_IND AS 'COMPTD AMT TRN LOCAL DR CR IND'
      ,CASH_BACK_AMT_LOCAL AS 'CASH BACK AMT LOCAL'
      ,CASH_BACK_AMT_LOCAL_DR_CR_IND AS 'CASH BACK AMT LOCAL DR CR IND'
      ,ACCESS_FEE_LOCAL AS 'ACCESS FEE LOCAL'
      ,ACCESS_FEE_LOCAL_DR_CR_IND AS 'ACCESS FEE LOCAL DR CR IND'
      ,CUR_CODE_STMNT AS 'CUR CODE STMNT'
      ,IMPLIED_DEC_STMNT AS 'IMPLIED DEC STMNT'
      ,CONVERSION_RATE_STMNT AS 'CONVERSION RATE STMNT'
      ,COMPTD_AMT_STMNT AS 'COMPTD AMT STMNT'
      ,COMPTD_AMOUNT_STMNT_DR_CR_IND AS 'COMPTD AMOUNT STMNT DR CR IND'
      ,INTRCHNG_FEE AS 'INTRCHNG FEE'
      ,INTRCHNG_FEE_DR_CR_IND AS  'INTRCHNG FEE DR CR IND'
      ,SER_LEVEL_IND AS 'SER LEVEL IND'
      ,RESP_CODE2 AS 'RESP CODE'
      ,FILLER1 AS 'FILLER1'
      ,POSITIVE_ID_IND AS 'POSITIVE ID IND'
      ,ATM_SURCHRGE_FREE_PRGM_ID AS 'ATM SURCHRGE FREE PRGM ID'
      ,CROSS_BORDER_IND AS 'CROSS BORDER IND'
      ,CROSS_BORDER_CUR_IND AS 'CROSS BORDER CUR IND'
      ,VISA_INTR_SER_ASS_IND AS 'VISA INTR SER ASS IND'
      ,REQ_AMT_TRN_LOCAL AS 'REQ AMT TRN LOCAL'
      ,FILLER2 AS 'FILLER2'
      ,TRACE_NUM_ADJ_TRNS AS 'TRACE NUM ADJ TRNS'
      ,FILLER3 AS 'FILLER3'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      ,RESP_CODE1 AS 'RESP CODE1'
  FROM CARD_ATM_MC_ACQ_STG
  WHERE RECON_STATUS
			IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			TRN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- ATM MASTER CARD EXTERNAL UNRECON -->
	<query id="563">
		<name>ATMMASTERCARD_EXTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,MSG_TYPE_IND AS 'MSG TYPE IND'
      ,SWITCH_SER_NUM AS 'SWITCH SER NUM'
      ,PROCESSOR_ACQ AS 'PROCESSOR ACQ'
      ,PROCESSOR_ID AS 'PROCESSOR ID'
      ,TRN_DATE AS 'TRN DATE'
      ,TRN_TIME AS 'TRN TIME'
      ,PAN_LENGTH AS 'PAN LENGTH'
      ,PRIMARY_ACC_NUM AS 'PRIMARY ACC NUM'
      ,PROCESSING_CODE AS 'PROCESSING CODE'
      ,TRACE_NUM1 AS 'TRACE NUM1'
      ,MERCHANT_TYPE AS 'MERCHANT TYPE'
      ,POS_ENTRY AS 'POS ENTRY'
      ,REF_NUMBER AS 'REF NUMBER'
      ,ACQ_INST_ID AS 'ACQ INST ID'
      ,TERMINAL_ID AS 'TERMINAL ID'
      ,BRAND AS 'BRAND'
      ,ADVICE_REASON_CODE AS 'ADVICE REASON CODE'
      ,INTRACUR_AGRMNT_CODE AS 'INTRACUR AGRMNT CODE'
      ,AUTHORIZATION_ID AS 'AUTHORIZATION ID'
      ,CUR_CODE_TRN AS 'CUR CODE TRN'
      ,IMP_DEC_TRN AS 'IMP DEC TRN'
      ,COMPTD_AMT_TRN_LOCAL AS 'COMPTD AMT TRN LOCAL'
      ,COMPTD_AMT_TRN_LOCAL_DR_CR_IND AS 'COMPTD AMT TRN LOCAL DR CR IND'
      ,CASH_BACK_AMT_LOCAL AS 'CASH BACK AMT LOCAL'
      ,CASH_BACK_AMT_LOCAL_DR_CR_IND AS 'CASH BACK AMT LOCAL DR CR IND'
      ,ACCESS_FEE_LOCAL AS 'ACCESS FEE LOCAL'
      ,ACCESS_FEE_LOCAL_DR_CR_IND AS 'ACCESS FEE LOCAL DR CR IND'
      ,CUR_CODE_STMNT AS 'CUR CODE STMNT'
      ,IMPLIED_DEC_STMNT AS 'IMPLIED DEC STMNT'
      ,CONVERSION_RATE_STMNT AS 'CONVERSION RATE STMNT'
      ,COMPTD_AMT_STMNT AS 'COMPTD AMT STMNT'
      ,COMPTD_AMOUNT_STMNT_DR_CR_IND AS 'COMPTD AMOUNT STMNT DR CR IND'
      ,INTRCHNG_FEE AS 'INTRCHNG FEE'
      ,INTRCHNG_FEE_DR_CR_IND AS  'INTRCHNG FEE DR CR IND'
      ,SER_LEVEL_IND AS 'SER LEVEL IND'
      ,RESP_CODE2 AS 'RESP CODE'
      ,FILLER1 AS 'FILLER1'
      ,POSITIVE_ID_IND AS 'POSITIVE ID IND'
      ,ATM_SURCHRGE_FREE_PRGM_ID AS 'ATM SURCHRGE FREE PRGM ID'
      ,CROSS_BORDER_IND AS 'CROSS BORDER IND'
      ,CROSS_BORDER_CUR_IND AS 'CROSS BORDER CUR IND'
      ,VISA_INTR_SER_ASS_IND AS 'VISA INTR SER ASS IND'
      ,REQ_AMT_TRN_LOCAL AS 'REQ AMT TRN LOCAL'
      ,FILLER2 AS 'FILLER2'
      ,TRACE_NUM_ADJ_TRNS AS 'TRACE NUM ADJ TRNS'
      ,FILLER3 AS 'FILLER3'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      ,RESP_CODE1 AS 'RESP CODE1'
      ,DATEDIFF(day,TRN_DATE,GETDATE()) AS 'AGE'
  FROM CARD_ATM_MC_ACQ_STG
  WHERE RECON_STATUS
			IS NULL
			AND ACTIVE_INDEX='Y' AND
			TRN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- ATM MASTER CARD SUPPRESS INTERNAL  -->
	<query id="563">
		<name>ATMMASTERCARD_INTERNAL_SUPPRESS_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,CARD_NUM AS 'CARD NUM'
      ,ACTUAL_REF_NUM AS 'ACTUAL REF NUM'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TXN_REF_NUM AS 'TXN REF NUM'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,DRCR_IND AS 'DRCR IND'
      ,TRAN_RMKS AS 'TRAN RMKS'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'ACTIVITY COMMENTS'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
  FROM CARD_ATM_MC_CBS_STG
  WHERE  ACTIVE_INDEX='N' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- ATM MASTER CARD SUPPRESS EXTERNAL -->
	<query id="563">
		<name>ATMMASTERCARD_EXTERNAL_SUPPRESS_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,MSG_TYPE_IND AS 'MSG TYPE IND'
      ,SWITCH_SER_NUM AS 'SWITCH SER NUM'
      ,PROCESSOR_ACQ AS 'PROCESSOR ACQ'
      ,PROCESSOR_ID AS 'PROCESSOR ID'
      ,TRN_DATE AS 'TRN DATE'
      ,TRN_TIME AS 'TRN TIME'
      ,PAN_LENGTH AS 'PAN LENGTH'
      ,PRIMARY_ACC_NUM AS 'PRIMARY ACC NUM'
      ,PROCESSING_CODE AS 'PROCESSING CODE'
      ,TRACE_NUM1 AS 'TRACE NUM1'
      ,MERCHANT_TYPE AS 'MERCHANT TYPE'
      ,POS_ENTRY AS 'POS ENTRY'
      ,REF_NUMBER AS 'REF NUMBER'
      ,ACQ_INST_ID AS 'ACQ INST ID'
      ,TERMINAL_ID AS 'TERMINAL ID'
      ,BRAND AS 'BRAND'
      ,ADVICE_REASON_CODE AS 'ADVICE REASON CODE'
      ,INTRACUR_AGRMNT_CODE AS 'INTRACUR AGRMNT CODE'
      ,AUTHORIZATION_ID AS 'AUTHORIZATION ID'
      ,CUR_CODE_TRN AS 'CUR CODE TRN'
      ,IMP_DEC_TRN AS 'IMP DEC TRN'
      ,COMPTD_AMT_TRN_LOCAL AS 'COMPTD AMT TRN LOCAL'
      ,COMPTD_AMT_TRN_LOCAL_DR_CR_IND AS 'COMPTD AMT TRN LOCAL DR CR IND'
      ,CASH_BACK_AMT_LOCAL AS 'CASH BACK AMT LOCAL'
      ,CASH_BACK_AMT_LOCAL_DR_CR_IND AS 'CASH BACK AMT LOCAL DR CR IND'
      ,ACCESS_FEE_LOCAL AS 'ACCESS FEE LOCAL'
      ,ACCESS_FEE_LOCAL_DR_CR_IND AS 'ACCESS FEE LOCAL DR CR IND'
      ,CUR_CODE_STMNT AS 'CUR CODE STMNT'
      ,IMPLIED_DEC_STMNT AS 'IMPLIED DEC STMNT'
      ,CONVERSION_RATE_STMNT AS 'CONVERSION RATE STMNT'
      ,COMPTD_AMT_STMNT AS 'COMPTD AMT STMNT'
      ,COMPTD_AMOUNT_STMNT_DR_CR_IND AS 'COMPTD AMOUNT STMNT DR CR IND'
      ,INTRCHNG_FEE AS 'INTRCHNG FEE'
      ,INTRCHNG_FEE_DR_CR_IND AS  'INTRCHNG FEE DR CR IND'
      ,SER_LEVEL_IND AS 'SER LEVEL IND'
      ,RESP_CODE2 AS 'RESP CODE'
      ,FILLER1 AS 'FILLER1'
      ,POSITIVE_ID_IND AS 'POSITIVE ID IND'
      ,ATM_SURCHRGE_FREE_PRGM_ID AS 'ATM SURCHRGE FREE PRGM ID'
      ,CROSS_BORDER_IND AS 'CROSS BORDER IND'
      ,CROSS_BORDER_CUR_IND AS 'CROSS BORDER CUR IND'
      ,VISA_INTR_SER_ASS_IND AS 'VISA INTR SER ASS IND'
      ,REQ_AMT_TRN_LOCAL AS 'REQ AMT TRN LOCAL'
      ,FILLER2 AS 'FILLER2'
      ,TRACE_NUM_ADJ_TRNS AS 'TRACE NUM ADJ TRNS'
      ,FILLER3 AS 'FILLER3'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      ,RESP_CODE1 AS 'RESP CODE1'
  FROM CARD_ATM_MC_ACQ_STG
  WHERE  ACTIVE_INDEX='N' AND
			TRN_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<query id="563">
		<name>ATMMASTERCARD_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMMASTERCARD_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ATMMASTERCARD_EXTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMMASTERCARD_EXTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
		<query id="563">
		<name>ATMMASTERCARD_RECONCILE_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMMASTERCARD_RECONCILE_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ATMMASTERCARD_UNRECONCILE_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMMASTERCARD_UNRECONCILE_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	
		<!-- MP CLEAR INTERNAL UPDATE RECON -->
	<query id="563">
		<name>MPCLEAR_INTERNAL_RECONCILED_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,PAYMENT_REFERENCE AS 'PAYMENT REFERENCE'
      ,CHN_REQ_DATE AS 'CHN REQ DATE'
      ,BD_STATUS AS 'BD STATUS'
      ,CUST_GSM_NUM AS 'CUST GSM NUM'
      ,SENDER_BANK AS 'SENDER BANK'
      ,DEBIT_ACCT_NUMBER AS 'DEBIT ACCT NUMBER'
      ,DEBIT_ACCT_NAME AS 'DEBIT ACCT NAME'
      ,STAFF_FLAG AS 'STAFF FLAG'
      ,CREDIT_ACCT_NUMBER AS 'CREDIT ACCT NUMBER'
      ,CREDIT_ACCT_NAME AS 'CREDIT ACCT NAME'
      ,PYMNT_TYPE AS 'PYMNT TYPE'
      ,REVERSED_FLAG AS 'REVERSED FLAG'
      ,TRAN_AMT  AS 'TRAN AMT'
      ,TRAN_ID AS 'TRAN ID'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TRAN_POST_FLG AS  'TRAN POST FLG'
      ,WALLET_TRANSFER_WITHIN_BD AS 'WALLET TRANSFER WITHIN BD'
      ,CUST_TYPE_CHRG  AS 'CUST TYPE CHRG'
      ,FINACLE_DATE_TIME AS 'FINACLE DATE TIME'
      ,START_DATE_TIME  AS 'START DATE TIME'
      ,END_DATE_TIME AS 'END DATE TIME'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS  'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS  'WORKFLOW STATUS'
      ,UPDATED_ON AS  'UPDATED ON'
      ,CREATED_ON AS  'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS   'RECON ID'
      ,ACTIVITY_COMMENTS AS  'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS  'MAIN REV IND'
      ,OPERATION AS  'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
  FROM FIN_MP_CLEAR_CBS_STG WITH (NOLOCK)
  
		 WHERE RECON_STATUS IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- MP CLEAR INTERNAL UPDATE UNRECON -->
	<query id="563">
		<name>MPCLEAR_INTERNAL_UNRECONCILED_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,PAYMENT_REFERENCE AS 'PAYMENT REFERENCE'
      ,CHN_REQ_DATE AS 'CHN REQ DATE'
      ,BD_STATUS AS 'BD STATUS'
      ,CUST_GSM_NUM AS 'CUST GSM NUM'
      ,SENDER_BANK AS 'SENDER BANK'
      ,DEBIT_ACCT_NUMBER AS 'DEBIT ACCT NUMBER'
      ,DEBIT_ACCT_NAME AS 'DEBIT ACCT NAME'
      ,STAFF_FLAG AS 'STAFF FLAG'
      ,CREDIT_ACCT_NUMBER AS 'CREDIT ACCT NUMBER'
      ,CREDIT_ACCT_NAME AS 'CREDIT ACCT NAME'
      ,PYMNT_TYPE AS 'PYMNT TYPE'
      ,REVERSED_FLAG AS 'REVERSED FLAG'
      ,TRAN_AMT  AS 'TRAN AMT'
      ,TRAN_ID AS 'TRAN ID'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TRAN_POST_FLG AS  'TRAN POST FLG'
      ,WALLET_TRANSFER_WITHIN_BD AS 'WALLET TRANSFER WITHIN BD'
      ,CUST_TYPE_CHRG  AS 'CUST TYPE CHRG'
      ,FINACLE_DATE_TIME AS 'FINACLE DATE TIME'
      ,START_DATE_TIME  AS 'START DATE TIME'
      ,END_DATE_TIME AS 'END DATE TIME'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS  'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS  'WORKFLOW STATUS'
      ,UPDATED_ON AS  'UPDATED ON'
      ,CREATED_ON AS  'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS   'RECON ID'
      ,ACTIVITY_COMMENTS AS  'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS  'MAIN REV IND'
      ,OPERATION AS  'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
	  , DATEDIFF(day, TRAN_DATE,GETDATE() ) AS 'AGE'
  FROM FIN_MP_CLEAR_CBS_STG WITH (NOLOCK)
  
		 WHERE RECON_STATUS IN ('AU','MU')
			AND ACTIVE_INDEX='Y' AND
			TRAN_DATE BETWEEN ? AND ?
	</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- MP CLEAR EXTERNAL UPDATE RECON -->
	<query id="563">
		<name>MPCLEAR_EXTERNAL_RECONCILED_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'
      ,SESSION_SEQ AS 'SESSION SEQ'
      ,CURRENCY AS 'CURRENCY'
      ,PARTICIPANT AS 'PARTICIPANT'
      ,SETTLEMENTRETRY AS 'SETTLEMENTRETRY'
      ,ID AS 'ID'
      ,TYPE AS 'TYPE'
      ,AMOUNT AS 'AMOUNT'
      ,STATE AS 'STATE'
      ,REASON AS 'REASON'
      ,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
  FROM FIN_MP_CLEAR_EXT_STG WITH (NOLOCK)
    WHERE RECON_STATUS IN ('AM','MM')
			AND ACTIVE_INDEX='Y' AND
			SETTLEMENT_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- MP CLEAR EXTERNAL UPDATE UNRECON -->
	<query id="563">
		<name>MPCLEAR_EXTERNAL_UNRECONCILED_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'
      ,SESSION_SEQ AS 'SESSION SEQ'
      ,CURRENCY AS 'CURRENCY'
      ,PARTICIPANT AS 'PARTICIPANT'
      ,SETTLEMENTRETRY AS 'SETTLEMENTRETRY'
      ,ID AS 'ID'
      ,TYPE AS 'TYPE'
      ,AMOUNT AS 'AMOUNT'
      ,STATE AS 'STATE'
      ,REASON AS 'REASON'
      ,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
       ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      ,DATEDIFF(day, SETTLEMENT_DATE,GETDATE() ) AS 'AGE'
  FROM FIN_MP_CLEAR_EXT_STG WITH (NOLOCK)
    WHERE RECON_STATUS IS NULL
			AND ACTIVE_INDEX='Y' AND
			SETTLEMENT_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- MP CLEAR INTERNAL SUPPRESS -->
	<query id="563">
		<name>MPCLEAR_INTERNAL_SUPPRESS_UPDATE_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT  SID AS 'SID'
      ,PAYMENT_REFERENCE AS 'PAYMENT REFERENCE'
      ,CHN_REQ_DATE AS 'CHN REQ DATE'
      ,BD_STATUS AS 'BD STATUS'
      ,CUST_GSM_NUM AS 'CUST GSM NUM'
      ,SENDER_BANK AS 'SENDER BANK'
      ,DEBIT_ACCT_NUMBER AS 'DEBIT ACCT NUMBER'
      ,DEBIT_ACCT_NAME AS 'DEBIT ACCT NAME'
      ,STAFF_FLAG AS 'STAFF FLAG'
      ,CREDIT_ACCT_NUMBER AS 'CREDIT ACCT NUMBER'
      ,CREDIT_ACCT_NAME AS 'CREDIT ACCT NAME'
      ,PYMNT_TYPE AS 'PYMNT TYPE'
      ,REVERSED_FLAG AS 'REVERSED FLAG'
      ,TRAN_AMT  AS 'TRAN AMT'
      ,TRAN_ID AS 'TRAN ID'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TRAN_POST_FLG AS  'TRAN POST FLG'
      ,WALLET_TRANSFER_WITHIN_BD AS 'WALLET TRANSFER WITHIN BD'
      ,CUST_TYPE_CHRG  AS 'CUST TYPE CHRG'
      ,FINACLE_DATE_TIME AS 'FINACLE DATE TIME'
      ,START_DATE_TIME  AS 'START DATE TIME'
      ,END_DATE_TIME AS 'END DATE TIME'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS  'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS  'WORKFLOW STATUS'
      ,UPDATED_ON AS  'UPDATED ON'
      ,CREATED_ON AS  'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS   'RECON ID'
      ,ACTIVITY_COMMENTS AS  'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS  'MAIN REV IND'
      ,OPERATION AS  'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA' FROM FIN_MP_CLEAR_CBS_STG WITH (NOLOCK)
		 WHERE  ACTIVE_INDEX='N' AND
			TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- MP CLEAR EXTERNAL SUPPRESS -->
	<query id="563">
		<name>MPCLEAR_EXTERNAL_SUPPRESS_UPDATE_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT SID AS 'SID'
      ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'
      ,SESSION_SEQ AS 'SESSION SEQ'
      ,CURRENCY AS 'CURRENCY'
      ,PARTICIPANT AS 'PARTICIPANT'
      ,SETTLEMENTRETRY AS 'SETTLEMENTRETRY'
      ,ID AS 'ID'
      ,TYPE AS 'TYPE'
      ,AMOUNT AS 'AMOUNT'
      ,STATE AS 'STATE'
      ,REASON AS 'REASON'
      ,ADDITIONAL_INFO AS 'ADDITIONAL INFO'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
  FROM FIN_MP_CLEAR_EXT_STG WITH (NOLOCK)
    WHERE  ACTIVE_INDEX='N' AND
			SETTLEMENT_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- CARD CENTER ATM VIA ISSURE RECONSILED -->
	
	<!-- ATM VISA ISSUER -->
	<query id="563">
		<name>ATM_VISA_ISSUER_INTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
     		SELECT * FROM ATM_VISA_ISSUER_INTERNAL_RECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATM_VISA_ISSUER_INTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
	  		SELECT * FROM ATM_VISA_ISSUER_INTERNAL_UNRECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATM_VISA_ISSUER_EXTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_VISA_ISSUER_EXTERNAL_RECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATM_VISA_ISSUER_EXTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_VISA_ISSUER_EXTERNAL_UNRECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		<query id="563">
		<name>ATM_VISA_ISSUER_INTERNAL_SUPPRESSED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_VISA_ISSUER_INTERNAL_SUPPRESSED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATM_VISA_ISSUER_EXTERNAL_SUPPRESSED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_VISA_ISSUER_EXTERNAL_SUPPRESSED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- ATM VISA ACQUIRER START-->		
		
		<query id="563">
		<name>ATM_VISA_ACQUIRER_INTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		
	   SELECT  [SID]
      ,[ACCT_NUM]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[CUSTOMER_ACCT]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[POSTED_ON]
      ,[POSTED_BY]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]
  FROM CARD_ATM_VISA_ACQ_CBS_STG WITH (NOLOCK)
       WHERE ACTIVE_INDEX='Y' 
       AND RECON_STATUS IN ('AM','MM')
       AND TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="663">
		<name>ATM_VISA_ACQUIRER_INTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		
	   SELECT  [SID]
      ,[ACCT_NUM]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[CUSTOMER_ACCT]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[POSTED_ON]
      ,[POSTED_BY]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]
      , DATEDIFF(day, TRAN_DATE,GETDATE() ) AS 'AGE'
  FROM [CARD_ATM_VISA_ACQ_CBS_STG] WITH (NOLOCK)
  WHERE RECON_STATUS IN ('AU','MU') 
  AND ACTIVE_INDEX='Y' AND TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
		<query id="663">
		<name>ATM_VISA_ACQUIRER_INTERNAL_SUPPRESSED_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
		
	   SELECT  [SID]
      ,[ACCT_NUM]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[CUSTOMER_ACCT]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[POSTED_ON]
      ,[POSTED_BY]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]
  FROM [CARD_ATM_VISA_ACQ_CBS_STG] WITH (NOLOCK)
  		WHERE  ACTIVE_INDEX='N' AND TRAN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>ATM_VISA_ACQUIRER_EXTERNAL_RECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	    
      SELECT [SID]
      ,[BAT_NUM]
      ,[XMIT_DATE]
      ,[TIME]
      ,[CARD_NUMBER]
      ,[RETRIEVAL_REF_NUMBER]
      ,[TRACE_NUMBER]
      ,[ISSUER_TRMNL]
      ,[TRAN_TYPE]
      ,[PROCSS_CODE]
      ,[ENT_MOD]
      ,[CN_STP]
      ,[RSP_CD]
      ,[REAS_CODE]
      ,[TRANSACTION_AMOUNT]
      ,[KES_CURRENCY]
      ,[SETTLEMENT_AMOUNT]
      ,[CR_CURRENCY]
      ,[CA_ID]
      ,[TERMINAL_ID]
      ,[FPI]
      ,[CI]
      ,[REPORT_DATE]
      ,[TR_ID]
      ,[ACI]
      ,[FEE_JURIS]
      ,[ROUTING]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[STATUS]
      ,[COMMENTS]
      ,[WORKFLOW_STATUS]
      ,[USER_ID]
      ,[OPERATION]
      ,[UPDATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[MAIN_REV_IND]
      ,[CREATED_ON]
      ,[PROC_CODE]
      ,[FILENAME]
      ,[ACTIVITY_COMMENTS]
  FROM [CARD_ATM_VISA_ACQ_EXT_STG] WITH (NOLOCK)
       WHERE  RECON_STATUS IN ('AM','MM')
        AND   ACTIVE_INDEX='Y'
       AND REPORT_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
		<query id="563">
		<name>ATM_VISA_ACQUIRER_EXTERNAL_UNRECONCILED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>	    
     SELECT TOP 1000 [SID]
      ,[BAT_NUM]
      ,[XMIT_DATE]
      ,[TIME]
      ,[CARD_NUMBER]
      ,[RETRIEVAL_REF_NUMBER]
      ,[TRACE_NUMBER]
      ,[ISSUER_TRMNL]
      ,[TRAN_TYPE]
      ,[PROCSS_CODE]
      ,[ENT_MOD]
      ,[CN_STP]
      ,[RSP_CD]
      ,[REAS_CODE]
      ,[TRANSACTION_AMOUNT]
      ,[KES_CURRENCY]
      ,[SETTLEMENT_AMOUNT]
      ,[CR_CURRENCY]
      ,[CA_ID]
      ,[TERMINAL_ID]
      ,[FPI]
      ,[CI]
      ,[REPORT_DATE]
      ,[TR_ID]
      ,[ACI]
      ,[FEE_JURIS]
      ,[ROUTING]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[STATUS]
      ,[COMMENTS]
      ,[WORKFLOW_STATUS]
      ,[USER_ID]
      ,[OPERATION]
      ,[UPDATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[MAIN_REV_IND]
      ,[CREATED_ON]
      ,[PROC_CODE]
      ,[FILENAME]
      ,[ACTIVITY_COMMENTS]
       , DATEDIFF(day, REPORT_DATE,GETDATE() ) AS 'AGE'
  FROM [CARD_ATM_VISA_ACQ_EXT_STG] WITH (NOLOCK)
       WHERE ACTIVE_INDEX='Y' 
       AND RECON_STATUS IS NULL
       AND REPORT_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>ATM_VISA_ACQUIRER_EXTERNAL_SUPPRESSED_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
	  SELECT [SID]
      ,[BAT_NUM]
      ,[XMIT_DATE]
      ,[TIME]
      ,[CARD_NUMBER]
      ,[RETRIEVAL_REF_NUMBER]
      ,[TRACE_NUMBER]
      ,[ISSUER_TRMNL]
      ,[TRAN_TYPE]
      ,[PROCSS_CODE]
      ,[ENT_MOD]
      ,[CN_STP]
      ,[RSP_CD]
      ,[REAS_CODE]
      ,[TRANSACTION_AMOUNT]
      ,[KES_CURRENCY]
      ,[SETTLEMENT_AMOUNT]
      ,[CR_CURRENCY]
      ,[CA_ID]
      ,[TERMINAL_ID]
      ,[FPI]
      ,[CI]
      ,[REPORT_DATE]
      ,[TR_ID]
      ,[ACI]
      ,[FEE_JURIS]
      ,[ROUTING]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[STATUS]
      ,[COMMENTS]
      ,[WORKFLOW_STATUS]
      ,[USER_ID]
      ,[OPERATION]
      ,[UPDATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[MAIN_REV_IND]
      ,[CREATED_ON]
      ,[PROC_CODE]
      ,[FILENAME]
      ,[ACTIVITY_COMMENTS]
  FROM [CARD_ATM_VISA_ACQ_EXT_STG]  WITH (NOLOCK)
  	   WHERE  ACTIVE_INDEX='N' 
  	   AND REPORT_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>ATMVISAACQ_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMVISAACQ_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATMVISAACQ_EXTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMVISAACQ_EXTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ATMVISAACQ_RECONCILE_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMVISAACQ_RECONCILE_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATMVISAACQ_UNRECONCILE_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATMVISAACQ_UNRECONCILE_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- ONS -->

	<query id="563">
		<name>ONS_INTERNAL_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_INTERNAL_RECON(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_INTERNAL_UNRECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_INTERNAL_UNRECON(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_EXTERNAL_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_EXTERNAL_RECON(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_EXTERNAL_UNRECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_EXTERNAL_UNRECON(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_INTERNAL_SUPPRESS_UPDATE_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT  SID AS 'SID'
      ,INTERNAL_REF_NUM AS 'INTERNAL REF NUM'
      ,ACNT AS 'ACNT'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,AMOUNT AS 'AMOUNT'
      ,DRCR AS 'DRCR'
      ,CURRENCY AS 'CURRENCY'
      ,CARD_NUMBER AS 'CARD NUMBER'
      ,ACCT_BRANCH_ID AS 'ACCT BRANCH ID'
      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,TRAN_REMARKS AS 'TRAN REMARKS'
      ,TRAN_ENTRY_USER AS 'TRAN ENTRY USER'
      ,TRAN_POSTED_USER AS 'TRAN POSTED USER'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND'
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
      ,TRAN_REF_NUM AS 'TRAN REF NUM'
  FROM FIN_ONS_CBS_STG WITH (NOLOCK) WHERE
			ACTIVE_INDEX='N' AND VALUE_DATE
			BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_EXTERNAL_SUPPRESS_UPDATE_RECON_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
	SELECT  SID AS 'SID'
      ,TRAN_REF_NUM AS 'TRAN REF NUM'
      ,INTERNAL_REF_NUM AS 'INTERNAL REF NUM'
      ,ACCOUNT AS 'ACCOUNT'
      ,TRAN_DATE AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,AMOUNT AS 'AMOUNT'
      ,DRCR AS 'DRCR'
      ,CURRENCY AS 'CURRENCY'
      ,PAN_NUMBER AS 'PAN NUMBER'
      ,ORIGINATOR_BID AS 'ORIGINATOR BID'
      ,DESTINATION_BID AS 'DESTINATION BID'
      ,ACQUIRING_INSTITUTION_ID AS 'ACQUIRING INSTITUTION ID'
      ,CARD_ACCEPTOR_NAME AS 'CARD ACCEPTOR NAME'
      ,MERCHANT_CATEGORY_CODE AS 'MERCHANT CATEGORY CODE'
      ,TRANSACTION_TYPE AS 'TRANSACTION TYPE'
      ,COMMENTS AS 'COMMENTS'
      ,VERSION AS 'VERSION'
      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,UPDATED_ON AS 'UPDATED ON'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON  STATUS'
      ,RECON_ID AS 'RECON ID'
      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,MAIN_REV_IND AS 'MAIN REV IND' 
      ,OPERATION AS 'OPERATION'
      ,FILE_NAME AS 'FILE NAME'
      ,BUSINESS_AREA AS 'BUSINESS AREA'
       FROM FIN_ONS_CBO_STG WITH (NOLOCK) WHERE  ACTIVE_INDEX='N' AND
			VALUE_DATE BETWEEN ? AND ?
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_EXTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_EXTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- ONS END -->
	
<query id="563">
		<name>SUSPENCE_INTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
SELECT [SID]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[ACCT_NUMBER]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]

 FROM [FIN_SUSPENSE_STG] WITH (NOLOCK)
  WHERE RECON_STATUS IN ('AM','MM') AND DRCR='C'
			AND ACTIVE_INDEX='Y'AND TRAN_DATE
			BETWEEN ? AND ?
 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>SUSPENCE_INTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
SELECT [SID]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[ACCT_NUMBER]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]
	  , DATEDIFF(day, TRAN_DATE,GETDATE() ) AS 'AGE'
  FROM [FIN_SUSPENSE_STG] WITH (NOLOCK)
  WHERE RECON_STATUS IN ('AU','MU') AND DRCR='C'
			AND ACTIVE_INDEX='Y'AND TRAN_DATE
			BETWEEN ? AND ?

 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>





<query id="563">
		<name>SUSPENCE_EXTERNAL_RECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
     SELECT [SID]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[ACCT_NUMBER]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]

 FROM [FIN_SUSPENSE_STG] WITH (NOLOCK)
  WHERE RECON_STATUS IN ('AM','MM') AND DRCR='D'
			AND ACTIVE_INDEX='Y'AND TRAN_DATE
			BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>


<query id="563">
		<name>SUSPENCE_EXTERNAL_UNRECONCILE_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
    SELECT [SID]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[ACCT_NUMBER]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]
	  , DATEDIFF(day, TRAN_DATE,GETDATE() ) AS 'AGE'
  FROM [FIN_SUSPENSE_STG] WITH (NOLOCK)
  WHERE RECON_STATUS IS NULL AND DRCR='D'
			AND ACTIVE_INDEX='Y'AND TRAN_DATE
			BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>SUSPENCE_INTERNAL_SUPPRESS_REPORT_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
SELECT [SID]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[ACCT_NUMBER]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]

 FROM [FIN_SUSPENSE_STG] WITH (NOLOCK)
  WHERE DRCR='C' AND
		 ACTIVE_INDEX='N'AND TRAN_DATE
			BETWEEN ? AND ?
 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>SUSPENCE_EXTERNAL_SUPPRESS_REPORT_BK</name>
		<queryType>SELECT</queryType>
		<queryString>   
     SELECT [SID]
      ,[BRANCH_ID]
      ,[TRAN_ID]
      ,[TRAN_DATE]
      ,[VALUE_DATE]
      ,[ACCT_NUMBER]
      ,[DRCR]
      ,[AMOUNT]
      ,[TRAN_PARTICULAR]
      ,[REFERENCE_NUMBER]
      ,[TRAN_REMARKS]
      ,[TRAN_CRNCY_CODE]
      ,[REF_CRNCY_CODE]
      ,[REF_AMT]
      ,[COMMENTS]
      ,[VERSION]
      ,[ACTIVE_INDEX]
      ,[WORKFLOW_STATUS]
      ,[UPDATED_ON]
      ,[CREATED_ON]
      ,[RECON_STATUS]
      ,[RECON_ID]
      ,[ACTIVITY_COMMENTS]
      ,[MAIN_REV_IND]
      ,[OPERATION]
      ,[FILE_NAME]
      ,[BUSINESS_AREA]

 FROM [FIN_SUSPENSE_STG] WITH (NOLOCK)
  WHERE  DRCR='D' AND ACTIVE_INDEX='N'AND TRAN_DATE
			BETWEEN ? AND ?
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>SUSPENCE_INTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_INTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>SUSPENCE_EXTERNAL_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM SUSPENCE_EXTERNAL_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	

<query id="563">
		<name>ONS_AGING_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ONS_AGING
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<query id="563">
		<name>SUSPENCE_AGING_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			 SELECT * FROM SUSPENCE_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>POSNI_ACQUIRER_AGING</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM POS_NI_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATM_AGING_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>CDM_AGING_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CDM_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ATM_VISA_ISSUER1_SUMMARY_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_VISSA_ISSUER_SUMMERY1
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ATM_VISA_ACQUIRER_AGING_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			 SELECT * FROM ATM_VISA_AQUIRE_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
		
	<query id="563">
		<name>ATMMASTERCARD_AGING_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ATM_MASTER_CARD_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>ACH_AGING_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ACH_AGING WITH (NOLOCK)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- Reports End-->
	
	<!-- Escalation matrix-->
	
	<query id="563">
		<name>GET_ALL_PENDING_CASES</name>
		<queryType>SELECT</queryType>
		<queryString>
			<!-- SELECT * FROM ( SELECT '0-3' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,
                           CASE WHEN AGE BETWEEN 0 AND 3  THEN 'UNIT HEAD'  
                           END AS PERSON FROM (SELECT CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE())
                           AS AGE FROM RECON_CASE_MANAGEMENT where active_index='Y' and status like 'Pend%' 
                           ) A)B INNER JOIN [ESCALATION_MATRIX] C ON ESCALATED_PERSON=PERSON  and C.DEPARTMENT=B.business_area
                           UNION ALL
                           SELECT * FROM ( SELECT '4-7' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area, CASE WHEN AGE BETWEEN 3 AND 5  THEN 'AGM'  
                           END AS PERSON FROM (SELECT CASE_ID,SID,RECON,status,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE()) AS AGE FROM RECON_CASE_MANAGEMENT
                           where active_index='Y' and status like 'Pend%' ) A ) B
						   INNER JOIN [ESCALATION_MATRIX] C ON ESCALATED_PERSON=PERSON and C.DEPARTMENT=B.business_area
                           UNION ALL
                           SELECT * FROM ( SELECT '8-100' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,CASE WHEN AGE BETWEEN 7 AND 100 THEN 'GM' END AS PERSON FROM (
                           SELECT CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE()) AS AGE
                           FROM RECON_CASE_MANAGEMENT where active_index='Y'  and status like 'Pend%' 
                           ) A)B 
						   INNER JOIN
						   ESCALATION_MATRIX C ON 
						   ESCALATED_PERSON=PERSON 
						   and C.DEPARTMENT=B.business_area -->
			SELECT * FROM ( SELECT '5-6' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,
                       CASE WHEN AGE BETWEEN 5 AND 6 THEN 'HOD'  
                       END AS PERSON FROM (SELECT CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE())
                       AS AGE FROM RECON_CASE_MANAGEMENT WITH (NOLOCK)
                        where active_index='Y' and status like 'Pend%' and business_area = 'FINANCE DEPARTMENT'
                       ) A)B INNER JOIN [ESCALATION_MATRIX] C ON ESCALATED_PERSON=PERSON  and C.DEPARTMENT=B.business_area
                       UNION ALL
                       SELECT * FROM ( SELECT '7-13' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area, CASE WHEN AGE BETWEEN 7 AND 13  THEN 'AGM'  
                       END AS PERSON FROM (SELECT CASE_ID,SID,RECON,status,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE()) AS AGE FROM RECON_CASE_MANAGEMENT WITH (NOLOCK)
                       where active_index='Y' and status like 'Pend%' and business_area = 'FINANCE DEPARTMENT ') A ) B
		   INNER JOIN [ESCALATION_MATRIX] C WITH (NOLOCK) ON ESCALATED_PERSON=PERSON and C.DEPARTMENT=B.business_area
		   UNION ALL
                       SELECT * FROM ( SELECT '14' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area, CASE WHEN AGE='14' THEN 'AGM-CO and SS'  
                       END AS PERSON FROM (SELECT CASE_ID,SID,RECON,status,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE()) AS AGE FROM RECON_CASE_MANAGEMENT WITH (NOLOCK)
                       where active_index='Y' and status like 'Pend%' and business_area ='FINANCE DEPARTMENT') A ) B
		   INNER JOIN [ESCALATION_MATRIX] C ON ESCALATED_PERSON=PERSON and C.DEPARTMENT=B.business_area
                       UNION ALL
                       SELECT * FROM ( SELECT '15-3000' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,CASE WHEN AGE BETWEEN 15 AND 3000 THEN 'GM' END AS PERSON FROM (
                       SELECT CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE()) AS AGE
                       FROM RECON_CASE_MANAGEMENT WITH (NOLOCK) where active_index='Y'  and status like 'Pend%' and business_area = 'FINANCE DEPARTMENT'
                       ) A)B 
		   INNER JOIN
		   ESCALATION_MATRIX C WITH (NOLOCK) ON 
		   ESCALATED_PERSON=PERSON 
		   and C.DEPARTMENT=B.business_area
		   UNION ALL
		   SELECT * FROM ( SELECT '3-4' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,
                       CASE WHEN AGE BETWEEN 3 AND 4 THEN 'UNIT HEAD'  
                       END AS PERSON FROM (SELECT CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE())
                       AS AGE FROM RECON_CASE_MANAGEMENT WITH (NOLOCK) where active_index='Y' and status like 'Pend%' and business_area = 'CARDS'
                       ) A)B INNER JOIN [ESCALATION_MATRIX] C WITH (NOLOCK) ON ESCALATED_PERSON=PERSON  and C.DEPARTMENT=B.business_area
                       UNION ALL
                       SELECT * FROM ( SELECT '5-7' AGELIMIT ,AGE,CASE_ID,SID,RECON,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area, CASE WHEN AGE BETWEEN 5 AND 7  THEN 'HOD'  
                       END AS PERSON FROM (SELECT CASE_ID,SID,RECON,status,activity_id,created_by,transaction_amount,allowed_approvers,assigned_to,business_area,DATEDIFF(DAY,CREATED_ON,GETDATE()) AS AGE FROM RECON_CASE_MANAGEMENT
                       where active_index='Y' and status like 'Pend%' and business_area = 'CARDS') A ) B
		   INNER JOIN [ESCALATION_MATRIX] C WITH (NOLOCK) ON ESCALATED_PERSON=PERSON and C.DEPARTMENT=B.business_area
			
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- Escalation matrix end-->
	
		<!-- ATM Internal Suppress Record -->
	<query id="563">
		<name>GET_ATM_INTERNAL_SUPPRESS_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT 
			CUSTOMER_ACCT AS 'CUSTOMER ACCT'
			,ATM_ACCOUNT AS 'ATM ACCOUNT'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_DATE AS 'TRAN DATE'
			,VALUE_DATE AS 'VALUE DATE'
			,DRCR AS 'DRCR'
			,AMOUNT AS 'AMOUNT'
			,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
			,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
			,REF_CRNCY_CODE AS 'REF CRNCY CODE'
			,REF_AMT AS 'REF AMT'
			,RECON_STATUS AS 'RECON STATUS' FROM CO_ATM_CBS_STG WITH
			(NOLOCK) WHERE ACTIVE_INDEX='N' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- Cod_Atm_extenal_reconcile -->
	<query id="563">
		<name>GET_COD_ATM_EXTERNAL_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
			 SELECT TXNMESSAGES_ID AS 'TXNMESSAGES ID'
			,CREATEDDATE AS 'CREATEDDATE'
			,TXNDATETIME AS 'TXNDATETIME'
			,TXNDATE AS 'TXNDATE'
			,TXNTIME AS 'TXNTIME'
			,TERMINALID 'TERMINALID'
			,SEQUENCENUMBER AS 'SEQUENCENUMBER'
			,TXNTYPE AS 'TXNTYPE'
			,CARDNUMBER AS 'CARDNUMBER'
			,ACCOUNTNO1 AS 'ACCOUNTNO1'
			,AMOUNT AS 'AMOUNT'
			,RECON_STATUS AS 'RECON STATUS'
			FROM CO_ATM_JOURNAL_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AM')
			AND
			ACTIVE_INDEX='Y' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- COD INTERNAL RECONCILE RECORDS LOAD -->

	<query id="563">
		<name>GET_COD_ATM_INTERNAL_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT CUSTOMER_ACCT AS 'CUSTOMER ACCT'
			,ATM_ACCOUNT AS 'ATM ACCOUNT'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_DATE AS 'TRAN DATE'
			,VALUE_DATE AS 'VALUE DATE'
			,DRCR AS 'DRCR'
			,AMOUNT AS 'AMOUNT'
			,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,REFERENCE_NUMBER AS 'REFERENCE NUMBER'
			,TRAN_REMARKS AS 'TRAN REMARKS'
			,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
			,REF_CRNCY_CODE AS 'REF CRNCY CODE'
			,REF_AMT AS 'REF AMT'
			,RECON_STATUS AS 'RECON STATUS'
			FROM CO_ATM_CBS_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AM')
			AND
			ACTIVE_INDEX='Y' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- COD EXTERNAL SUPPRESS RECORDS LOAD -->
	<query id="563">
		<name>GET_COD_ATM_EXTERNAL_SUPPRESS_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT 
			TXNMESSAGES_ID AS 'TXNMESSAGES ID'
			,CREATEDDATE AS 'CREATEDDATE'
			,TXNDATETIME AS 'XNDATETIME'
			,TXNDATE AS 'TXNDATE'
			,TXNTIME AS 'TXNTIME'
			,TERMINALID 'TERMINALID'
			,SEQUENCENUMBER AS 'SEQUENCENUMBER'
			,TXNTYPE AS 'TXNTYPE'
			,CARDNUMBER AS 'CARDNUMBER'
			,ACCOUNTNO1 AS 'ACCOUNTNO1'
			,AMOUNT AS 'AMOUNT'
			FROM CO_ATM_JOURNAL_STG WITH (NOLOCK) WHERE
			ACTIVE_INDEX='N' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="563">
		<name>GET_COD_CDM_INTERNAL_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
	SELECT  CDM_ID AS 'CDM ID'
			,CDM_BRANCH AS 'CDM BRANCH'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_DATE AS 'TRAN DATE'
			,VALUE_DATE AS 'VALUE DATE'
			,CDM_ACCOUNT AS 'CDM ACCOUNT'
			,DRCR AS 'DRCR'
			,AMOUNT AS 'AMOUNT'
			,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
			,REF_CRNCY_CODE AS 'REF CRNCY CODE'
			,REF_AMT AS 'REF AMT'
			,RECON_STATUS AS 'RECON STATUS'
			FROM CO_CDM_CBS_STG WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AM')
			AND
			ACTIVE_INDEX='Y' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


	<query id="563">
		<name>GET_CDM_INTERNAL_SUPPRESS_RECORDS_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT CDM_ID AS 'CDM ID'
			,CDM_BRANCH AS 'CDM BRANCH'
			,TRAN_ID AS 'TRAN ID'
			,TRAN_DATE AS 'TRAN DATE'
			,VALUE_DATE AS 'VALUE DATE'
			,CDM_ACCOUNT AS 'CDM ACCOUNT'
			,DRCR AS 'DRCR'
			,AMOUNT AS 'AMOUNT'
			,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
			,REF_CRNCY_CODE AS 'REF CRNCY CODE'
			,REF_AMT AS 'REF AMT'
			,RECON_STATUS AS 'RECON STATUS'
			FROM CO_CDM_CBS_STG WITH
			(NOLOCK) WHERE  ACTIVE_INDEX='N' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


	<query id="563">
		<name>GET_COD_CDM_EXTERNAL_RECONCILE_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
		SELECT TXNMESSAGES_ID AS 'TXNMESSAGES ID'
      ,CREATEDDATE AS 'CREATEDDATE'
      ,TXNDATETIME AS 'TXNDATETIME'
      ,TXNDATE AS 'TXNDATE'
      ,TXNTIME AS 'TXNTIME'
      ,TERMINALID AS 'TERMINALID'
      ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
      ,TXNTYPE AS 'TXNTYPE'
      ,CARDNUMBER AS 'CARDNUMBER'
      ,ACCOUNTNO1 AS 'ACCOUNTNO1'
      ,AMOUNT AS 'AMOUNT'
      ,RECON_STATUS AS 'RECON STATUS'
       FROM CO_CDM_JOURNAL_STG WITH
	  (NOLOCK) WHERE RECON_STATUS
			IN ('AM') AND
			ACTIVE_INDEX='Y' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

   <query id="563">
		<name>GET_COD_CDM_EXTERNAL_SUPPRESS_RECORDS_BK</name>
		<queryType>SELECT</queryType>
		<queryString>
       SELECT TXNMESSAGES_ID AS 'TXNMESSAGES ID'
      ,CREATEDDATE AS 'CREATEDDATE'
      ,TXNDATETIME AS 'TXNDATETIME'
      ,TXNDATE AS 'TXNDATE'
      ,TERMINALID AS 'TERMINALID'
      ,SEQUENCENUMBER AS 'SEQUENCENUMBER'
      ,TXNTYPE AS 'TXNTYPE'
      ,CARDNUMBER AS 'CARDNUMBER'
      ,ACCOUNTNO1 AS 'ACCOUNTNO1'
      ,AMOUNT AS 'AMOUNT'
       FROM CO_CDM_JOURNAL_STG WITH
	  (NOLOCK) WHERE ACTIVE_INDEX='N' AND CREATED_ON = convert(varchar(10), getdate(), 102)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


    <!--ACH INTERNAL RECONCILE RECORDS-->
    
    <query id="563">
	<name>GET_ACH_INTERNAL_RECONCILE_RECORDS</name>
	<queryType>SELECT</queryType>
	<queryString>
      SELECT ACCT_NUM AS 'ACCT NUM'
      ,DRCR AS 'DRCR'
      ,TRANID AS 'TRANID'
      ,TRAN_DT AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE'
      ,TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,BANK AS 'BANK'
      ,CREATED_ON AS 'CREATED ON'
      ,RECON_STATUS AS 'RECON STATUS'
      FROM BANK_DHOFAR_CONVENTIONAL.dbo.CO_ACH_CBS_STG  WITH
	  (NOLOCK) WHERE RECON_STATUS
	   IN ('AM')
	   AND ACTIVE_INDEX='Y' AND CAST(CREATED_ON AS DATE) = CAST(GETDATE()-1 AS DATE)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <!--ACH EXTERBAL RECONCILE RECORDS-->
	
	
	 <query id="563">
	<name>GET_ACH_EXTERNAL_RECONCILE_RECORDS</name>
	<queryType>SELECT</queryType>
	<queryString>
     SELECT TRANSACTION_ID AS 'TRANSACTION ID'
      ,CURRENCY AS 'CURRENCY'
      ,AMOUNT AS 'AMOUNT'
      ,INSTRUCTING_PARTICIPANT AS 'INSTRUCTING PARTICIPANT'
      ,INSTRUCTING_BRANCH AS 'INSTRUCTING BRANCH'
      ,INSTRUCTED_PARTICIPANT AS 'INSTRUCTED PARTICIPANT'
      ,INSTRUCTED_BRANCH AS 'INSTRUCTED BRANCH'
      ,DEBTOR_BANK AS 'DEBTOR BANK'
      ,DEBTOR_BRANCH AS 'DEBTOR BRANCH'
      ,DEBTOR_NAME AS 'DEBTOR NAME'
      ,DEBTOR_ACCOUNT AS 'DEBTOR ACCOUNT'
      ,CREDITOR_BANK AS 'CREDITOR BANK'
      ,CREDITOR_BRANCH AS 'CREDITOR BRANCH'
      ,CREATED_ON AS 'CREATED ON'
      ,CREDITOR_NAME AS 'CREDITOR NAME'
      ,CREATED_BY AS 'CREATED BY'
      ,TRANSACTION_PURPOSE AS 'TRANSACTION PURPOSE'
      ,SETTLEMENT_DATE AS 'SETTLEMENT DATE'   
      ,RECON_STATUS AS 'RECON STATUS'
      FROM CO_ACH_CBO_STG  WITH
			(NOLOCK) WHERE RECON_STATUS
			IN ('AM')
			AND ACTIVE_INDEX='Y' AND CAST(CREATED_ON AS DATE) = CAST(GETDATE()-1 AS DATE) 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <!--ACH INTERNAL  SUPRESS RECORDS-->
	
	<query id="563">
	<name>GET_ACH__INTERNAL_SUPPRESS_RECORDS</name>
	<queryType>SELECT</queryType>
	<queryString>
     SELECT ACCT_NUM AS 'ACCT NUM'
      ,TRANID AS 'TRANID'
      ,TRAN_DT AS 'TRAN DATE'
      ,VALUE_DATE AS 'VALUE DATE' 
      ,TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,BANK AS 'BANK'
      ,RECON_STATUS AS 'RECON STATUS'  
       FROM CO_ACH_CBS_STG  WITH
			(NOLOCK) WHERE
			 ACTIVE_INDEX='N' AND CAST(CREATED_ON AS DATE) = CAST(GETDATE()-1 AS DATE)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!--ACH EXTERNAL SUPRESS RECORDS-->
	<query id="563">
	<name>GET_ACH_EXTERNAL_SUPPRESS_RECORDS</name>
	<queryType>SELECT</queryType>
	<queryString>
       SELECT 
       BATCH AS 'BATCH'
      ,CURRENCY AS 'CURRENCY'
      ,AMOUNT AS 'AMOUNT'
      ,INSTRUCTING_BRANCH AS 'INSTRUCTING BRANCH'
      ,INSTRUCTED_PARTICIPANT AS 'INSTRUCTED PARTICIPANT'
      ,INSTRUCTED_BRANCH AS 'INSTRUCTED BRANCH'
      ,DEBTOR_BANK AS 'DEBTOR BANK'
      ,DEBTOR_BRANCH AS 'DEBTOR BRANCH'
      ,DEBTOR_NAME AS 'DEBTOR NAME'
      ,DEBTOR_ACCOUNT AS 'DEBTOR ACCOUNT'
      ,CREDITOR_BANK AS 'CREDITOR BANK'
      ,CREDITOR_BRANCH AS 'CREDITOR BRANCH'
      ,CREATED_ON AS 'CREATED ON' 
      ,RECON_STATUS AS 'RECON STATUS'
      FROM CO_ACH_CBO_STG  WITH
	  (NOLOCK) WHERE ACTIVE_INDEX='N' AND CAST(CREATED_ON AS DATE) = CAST(GETDATE()-1 AS DATE)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- Summery Start -->
	
	<query id="563">
		<name>ONS_TOTAL_UNRECONCILE_SUMMERY</name>
		<queryType>SELECT</queryType>
		<queryString>
			EXEC ONS_TOTAL_UNRECONCILE_SUMMERY ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ONS_UNRECONCILE_SUMMERY</name>
		<queryType>SELECT</queryType>
		<queryString>
			EXEC ONS_UNRECONCILE_SUMMERY ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ONS_SUMMERY_CALCULATION</name>
		<queryType>SELECT</queryType>
		<queryString>
			EXEC ONS_SUMMERY_CALCULATION ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<query id="563">
		<name>ONS_INTERNAL_UNRECONCILE</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT SID AS 'SID'
	      ,INTERNAL_REF_NUM AS 'INTERNAL REF NUM'
	      ,ACNT AS 'ACNT'
	      ,TRAN_DATE AS 'TRAN DATE'
	      ,VALUE_DATE AS 'VALUE DATE'
	      ,AMOUNT AS 'AMOUNT'
	      ,DRCR AS 'DRCR'
	      ,CURRENCY AS 'CURRENCY'
	      ,CARD_NUMBER AS 'CARD NUMBER'
	      ,ACCT_BRANCH_ID AS 'ACCT BRANCH ID'
	      ,TRAN_PARTICULAR AS 'TRAN PARTICULAR'
	      ,TRAN_REMARKS AS 'TRAN REMARKS'
	      ,TRAN_ENTRY_USER AS 'TRAN ENTRY USER'
	      ,TRAN_POSTED_USER AS 'TRAN POSTED USER'
	      ,COMMENTS AS 'COMMENTS'
	      ,VERSION AS 'VERSION'
	      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
	      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
	      ,UPDATED_ON AS 'UPDATED ON'
	      ,CREATED_ON AS 'CREATED ON'
	      ,RECON_STATUS AS 'RECON STATUS'
	      ,RECON_ID AS 'RECON ID'
	      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
	      ,MAIN_REV_IND AS 'MAIN REV IND'
	      ,OPERATION AS 'OPERATION'
	      ,FILE_NAME AS 'FILE NAME'
	      ,BUSINESS_AREA AS 'BUSINESS AREA'
	      ,TRAN_REF_NUM AS 'TRAN REF NUM'
	     , DATEDIFF(day, VALUE_DATE,GETDATE() ) AS 'AGE'
	     ,(CASE WHEN MAIN_REV_IND='MANUAL' THEN MAIN_REV_IND ELSE 'SYSTEM' END) AS ENTRY_TYPE
  		 FROM FIN_ONS_CBS_STG  WITH (NOLOCK) WHERE
		RECON_STATUS IN ('AU','MU')
		AND ACTIVE_INDEX='Y' AND VALUE_DATE &lt;= ? ORDER BY AGE
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<query id="563">
		<name>ONS_EXTERNAL_UNRECONCILE</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT  SID AS 'SID'
	      ,TRAN_REF_NUM AS 'TRAN REF NUM'
	      ,INTERNAL_REF_NUM AS 'INTERNAL REF NUM'
	      ,ACCOUNT AS 'ACCOUNT'
	      ,TRAN_DATE AS 'TRAN DATE'
	      ,VALUE_DATE AS 'VALUE DATE'
	      ,AMOUNT AS 'AMOUNT'
	      ,DRCR AS 'DRCR'
	      ,CURRENCY AS 'CURRENCY'
	      ,PAN_NUMBER AS 'PAN NUMBER'
	      ,ORIGINATOR_BID AS 'ORIGINATOR BID'
	      ,DESTINATION_BID AS 'DESTINATION BID'
	      ,ACQUIRING_INSTITUTION_ID AS 'ACQUIRING INSTITUTION ID'
	      ,CARD_ACCEPTOR_NAME AS 'CARD ACCEPTOR NAME'
	      ,MERCHANT_CATEGORY_CODE AS 'MERCHANT CATEGORY CODE'
	      ,TRANSACTION_TYPE AS 'TRANSACTION TYPE'
	      ,COMMENTS AS 'COMMENTS'
	      ,VERSION AS 'VERSION'
	      ,ACTIVE_INDEX AS 'ACTIVE INDEX'
	      ,WORKFLOW_STATUS AS 'WORKFLOW STATUS'
	      ,UPDATED_ON AS 'UPDATED ON'
	      ,CREATED_ON AS 'CREATED ON'
	      ,RECON_STATUS AS 'RECON  STATUS'
	      ,RECON_ID AS 'RECON ID'
	      ,ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
	      ,MAIN_REV_IND AS 'MAIN REV IND' 
	      ,OPERATION AS 'OPERATION'
	      ,FILE_NAME AS 'FILE NAME'
	      ,BUSINESS_AREA AS 'BUSINESS AREA'
	      , DATEDIFF(day, VALUE_DATE,GETDATE() ) AS 'AGE'
	      ,(CASE WHEN MAIN_REV_IND='MANUAL' THEN MAIN_REV_IND ELSE 'SYSTEM' END) AS ENTRY_TYPE
	       FROM FIN_ONS_CBO_STG  WITH (NOLOCK) WHERE RECON_STATUS IS NULL
			AND ACTIVE_INDEX='Y' AND DRCR!='N' AND
			VALUE_DATE &lt;= ? ORDER BY AGE
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- Summery End -->
	
	<!-- AGEWISE_OUTSTANDING_ESCALATION -->
	<query id="563">
		<name>ONS_INTERNAL_ESCALATION</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM AGEWISE_OUTSTANDING_ESCALATION_INTERNAL() ORDER BY AGE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<query id="563">
		<name>ONS_EXTERNAL_ESCALATION</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM AGEWISE_OUTSTANDING_ESCALATION_EXTERNAL() ORDER BY AGE
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- AGEWISE_OUTSTANDING_ESCALATION END-->
	
	
	<!-- CASE CLOSE -->
	<query id="563">
		<name>CHECK_CASE_CLOSE</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT COUNT(*) AS COUNT FROM RECON_CASE_CLOSE  WITH (NOLOCK) WHERE CASE_ID=? AND ACTIVE_INDEX=?
		</queryString>
		<queryParam>CASE_ID@VARCHAR,ACTIVE_INDEX@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="9">
        <name>INSERT_CASE_CLOSE</name>
        <queryType>INSERT</queryType>
        <queryString>INSERT INTO RECON_CASE_CLOSE(CASE_ID,FILE_NAME,CREATED_ON,USER_ID,ACTIVE_INDEX) VALUES(?,?,?,?,?)
        </queryString>
        <queryParam>CASE_ID@VARCHAR,FILE_NAME@VARCHAR,CREATED_ON@TIMESTAMP,USER_ID@VARCHAR,ACTIVE_INDEX@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="18">
        <name>UPDATE_CASE_CLOSE</name>
        <queryType>UPDATE</queryType>
        <queryString>UPDATE RECON_CASE_CLOSE SET ACTIVE_INDEX=?,UPDATED_ON=? WHERE CASE_ID=?</queryString>
        <queryParam>ACTIVE_INDEX@VARCHAR,UPDATED_ON@TIMESTAMP,CASE_ID@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="563">
		<name>GET_CASE_FILE_NAME</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT FILE_NAME FROM RECON_CASE_CLOSE  WITH (NOLOCK) WHERE CASE_ID=? AND ACTIVE_INDEX=?
		</queryString>
		<queryParam>CASE_ID@VARCHAR,ACTIVE_INDEX@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<!-- CASE CLOSE END -->
	
	<query id="563">
		<name>ACH_SUMMARY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM ACH_SUMMARY(?,?)
		</queryString>
	</query>
	
	
	<!-- ECOM_ONUS -->
	
	<query id="24">
		<name>CARD_ECOM_ONUS_RECON_CARD_ECOM_ONUS_SETTL_STG</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
			SELECT 'ONUS_SETTL' AS RECON_SIDE,SID, REQUEST_DATE AS TRA_DATE,TRAN_AMT AS TRA_AMT ,RRN AS REF_NUM,
			DRCR AS DEB_CRE_IND,'CARD_ECOM_ONUS_SETTL_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS
			FROM CARD_ECOM_ONUS_SETTL_STG WITH (NOLOCK) WHERE  SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_INTERNAL_RECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_INTERNAL_RECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_EXTERNAL_RECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_EXTERNAL_RECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_RECONCILED_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_RECONCILED_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_INTERNAL_UNRECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_INTERNAL_UNRECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_EXTERNAL_UNRECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_EXTERNAL_UNRECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_UNRECONCILED_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_UNRECONCILED_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_INTERNAL_SUPPRESS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_INTERNAL_SUPPRESS(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_EXTERNAL_SUPPRESS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_EXTERNAL_SUPPRESS(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_UNRECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_UNRECONCILED(?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_SUMMARY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_SUMMARY(?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- ECOM_ONUS END -->
	
	
	<!-- ECOM_OFFUS -->
	
	<query id="24">
		<name>CARD_ECOM_OFFUS_RECON_CARD_ECOM_OFFUS_SETTL_STG</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
			SELECT 'OFFUS_SETTL' AS RECON_SIDE,SID, REQUEST_DATE AS TRA_DATE,TRAN_AMT AS TRA_AMT ,RRN AS REF_NUM,
			DRCR AS DEB_CRE_IND,'CARD_ECOM_OFFUS_SETTL_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS
			FROM CARD_ECOM_OFFUS_SETTL_STG WITH (NOLOCK) WHERE  SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>OFFUS_INTERNAL_RECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_INTERNAL_RECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_EXTERNAL_RECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_EXTERNAL_RECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_RECONCILED_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_RECONCILED_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_INTERNAL_UNRECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_INTERNAL_UNRECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_EXTERNAL_UNRECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_EXTERNAL_UNRECONCILED(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_UNRECONCILED_DRCR</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_UNRECONCILED_DRCR(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_INTERNAL_SUPPRESS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_INTERNAL_SUPPRESS(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_EXTERNAL_SUPPRESS</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_EXTERNAL_SUPPRESS(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_UNRECONCILED</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_UNRECONCILED(?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>OFFUS_SUMMARY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_SUMMARY(?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>OFFUS_TOTAL_SUMMARY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_TOTAL_SUMMARY(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_TOTAL_SUMMARY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_TOTAL_SUMMARY(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONUS_SUMMARY_NET_AMOUNT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_SUMMARY_NET_AMOUNT(?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>OFFUS_SUMMARY_NET_AMOUNT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_SUMMARY_NET_AMOUNT(?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>ONUS_TOTAL_SUMMARY_NET_AMOUNT</name> 
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_ONUS_TOTAL_SUMMARY_NET_AMOUNT(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="563">
		<name>OFFUS_TOTAL_SUMMARY_NET_AMOUNT</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM CARD_ECOM_OFFUS_TOTAL_SUMMARY_NET_AMOUNT(?,?)
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- ECOM_OFFUS END-->
	
	<!-- New Suppress reports start -->
	<query id="563">
		<name>ONS_INTERNAL_SUPPRESS_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT C.SID AS 'SID'
      ,C.INTERNAL_REF_NUM AS 'INTERNAL REF NUM'
      ,C.ACNT AS 'ACNT'
      ,C.TRAN_DATE AS 'TRAN DATE'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.AMOUNT AS 'AMOUNT'
      ,C.DRCR AS 'DRCR'
      ,C.CURRENCY AS 'CURRENCY'
      ,C.CARD_NUMBER AS 'CARD NUMBER'
      ,C.ACCT_BRANCH_ID AS 'ACCT BRANCH ID'
      ,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,C.TRAN_REMARKS AS 'TRAN REMARKS'
      ,C.TRAN_ENTRY_USER AS 'TRAN ENTRY USER'
      ,C.TRAN_POSTED_USER AS 'TRAN POSTED USER'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.OPERATION AS 'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME'
      ,C.BUSINESS_AREA AS 'BUSINESS AREA'
      ,C.TRAN_REF_NUM AS 'TRAN REF NUM'
	   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
       FROM FIN_ONS_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  AND C.VALUE_DATE
BETWEEN ? AND ?
AND A.RECON='ONS' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ONS'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>ONS_EXTERNAL_SUPPRESS_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
	   SELECT C.SID AS 'SID'
      ,C.TRAN_REF_NUM AS 'TRAN REF NUM'
      ,C.INTERNAL_REF_NUM AS 'INTERNAL REF NUM'
      ,C.ACCOUNT AS 'ACCOUNT'
      ,C.TRAN_DATE AS 'TRAN DATE'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.AMOUNT AS 'AMOUNT'
      ,C.DRCR AS 'DRCR'
      ,C.CURRENCY AS 'CURRENCY'
      ,C.PAN_NUMBER AS 'PAN NUMBER'
      ,C.ORIGINATOR_BID AS 'ORIGINATOR BID'
      ,C.DESTINATION_BID AS 'DESTINATION BID'
      ,C.ACQUIRING_INSTITUTION_ID AS 'ACQUIRING INSTITUTION ID'
      ,C.CARD_ACCEPTOR_NAME AS 'CARD ACCEPTOR NAME'
      ,C.MERCHANT_CATEGORY_CODE AS 'MERCHANT CATEGORY CODE'
      ,C.TRANSACTION_TYPE AS 'TRANSACTION TYPE'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.RECON_STATUS AS 'RECON  STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'MAIN REV IND' 
      ,C.OPERATION AS 'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME'
      ,C.BUSINESS_AREA AS 'BUSINESS AREA'
	   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
       FROM FIN_ONS_CBO_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  AND C.VALUE_DATE
BETWEEN ? AND ?
AND A.RECON='ONS' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ONS'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>MPCLEAR_INTERNAL_SUPPRESS_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		 SELECT  C.SID AS 'SID'
      ,C.PAYMENT_REFERENCE AS 'PAYMENT REFERENCE'
      ,C.CHN_REQ_DATE AS 'CHN REQ DATE'
      ,C.BD_STATUS AS 'BD STATUS'
      ,C.CUST_GSM_NUM AS 'CUST GSM NUM'
      ,C.SENDER_BANK AS 'SENDER BANK'
      ,C.DEBIT_ACCT_NUMBER AS 'DEBIT ACCT NUMBER'
      ,C.DEBIT_ACCT_NAME AS 'DEBIT ACCT NAME'
      ,C.STAFF_FLAG AS 'STAFF FLAG'
      ,C.CREDIT_ACCT_NUMBER AS 'CREDIT ACCT NUMBER'
      ,C.CREDIT_ACCT_NAME AS 'CREDIT ACCT NAME'
      ,C.PYMNT_TYPE AS 'PYMNT TYPE'
      ,C.REVERSED_FLAG AS 'REVERSED FLAG'
      ,C.TRAN_AMT  AS 'TRAN AMT'
      ,C.TRAN_ID AS 'TRAN ID'
      ,C.TRAN_DATE AS 'TRAN DATE'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.TRAN_POST_FLG AS  'TRAN POST FLG'
      ,C.WALLET_TRANSFER_WITHIN_BD AS 'WALLET TRANSFER WITHIN BD'
      ,C.CUST_TYPE_CHRG  AS 'CUST TYPE CHRG'
      ,C.FINACLE_DATE_TIME AS 'FINACLE DATE TIME'
      ,C.START_DATE_TIME  AS 'START DATE TIME'
      ,C.END_DATE_TIME AS 'END DATE TIME'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS  'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS  'WORKFLOW STATUS'
      ,C.UPDATED_ON AS  'UPDATED ON'
      ,C.CREATED_ON AS  'CREATED ON'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS   'RECON ID'
      ,C.ACTIVITY_COMMENTS AS  'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS  'MAIN REV IND'
      ,C.OPERATION AS  'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME' 
      ,C.BUSINESS_AREA AS 'BUSINESS AREA' 
	   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
	  FROM FIN_MP_CLEAR_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A  WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRAN_AMT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='MPCLEAR' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='MPCLEAR'
AND TRANSACTION_AMOUNT=C.TRAN_AMT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	<!-- MP CLEAR EXTERNAL SUPPRESS -->
	<query id="563">
		<name>MPCLEAR_EXTERNAL_SUPPRESS_UPDATE_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		 SELECT C.SID AS 'SID'
      ,C.SETTLEMENT_DATE AS 'SETTLEMENT DATE'
      ,C.SESSION_SEQ AS 'SESSION SEQ'
      ,C.CURRENCY AS 'CURRENCY'
      ,C.PARTICIPANT AS 'PARTICIPANT'
      ,C.SETTLEMENTRETRY AS 'SETTLEMENTRETRY'
      ,C.ID AS 'ID'
      ,C.TYPE AS 'TYPE'
      ,C.AMOUNT AS 'AMOUNT'
      ,C.STATE AS 'STATE'
      ,C.REASON AS 'REASON'
      ,C.ADDITIONAL_INFO AS 'ADDITIONAL INFO'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.OPERATION AS 'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME' 
      ,C.BUSINESS_AREA AS 'BUSINESS AREA'
	   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
  FROM FIN_MP_CLEAR_EXT_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK)ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.SETTLEMENT_DATE BETWEEN ? AND ?
AND A.RECON='MPCLEAR' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='MPCLEAR'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
     
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<query id="563">
		<name>SUSPENCE_INTERNAL_SUPPRESS_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
 SELECT C.SID
      ,C.BRANCH_ID
      ,C.TRAN_ID
      ,C.TRAN_DATE
      ,C.VALUE_DATE
      ,C.ACCT_NUMBER
      ,C.DRCR
      ,C.AMOUNT
      ,C.TRAN_PARTICULAR
      ,C.REFERENCE_NUMBER
      ,C.TRAN_REMARKS
      ,C.TRAN_CRNCY_CODE
      ,C.REF_CRNCY_CODE
      ,C.REF_AMT
      ,C.COMMENTS
      ,C.VERSION
      ,C.ACTIVE_INDEX
      ,C.WORKFLOW_STATUS
      ,C.UPDATED_ON
      ,C.CREATED_ON
      ,C.RECON_STATUS
      ,C.RECON_ID
      ,C.ACTIVITY_COMMENTS
      ,C.MAIN_REV_IND
      ,C.OPERATION
      ,C.FILE_NAME
      ,C.BUSINESS_AREA
	  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'

 FROM FIN_SUSPENSE_STG C  WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.DRCR='C' AND C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='SUSPENSE ACCOUNTS' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='SUSPENSE ACCOUNTS'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
 
 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="563">
		<name>SUSPENCE_EXTERNAL_SUPPRESS_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
     SELECT C.SID
      ,C.BRANCH_ID
      ,C.TRAN_ID
      ,C.TRAN_DATE
      ,C.VALUE_DATE
      ,C.ACCT_NUMBER
      ,C.DRCR
      ,C.AMOUNT
      ,C.TRAN_PARTICULAR
      ,C.REFERENCE_NUMBER
      ,C.TRAN_REMARKS
      ,C.TRAN_CRNCY_CODE
      ,C.REF_CRNCY_CODE
      ,C.REF_AMT
      ,C.COMMENTS
      ,C.VERSION
      ,C.ACTIVE_INDEX
      ,C.WORKFLOW_STATUS
      ,C.UPDATED_ON
      ,C.CREATED_ON
      ,C.RECON_STATUS
      ,C.RECON_ID
      ,C.ACTIVITY_COMMENTS
      ,C.MAIN_REV_IND
      ,C.OPERATION
      ,C.FILE_NAME
      ,C.BUSINESS_AREA
	  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'

 FROM FIN_SUSPENSE_STG C  WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.DRCR='D' AND C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='SUSPENSE ACCOUNTS' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='SUSPENSE ACCOUNTS'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
     
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ACH INTERNAL SUPPRESS  -->

<query id="563">
		<name>ACH_SUPPRESS_INTERNAL_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
       	           SELECT C.SID AS 'SID' 
      ,C.ACCT_NUM AS 'ACCT NUM'
      ,C.DRCR AS 'DRCR'
      ,C.EXTRACTION_DATE AS 'EXTRACTION DATE'
      ,C.CHNL AS 'CHANNEL'
      ,C.END_TO_END_ID 'END TO END ID'
      ,C.TRANID AS 'TRANID'
      ,C.TRAN_DT AS 'TRAN DATE'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,C.FILENAME AS 'FILENAME'
      ,C.TAG_20 AS 'TAG 20'
      ,C.TAG_21 AS 'TAG 21'
      ,C.BANK AS 'BANK'
      ,C.MSG_GENERATED AS 'MSG GENERATED'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.OPERATION AS 'OPERATION'
      ,C.BUSINESS_AREA AS 'BUSINESS_AREA'

  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
 FROM CO_ACH_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRAN_AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DT BETWEEN ? AND ?
AND A.RECON='ACH' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ACH'
AND TRANSACTION_AMOUNT=C.TRAN_AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
			
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ACH EXTERNAL SUPPRESS  -->

<query id="563">
		<name>ACH_SUPPRESS_EXTERNAL_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
      SELECT   C.SID AS 'SID'
      ,C.STATUS AS 'STATUS'
      ,C.DIRECTION AS 'DIRECTION'
      ,C.TYPE AS 'TYPE'
      ,C.TRANSACTION_ID AS 'TRANSACTION ID'
      ,C.END_TO_END_ID AS 'END TO END ID'
      ,C.INSTRUCTION_ID AS 'INSTRUCTION ID'
      ,C.BATCH AS 'BATCH'
      ,C.CURRENCY AS 'CURRENCY'
      ,C.AMOUNT AS 'AMOUNT'
      ,C.INSTRUCTING_PARTICIPANT AS 'INSTRUCTING PARTICIPANT'
      ,C.INSTRUCTING_BRANCH AS 'INSTRUCTING BRANCH'
      ,C.INSTRUCTED_PARTICIPANT AS 'INSTRUCTED PARTICIPANT'
      ,C.INSTRUCTED_BRANCH AS 'INSTRUCTED BRANCH'
      ,C.DEBTOR_BANK AS 'DEBTOR BANK'
      ,C.DEBTOR_BRANCH AS 'DEBTOR BRANCH'
      ,C.DEBTOR_NAME AS 'DEBTOR NAME'
      ,C.DEBTOR_ACCOUNT AS 'DEBTOR ACCOUNT'
      ,C.DEBTOR_IBAN AS 'DEBTOR IBAN'
      ,C.CREDITOR_BANK AS 'CREDITOR BANK'
      ,C.CREDITOR_BRANCH AS 'CREDITOR BRANCH'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.DUE_ON AS 'DUE ON'
      ,C.DELETED_ON AS 'DELETED ON'
      ,C.CREDITOR_NAME AS 'CREDITOR NAME'
      ,C.CREATED_BY AS 'CREATED BY'
      ,C.UPDATED_BY AS 'UPDATED BY'
      ,C.LOCKED_BY AS 'LOCKED BY'
      ,C.DELETED_BY AS 'DELETED BY'
      ,C.DELETED AS 'DELETED'
      ,C.CREDITOR_ACCOUNT AS 'CREDITOR ACCOUNT'
      ,C.CREDITOR_IBAN AS 'CREDITOR IBAN'
      ,C.TRANSACTION_PURPOSE AS 'TRANSACTION PURPOSE'
      ,C.SETTLEMENT_DATE AS 'SETTLEMENT DATE'
      ,C.SESSION_NO AS 'SESSION NO'
      ,C.REASON AS 'REASON'
      ,C.ADDITIONAL_INFO AS 'ADDITIONAL INFO'
      ,C.RELATED_REASON AS 'RELATED REASON'
      ,C.RELATED_ADDITIONAL_INFO AS 'RELATED ADDITIONAL INFO'
      ,C.MANDATE AS 'MANDATE'
      ,C.CANDIDATE_PAYMENT AS 'CANDIDATE PAYMENT'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.OPERATION AS 'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME'
      ,C.BUSINESS_AREA AS 'BUSINESS AREA'
	   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'

      FROM CO_ACH_CBO_STG  C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.SETTLEMENT_DATE BETWEEN ? AND ?
AND A.RECON='ACH' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ACH'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
      </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>
	
<!-- ATM MASTER CARD SUPPRESS INTERNAL  -->
	<query id="563">
		<name>ATMMASTERCARD_INTERNAL_SUPPRESS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		 SELECT  C.SID AS 'SID'
      ,C.CARD_NUM AS 'CARD NUM'
      ,C.ACTUAL_REF_NUM AS 'ACTUAL REF NUM'
      ,C.TRAN_DATE AS 'TRAN DATE'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.TXN_REF_NUM AS 'TXN REF NUM'
      ,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,C.TRAN_AMOUNT AS 'TRAN AMOUNT'
      ,C.DRCR_IND AS 'DRCR IND'
      ,C.TRAN_RMKS AS 'TRAN RMKS'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'ACTIVITY COMMENTS'
      ,C.OPERATION AS 'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME'
      ,C.BUSINESS_AREA AS 'BUSINESS AREA'

	  
  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
  FROM CARD_ATM_MC_CBS_STG   C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRAN_AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='ATM MASTER CARD ACQUIRER' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ATM MASTER CARD ACQUIRER'
AND TRANSACTION_AMOUNT=C.TRAN_AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- ATM MASTER CARD SUPPRESS EXTERNAL -->
	<query id="563">
		<name>ATMMASTERCARD_EXTERNAL_SUPPRESS_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		 SELECT  C.SID AS 'SID'
      ,C.MSG_TYPE_IND AS 'MSG TYPE IND'
      ,C.SWITCH_SER_NUM AS 'SWITCH SER NUM'
      ,C.PROCESSOR_ACQ AS 'PROCESSOR ACQ'
      ,C.PROCESSOR_ID AS 'PROCESSOR ID'
      ,C.TRN_DATE AS 'TRN DATE'
      ,C.TRN_TIME AS 'TRN TIME'
      ,C.PAN_LENGTH AS 'PAN LENGTH'
      ,C.PRIMARY_ACC_NUM AS 'PRIMARY ACC NUM'
      ,C.PROCESSING_CODE AS 'PROCESSING CODE'
      ,C.TRACE_NUM1 AS 'TRACE NUM1'
      ,C.MERCHANT_TYPE AS 'MERCHANT TYPE'
      ,C.POS_ENTRY AS 'POS ENTRY'
      ,C.REF_NUMBER AS 'REF NUMBER'
      ,C.ACQ_INST_ID AS 'ACQ INST ID'
      ,C.TERMINAL_ID AS 'TERMINAL ID'
      ,C.BRAND AS 'BRAND'
      ,C.ADVICE_REASON_CODE AS 'ADVICE REASON CODE'
      ,C.INTRACUR_AGRMNT_CODE AS 'INTRACUR AGRMNT CODE'
      ,C.AUTHORIZATION_ID AS 'AUTHORIZATION ID'
      ,C.CUR_CODE_TRN AS 'CUR CODE TRN'
      ,C.IMP_DEC_TRN AS 'IMP DEC TRN'
      ,C.COMPTD_AMT_TRN_LOCAL AS 'COMPTD AMT TRN LOCAL'
      ,C.COMPTD_AMT_TRN_LOCAL_DR_CR_IND AS 'COMPTD AMT TRN LOCAL DR CR IND'
      ,C.CASH_BACK_AMT_LOCAL AS 'CASH BACK AMT LOCAL'
      ,C.CASH_BACK_AMT_LOCAL_DR_CR_IND AS 'CASH BACK AMT LOCAL DR CR IND'
      ,C.ACCESS_FEE_LOCAL AS 'ACCESS FEE LOCAL'
      ,C.ACCESS_FEE_LOCAL_DR_CR_IND AS 'ACCESS FEE LOCAL DR CR IND'
      ,C.CUR_CODE_STMNT AS 'CUR CODE STMNT'
      ,C.IMPLIED_DEC_STMNT AS 'IMPLIED DEC STMNT'
      ,C.CONVERSION_RATE_STMNT AS 'CONVERSION RATE STMNT'
      ,C.COMPTD_AMT_STMNT AS 'COMPTD AMT STMNT'
      ,C.COMPTD_AMOUNT_STMNT_DR_CR_IND AS 'COMPTD AMOUNT STMNT DR CR IND'
      ,C.INTRCHNG_FEE AS 'INTRCHNG FEE'
      ,C.INTRCHNG_FEE_DR_CR_IND AS  'INTRCHNG FEE DR CR IND'
      ,C.SER_LEVEL_IND AS 'SER LEVEL IND'
      ,C.RESP_CODE2 AS 'RESP CODE'
      ,C.FILLER1 AS 'FILLER1'
      ,C.POSITIVE_ID_IND AS 'POSITIVE ID IND'
      ,C.ATM_SURCHRGE_FREE_PRGM_ID AS 'ATM SURCHRGE FREE PRGM ID'
      ,C.CROSS_BORDER_IND AS 'CROSS BORDER IND'
      ,C.CROSS_BORDER_CUR_IND AS 'CROSS BORDER CUR IND'
      ,C.VISA_INTR_SER_ASS_IND AS 'VISA INTR SER ASS IND'
      ,C.REQ_AMT_TRN_LOCAL AS 'REQ AMT TRN LOCAL'
      ,C.FILLER2 AS 'FILLER2'
      ,C.TRACE_NUM_ADJ_TRNS AS 'TRACE NUM ADJ TRNS'
      ,C.FILLER3 AS 'FILLER3'
      ,C.COMMENTS AS 'COMMENTS'
      ,C.VERSION AS 'VERSION'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.CREATED_ON AS 'CREATED ON'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.OPERATION AS 'OPERATION'
      ,C.FILE_NAME AS 'FILE NAME'
      ,C.BUSINESS_AREA AS 'BUSINESS AREA'
      ,C.RESP_CODE1 AS 'RESP CODE1'
	  
  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'

  FROM CARD_ATM_MC_ACQ_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.COMPTD_AMT_TRN_LOCAL
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRN_DATE BETWEEN ? AND ?
AND A.RECON='ATM MASTER CARD ACQUIRER' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ATM MASTER CARD ACQUIRER'
AND TRANSACTION_AMOUNT=C.COMPTD_AMT_TRN_LOCAL AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 	
	
	<!-- ATM INTERNAL SUPPRESS-->

<query id="563">
		<name>ATM_RECONCILATION_SUPPRESS_INTERNAL</name>
		<queryType>SELECT</queryType>
		<queryString> 
         SELECT        C.SID AS 'SID' 
              ,C.CUSTOMER_ACCT AS 'CUSTOMER ACCT'
              ,C.ATM_ACCOUNT AS 'ATM ACCOUNT'
              ,C.TRAN_ID AS 'TTRAN ID'
              ,C.TRAN_DATE AS 'TRAN DATE'
              ,C.VALUE_DATE AS 'VALUE DATE'
              ,C.CUSTOMER_ACCT1 AS 'CUSTOMER ACCT1'
              ,C.ATM_ACCOUNT1 AS 'ATM ACCOUNT1'
              ,C.DRCR AS 'DRCR'
              ,C.AMOUNT AS 'AMOUNT'
              ,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
              ,C.REFERENCE_NUMBER AS 'REFERENCE NUMBER'
              ,C.TRAN_REMARKS AS 'TRAN REMARKS'
              ,C.TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
              ,C.REF_CRNCY_CODE AS 'REF CRNCY CODE'
              ,C.REF_AMT AS 'REF AMT'
              ,C.COMMENTS AS 'COMMENTS'
              ,C.VERSION AS 'VERSION'
              ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,C.UPDATED_ON AS 'UPDATED ON'
              ,C.CREATED_ON AS 'CREATED ON'
              ,C.RECON_STATUS AS 'RECON STATUS'
              ,C.RECON_ID AS 'RECON ID' 
              ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,C.MAIN_REV_IND AS 'MAIN REV IND'
              ,C.OPERATION AS 'OPERATION'
              ,C.BUSINESS_AREA AS 'BUSINESS AREA'
              ,C.FILE_NAME AS 'FILE NAME' 
			   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
			  FROM CO_ATM_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='ATM TRANSACTIONS' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ATM TRANSACTIONS'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
	 </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- ATM INTERNAL SUPPRESS -->

<query id="563">
		<name>ATM_RECONCILATION_SUPPRESS_EXTERNAL</name>
		<queryType>SELECT</queryType>
		<queryString>   
            SELECT        C.SID AS 'SID'
              ,C.TXNMESSAGES_ID AS 'TXNMESSAGES ID'
              ,C.CREATEDDATE AS 'CREATEDDATE' 
              ,C.TXNDATETIME AS 'XNDATETIME'
              ,C.TXNDATE AS 'TXNDATE'
              ,C.TXNTIME AS 'TXNTIME'
              ,C.TERMINALID 'TERMINALID'
              ,C.SEQUENCENUMBER AS 'SEQUENCENUMBER'
              ,C.TXNTYPE_ID AS 'TXNTYPE ID'
              ,C.TXNTYPE AS 'TXNTYPE'
              ,C.CHIPCARD AS 'CHIPCARD'
              ,C.CARDNUMBER AS 'CARDNUMBER'
              ,C.ACCOUNTNO1 AS 'ACCOUNTNO1'
              ,C.ACCOUNTNO2 AS 'ACCOUNTNO2'
              ,C.AMOUNT AS 'AMOUNT'
              ,C.NOTEDETAILS AS 'NOTEDETAILS'
              ,C.CARDTAKEN AS 'CARDTAKEN'
              ,C.CARDCAPTURE AS 'CARDCAPTURE'
              ,C.NOTESPRESENTED AS 'NOTESPRESENTED'
              ,C.NOTESTAKEN AS 'NOTESTAKEN'
              ,C.NOTESRETRACT AS 'NOTESRETRACT'
              ,C.RESPONSECODE AS 'RESPONSECODE'
              , C.RESPONSEDESC AS 'RESPONSEDESC'
              ,C.HARDWARESTATUS AS 'HARDWARESTATUS'
              ,C.COMMENTS AS 'COMMENTS'
              ,C.VERSION AS 'VERSION'
              ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
              ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
              ,C.UPDATED_ON AS 'UPDATED ON'
              ,C.CREATED_ON AS 'CREATED ON'
              ,C.RECON_STATUS AS 'RECON STATUS'
              ,C.RECON_ID AS 'RECON ID'
              ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
              ,C.MAIN_REV_IND AS 'MAIN REV IND'
              ,C.OPERATION AS 'OPERATION'
              ,C.BUSINESS_AREA AS 'BUSINESS AREA'
              ,C.FILE_NAME AS 'FILE NAME'
			   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
               FROM CO_ATM_JOURNAL_STG  C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TXNDATETIME BETWEEN ? AND ?
AND A.RECON='ATM TRANSACTIONS' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ATM TRANSACTIONS'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		  </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<query id="663">
		<name>ATM_VISA_ACQUIRER_INTERNAL_SUPPRESSED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		
	   SELECT  C.SID
      ,C.ACCT_NUM
      ,C.BRANCH_ID
      ,C.TRAN_ID
      ,C.TRAN_DATE
      ,C.VALUE_DATE
      ,C.CUSTOMER_ACCT
      ,C.DRCR
      ,C.AMOUNT
      ,C.TRAN_PARTICULAR
      ,C.REFERENCE_NUMBER
      ,C.TRAN_REMARKS
      ,C.TRAN_CRNCY_CODE
      ,C.REF_CRNCY_CODE
      ,C.REF_AMT
      ,C.POSTED_ON
      ,C.POSTED_BY
      ,C.COMMENTS
      ,C.VERSION
      ,C.ACTIVE_INDEX
      ,C.WORKFLOW_STATUS
      ,C.UPDATED_ON
      ,C.CREATED_ON
      ,C.RECON_STATUS
      ,C.RECON_ID
      ,C.ACTIVITY_COMMENTS
      ,C.MAIN_REV_IND
      ,C.OPERATION
      ,C.FILE_NAME
      ,C.BUSINESS_AREA
	  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
  FROM CARD_ATM_VISA_ACQ_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='ATM VISA ACQUIRER' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ATM VISA ACQUIRER'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>ATM_VISA_ACQUIRER_EXTERNAL_SUPPRESSED_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
	   SELECT C.SID
      ,C.BAT_NUM
      ,C.XMIT_DATE
      ,C.TIME
      ,C.CARD_NUMBER
      ,C.RETRIEVAL_REF_NUMBER
      ,C.TRACE_NUMBER
      ,C.ISSUER_TRMNL
      ,C.TRAN_TYPE
      ,C.PROCSS_CODE
      ,C.ENT_MOD
      ,C.CN_STP
      ,C.RSP_CD
      ,C.REAS_CODE
      ,C.TRANSACTION_AMOUNT
      ,C.KES_CURRENCY
      ,C.SETTLEMENT_AMOUNT
      ,C.CR_CURRENCY
      ,C.CA_ID
      ,C.TERMINAL_ID
      ,C.FPI
      ,C.CI
      ,C.REPORT_DATE
      ,C.TR_ID
      ,C.ACI
      ,C.FEE_JURIS
      ,C.ROUTING
      ,C.VERSION
      ,C.ACTIVE_INDEX
      ,C.STATUS
      ,C.COMMENTS
      ,C.WORKFLOW_STATUS
      ,C.USER_ID
      ,C.OPERATION
      ,C.UPDATED_ON
      ,C.RECON_STATUS
      ,C.RECON_ID
      ,C.MAIN_REV_IND
      ,C.CREATED_ON
      ,C.PROC_CODE
      ,C.FILENAME
      ,C.ACTIVITY_COMMENTS
	  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
  FROM  CARD_ATM_VISA_ACQ_EXT_STG   C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRANSACTION_AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.REPORT_DATE BETWEEN ? AND ?
AND A.RECON='ATM VISA ACQUIRER' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='ATM VISA ACQUIRER'
AND TRANSACTION_AMOUNT=C.TRANSACTION_AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="563">
		<name>GET_CDM_INTERNAL_SUPPRESS_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
			 SELECT       C.CDM_ID AS 'CDM ID'
			,C.CDM_BRANCH AS 'CDM BRANCH'
			,C.TRAN_ID AS 'TRAN ID'
			,C.TRAN_DATE AS 'TRAN DATE'
			,C.VALUE_DATE AS 'VALUE DATE'
			,C.CDM_ACCOUNT AS 'CDM ACCOUNT'
			,C.DRCR AS 'DRCR'
			,C.AMOUNT AS 'AMOUNT'
			,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,C.TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
			,C.REF_CRNCY_CODE AS 'REF CRNCY CODE'
			,C.REF_AMT AS 'REF AMT'
			,C.RECON_STATUS AS 'RECON STATUS'
			,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
			FROM CO_CDM_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
 AND C.CREATED_ON = convert(varchar(10), getdate(), 102)
AND A.RECON='CDM' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='CDM'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
			
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="563">
		<name>GET_COD_CDM_EXTERNAL_SUPPRESS_RECORDS</name>
		<queryType>SELECT</queryType>
		<queryString>
        SELECT C.TXNMESSAGES_ID AS 'TXNMESSAGES ID'
      ,C.CREATEDDATE AS 'CREATEDDATE'
      ,C.TXNDATETIME AS 'TXNDATETIME'
      ,C.TXNDATE AS 'TXNDATE'
      ,C.TERMINALID AS 'TERMINALID'
      ,C.SEQUENCENUMBER AS 'SEQUENCENUMBER'
      ,C.TXNTYPE AS 'TXNTYPE'
      ,C.CARDNUMBER AS 'CARDNUMBER'
      ,C.ACCOUNTNO1 AS 'ACCOUNTNO1'
      ,C.AMOUNT AS 'AMOUNT'
	  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
       FROM CO_CDM_JOURNAL_STG  C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
 AND C.CREATED_ON = convert(varchar(10), getdate(), 102)
AND A.RECON='CDM' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='CDM'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- CREDIT CARD SUPPRESS INTERNAL  -->
	<query id="563">
		<name>CREDIT_SUPRESS_INTERNAL_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		 SELECT C.SID AS 'SID'
      ,C.FORACID AS 'FORACID'
      ,C.CIF_ID As 'CIF ID'
      ,C.TRAN_DATE AS 'TRAN DATE'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.PART_TRAN_TYPE AS 'PART TRAN TYPE'
      ,C.TRAN_AMT AS 'TRAN AMT'
      ,C.CARD_NUM AS 'CARD NUM'
      ,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
      ,C.TRAN_NOTES AS 'TRAN NOTES'
      ,C.TXN_CAT AS 'TXN CAT'
      ,C.TXN_ID AS 'TXN ID'
      ,C.PSTD_FLG AS 'PSTD FLG'
      ,C.PSTD_DATE AS 'PSTD DATE'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.VERSION AS 'VERSION'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_ID AS 'ACTIVITY ID'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.RECON_REMARKS AS 'RECON REMARKS'
      ,C.CREATED_ON AS 'CREATIONTIME'
      ,C.UPDATED_ON AS 'UPDATETIME'
      ,C.UPDATEDBY AS 'UPDATEDBY '
      ,C.OPSOURCEID AS 'OPSOURCEID'
      ,C.OPIPADDRESS AS 'OPIPADDRESS'
      ,C.SOURCE_NAME AS 'SOURCE NAME'
      ,C.SOURCE_TYPE AS 'SOURCE TYPE'
      ,C.TTUM_STATUS AS 'TTUM STATUS'
      ,C.RECON_DATE AS 'RECON DATE'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.OPERATION AS 'OPERATION'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
	  ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
        FROM CREDIT_CARD_STATEMENT_CBS_STG   C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRAN_AMT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='CREDIT CARD STATEMENT' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='CREDIT CARD STATEMENT'
AND TRANSACTION_AMOUNT=C.TRAN_AMT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
  
   
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- CREDIT CARD SUPPRESS EXTERNAL  -->
	<query id="563">
		<name>CREDIT_SUPRESS_EXTERNAL_RECON</name>
		<queryType>SELECT</queryType>
		<queryString>
		 SELECT C.SID AS 'SID'
      ,C.DB_TS AS 'DB TS'
      ,C.TXN_ID AS 'TXN ID'
      ,C.TXN_DATE AS 'TXN DATE'
      ,C.TXN_SRL_NO AS 'TXN SRL NO'
      ,C.TXN_AMT AS 'TXN AMT'
      ,C.ORG_AMT AS 'ORG AMT'
      ,C.ORG_CUR AS 'ORG CUR'
      ,C.TXN_TYP AS 'TXN TYP'
      ,C.TXN_CAT AS 'TXN CAT'
      ,C.TXN_ORGN AS 'TXN ORGN'
      ,C.TXN_DESC AS 'TXN DESC'
      ,C.BANK_ID AS 'BANK ID'
      ,C.BRANCH_ID AS 'BRANCH ID'
      ,C.AC_ID AS 'AC ID'
      ,C.PRIMARYACID AS 'PRIMARYACID'
      ,C.CARD_NUMBER AS 'CARD NUMBER'
      ,C.PST_DATE AS 'PST DATE'
      ,C.FREE_AMOUNT AS 'FREE AMOUNT'
      ,C.VALUE_DATE AS 'VALUE DATE'
      ,C.DEL_FLG AS 'DEL FLG'
      ,C.R_MOD_ID AS 'R MOD ID'
      ,C.R_MOD_TIME AS 'R MOD TIME'
      ,C.R_CRE_ID AS 'R CRE ID'
      ,C.R_CRE_TIME AS 'R CRE TIME'
      ,C.RECON_STATUS AS 'RECON STATUS'
      ,C.VERSION AS 'VERSION'
      ,C.RECON_ID AS 'RECON ID'
      ,C.ACTIVITY_ID AS 'ACTIVITY ID'
      ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
      ,C.RECON_REMARKS AS 'RECON REMARKS'
      ,C.CREATED_ON AS 'CREATIONTIME'
      ,C.UPDATED_ON AS 'UPDATETIME' 
      ,C.UPDATEDBY AS 'UPDATEDBY'
      ,C.OPSOURCEID AS 'OPSOURCEID'
      ,C.OPIPADDRESS AS 'OPIPADDRESS'
      ,C.SOURCE_NAME AS 'SOURCE NAME' 
      ,C.SOURCE_TYPE AS 'SOURCE TYPE'
      ,C.TTUM_STATUS AS 'TTUM STATUS'
      ,C.RECON_DATE AS 'RECON DATE'
      ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
      ,C.MAIN_REV_IND AS 'MAIN REV IND'
      ,C.UPDATED_ON AS 'UPDATED ON'
      ,C.OPERATION AS 'OPERATION'
      ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS' 
	   ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
  FROM CREDIT_CARD_STATEMENT_TRAN_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TXN_AMT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TXN_DATE BETWEEN ? AND ?
AND A.RECON='CREDIT CARD STATEMENT' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='CREDIT CARD STATEMENT'
AND TRANSACTION_AMOUNT=C.TXN_AMT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
  
  
  
 
			</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<!-- POSNI ACQUIRER SUPPRESS INTERNAL -->

	<query id="563">
		<name>POSNI_ACQUIRER_SUPPRESS_INTERNAL</name>
		<queryType>SELECT</queryType>
		<queryString>
			 SELECT       C.SID AS 'SID'
			,C.ACCT_NUM AS 'ACCT NUM'
			,C.CARD_TYPE AS 'CARD TYPE'
			,C.VALUE_DATE AS 'VALUE DATE'
			,C.TRAN_DATE AS 'TRAN DATE'
			,C.REF_NUM AS 'REF NO'
			,C.TRAN_ID AS 'TRAN ID'
			,C.TRAN_AMT AS 'TRAN AMT'
			,C.PART_TRAN_TYPE AS 'PART TRAN TYPE'
			,C.TRAN_RMKS AS 'TRAN RMKS'
			,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
			,C.PSTD_FLG AS 'PSTD FLG'
			,C.PSTD_DATE AS 'PSTD DATE'
			,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
			,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,C.UPDATED_ON AS 'UPDATED ON'
			,C.CREATED_ON AS 'CREATED ON'
			,C.RECON_STATUS AS 'RECON STATUS'
			,C.RECON_ID AS 'RECON ID'
			,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,C.MAIN_REV_IND AS 'MAIN REV IND'
			,C.OPERATION AS 'OPERATION'
			,C.BUSINESS_AREA AS 'BUSINESS AREA'
			,C.VERSION AS 'VERSION'
			 ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
			FROM CARD_POSNI_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRAN_AMT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.TRAN_DATE BETWEEN ? AND ?
AND A.RECON='POS NI ACQUIRER' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='POS NI ACQUIRER'
AND TRANSACTION_AMOUNT=C.TRAN_AMT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<!-- POSNI ACQUIRER SUPPRESS EXTERNAL -->

	<query id="563">
		<name>POSNI_ACQUIRER_SUPPRESS_EXTERNAL</name>
		<queryType>SELECT</queryType>
		<queryString>
			 SELECT       C.SID AS 'SID'
			,C.ACCT_NUM AS 'ACCT NUM'
			,C.CARD_TYPE AS 'CARD TYPE'
			,C.FILE_NAME AS 'FILE NAME'
			,C.VALUE_DATE AS 'VALUE DATE'
			,C.UPLOAD_DATE AS 'UPLOAD DATE'
			,C.REF_NUM AS 'REF NUM'
			,C.CARD_NO AS 'CARD NO'
			,C.TRAN_AMT AS 'TRAN AMT'
			,C.MERCHANT_ID AS'MERCHANT ID'
			,C.MERCHANT_NAME AS 'MERCHANT NAME'
			,C.LOCATION AS 'LOCATION'
			,C.TRAN_CRNCY AS 'TRAN CRNCY'
			,C.TRAN_TYPE AS 'TRAN TYPE'
			,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
			,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
			,C.UPDATED_ON AS 'UPDATED ON'
			,C.CREATED_ON AS 'CREATED ON'
			,C.RECON_STATUS AS 'RECON STATUS'
			,C.RECON_ID AS 'RECON ID'
			,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
			,C.MAIN_REV_IND AS 'MAIN REV IND'
			,C.OPERATION AS 'OPERATION'
			,C.BUSINESS_AREA AS 'BUSINESS AREA'
			,C.VERSION AS 'VERSION'
			 ,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
			FROM CARD_POSNI_TRAN_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.TRAN_AMT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
AND C.VALUE_DATE BETWEEN ? AND ?
AND A.RECON='POS NI ACQUIRER' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='POS NI ACQUIRER'
AND TRANSACTION_AMOUNT=C.TRAN_AMT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- CDM RECONCILILATION SUPPRESS INTERNAL -->

<query id="563">
		<name>CDM_SUPPRESS_INTERNAL_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
            SELECT C.SID AS 'SID'
                  ,C.CDM_ID AS 'CDM ID'
                  ,C.CDM_BRANCH AS 'CDM BRANCH'
                 ,C.TRAN_ID AS 'TRAN ID'
                 ,C.TRAN_DATE AS 'TRAN DATE'
                 ,C.VALUE_DATE AS 'VALUE DATE'
                 ,C.CUSTOMER_ACCT AS 'CUSTOMER ACCT'
                 ,C.CDM_ACCOUNT AS 'CDM ACCOUNT'
                 ,C.DRCR AS 'DRCR'
                 ,C.AMOUNT AS 'AMOUNT'
                 ,C.TRAN_PARTICULAR AS 'TRAN PARTICULAR'
                 ,C.REFERENCE_NUMBER AS 'REFERENCE NUMBER'
                 ,C.TRAN_REMARKS AS 'TRAN REMARKS'
                 ,C.TRAN_CRNCY_CODE AS 'TRAN CRNCY CODE'
                 ,C.REF_CRNCY_CODE AS 'REF CRNCY CODE'
                 ,C.REF_AMT AS 'REF AMT'
                 ,C.COMMENTS AS 'COMMENTS'
                 ,C.VERSION AS 'VERSION'
                 ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
                 ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                 ,C.UPDATED_ON AS 'UPDATED ON'
                 ,C.CREATED_ON AS 'CREATED ON'
                 ,C.RECON_STATUS AS 'RECON STATUS'
                 ,C.RECON_ID AS 'RECON ID'
                 ,C.ACTIVITY_COMMENTS AS 'ACTIVITY COMMENTS'
                 ,C.MAIN_REV_IND AS 'MAIN REV IND'
                 ,C.OPERATION AS 'OPERATION'
                 ,C.FILE_NAME AS 'FILE NAME'
                 ,C.BUSINESS_AREA AS 'BUSINESS AREA'

				,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
                  FROM CO_CDM_CBS_STG C WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
 AND C. TRAN_DATE BETWEEN ? AND ?
AND A.RECON='CDM' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='CDM'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
		  </queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>

<!-- CDM RECONCILILATION SUPPRESS EXTERNAL -->
<query id="563">
		<name>CDM_SUPPRESS_EXTERNAL_REPORT</name>
		<queryType>SELECT</queryType>
		<queryString>   
        SELECT           C.SID AS 'SID'
                ,C.TXNMESSAGES_ID AS 'TXNMESSAGES ID'
                ,C.CREATEDDATE AS 'CREATEDDATE'
                ,C.TXNDATETIME AS 'TXNDATETIME'
                ,C.TXNDATE AS 'TXNDATE'
                ,C.TXNTIME AS 'TXNTIME'
                ,C.TERMINALID AS 'TERMINALID'
                ,C.SEQUENCENUMBER AS 'SEQUENCENUMBER'
                ,C.TXNTYPE_ID AS 'TXNTYPE ID'
                ,C.TXNTYPE AS 'TXNTYPE'
                ,C.CARDNUMBER AS 'CARDNUMBER'
                ,C.ACCOUNTNO1 AS 'ACCOUNTNO1'
                ,C.ACCOUNTNAME AS 'ACCOUNTNAME'
                ,C.AMOUNT AS 'AMOUNT'
                ,C.NOTEDETAILS AS 'NOTEDETAILS'
                ,C.CARDTAKEN AS 'CARDTAKEN'
                ,C.CARDCAPTURE AS 'CARDCAPTURE'
                ,C.NOTESENCASHED AS 'NOTESENCASHED'
                ,C.CASHRETRACT AS 'CASHRETRACT'
                ,C.RESPONSECODE AS 'RESPONSECODE'
                ,C.RESPONSEDESC AS 'RESPONSEDESC'
                ,C.HARDWARESTATUS AS 'HARDWARESTATUS'
                ,C.COMMENTS AS 'COMMENTS'
                ,C.VERSION AS 'VERSION'
                ,C.ACTIVE_INDEX AS 'ACTIVE INDEX'
                ,C.WORKFLOW_STATUS AS 'WORKFLOW STATUS'
                ,C.UPDATED_ON AS 'UPDATED ON'
                ,C.CREATED_ON AS 'CREATED ON'
                ,C.RECON_STATUS AS 'RECON STATUS'
                ,C.RECON_ID AS 'RECON ID'
                ,C.ACTIVITY_COMMENTS AS 'ACTIVITY_COMMENTS'
                ,C.MAIN_REV_IND AS 'MAIN_REV_IND'
                ,C.OPERATION AS 'OPERATION'
                ,C.FILE_NAME AS 'FILE_NAME'
                ,C.BUSINESS_AREA AS 'BUSINESS_AREA'
				,SUBSTRING(C.ACTIVITY_COMMENTS,0, CHARINDEX(':',C.ACTIVITY_COMMENTS)) AS 'VERIFIER USER ID'
	   ,C.ACTIVITY_COMMENTS  AS 'VERIFIER COMMENTS'
	   ,A.RECENT_ACTOR AS 'MAKER USER ID'
	   ,A.COMMENT AS 'MAKER COMMENTS'
                 FROM CO_CDM_JOURNAL_STG C  WITH (NOLOCK)
   JOIN RECON_ACTIVITY_FLOW A WITH (NOLOCK) ON A.TRANSACTION_AMOUNT=C.AMOUNT
WHERE C.ACTIVE_INDEX='N' AND  A.ACTIVITY_TYPE='SUPPRESS'  
 AND C. TXNDATETIME BETWEEN ? AND ?
AND A.RECON='CDM' AND A.ACTIVITY_LEVEL=1
AND SNO=(SELECT MIN(SNO) FROM RECON_ACTIVITY_FLOW WHERE ACTIVITY_TYPE='SUPPRESS' AND RECON='CDM'
AND TRANSACTION_AMOUNT=C.AMOUNT AND ACTIVITY_LEVEL=1 AND STATUS='PENDING')
	 		 
</queryString>
     <queryParam></queryParam>
	<queryParamLiteralValues></queryParamLiteralValues>
</query>


	
</queries>