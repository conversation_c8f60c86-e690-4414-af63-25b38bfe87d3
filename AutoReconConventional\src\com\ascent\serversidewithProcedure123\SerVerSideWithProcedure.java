package com.ascent.serversidewithProcedure123;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

public class SerVerSideWithProcedure extends HttpServlet
{
	
	private static final long serialVersionUID = 1L;
	static Connection connection;
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException
	{
		System.out.println(request.getParameter("datarequest"));
		HashMap<String, Object> valueMap = new Gson().fromJson(request.getParameter("datarequest"), new TypeToken<HashMap<String, Object>>() {}.getType());
		System.out.println("SSSSS"+valueMap);
		/*System.out.println("SSSSS"+request.getParameter("message_type"));
		System.out.println("SSSSS"+request.getParameter("match_type"));
		System.out.println("SSSSS"+request.getParameter("card_number"));
		System.out.println("SSSSS"+request.getParameter("reference_number"));
		System.out.println("SSSSS"+request.getParameter("startDate"));
		System.out.println("SSSSS"+request.getParameter("endDate"));*/
		
		JQueryDataTableParamModel param = DataTablesParamUtility.getParam(request);
		List<HashMap<String,String>> list=new ArrayList<HashMap<String,String>>();
		try
		{
			Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
			//Connection connection = HistoryConnection.getConnection();
    		connection = DriverManager.getConnection("************************************************************************","Ascent", "AutoRecon@123");
    		
    		// int sortColumnIndex = param.iSortColumnIndex;
    		 String sortDirection=param.sSortDirection;
    		 String searcString=param.sSearch.toLowerCase();
         	 int displayLength=param.iDisplayLength;
         	 int displayStart=param.iDisplayStart;
         	 int iTotalRecords=0;
         	 
    	    System.out.println(" this is procedure calling    "+param.sEcho+"  "+param.iDisplayLength+"  "+param.iDisplayStart+"  "+param.sSortDirection);
    	    String sql = valueMap.get("reconName")+"_procedure ?,?,?,?,?" ;
    	    System.out.println( "sql          "+sql);
    	    String criteriaFetch="";
    	    
    	    
    	    
    	    String tableName=(String)valueMap.get("reconName");
    	    
    	    if(tableName.equalsIgnoreCase("ONUS_POS_CREDIT_RECON")|| tableName.equalsIgnoreCase("ONUS_POS_PAYROL_RECON")
    				|| tableName.equalsIgnoreCase("ACQ_MPOS_CRDS_RECON")|| tableName.equalsIgnoreCase("ACQ_UPOS_CRDS_RECON") 
    				|| tableName.equalsIgnoreCase("ACQ_VPOS_CRDS_RECON")|| tableName.equalsIgnoreCase("ISSUER_VATM_CREDIT_RECON")
    				|| tableName.equalsIgnoreCase("ISSUER_MPOS_CREDIT_RECON")|| tableName.equalsIgnoreCase("ISSUER_VPOS_CREDIT_RECON")
    				|| tableName.equalsIgnoreCase("ISSUER_MATM_CREDIT_RECON")){
    	    	//TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    	    	
    	    }
         	
            CallableStatement stmt = connection.prepareCall(sql);
            stmt.setInt(1,displayLength);
            stmt.setInt(2,displayStart);
            stmt.setString(3,"sid");
            stmt.setString(4,sortDirection);
            stmt.setString(5,searcString);
            ResultSet rs=stmt.executeQuery();
            
            
            List<String> columnList=new ArrayList<String>();
            ResultSetMetaData rsmd=rs.getMetaData();
			String columns="";
			for (int i=1;i<=rsmd.getColumnCount();i++)
			{
				
				if( (i<rsmd.getColumnCount() ) && i>2 )
					columns=columns+rsmd.getColumnLabel(i)+",";
				else if(i>2)
					columns=columns+rsmd.getColumnLabel(i);
				columnList.add(rsmd.getColumnLabel(i));
				
			}
        	while(rs.next())
			{
				LinkedHashMap<String,String> dataRecord=new LinkedHashMap<String,String>();
				
				iTotalRecords=rs.getInt(2);
				for(int c=0;c<rsmd.getColumnCount();c++)
				{
				
				
						/*if(valueMap.get("match_type")!=null || !(valueMap.get("match_type").equals(0)))
						{
						    if(rs.getString("MATCH_TYPE").contains(((String)valueMap.get("match_type")).trim()))
							{
						    	dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
						    	list.add(dataRecord);
							}
							
						}*/
						
						/*else if(valueMap.get("message_type")!=null || !(valueMap.get("message_type").equals(0)))
						{
						    if(rs.getString("MESSAGE_TYPE").equals(valueMap.get("message_type")))
							{
						    	dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
							}
							
						}
						
						else if(valueMap.get("reference_no")!=null)
						{
						    if(rs.getString("REFERENCE_NO").equals(request.getParameter("reference_no")))
							{
						    	dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
							}
							
						}
						
						else if(valueMap.get("card_no")!=null)
						{
						    if(rs.getString("CARD_NO").equals(request.getParameter("card_no")))
							{
						    	dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
							}
							
						}*/
						/*else if(valueMap.get("startDate")!=null && valueMap.get("startDate")!=null)
						{
							if(rs.getString("CARD_NO").equals(request.getParameter("card_no")))
							{
								
							}
						}*/
						/*else{
							dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
						}*/
						
						/*else if(valueMap.get("startDate")!=null && valueMap.get("endDate")!=null)
						{
						    if(rs.getString("TRA_DATE").equals(valueMap.get("startDate")) && rs.getString("TRA_DATE").equals(valueMap.get("endDate")))
							{
						    	dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
							}
							
						}else{*/
						dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
						list.add(dataRecord);
						//}
			
				}
				//dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
				//list.add(dataRecord);
				/*System.out.println(dataRecord.size()+ " "+columnList.size());
				if(dataRecord.size()==columnList.size())
				{
					list.add(dataRecord);
				}*/
				
				
				
				
				
				/////////////////////////////////////
				
				/*if(tableName.equalsIgnoreCase("ONUS_POS_CREDIT_RECON")|| tableName.equalsIgnoreCase("ONUS_POS_PAYROL_RECON")
						|| tableName.equalsIgnoreCase("ACQ_MPOS_CRDS_RECON")|| tableName.equalsIgnoreCase("ACQ_UPOS_CRDS_RECON") 
						|| tableName.equalsIgnoreCase("ACQ_VPOS_CRDS_RECON")|| tableName.equalsIgnoreCase("ISSUER_VATM_CREDIT_RECON")
						|| tableName.equalsIgnoreCase("ISSUER_MPOS_CREDIT_RECON")|| tableName.equalsIgnoreCase("ISSUER_VPOS_CREDIT_RECON")
						|| tableName.equalsIgnoreCase("ISSUER_MATM_CREDIT_RECON")){ //CARD NUMBER AS I002_NUMBER 
					if(referenceNo.isEmpty()){
						fetchQuery="SELECT * FROM "+tableName+" WHERE  ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
					}else if(card_no.isEmpty()){
						fetchQuery="SELECT * FROM "+tableName+" WHERE  I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
					}else if(referenceNo.isEmpty() && card_no.isEmpty()){
						fetchQuery="SELECT * FROM "+tableName+" WHERE  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
					}
					else{
		        	 fetchQuery="SELECT * FROM "+tableName+" WHERE I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        	}
		        }
		    	else if(tableName.equalsIgnoreCase("PAYROL_RECON")){
		    		if(card_no.isEmpty()){
		    			fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}else{
		    		 fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND  MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}
		    	}
		    	else if(tableName.equalsIgnoreCase("ACQ_QPAY_CRDS_RECON")){
		    		if(referenceNo.isEmpty()){
		    			 fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}else{
		    		 fetchQuery="SELECT * FROM "+tableName+" WHERE RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}
		    	}
		    	else if(tableName.equalsIgnoreCase("ISSUER_NATM_DEBIT_RECON") ||tableName.equalsIgnoreCase("ISSUER_NPOS_DEBIT_RECON")){// CARD_NUMBER AND RETR_REF_NO
		    		if(referenceNo.isEmpty()){
		    			fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}else if(card_no.isEmpty()){
		    			fetchQuery="SELECT * FROM "+tableName+" WHERE  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
		    		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
		    			fetchQuery="SELECT * FROM "+tableName+" WHERE  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}
					else{
		    		 fetchQuery="SELECT * FROM "+tableName+" WHERE RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		    		}
		    	}
				
				
		    	else{ // CARD NUMBER AS PAN
		    		if(tableName.equalsIgnoreCase("ONUS_ATM_DEBIT_RECON")||tableName.equalsIgnoreCase("ONUS_ATM_DEPOSIT_RECON")||
		    		   tableName.equalsIgnoreCase("ATM_RECON")){
		    			if(referenceNo.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}else if(card_no.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
		        		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    			else{
		        		 fetchQuery="SELECT * FROM "+tableName+" WHERE RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    			
		    		}else if(tableName.equalsIgnoreCase("ACQ_NATM_CRDS_RECON") || tableName.equalsIgnoreCase("ACQ_MATM_CRDS_RECON") ||
		    				 tableName.equalsIgnoreCase("ACQ_UATM_CRDS_RECON")|| tableName.equalsIgnoreCase("ACQ_VATM_CRDS_RECON") ||
		    				 tableName.equalsIgnoreCase("ONUS_ATM_CREDIT_RECON") || tableName.equalsIgnoreCase("ONUS_ATM_PAYROL_RECON") ||
		    				 tableName.equalsIgnoreCase("ONUS_POS_DEBIT_RECON")){//ONUS_ATM_PAYROL_RECON, ONUS_POS_DEBIT_RECON
		    			if(referenceNo.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y'  AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    			else{
		        		 fetchQuery="SELECT * FROM "+tableName+" WHERE RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    		}else if(tableName.equalsIgnoreCase("ACQ_MATM_CRDS_RECON") || tableName.equalsIgnoreCase("ACQ_UATM_CRDS_RECON")){
		    			if(referenceNo.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND  MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    			else{
		        		 fetchQuery="SELECT * FROM "+tableName+" WHERE RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND  MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    		}
		    		else{
		    			if(referenceNo.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}else if(card_no.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
		        		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
		        			fetchQuery="SELECT * FROM "+tableName+" WHERE  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    			else{
		        		 fetchQuery="SELECT * FROM "+tableName+" WHERE RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
		        		}
		    		}*/
				
				//////////////////////////////////////
				
			}
        	
        	
        		    JsonObject jsonResponse = new JsonObject();		
        			jsonResponse.addProperty("sEcho", param.sEcho);
        			jsonResponse.addProperty("iTotalRecords", iTotalRecords);
        	        jsonResponse.addProperty("iTotalDisplayRecords", iTotalRecords);			
        	        jsonResponse.addProperty("sColumns",columns);
        	        Gson gson = new Gson();
        			jsonResponse.add("aaData", gson.toJsonTree(list));
        			System.out.println(list);
        			
        			response.setContentType("application/Json");
        			response.getWriter().print(jsonResponse.toString());
        			
        	
        	
            
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
	}

	


}
