package com.ascent.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class RequestedDrillDownDS extends BasicDataSource{

 private static final long serialVersionUID = 1L;

 public RequestedDrillDownDS() {
  
 }
public DSResponse executeFetch(DSRequest request){
 DSResponse response =new DSResponse();
 System.out.println("drill.............................");
 Map map=request.getCriteria();
  List<Map<String,Object>> activitydataList=null;
  List fields=new ArrayList();
 //System.out.println(map.get("data"));
 String val= (String) map.get("value");
 Map m= (Map) map.get("data");
 Map newMap=(Map) m.get("activity_data");
 activitydataList = (List<Map<String, Object>>) newMap.get("activity_data");
 
 if(val.equalsIgnoreCase("RequestFields")){
  Map mapFields=activitydataList.get(0);
  System.out.println("mapFields :" +   mapFields);
  Set set=mapFields.keySet();
  Iterator itr=set.iterator();   
  while(itr.hasNext())
  {
   String keys=(String) itr.next();
   if(keys.contains("_selection")|| keys.contains("_embeddedComponents_irisExListGrid")||
		   keys.contains("_embeddedComponents_irisExReProcessGrid")){}
   //if(keys.contains("_selection")||keys.contains("_embeddedComponents_irisExListGrid")||keys.contains("ex")||keys.contains("BIN")||keys.contains("GEOGRAPHY")||keys.contains("REVERSAL")){}
   else{
    Map keysMap=new HashMap();
    keysMap.put("keys", keys);
    fields.add(keysMap);
    response.setData(fields);
    System.out.println("keys :"  +   keys );
      }      
  } 
 }
 else if(val.equalsIgnoreCase("RequestedData")){  
	  System.out.println(activitydataList);
	   response.setData(activitydataList);
	 }
 return response;
}
}