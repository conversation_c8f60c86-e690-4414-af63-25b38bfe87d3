package com.ascent.boot;

import java.io.File;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.custumize.integration.Integrations;
import com.ascent.custumize.query.Queries;
import com.ascent.util.AscentAutoReconConstants;

public class AppplicationController {
	private static Logger logger = LogManager.getLogger(AppplicationController.class);

	public static void main(String args[]) {

		AppplicationController bootIntegration = new AppplicationController();
		bootIntegration.boot();
	}

	public void boot() {

		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();

		// meta data test
		Properties bootProps = etlMetaInstance.getBootProperties();
		Properties dbProps = etlMetaInstance.getDbProperties();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		Integrations integrations = etlMetaInstance.getEtlConfs();
		Queries etlQueryConfs = etlMetaInstance.getEtlQueryConfs();

		/////
		System.out.println("Properties Loaded");
		logger.trace("Properties Loaded");
		String extPath = (String) appProps.get(AscentAutoReconConstants.AUTO_RECON_HOME)
				+ (String) appProps.get(AscentAutoReconConstants.SFTP_FOLDER_NAME);

		try {

			File sftpFolder = new File(extPath);

			// iris

			// IrisIntegration irisIntegration = new
			// IrisIntegration("Switch_Iris");
			// irisIntegration.processFile(sftpFolder);
			// GL_1002
			// GLIntegration glParser1002 = new GLIntegration("GL_1002");
			// glParser1002.processFile(sftpFolder);

			// GL_1472
			//GLIntegration glParser1472 = new GLIntegration("GL_1472");
			//glParser1472.processFile(sftpFolder);

			// GL_1015
		/*	GLIntegration glParser1015 = new GLIntegration("GL_1015");
			glParser1015.processFile(sftpFolder);

			// GL_1016
			GLIntegration glParser1016 = new GLIntegration("GL_1016");
			glParser1016.processFile(sftpFolder);

			// GL_2247
			GLIntegration glParser2247 = new GLIntegration("GL_2247");
			glParser2247.processFile(sftpFolder);
			
			//GL_1006
			GLIntegration glParser1006 = new GLIntegration("GL_1006");
			glParser1006.processFile(sftpFolder);
			
			//GL_1482
			GLIntegration glParser1006 = new GLIntegration("GL_1482");
			glParser1006.processFile(sftpFolder);
			
			//GL_2279
			GLIntegration glParser1006 = new GLIntegration("GL_2279");
			glParser1006.processFile(sftpFolder);

*/
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
}
