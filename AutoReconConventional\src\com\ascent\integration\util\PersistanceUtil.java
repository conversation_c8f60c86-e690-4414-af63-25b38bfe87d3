package com.ascent.integration.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.query.Query;

public class PersistanceUtil {
	private static Logger logger = LogManager.getLogger(PersistanceUtil.class.getName());

	public static void persistTxn(PreparedStatement insertOrUpdatePstmt, Query qryConf, Map<String, Object> txn)
			throws Exception {

		try {
			List<String> qryParams = qryConf.getQueryParamList();
			Map<String, Integer> paramTypeMap = qryConf.getQueryParamTypeMap();

			if (qryParams != null && qryParams.size() > 0) {
				int index = 1;
				for (String param : qryParams) {
					try {
						//System.out.println(index+","+txn.get(param)+","+paramTypeMap.get(param));
						insertOrUpdatePstmt.setObject(index, txn.get(param), paramTypeMap.get(param));
					} catch (Exception e) {

						System.err.println(param + ":" + txn.get(param));
						System.err.println(insertOrUpdatePstmt);
						e.printStackTrace();
						throw e;
					}
					index++;

				}
				try {
					insertOrUpdatePstmt.executeUpdate();
					// System.in.read();
				} catch (Exception e) {
					// System.err.println(param+":"+txn.get(param));
					System.out.println("Query Conf Name" + qryConf.getName());
					System.out.println(txn);
					System.err.println(insertOrUpdatePstmt.toString());
					e.printStackTrace();
					throw e;

				}
			}
		} catch (Exception e) {
			//e.printStackTrace();
			logger.error(e.getMessage(),e);
			throw e;

		}
	}

	public static synchronized Long generateSeqNo(Connection connection, PreparedStatement seqNoPstmt, String seqName)
			throws Exception {
		DbUtil dbUtil = new DbUtil();
		ResultSet txnRs = null;
		try {
			if (connection == null && seqNoPstmt == null) {
				throw new Exception(" Connection Unavailable to Proceed");
			}

			txnRs = seqNoPstmt.executeQuery();
			if (txnRs.next()) {
				return txnRs.getLong(1);
			}
		} catch (SQLException e) {

			PreparedStatement createSeqPstmt = null;
			PreparedStatement seqNumPstmtTemp = null;
			String createSeqNumQry = null;
			String seqNumQry = null;
			try {
				connection = dbUtil.getConnection();
				createSeqNumQry = "CREATE  SEQUENCE " + seqName + "   AS BIGINT START WITH 1  INCREMENT BY 1 CYCLE";
				seqNumQry = "SELECT NEXT VALUE FOR " + seqName + "  as sno";

				createSeqPstmt = connection.prepareStatement(createSeqNumQry);
				createSeqPstmt.executeUpdate();

				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);

				ResultSet rs = seqNumPstmtTemp.executeQuery();
				if (rs.next()) {
					return rs.getLong(1);
				}

			} catch (Exception e1) {
				logger.trace("sequence creation error" + e1);
				e1.printStackTrace();
				logger.error(e1.getMessage(), e1);
				throw e1;

			} finally {

				DbUtil.closePreparedStatement(createSeqPstmt);
				DbUtil.closePreparedStatement(seqNumPstmtTemp);

			}
		} finally {
			DbUtil.closeResultSet(txnRs);

		}
		return 0l;

	}

	public static List<Map<String, Object>> retrieveDataLimited(Connection connection, Query queryConf,
			Map<String, Object> paramValueMap, PreparedStatement pstmt) throws Exception {

		List<Map<String, Object>> data = new ArrayList<Map<String, Object>>();

		try {

			Map<String, Integer> paramTypeMap = queryConf.getQueryParamTypeMap();

			if (queryConf.getQueryParamList() != null && paramTypeMap != null) {

				for (String param : queryConf.getQueryParamList()) {

					pstmt.setString(1, (String) paramValueMap.get(param));

					/*
					 * pstmt.setObject(index, paramValueMap.get(param),
					 * (ColumnTypeUtil.dbTypes.get(paramTypeMap.get(param))).
					 * intValue());
					 */

				}

			}

			ResultSet txnRs = pstmt.executeQuery();

			ResultSetMetaData resultSetMetaData = txnRs.getMetaData();
			int noOfColumns = resultSetMetaData.getColumnCount();

			List<String> columnNames = new ArrayList<String>();

			for (int i = 1; i <= noOfColumns; i++) {
				String columnName = resultSetMetaData.getColumnName(i);
				columnNames.add(columnName);
			}
			int noColumns = columnNames.size();
			while (txnRs.next()) {
				Map<String, Object> record = new HashMap<String, Object>();
				for (int i = 0; i < noColumns; i++) {
					record.put(columnNames.get(i), txnRs.getObject(i + 1));
				}
				data.add(record);

			}
		} catch (SQLException e) {
			logger.error(e.getMessage(), e);
		} finally {

			DbUtil.closePreparedStatement(pstmt);

		}
		return data;

	}

	public static int updateTxn(PreparedStatement reconUpdatePstmt, Query updateQryConf, Map<String, Object> rec)
			throws Exception {
		int updateCount = 0;
		try {

			List<String> qryParams = updateQryConf.getQueryParamList();
			Map<String, Integer> paramTypeMap = updateQryConf.getQueryParamTypeMap();

			if (qryParams != null && qryParams.size() > 0) {
				int index = 1;
				for (String param : qryParams) {
					try {

						reconUpdatePstmt.setObject(index, rec.get(param),
								(ColumnTypeUtil.dbTypes.get(paramTypeMap.get(param))).intValue());
					} catch (Exception e) {
						System.err.println(param + ":" + rec.get(param));
						System.err.println(reconUpdatePstmt);
						logger.error(e.getMessage(),e);
						e.printStackTrace();

						throw e;
					}
					index++;

				}
				try {
					updateCount = reconUpdatePstmt.executeUpdate();
				} catch (Exception e) {

					System.err.println(reconUpdatePstmt.toString());
					//e.printStackTrace();
					logger.error(e.getMessage(),e);
					throw e;

				}
			}
		} catch (Exception e) {
			//e.printStackTrace();
			logger.error(e.getMessage(),e);
			throw e;

		}
		return updateCount;

	}

	public static void updateExTxn(PreparedStatement stagingExUpdatePstmt, Query stgExUpdateQry, long exid,
			long version) {

		try {

			String updateQry = stgExUpdateQry.getQueryString();
			version = version - 1;
			stagingExUpdatePstmt.setString(1, version+"");
			stagingExUpdatePstmt.setLong(2, exid);
			
			//stagingExUpdatePstmt.setString(2, version+"");
			stagingExUpdatePstmt.executeUpdate();
		} catch (SQLException e) {
			logger.error(e.getMessage(),e);
			//e.printStackTrace();
		}
	}
}