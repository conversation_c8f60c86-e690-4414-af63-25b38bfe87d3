package com.ascent.ds.operations;

import java.io.FileOutputStream;
import java.sql.Connection;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.ascent.banknizwa.metainstance.EtlMetaInstance;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class CaseManagementOperations extends BasicDataSource implements PagesConstants{

	private static final long serialVersionUID = 1L;

	private static final String INSERT_RECON_CASE_OPERATION = "INSERT_RECON_CASE_OPERATION";
	private static final String UPDATE_USER_IN_RECON_CASE_MANAGEMENT = "UPDATE_USER_IN_RECON_CASE_MANAGEMENT";
	private static final String UPDATE_STATUS_IN_RECON_CASE_MANAGEMENT = "UPDATE_STATUS_IN_RECON_CASE_MANAGEMENT";
	private static final String GET_CASE_OPERATIONS_BY_CASE_ID = "GET_CASE_OPERATIONS_BY_CASE_ID";
	private static final String CHECK_CASE_CLOSE = "CHECK_CASE_CLOSE";
	private static final String INSERT_CASE_CLOSE = "INSERT_CASE_CLOSE";
	private static final String UPDATE_CASE_CLOSE = "UPDATE_CASE_CLOSE";
	private static final String GET_CASE_FILE_NAME = "GET_CASE_FILE_NAME";


	private static Logger logger = LogManager.getLogger(CaseManagementOperations.class.getName());

	public DSResponse executeFetch(final DSRequest request)throws Exception 
	{
		DSResponse dsResponse = new DSResponse();
		CustomerDao customerDao = new CustomerDao();

		Map<String, Object> result = null;

		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		String reconName = httpSession.getAttribute("user_selected_recon").toString();

		Map requestParams = request.getCriteria();
		String action = (String) requestParams.get("action");

		if (action != null && action.equals("CaseTransfer")) {

			boolean responseStatus = false;
			Connection connection = null;
			result = new HashMap<String, Object>();
			HashMap<String,Object> insertParamMap=new HashMap<String,Object>();
			HashMap<String,Object> updateParamMap=new HashMap<String,Object>();

			Map reqCriteria = request.getValues();

			connection = DbUtil.getConnection();

			//Map<String, Object> selectedRecords = (Map<String, Object>) reqCriteria.get("selectedRecords");
			//List<Map<String, Object>> recordsList = (List<Map<String, Object>>) reqCriteria.get("recordsList");

			long sid = Long.parseLong(reqCriteria.get("sid").toString());
			String caseID = reqCriteria.get("case_id").toString();
			String department = String.valueOf(reqCriteria.get("department"));
			String assignedTO = String.valueOf(reqCriteria.get("assignedTO"));
			String comment = String.valueOf(reqCriteria.get("comment"));

			//keeping(PUT) RECON_CASE_OPERATION table data for inserting
			insertParamMap.put("sid", sid);
			insertParamMap.put("case_id", caseID);
			insertParamMap.put("operation_name", "CaseTransfer");
			insertParamMap.put("operation_date", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			insertParamMap.put("comment", comment);
			insertParamMap.put("user_id", user.getUserId());

			responseStatus = customerDao.insertData(connection, insertParamMap, INSERT_RECON_CASE_OPERATION);


			//keeping(PUT) RECON_CASE_MANAGEMENT table data for update user
			updateParamMap.put("recently_updated_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			updateParamMap.put("department", department);
			updateParamMap.put("assigned_to", assignedTO);
			updateParamMap.put("case_id", caseID);
			updateParamMap.put("sid", sid);
			customerDao.update(connection, updateParamMap, UPDATE_USER_IN_RECON_CASE_MANAGEMENT);


			if(responseStatus) {
				updateResultStatus(result, SUCCESS, "Case Transfered Sucessfully");
			}
			else {
				updateResultStatus(result, FAILED, "Operation Failed");
			}

			dsResponse.setData(result);
		}

		if (action != null && action.equals("caseComments")) {

			boolean responseStatus = false;
			Connection connection = null;
			result = new HashMap<String, Object>();
			HashMap<String,Object> insertParamMap=new HashMap<String,Object>();

			Map reqCriteria = request.getValues();

			connection = DbUtil.getConnection();

			long sid = Long.parseLong(reqCriteria.get("sid").toString());
			String caseID = reqCriteria.get("case_id").toString();
			String comment = String.valueOf(reqCriteria.get("comment"));

			//keeping(PUT) RECON_CASE_OPERATION table data for inserting
			insertParamMap.put("sid", sid);
			insertParamMap.put("case_id", caseID);
			insertParamMap.put("operation_name", "CaseComments");
			insertParamMap.put("operation_date", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			insertParamMap.put("comment", comment);
			insertParamMap.put("user_id", user.getUserId());

			responseStatus = customerDao.insertData(connection, insertParamMap, INSERT_RECON_CASE_OPERATION);

			if(responseStatus) {
				updateResultStatus(result, SUCCESS, "Comment Added Sucessfully");
			}
			else {
				updateResultStatus(result, FAILED, "Operation Failed");
			}

			dsResponse.setData(result);


			//Mail Trigger Start

			List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
			String toMail = reqCriteria.get("assignedTo").toString();

			String subject = "Recon : Case Management Updation ("+reconName+") : "+caseID;
			String content = "Dear Sir/Madam, <br> <br> <br> The captioned request has been Updated with additional information by the " +user.getUserId()+ " and assigned to you for further action. <br> <br>";

			Map<String,String> map = new HashMap<String,String>();
			map.put("subject", subject);
			map.put("content", content);
			map.put("comment", comment);
			map.put("toMail", toMail);
			map.put("ccMail", user.getEmailId());

			new CaseMailTrigger().setContentMailTrigger(selectedRecords,map);

			//Mail Trigger End
		}

		if (action != null && action.equals("caseReOpen")) {

			boolean responseStatus = false;
			Connection connection = null;
			result = new HashMap<String, Object>();
			HashMap<String,Object> updateParamMap=new HashMap<String,Object>();

			Map reqCriteria = request.getValues();
			connection = DbUtil.getConnection();

			long sid = Long.parseLong(reqCriteria.get("sid").toString());
			String caseID = reqCriteria.get("case_id").toString();

			//keeping(PUT) RECON_CASE_MANAGEMENT table data for update status
			updateParamMap.put("recently_updated_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			updateParamMap.put("status", "PENDING");
			updateParamMap.put("case_id", caseID);
			updateParamMap.put("sid", sid);

			responseStatus = customerDao.update(connection, updateParamMap, UPDATE_STATUS_IN_RECON_CASE_MANAGEMENT);

			if(responseStatus) {
				updateResultStatus(result, SUCCESS, "Case Re-Open Sucessfully");
			}
			else {
				updateResultStatus(result, FAILED, "Operation Failed");
			}

			dsResponse.setData(result);


			//Mail Trigger Start

			List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
			String toMail = reqCriteria.get("assignedTo").toString();

			String subject = "Recon : Case Management Re-open ("+reconName+") : "+caseID;
			String content = "Dear Sir/Madam, <br> <br> <br> The captioned request has been Re-open by the " +user.getUserId()+". <br> <br>";

			Map<String,String> map = new HashMap<String,String>();
			map.put("subject", subject);
			map.put("content", content);
			map.put("toMail", toMail);
			map.put("ccMail", user.getEmailId());

			new CaseMailTrigger().setContentMailTrigger(selectedRecords,map);

			//Mail Trigger End

		}


		if (action != null && action.equals("GetCaseOperationByCaseID")) {

			LoadRegulator loadRegulator = new LoadRegulator();
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
			List<Map<String, Object>> caseOperationMap = new ArrayList<Map<String, Object>>();

			Map reqCriteria = request.getValues();

			long sid = Long.parseLong(reqCriteria.get("sid").toString());
			String caseID = reqCriteria.get("case_id").toString();

			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(GET_CASE_OPERATIONS_BY_CASE_ID);

			paramMap.put("case_id", caseID);
			paramMap.put("sid", sid);
			caseOperationMap = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(caseOperationMap);
			logger.debug("caseOperationMap : "+caseOperationMap);

		}
		
		if (action != null && action.equals("closeCase")) {

			boolean responseStatus = false;
			Connection connection = null;
			result = new HashMap<String, Object>();
			HashMap<String,Object> insertParamMap=new HashMap<String,Object>();
			HashMap<String,Object> updateParamMap=new HashMap<String,Object>();

			Map reqCriteria = request.getValues();

			connection = DbUtil.getConnection();

			long sid = Long.parseLong(reqCriteria.get("sid").toString());
			String caseID = reqCriteria.get("case_id").toString();
			String comment = String.valueOf(reqCriteria.get("comment"));

			//keeping(PUT) RECON_CASE_OPERATION table data for inserting
			insertParamMap.put("sid", sid);
			insertParamMap.put("case_id", caseID);
			insertParamMap.put("operation_name", "Close");
			insertParamMap.put("operation_date", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			insertParamMap.put("comment", comment);
			insertParamMap.put("user_id", user.getUserId());

			customerDao.insertData(connection, insertParamMap, INSERT_RECON_CASE_OPERATION);


			//keeping(PUT) RECON_CASE_MANAGEMENT table data for update status
			updateParamMap.put("recently_updated_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			updateParamMap.put("status", "CLOSED");
			updateParamMap.put("case_id", caseID);
			updateParamMap.put("sid", sid);
			responseStatus = customerDao.update(connection, updateParamMap, UPDATE_STATUS_IN_RECON_CASE_MANAGEMENT);


			if(responseStatus) {
				updateResultStatus(result, SUCCESS, "Case Closed Sucessfully");
			}
			else {
				updateResultStatus(result, FAILED, "Operation Failed");
			}

			dsResponse.setData(result);


			//file uploading code starts
			
			String fileData = (String) requestParams.get("fileData");
			String fileName = (String) requestParams.get("fileName");
			//fileName = fileName.substring(0,fileName.lastIndexOf("."))+"_"+caseID+fileName.substring(fileName.lastIndexOf("."),fileName.length());
			byte fileByte[] = Base64.getDecoder().decode(fileData);
			ServletContext context= request.getServletContext();
			String uploadDirectory = context.getRealPath("/closedFiles")+"/"+fileName;
			FileOutputStream fos = new FileOutputStream(uploadDirectory);
			fos.write(fileByte);
			fos.close();
			
			HashMap<String,Object> checkClose=new HashMap<String,Object>();
			Query queryConf = AscentWebMetaInstance.getInstance().getWebQueryConfs().getQueryConf(CHECK_CASE_CLOSE);
			checkClose.put("CASE_ID", caseID);
			checkClose.put("ACTIVE_INDEX", "Y");
			List<Map<String, Object>> closeCount = new LoadRegulator().loadCompleteData(checkClose, queryConf);
			int count = (int)closeCount.get(0).get("COUNT");
			if(count > 0) {				
				HashMap<String,Object> updateClose=new HashMap<String,Object>();
				updateClose.put("ACTIVE_INDEX", "N");
				updateClose.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				updateClose.put("CASE_ID", caseID);
				customerDao.update(connection, updateClose, UPDATE_CASE_CLOSE);
			}
			
			HashMap<String,Object> insertClose=new HashMap<String,Object>();
			insertClose.put("CASE_ID", caseID);
			insertClose.put("FILE_NAME", fileName);
			insertClose.put("CREATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			insertClose.put("USER_ID", user.getUserId());
			insertClose.put("ACTIVE_INDEX", "Y");
			customerDao.insertData(connection, insertClose, INSERT_CASE_CLOSE);
			
			//file uploading code ends
			

			//Mail Trigger Start

			List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
			String toMail = reqCriteria.get("assignedTo").toString();

			String subject = "Recon : Case Management Closed ("+reconName+") : "+caseID;
			String content = "Dear Sir/Madam, <br> <br> <br> The captioned request has been closed by the " +user.getUserId()+". <br> <br>";

			Map<String,String> map = new HashMap<String,String>();
			map.put("subject", subject);
			map.put("content", content);
			map.put("comment", comment);
			map.put("toMail", toMail);
			map.put("ccMail", user.getEmailId());

			new CaseMailTrigger().setContentMailTrigger(selectedRecords,map);

			//Mail Trigger End

		}

		if (action != null && action.equals("caseDownload")) {
			
			result = new HashMap<String, Object>();
			HashMap<String,Object> insertParam=new HashMap<String,Object>();
			String caseID= (String) request.getCriteria().get("case_id");
			String downloadUrl = EtlMetaInstance.getInstance().getDbProperties().getProperty("caseDownloadUrl");
			Query queryConf = AscentWebMetaInstance.getInstance().getWebQueryConfs().getQueryConf(GET_CASE_FILE_NAME);
			insertParam.put("CASE_ID", caseID);
			insertParam.put("ACTIVE_INDEX", "Y");
			List<Map<String, Object>> fileName = new LoadRegulator().loadCompleteData(insertParam, queryConf);
			downloadUrl = downloadUrl+fileName.get(0).get("FILE_NAME").toString();
			result.put("downloadUrl", downloadUrl);
			dsResponse.setData(result);
			
		}

		return dsResponse;

	}


	//set response map
	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);
		return result;
	}
}

