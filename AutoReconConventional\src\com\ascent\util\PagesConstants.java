


package com.ascent.util;

import java.io.Serializable;

public interface PagesConstants extends Serializable {
	
	public static final String PERSIST_CLASS = "PERSIST_CLASS";
	
	
	//ALL RECON OPERATIONS CONSTANTS
	
	public static final String UPSTREAM_MODULE = "UPSTREAM";
	public static final String RECON_MODULE = "RECON";
	public static final String UNDO_SUPPRESS_UPSTREAM_MODULE = "UPSTREAM_UNDO_SUPPRESS";
	public static final String RE_PROCESS_OPERATION = "RE_PROCESS";
	public static final String SUPPRESS_OPERATION = "SUPPRESS";
	public static final String UNDO_SUPPRESS_OPERATION = "UNDO_SUPPRESS";
	public static final String FORCE_MATCH_OPERATION = "FORCE_MATCH";
	public static final String FORCE_UNMATCH_OPERATION = "FORCE_UNMATCH";
	public static final String CASE_MANAGEMENT_OPERATION = "CASE_MANAGEMENT";
	public static final String MARK_ORPHANS_OPERATION = "MARK_ORPHANS";
	public static final String CREATE_GL_ENTRY_OPERATION = "CREATE_GL_ENTRY";
	public static final String CREATE_GL_OPERATION_EXCEPTION = "CREATE_GL_EXCEPTION";
	public static final String SAME_SIDE_REVERSAL_OPERATION = "SAME_SIDE_REVERSAL";
	public static final String CREATE_VISA_ENTRY_OPERATION = "CREATE_GL_ENTRY_FOR_INTERCHANGE";
	
	public static final String UPSTREAM_DATA_SUPPRESS = "UPSTREAM-DATA-SUPPRESS";
	public static final String UPSTREAM_DATA_UNDO_SUPPRESS = "UPSTREAM-DATA-UNDO-SUPPRESS";
	public static final String ADD_COMMENTS = "ADD COMMENTS";
	public static final String RECON_FORCE_MATCH_PROCESS = "RECON-FORCEMATCH-PROCESS";
	public static final String RECON_FORCE_UNMATCH_PROCESS = "RECON-FORCEUNMATCH-PROCESS";
	public static final String RECON_MARK_ORPAHN_PROCESS = "RECON-MARKORPAHN-PROCESS";
 
	// All RECON OPERATIONS PLUGINS NAMES
	
	public static final String SUPPRESS_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.SuppressPlugIn";
	public static final String RE_PROCESS_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.ReProcessPlugIn";
	public static final String UNDO_SUPPRESS_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.UndoSuppressPlugIn";
	public static final String FORCE_MATCH_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.ForceMatchPlugIn";
	public static final String CASE_MANAGEMENT_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.CaseManagement";
	public static final String FORCE_UNMATCH_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.ForceUnMatchPlugIn";
	public static final String MARK_ORPHANS_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.MarkOrphanPlugIn";
	public static final String CREATE_GL_ENTRY_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.CreateGLEntryPlugIn";
	public static final String CREATE_GL_EXCEPTION_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.CreateGlExceptionPlugIn";
	public static final String SAME_SIDE_REVERSAL_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.SameSideReversalPlugIn";
	public static final String CREAT_VISA_ENTRY_PLUGIN_CLAS_NAME="com.ascent.ds.operations.CreateVisaEntryPlugin";
	
	
	// MASTER DATA CONSTAN NAMES	
	
	public static final String MASTER_DATA_ADD_OPERATION = "MASTER_DATA_ADD";
	public static final String MASTER_DATA_UPDATE_OPERATION = "MASTER_DATA_UPDATE";
	public static final String MASTER_DATA_DELETE_OPERATION = "MASTER_DATA_DELETE";
	
	public static final String MASTER_DATA_CARD_BIN = "MASERT-DATA-CARD-BIN";
	public static final String MASTER_DATA_CHANNEL_SERIAL_NO = "MASERT-DATA-CHANNEL-SERIAL-NO";
	public static final String MASTER_DATA_CHANNELS = "MASERT-DATA-CHANNELS";
	public static final String MASTER_DATA_GL_ACCOUNTS = "MASERT-DATA-GL-ACCOUNTS";
	public static final String MASTER_DATA_PROCESS_CODES = "MASERT-DATA-PROCESS-CODES";
	public static final String MASTER_DATA_SUSPENSE_ACCOUNTS = "MASERT-DATA-SUSPENSE-ACCOUNTS";
	
	// MASTER DATA OPERATIONS PLUGIN NAMES
	
	public static final Object MASTER_DATA_ADD_OPREATION_PLUGIN_CLASS_NAME ="com.ascent.ds.operations.MasterDataAddPlugin";
	public static final Object MASTER_DATA_UPDATE_OPREATION_PLUGIN_CLASS_NAME ="com.ascent.ds.operations.MasterDataUpdatePlugin";
	public static final Object MASTER_DATA_DELETE_OPREATION_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.MasterDataDeletePlugin";
	
	// For ManulEntries
	
	public static final String INTERNAL_MANUAL_OPERATION = "INTERNALMANUAL";// kaushal
	public static final String EXTERNAL_MANUAL_OPERATION = "EXTERNALMANUAL";
    public static final String INSERT_ONS_INTERNAL_MANUAL_ENTRIES= "INSERT_ONS_INTERNAL_MANUAL_ENTRIES";//kaushal
    public static final String MANUALENTRIES_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.ManualRecon";//KAUSHAL
    public static final String  EXTERNAL_MANUALENTRIES_PLUGIN_CLASS_NAME = "com.ascent.ds.operations.ManualExternalEntries";
	public static final String ManualEntries = "ManualEntries";
	public static final String ManualEntriesDS = "ManualEntriesDS";
    public static final String SHOW_MANUALENTRIES_DATA = "com.ascent.ds.operations.ShowData";
    public static final String ESHOW_MANUALENTRIES_DATA = "com.ascent.ds.operations.EShowData";

	
	
	// COMMON CONSTANTS FOR ALL
	public static final String SID = "ID";
	public static final String COMMA = ",";
	public static final String COMMENT = "COMMENT";
	public static final String STATUS = "STATUS";
	public static final String FAILED = "FAILED";
	public static final String OPERATION_FAILED = "OPERATION FAILED";
	public static final String TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY = "transactions submitted for approval sucessfully";
	public static final String COMMENTS = "COMMENTS";
	public static final String RECON_NAME = "RECON_NAME";
	public static final String BUSINES_AREA = "BUSINES_AREA";
	public static final String INTEGRATION_NAME = "INTEGRATION_NAME";
	public static final String SELECTED_RECORDS = "SELECTED_RECORDS";
	public static final String USER_ID = "USER_ID";
	public static final String ACTION = "ACTION";
	public static final String DS_NAME = "DS_NAME";
	public static final String TABLE_NAME="tableName";
	public static final String MODULE = "MODULE";
	public static final String SUBMIT = "SUBMIT";
	public static final String SAVE = "SAVE";
	public static final String SUCCESS = "SUCCESS";
	public static final String APPROVED = "APPROVED";
	public static final String REJECTED = "REJECTED";
	public static final String PENDING_APPROVAL = "PENDING";
	public static final String GL_CODE = "GL_CODE";
	public static final String ACC_CATEGORY="ACC_CATEGORY";
	public static final String DEBIT_CREDIT_DETAILS="DEBIT_CREDIT_DETAILS";
	
	// USER ADMINISTRATION CONSTANTS 
	public static String OWNER_NON_INDIVIDUAL = "ownernonindividual";
	public static String OWNER_NON_INDIVIDUAL_NAME = "Owner Non Individual";
	public static String OWNER_NON_INDIVIDUAL_DESC = "Owner Non-Individuals";
	public static String OWNER_INDIVIDUAL = "ownerindividual";
	public static String OWNER_INDIVIDUAL_NAME = "Owner Individual";
	public static String OWNER_INDIVIDUAL_DESC = "Owner Individuals";
	public static String VENDOR = "vendor";
	public static String VENDOR_NAME = "Vendor";
	public static String VENDOR_DESC = "Vendors";
	public static String DEVELOPER = "developer";
	public static String DEVELOPER_NAME = "Developer";
	public static String DEVELOPER_DESC = "Developers";
	public static String TRUSTEE_BANK = "trusteebank";
	public static String TRUSTEE_BANK_NAME = "Trustee Bank";
	public static String TRUSTEE_BANK_DESC = "Trustee Banks";
	public static String BROKER = "broker";
	public static String BROKER_NAME = "Broker";
	public static String BROKER_DESC = "Brokers";
	public static String QUALITY_SURVEYOR = "qualitysurveyor";
	public static String QUALITY_SURVEYOR_NAME = "Quality Surveyor";
	public static String QUALITY_SURVEYOR_DESC = "Quality Surveyors";
	public static String FINANCIER = "financier";
	public static String FINANCIER_NAME = "Financier";
	public static String FINANCIER_DESC = "Financiers";
	public static String CAPITAL_INVESTOR = "capitalInvestor";
	public static String CAPITAL_INVESTOR_NAME = "Capital Investor";
	public static String CAPITAL_INVESTOR_DESC = "Capital Investors";
	public static String AUDITOR = "auditor";
	public static String AUDITOR_NAME = "Auditor";
	public static String AUDITOR_DESC = "Auditors";
	public static String JOINT_OWNER = "jointowner";
	public static String JOINT_OWNER_NAME = "Joint Owner";
	public static String JOINT_OWNER_DESC = "Joint Owners";
	public static String PROJECT = "Project";
	public static String OTHER = "other";

	public static final String ALPHANUMERIC_FIELDS = "ALPHANUMERIC_FIELDS";
	public static final String NUMERIC_FIELDS = "NUMERIC_FIELDS";
	public static final String ALPHABETIC_FIELDS = "ALPHABETIC_FIELDS";
	public static final String MANDATORY_FIELDS = "MANDATORY_FIELDS";
	public static final String EMAILID_FIELDS = "EMAILID_FIELDS";



	public static final String OPERATION_NEW = "NEW";	
	public static final String OPERATION_DELETE = "DELETE";
	public static final String OPERATION_EDIT = "EDIT";	
	public static final String MODULE_USER_ADMIN = "USER_ADMIN";
	public static final String ESCALATION_MATRIX = "ESCALATION_MATRIX";
	
	public static final String OPERATION_FUNDS_ASSIGN = "FUNDS_ASSIGN";	
	public static final String OPERATION_UNIT_CANCEL = "UNIT_CANCEL";	
	public static final String OPERATION_ROLLBACK_TRANSACTION = "ROLLBACK_TRANSACTION";	
	public static final String MODULE_ON_BOARD = "ON-BOARD";
	
	
	public static final String MODULE_RECEIPTS_UNIDENTIFIED = "RECEIPTS-UNIDENTIFIED";
	
	public static final String MODULE_RECEIPTS_ALLOCATION = "RECEIPTS-ALLOCATION";
	public static final String ALLOCATION_SALES = "SALES";
	public static final String ALLOCATION_PROJECT_FINANCE = "PROJECT-FINANCE";
	public static final String ALLOCATION_CAPITAL_INVESTMENT = "CAPITAL-INVESTMENT";
	public static final String ALLOCATION_UNRECONCILED = "UN-RECONCILED";
	public static final String ALLOCATION_REFUND = "REFUND";
	public static final String ALLOCATION_OPERATION_UNDO = "OPERATION-UNDO";
	public static final String ALLOCATION_OPERATION_SUPRESS = "OPERATION-SUPRESS";
	public static final String ALLOCATION_SPLITTRANSACTION = "SPLITTRANSACTION";
	public static final String DOWNSTREAM = "DOWNSTREAM";
	public static final String MODULE_RECEIPTS_SALES = "RECEIPTS-SALES";	
	public static final String MODULE_RECEIPTS_ROLLBACK = "RECEIPTS_ROLLBACK";
	
	
	public static final String departments_table = "departments";
	public static final String roles_table = "roles";
	public static final String users_table = "users";
	public static final String password_policy = "password_policy";
	public static final String DELETE = "DELETE";
	public static final String RE_SUBMIT = "RE-SUBMIT";	
	public static final String UPDATE = "UPDATE";
	public static final String BEAN_NAME = "BEAN_NAME";
	public static final String IS_SESSION_BEAN = "IS_SESSION_BEAN";
	public static final String EMPTY_STRING = ""; 
	public static final String DRAFT = "DRAFT";
	public static final String MODULE_PROJECT_VOCHER_PAYMENT = "PROJ-VOCHER-PAYMENT";
	public static final String PROJECT_ADMIN_TYPE_CLASS_NAME = "com.ascent.promart.projectadmin.ProjectAdminView";	
	public static final String JOINT_OWNER_LIST = "JOINT_OWNER_LIST";
	
	// USER ADMINISTRATION OPERATIONS PLUGIN NAMES
	
	//public static final String ENTITY_TYPE_CLASS_NAME = "com.ascent.promart.beans.entity.EntityType";
	public static final String ROLES_CLASS_NAME = "com.ascent.service.dto.Role";
	public static final String PRIVILEGE_CLASS_NAME = "com.ascent.service.dto.Privilege";
	public static final String DEPARTMENT_CLASS_NAME = "com.ascent.service.dto.Department";	
	public static final String USERS_CLASS_NAME = "com.ascent.service.dto.User";
	public static final String ESCALATIONMATRIX_CLASS_NAME = "com.ascent.ds.operations.EscalationMatrix";

	
	// CHECKING ALL RECON NAMES FOR RECON PROCESS
	
	//ONUS
	
	 public static final String ONUS_ATM_DEPOSITS="DEPOSITS";
	 public static final String ONUS_ATM_DEBIT_CARD="ATM - DEBIT CARD";
	 public static final String ONUS_ATM_CREDIT_CARD="ATM - CREDIT CARD";
	 public static final String ONUS_ATM_PAYROLL_CARD=" ATM - PAYROLL CARD";
	 public static final String ONUS_POS_DEBIT_CARD=" POS - DEBIT CARD";
	 public static final String ONUS_POS_CREDIT_CARD="POS - CREDIT CARD";
	 public static final String ONUS_POS_PAYROLL_CARD="POS - PAYROLL CARD";
	 
	 //ISSUER
	 
	 public static final String ISSUER_ATM_NAPS_DEBIT_CARD="ATM - NAPS - DEBIT CARD";
	 public static final String ISSUER_ATM_VISA_DEBIT_CARD="ATM - VISA - DEBIT CARD";
	 public static final String ISSUER_ATM_VISA_CREDIT_CARD="ATM - VISA - CREDIT CARD";
	 public static final String ISSUER_ATM_MASTER_CREDIT_CARD="ATM - MASTER - CREDIT CARD";
	 public static final String ISSUER_POS_NAPS_DEBIT_CARD="POS - NAPS - DEBIT CARD";
	 public static final String ISSUER_POS_VISA_DEBIT_CARD="POS - VISA - DEBIT CARD";
	 public static final String ISSUER_POS_VISA_CREDIT_CARD="POS - VISA - CREDIT CARD";
	 public static final String ISSUER_POS_MASTER_CREDIT_CARD="POS - MASTER - CREDIT CARD";
	 
	 //ACQUIRER
	 
	 public static final String ACQUIRER_ATM_NAPS_CARDS ="ATM - NAPS - CARDS";
	 public static final String ACQUIRER_ATM_NAPS2_CARDS="ATM - NAPS 2 - CARDS";
	 public static final String ACQUIRER_ATM_VISA_CARDS="ATM - VISA - CARDS";
	 public static final String ACQUIRER_ATM_UP_CARDS="ATM - UP - CARDS";
	 public static final String ACQUIRER_POS_NAPS_CARD="POS - NAPS - CARDS";
	 public static final String  ACQUIRER_POS_VISA_CARDS=" POS - VISA - CARDS";
	 public static final String ACQUIRER_POS_MASTER_CARDS="POS - MASTER - CARDS";
	 public static final String ACQUIRER_POS_UP_CARDS="POS - UP - CARDS";
	 public static final String ACQUIRER_POS_QPAY_CARDS= "POS - QPAY - CARDS";
	 public static final String ACQUIRER_ATM_MASTER_CARDS="ATM-MASTER-CARDS";
	 public static final String PAYROLL="PAYROLL";


}
