package com.ascent.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;

import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class OperationsUtil {
	public static Query getInsertQueryConf(String tableName,Connection connection){
		Query insertQueryConf=null;
		try{
			String queryparams="";
			String qryString="select * from "+tableName+" where 1=0"; //and version = (select max(version) from "+" tableName"+")";
			String insertQuery="insert into "+tableName+"(";
			String valueQuery=" values(";
			PreparedStatement pstmt=connection.prepareStatement(qryString);
			ResultSet rs=pstmt.executeQuery();
		ResultSetMetaData rsm=rs.getMetaData();
		int count=rsm.getColumnCount();
		List<String> columnList=new ArrayList<String>();
		for(int i=1;i<=count;i++){
			
			
			
			if(i!=count){
				String columntype=rsm.getColumnTypeName(i).toUpperCase();
				if(columntype.equalsIgnoreCase("INT")){
					columntype="INTEGER";
				}else if(columntype.equalsIgnoreCase("DATETIME")){
					columntype="TIMESTAMP";
					
				}
			queryparams=queryparams+rsm.getColumnName(i).toUpperCase()+"@"+columntype+",";
			insertQuery=insertQuery+rsm.getColumnName(i).toUpperCase()+",";
			valueQuery=valueQuery+"?,";
			}else{
				String columntype=rsm.getColumnTypeName(i).toUpperCase();
				if(columntype.equalsIgnoreCase("INT")){
					columntype="INTEGER";
				}
				if(columntype.equalsIgnoreCase("DATETIME")){
					columntype="TIMESTAMP";
					}
				queryparams=queryparams+rsm.getColumnName(i).toUpperCase()+"@"+columntype;
				insertQuery=insertQuery+rsm.getColumnName(i).toUpperCase()+")";
				valueQuery=valueQuery+"?)";
			}
			columnList.add(rsm.getColumnName(i).toUpperCase());
			
		}
		
		insertQueryConf=new Query();
		insertQueryConf.setId(143);
		insertQueryConf.setName("AuditInsertQuery");
		insertQueryConf.setQueryParam(queryparams);
		insertQueryConf.setQueryParamList(columnList);
		insertQueryConf.setQueryString(insertQuery+valueQuery);
		System.out.println(insertQuery+valueQuery+"lak");
		
			
		}catch(Exception e){
			e.printStackTrace();
		}
				
		return insertQueryConf;
		
	}
	
	}

