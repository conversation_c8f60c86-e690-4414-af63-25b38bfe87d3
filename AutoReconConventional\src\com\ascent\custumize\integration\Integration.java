//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.06.20 at 06:07:52 AM IST 
//

package com.ascent.custumize.integration;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;

/**
 * <p>
 * Java class for Integration complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Integration">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fileType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StagingCreateQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StagingExCreateQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StagingInsertQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StagingUpdateQueryName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="StagingExInsertQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StagingExUpdateQueryName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}integer" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Integration", propOrder = { "name", "fileType", "fileNamePattern", "columnNamesString", "sequenceName",
		"stagingCreateQueryName", "stagingExCreateQueryName", "stagingInsertQueryName", "stagingUpdateQueryName",
		"stagingExInsertQueryName", "stagingExUpdateQueryName", "enrichmentPlugin", "preEnrichValidationPlugin",
		"postEnrichValidationPlugin", "persistancePlugin" })
public class Integration implements Serializable{

	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String fileType;
	@XmlElement(name = "fileNamePattern", required = true)
	protected String fileNamePattern;
	@XmlElement(name = "columnNamesString", required = true)
	protected String columnNamesString;
	@XmlElement(name = "sequenceName", required = true)
	protected String sequenceName;
	@XmlElement(name = "StagingCreateQueryName", required = true)
	protected String stagingCreateQueryName;
	@XmlElement(name = "StagingExCreateQueryName", required = true)
	protected String stagingExCreateQueryName;
	@XmlElement(name = "StagingInsertQueryName", required = true)
	protected String stagingInsertQueryName;
	@XmlElement(name = "StagingUpdateQueryName")
	protected String stagingUpdateQueryName;
	@XmlElement(name = "StagingExInsertQueryName", required = true)
	protected String stagingExInsertQueryName;
	@XmlElement(name = "StagingExUpdateQueryName")
	protected String stagingExUpdateQueryName;
	@XmlElement(name = "enrichmentPlugin")
	protected String enrichmentPlugin;
	@XmlElement(name = "preEnrichValidationPlugin")
	protected String preEnrichValidationPlugin;
	@XmlElement(name = "postEnrichValidationPlugin")
	protected String postEnrichValidationPlugin;
	@XmlElement(name = "persistancePlugin")
	String persistancePlugin;
	@XmlAttribute(name = "id")
	protected BigInteger id;
	@XmlTransient
	List<String> columnNameList = new ArrayList<String>();
	@XmlTransient
	protected Query stagingCreateQuery;
	@XmlTransient
	protected Query stagingExCreateQuery;
	@XmlTransient
	protected Query stagingInsertQuery;
	@XmlTransient
	protected Query stagingUpdateQuery;
	@XmlTransient
	protected Query stagingExInsertQuery;
	@XmlTransient
	protected Query stagingExUpdateQuery;

	public Integration() {

	}

	public void bootConf(Queries queries) throws Exception {
		stagingCreateQuery = queries.getQueryConf(stagingCreateQueryName);
		stagingExCreateQuery = queries.getQueryConf(stagingExCreateQueryName);
		stagingInsertQuery = queries.getQueryConf(stagingInsertQueryName);
		stagingUpdateQuery = queries.getQueryConf(stagingUpdateQueryName);
		stagingExInsertQuery = queries.getQueryConf(stagingExInsertQueryName);
		stagingExUpdateQuery = queries.getQueryConf(stagingExUpdateQueryName);
		if (columnNamesString != null) {
			columnNamesString = columnNamesString.replaceAll("\\n", "");
			columnNamesString = columnNamesString.replaceAll("\\s", "");
			String columnNameArr[] = columnNamesString.split(",");
			columnNameList = Arrays.asList(columnNameArr);
		}
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the fileType property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getFileType() {
		return fileType;
	}

	/**
	 * Sets the value of the fileType property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setFileType(String value) {
		this.fileType = value;
	}

	/**
	 * Gets the value of the stagingCreateQueryName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStagingCreateQueryName() {
		return stagingCreateQueryName;
	}

	/**
	 * Sets the value of the stagingCreateQueryName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStagingCreateQueryName(String value) {
		this.stagingCreateQueryName = value;
	}

	/**
	 * Gets the value of the stagingExCreateQueryName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStagingExCreateQueryName() {
		return stagingExCreateQueryName;
	}

	/**
	 * Sets the value of the stagingExCreateQueryName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStagingExCreateQueryName(String value) {
		this.stagingExCreateQueryName = value;
	}

	/**
	 * Gets the value of the stagingInsertQueryName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStagingInsertQueryName() {
		return stagingInsertQueryName;
	}

	/**
	 * Sets the value of the stagingInsertQueryName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStagingInsertQueryName(String value) {
		this.stagingInsertQueryName = value;
	}

	/**
	 * Gets the value of the stagingUpdateQueryName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStagingUpdateQueryName() {
		return stagingUpdateQueryName;
	}

	/**
	 * Sets the value of the stagingUpdateQueryName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStagingUpdateQueryName(String value) {
		this.stagingUpdateQueryName = value;
	}

	/**
	 * Gets the value of the stagingExInsertQueryName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStagingExInsertQueryName() {
		return stagingExInsertQueryName;
	}

	/**
	 * Sets the value of the stagingExInsertQueryName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStagingExInsertQueryName(String value) {
		this.stagingExInsertQueryName = value;
	}

	/**
	 * Gets the value of the stagingExUpdateQueryName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStagingExUpdateQueryName() {
		return stagingExUpdateQueryName;
	}

	/**
	 * Sets the value of the stagingExUpdateQueryName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStagingExUpdateQueryName(String value) {
		this.stagingExUpdateQueryName = value;
	}

	/**
	 * Gets the value of the id property.
	 * 
	 * @return possible object is {@link BigInteger }
	 * 
	 */
	public BigInteger getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 * @param value
	 *            allowed object is {@link BigInteger }
	 * 
	 */
	public void setId(BigInteger value) {
		this.id = value;
	}

	public Query getStagingCreateQuery() {
		return stagingCreateQuery;
	}

	public void setStagingCreateQuery(Query stagingCreateQuery) {
		this.stagingCreateQuery = stagingCreateQuery;
	}

	public Query getStagingExCreateQuery() {
		return stagingExCreateQuery;
	}

	public void setStagingExCreateQuery(Query stagingExCreateQuery) {
		this.stagingExCreateQuery = stagingExCreateQuery;
	}

	public Query getStagingInsertQuery() {
		return stagingInsertQuery;
	}

	public void setStagingInsertQuery(Query stagingInsertQuery) {
		this.stagingInsertQuery = stagingInsertQuery;
	}

	public Query getStagingUpdateQuery() {
		return stagingUpdateQuery;
	}

	public void setStagingUpdateQuery(Query stagingUpdateQuery) {
		this.stagingUpdateQuery = stagingUpdateQuery;
	}

	public Query getStagingExInsertQuery() {
		return stagingExInsertQuery;
	}

	public void setStagingExInsertQuery(Query stagingExInsertQuery) {
		this.stagingExInsertQuery = stagingExInsertQuery;
	}

	public Query getStagingExUpdateQuery() {
		return stagingExUpdateQuery;
	}

	public void setStagingExUpdateQuery(Query stagingExUpdateQuery) {
		this.stagingExUpdateQuery = stagingExUpdateQuery;
	}

	public String getColumnNamesString() {
		return columnNamesString;
	}

	public void setColumnNamesString(String columnNamesString) {
		this.columnNamesString = columnNamesString;
	}

	public List<String> getColumnNameList() {
		return columnNameList;
	}

	public void setColumnNameList(List<String> columnNameList) {
		this.columnNameList = columnNameList;
	}

	public String getSequenceName() {
		return sequenceName;
	}

	public void setSequenceName(String sequenceName) {
		this.sequenceName = sequenceName;
	}

	public String getEnrichmentPlugin() {
		return enrichmentPlugin;
	}

	public void setEnrichmentPlugin(String enrichmentPlugin) {
		this.enrichmentPlugin = enrichmentPlugin;
	}

	public String getPreEnrichValidationPlugin() {
		return preEnrichValidationPlugin;
	}

	public void setPreEnrichValidationPlugin(String preEnrichValidationPlugin) {
		this.preEnrichValidationPlugin = preEnrichValidationPlugin;
	}

	public String getPostEnrichValidationPlugin() {
		return postEnrichValidationPlugin;
	}

	public void setPostEnrichValidationPlugin(String postEnrichValidationPlugin) {
		this.postEnrichValidationPlugin = postEnrichValidationPlugin;
	}

	public String getPersistancePlugin() {
		return persistancePlugin;
	}

	public void setPersistancePlugin(String persistancePlugin) {
		this.persistancePlugin = persistancePlugin;
	}

	public String getFileNamePattern() {
		return fileNamePattern;
	}

	public void setFileNamePattern(String fileNamePattern) {
		this.fileNamePattern = fileNamePattern;
	}

}
