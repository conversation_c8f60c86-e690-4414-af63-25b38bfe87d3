package com.onus.atm.etl;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EJEtl {

	String denomination="C1\\s*\\[\\d*\\],\\s*C2\\s*\\[\\d*\\],\\s*C3\\s*\\[\\d*],\\s*C4\\s*\\[\\d*]";
	Pattern pattern= null;
	Map<String,String> denomMap=null;
	private String START_BLOCK="TRANSACTION STARTED";
	private String END_BLOCK="TRANSACTION END";
	String tokens[]={"DATE:\\s*\\d{2}\\/s*\\d{2}\\/s*\\d{2}","TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}","ATM ID:\\s*\\d*","(TXN:\\s*CASH DEPOSIT)|(TXN:\\s*WITHDRAWAL)",
			"A\\/C:\\s*\\d*\\**\\d*","SEQ:\\s*\\d*","STAN:\\s*\\d*","RRN:\\s*\\d*","\\s*AMOUNT:\\s*\\OMR\\s*\\d*\\.\\d*"};
	public static void main(String[] args) {
		File file =new File("D:\\BANK NIZwA\\SampleData for Reconciliaton\\Cards\\ATM ONUS\\Sample EG.txt");
		EJEtl ej=new EJEtl();
		ej.extractFile(file);
	}
	{
		 denomMap=new HashMap<String, String>();
		
			
		denomMap.put("C1", "RM5");
		denomMap.put("C2", "RM10");
		denomMap.put("C3", "RM20");
		denomMap.put("C4", "RM50");
	}
	public void extractFile(File file){
		try {
			pattern= Pattern.compile(denomination);
			BufferedReader br =new BufferedReader(new FileReader(file));
			String line="";
			List<String>list=new ArrayList<String>();
			while((line=br.readLine())!=null){
				
				if(!line.isEmpty() && line.contains(START_BLOCK)){
					list.add(line);
					do{
						line=br.readLine();
						if(!line.isEmpty())
						list.add(line.trim());
					}while( !line.contains(END_BLOCK));
					extractBlock(list);
					list.clear();
				}
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
	
	private void extractBlock(List<String>list){
		Map<String,Object> map=new HashMap<String,Object>();
		for(String data:list){
			if(data.startsWith("DATE")){
				map.put("TRA_DATE", getData(getData(data, "DATE:\\s*\\d{2}\\/s*\\d{2}\\/s*\\d{2}"), "\\d{2}\\/s*\\d{2}\\/s*\\d{2}"));
				map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
			}else if(data.startsWith("ATM ID:")){
				map.put("ATM_ID", getData(getData(data, "ATM ID:\\s*\\d*"), "\\d*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(data.startsWith("STAN:")){
				map.put("STAN", getData(getData(data, "STAN:\\s*\\d*"), "\\d*"));
				map.put("RRN", getData(getData(data, "RRN:\\s*\\d*"), "\\d*"));
		
			}else if(data.startsWith("PAN:")){
				map.put("PAN", getData(getData(data, "PAN:\\s*\\d*\\**\\d*"), "\\d*\\**\\d*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(data.startsWith("A/C:")){
				map.put("ACCOUNT_NUMBER", getData(getData(data, "A\\/C:\\s*\\d*\\**\\d*"), "\\d*\\**\\d*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(data.startsWith("TXN:")){
				map.put("TXN_TYPE", getData(getData(data, "(TXN:\\s*CASH DEPOSIT)|(TXN:\\s*WITHDRAWAL)"), "(WITHDRAWAL)|(CASH DEPOSIT)"));
				map.put("SEQ", getData(getData(data, "SEQ:\\s*\\d*"), "\\d*"));
		
			}else if(data.startsWith("AMOUNT:")){
				map.put("AMOUNT", getData(getData(data, "AMOUNT:\\s*OMR\\s*\\d*.\\d*"), "\\s*\\d*\\.\\d*").trim());
				map.put("CURRENCY", getData(getData(data, "AMOUNT:\\s*OMR\\s*"), "\\:\\s*\\w{3}\\s").replace(":", "").trim());
		
			}else if(data.startsWith("STATUS:")){
				map.put("STATUS", getData(data, "\\:\\s*\\w*s*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(pattern.matcher(data).matches()){
				for(String denom:data.split(",")){
					
					String rm= denomMap.get(getData(denom, "C\\d*"));
					map.put(rm, getData(denom, "\\[\\d*]").replaceAll("\\[|\\]", ""));
				}
				
			}
		}
		System.out.println(map);
	}
	 public static String getData(String data, String token) {
	        String retStr;
	        StringBuilder result = new StringBuilder();
	        if (data != null) {
	            Pattern pattern = Pattern.compile(token);
	            Matcher matcher = pattern.matcher(data);
	            String sep = "";
	            while (matcher.find()) {
	                result.append(String.valueOf(String.valueOf("")) + matcher.group().toString());
	               
	            }
	        }
	        return (retStr = result.toString()).isEmpty() ? null : retStr;
	    }
}
