package com.ascent.scheduler;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
/**
 * <AUTHOR>
 *
 */
public class ExportExcel1 {
	//private static Logger logger = LogManager.getLogger(ExportExel.class.getName());

	
	public static void main(String[] args) throws IOException {
	//	exportExel();
	}

	public static String exportExcel(List<Map<String, Object>> unmatchList,String recon) throws IOException {
	/*public static String exportExcel1(List<Map<String, Object>> unmatchList,String department) throws IOException {*/
		Date date = new Date();
		String fileRec=recon.replace("/", "_");
		/*String fileRec=department.replace("/", "_");*/
		//String fileName = String.format("DhofarConventional_"+fileRec+".xlsx", recon, date);
		/*String fileName = String.format("kaushal_"+fileRec+".xlsx", department, date);
	*/
		
  		    List<Map<String,Object>> unmatchList1 = new ArrayList<Map<String,Object>>();
			for(Map map11 : unmatchList){
			Map<String, Object> dataMapList = new LinkedHashMap<String, Object>();
			
			
		    dataMapList.put("recon",map11.get("RECON"));
		    dataMapList.put("business_area",map11.get("business_area"));
		    dataMapList.put("case_ID",map11.get("CASE_ID"));
		    dataMapList.put("SID",map11.get("SID"));
		    dataMapList.put("created_by",map11.get("created_by"));
		    dataMapList.put("allowed_approvers",map11.get("allowed_approvers"));
		    dataMapList.put("transaction_amount",map11.get("transaction_amount"));
		    dataMapList.put("activity_id",map11.get("activity_id"));
			dataMapList.put("agelimit",map11.get("AGELIMIT"));
			dataMapList.put("age",map11.get("AGE"));
			 
			 unmatchList1.add(dataMapList);
		   }
			
	
	    
	    Set<String> columnNamesSet = unmatchList1.get(0).keySet();

		String[] columnNames = columnNamesSet.stream().toArray(String[] ::new);
		
		/*System.out.println("Before: "+columnNamesSet);
		
		ArrayList<String> myList = new ArrayList<String>(columnNamesSet);
		myList.remove("PERSON");
		myList.remove("business_area");
		
		
		columnNames = myList.toArray(new String[0]);
		
		System.out.println("After: "+Arrays.toString(columnNames));
		System.out.println(myList);
		*/
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Contacts");

		Font headerFont = workbook.createFont();
		CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);
		
		/*Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.RED.getIndex());

*/
		// Create a Row
		Row headerRow = sheet.createRow(0);
		
		
		for (int i = 0; i < columnNames.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columnNames[i]); 
			cell.setCellStyle(headerCellStyle);
		}
		



		

		// Create Other rows and cells with contacts data
		int rowNum = 1;

		// for (Contact contact : contacts) {
		for (int i = 0; i < unmatchList1.size(); i++) {
			Map<String, Object> map = unmatchList1.get(i);
			
			int count = 0;
			Row row = sheet.createRow(rowNum++);

			for (Map.Entry<String, Object> entry : map.entrySet()) {
				
			row.createCell(count++).setCellValue(entry.getValue() == null ? "" : entry.getValue().toString());
			}
		}
	
		// Resize all columns to fit the content size
		for (int i = 0; i < columnNames.length; i++) {
			sheet.autoSizeColumn(i);
		}

		File pathFile = new File(System.getProperty("java.io.tmpdir")+"\\EscalationMatrix\\");
		if (!pathFile.exists())
			pathFile.mkdirs();
		String fileName = String.format("DhofarConventional_"+fileRec+".xlsx", recon, date);
		File file = new File(pathFile + File.separator + fileName);
		
		// Write the output to a file
		FileOutputStream fileOut = new FileOutputStream(file);
		workbook.write(fileOut);
		fileOut.close();
	
		//logger.debug("export done...");

		return file.toString();
	
		}
}