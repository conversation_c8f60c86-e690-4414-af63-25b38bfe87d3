package com.ascent.persistance;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class UpdateRegulator implements DbRegulatorConstants,Serializable {
	
	private static final long serialVersionUID = 1882053126044571170L;
	private static Logger logger = LogManager.getLogger(UpdateRegulator.class
			.getName());
	
	public UpdateRegulator() {


	}

	// Update Functionality--Start----------------------------------------------------------
	
	public int update(Map<String, Object> args) {
		Connection connection = null;
		PreparedStatement updatePstmt = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		int rowsAffected = 0;
		try {
			
			String updateQry = (String) args.get(UPDATE_QRY);
			String updateQryParams = (String) args.get(UPDATE_QRY_PARAMS);
			
			Map<String, Object> paramValuemap = (Map<String, Object>) args
					.get(PARAM_VALUE_MAP);
			
			connection=DbUtil.getConnection();
			updatePstmt = connection.prepareStatement(updateQry);
			
			if (updateQryParams != null && !updateQryParams.isEmpty()) {
				
				String[] paramArrayWithType = updateQryParams.split(",");
				if (paramArrayWithType != null
						&& paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
				
			}

			int index = 1;
			for (String param : paramList) {
				
				updatePstmt.setObject(index, paramValuemap.get(param),
						paramTypeMap.get(param));
				index++;
				
			}
			
			rowsAffected = updatePstmt.executeUpdate();
			
		} catch (Exception e) {

			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(updatePstmt);
			RegulatorUtil.closeConnection(connection);
		}
		return rowsAffected;
	}

	public int update(Connection connection, Map<String, Object> args) {

		PreparedStatement updatePstmt = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		
		int rowsAffected = 0;
		try {
			String updateQry = (String) args.get(UPDATE_QRY);
			String updateQryParams = (String) args.get(UPDATE_QRY_PARAMS);
			Map<String, Object> paramValuemap = (Map<String, Object>) args
					.get(PARAM_VALUE_MAP);
			updatePstmt = connection.prepareStatement(updateQry);
			
			if (updateQryParams != null && !updateQryParams.isEmpty()) {
				String[] paramArrayWithType = updateQryParams.split(",");
				if (paramArrayWithType != null
						&& paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
			}

			int index = 1;
			for (String param : paramList) {
				updatePstmt.setObject(index, paramValuemap.get(param),
						paramTypeMap.get(param));
				index++;
			}
		
			rowsAffected = updatePstmt.executeUpdate();
		
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(updatePstmt);
		}
		return rowsAffected;
	}

	public int update(PreparedStatement updatePstmt, Map<String, Object> args,String updateQryParamsType ) {
	
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		int rowsAffected = 0;
		try {

			
			Map<String, Object> paramValuemap = (Map<String, Object>) args
					.get(PARAM_VALUE_MAP);

			if (updateQryParamsType != null && !updateQryParamsType.isEmpty()) {
				String[] paramArrayWithType = updateQryParamsType.split(",");
				if (paramArrayWithType != null
						&& paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
			}

			int index = 1;
			for (String param : paramList) {
				updatePstmt.setObject(index, paramValuemap.get(param),
						paramTypeMap.get(param));
				index++;
			}
			rowsAffected = updatePstmt.executeUpdate();
			
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(updatePstmt);
		}

		return rowsAffected;
	}
	// Update Functionality--End----------------------------------------------------------
	
	public static void main(String[] args) {
		UpdateRegulator dbCurdRUC = new UpdateRegulator();
		Connection connection = null;

		Map<String, Object> params = new HashMap<String, Object>();

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		try {

			Query query = queries.getQueryConf("name");
			connection = DbUtil.getConnection();

	/*		DbCursor ascentResultSet = dbCurdRUC.load(connection, query,
					params);
			boolean reachedEnd = false;
			
			List<Map<String, Object>> records = ascentResultSet.getNextBatch();
	*/		
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
}
