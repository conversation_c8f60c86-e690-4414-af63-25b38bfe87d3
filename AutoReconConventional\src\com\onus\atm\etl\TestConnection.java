package com.onus.atm.etl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.ascent.banknizwa.source.ej.Test;

public class TestConnection {

	public static void main(String[] args) {
		String adte="26/03/17";
		System.out.println(formateDateDDMMYY(adte));
	}
	public static java.sql.Date  formateDateDDMMYY(String date){
		//26/03/2017 0:00
		SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yy");
	
	 Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(date.trim());
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
}
