package com.ascent.ds.operations;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet("/GetHeaders")
public class GetHeaders extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException
	{
		try
		{
			
			Connection connection=HistoryConnection.getConnection();
		   
		    String sql = " SELECT * FROM "+request.getParameter("reconName");
	     	
	        CallableStatement stmt = connection.prepareCall(sql);
	        response.setContentType("application/Json");
	        ResultSet rs=stmt.executeQuery();
	        
	        
	        List<String> columnList=new ArrayList<String>();
	        ResultSetMetaData rsmd=rs.getMetaData();
			String columns="";
			for (int i=1;i<=rsmd.getColumnCount();i++)
			{
				
				if( (i<rsmd.getColumnCount() ) && i>2 )
					columns=columns+rsmd.getColumnLabel(i)+",";
				else if(i>2)
					columns=columns+rsmd.getColumnLabel(i);
				columnList.add(rsmd.getColumnLabel(i));
				
			}
			response.getWriter().print(columnList.toString());
		}
		catch(Exception e)
		{
			e.printStackTrace();
			
		}
	
		
	}

}
