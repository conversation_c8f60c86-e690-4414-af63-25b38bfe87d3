package com.ascent.stagingdata;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class StagingDataTreeRepresentationDs extends BasicDataSource {
	
	private static final long serialVersionUID = 1L;
	public DSResponse executeFetch(final DSRequest request) throws Exception {
		  boolean isFolder = false;
		  Map record=request.getValues();
		  String filename=null;
		  
		 if(record.get("name")!=null )
			 filename=(String) record.get("name");
		System.out.println("enters into the executeFetch method");
		DSResponse response = new DSResponse();
		Map<String, Object> map=null;		
		boolean b=false;
		List<Map<String,Object>> slist=new ArrayList<Map<String,Object>>();
		List list=new ArrayList(); 
		try{	
			File root=new File("E:\\AscentDohaStaging");
			System.out.println("Root " + root);
			  map=new HashMap();
			  if(record.get("name")!=null)
		            if(filename.equalsIgnoreCase(root.getAbsolutePath())){
		            	newFileRead(root,list,b,filename,slist,record);
				    }
			 
			  map.put("parentId", 0);
			 
			  map.put("id", root.getAbsolutePath());
			  map.put("name", root.getName());
			  map.put("isFolder", true);
			 
			  list.add(map);	 
			  File[] listOfFiles = root.listFiles();
			  for (int i = 0; i < listOfFiles.length; i++) {
		            Map m=new HashMap();	
		            
		        	if(listOfFiles[i].isDirectory()){
		        		File fsub=listOfFiles[i];
		        		if(fsub.listFiles()!=null){		        			
		        		  newFileRead(fsub,list,b,filename,slist,record);
		        		}
		        		b=true;	
		        		}
		           
		            m.put("id", listOfFiles[i].getAbsolutePath());
		            m.put("parentId", root.getAbsolutePath());
		            m.put("isFolder", b);	
		            m.put("name", listOfFiles[i].getName());
		            m.put("file", listOfFiles[i]);
		            b=false;
		            System.out.println("SubMap"+ m);
		            list.add(m);
		            if(record.get("name")!=null)
			            if(filename.equalsIgnoreCase("root") ){
			            	if(!listOfFiles[i].isDirectory()){
					    	slist.add(m);
			            	}
					    }
                  }      
			  
			  System.out.println(list);
		         }
                catch(Exception e)
		        {
				e.printStackTrace();
				}		
		if(record.get("name")!=null){
			response.setData(slist);
		}else{
		response.setData(list);
		}
		return response;
}
	private void newFileRead(File fsub, List list, boolean b, String filename, List<Map<String, Object>> slist, Map record) {
		System.out.println("Hi SubFolder");
		File[] listOfFiles = fsub.listFiles();
		System.out.println(listOfFiles.length+"<---------------->");
		for(int j=0;j<listOfFiles.length;j++){
			Map m=new HashMap();	
			System.out.println(listOfFiles[j].getName()+"**************");
			if(listOfFiles[j].isDirectory()){
        		File fsub1=listOfFiles[j] ;
        		if(fsub1.listFiles()!=null){		
        		  newFileRead(fsub1,list,b,filename,slist,record);
        		}
        		System.out.println("Hi Sub-SubFolder");
        		b=true;
        	}
            
            m.put("id", listOfFiles[j].getAbsolutePath());
            m.put("parentId", fsub.getAbsolutePath());
            m.put("isFolder", b);
            m.put("file", listOfFiles[j]);
            m.put("name",listOfFiles[j].getName() );
            b=false;
            if(record.get("name")!=null)
            if(filename.equalsIgnoreCase(fsub.getAbsolutePath())){
            	if(!listOfFiles[j].isDirectory()){
		    	slist.add(m);
            	}
		    }
            list.add(m);
		}
	}

}