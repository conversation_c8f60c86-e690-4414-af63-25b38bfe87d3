package com.ascent.ds.operations;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.primefaces.json.JSONException;
import org.primefaces.json.JSONObject;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
/**
 * Servlet implementation class PurgeData
 */
@WebServlet("/PurgeData")
public class PurgeData extends HttpServlet {
	private static final long serialVersionUID = 1L;
	private static Logger logger = LogManager.getLogger(HistoryConnection.class);

	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		
		try{
			Map<String, Object> map=new HashMap<>();;
			Connection connection = HistoryConnection.getConnection();
			response.setContentType("application/json");
			Map<String,Object> sendCriteiraMap= new HashMap<String, Object>();
			System.out.println("SSSSS"+request.getParameter("reconName"));
			String json = request.getParameter("historyMap");
			System.out.println("historyMapStr"+ json);
		
			//ObjectMapper mapper = new ObjectMapper();
		//	map = mapper.readValue(json, new TypeReference<Map<String, String>>(){});
			
			map=jsonToMap(json);
			
			logger.trace("convertedMap" + map);
			
		//	System.out.println("convertedMap :" + map);
			String card_no = "";
			    
			   
			    //System.out.println("card_no================="+card_no);
				String reconModule= (String)map.get("reconModule");
				String referenceNo= (String)map.get("reference_no");
				if(reconModule.equalsIgnoreCase("ACQ_MATM_CRDS_RECON")||reconModule.equalsIgnoreCase("ACQ_NATM_CRDS_RECON") || 
						reconModule.equalsIgnoreCase("ACQ_UATM_CRDS_RECON") ||reconModule.equalsIgnoreCase("ACQ_VATM_CRDS_RECON") ||
						reconModule.equalsIgnoreCase("ACQ_QPAY_CRDS_RECON") ||reconModule.equalsIgnoreCase("PAYROL_RECON") ||
				        reconModule.equalsIgnoreCase("ONUS_ATM_CREDIT_RECON")||reconModule.equalsIgnoreCase("ONUS_ATM_PAYROL_RECON") ||
				        reconModule.equalsIgnoreCase("ONUS_POS_DEBIT_RECON")){
					 }else{
						 String card_no1=(String)map.get("card_no");
						    String bin_no=(String)map.get("bin_no");
						    if(!(bin_no.isEmpty())){
						    	bin_no=(String)map.get("bin_no");	
						    	card_no=card_no.concat(bin_no);
						    }else{
						    	bin_no="";
						    }
						    if(!(card_no1.isEmpty())){
						    	 card_no1=(String)map.get("card_no");
						    	 card_no=card_no.concat("******").concat(card_no1);
						    }else{
						    	card_no1="";
						    }
					 }
				String match_type= (String)map.get("match_type");
				String message_type= (String)map.get("message_type");
				String startDate= (String)map.get("startDate");
				String endDate= (String)map.get("endDate");
				
				sendCriteiraMap.put("reconModule",reconModule);
				sendCriteiraMap.put("referenceNo",referenceNo);
				sendCriteiraMap.put("card_no",card_no);
		        sendCriteiraMap.put("MATCH_TYPE",match_type);
	        	sendCriteiraMap.put("MAIN_REV_IND",message_type);
	        	sendCriteiraMap.put("FROM_DATE",startDate);
	        	sendCriteiraMap.put("TO_DATE",endDate);
	            String criteria=retrieveData(sendCriteiraMap);
	     

			JQueryDataTableParamModel param = DataTablesParamUtility.getParam(request);
			List<HashMap<String,String>> list=new ArrayList<HashMap<String,String>>();
			try
			{
				//Connection connection = HistoryConnection.getConnection();
	    		 //int sortColumnIndex = param.iSortColumnIndex;
	    	//	 String sortDirection=param.sSortDirection;
	    	    //String searcString=param.sSearch.toLowerCase();
	         	 int displayLength=param.iDisplayLength;
	         	 int displayStart=param.iDisplayStart;
	         	 int iTotalRecords=0;
	         	 
	    	 //   System.out.println(" this is procedure calling    "+param.sEcho+"  "+param.iDisplayLength+"  "+param.iDisplayStart+"  "+param.sSortDirection);
	    	    String sql = "EXEC [dbo].[Get_Record_Count_Proc] ?,?";

	         	

	            CallableStatement stmt1 = connection.prepareCall(sql);
	            stmt1.setString(1,request.getParameter("reconName"));
	            stmt1.setString(2,criteria);
	            ResultSet rs1=stmt1.executeQuery();
	        	int count=0;
	            while(rs1.next())
	            {
	            	count=rs1.getInt(1);
	            }
	            sql= "EXEC [dbo].[List_Records_procedure] ?,?,?,?,?";
	            CallableStatement stmt = connection.prepareCall(sql);
	            stmt.setString(1,request.getParameter("reconName"));
	            stmt.setInt(2,displayLength);
	            stmt.setInt(3,displayStart);
	            stmt.setInt(4,0);
	            stmt.setString(5,criteria);
	            ResultSet rs=stmt.executeQuery();
	            
	            
	            List<String> columnList=new ArrayList<String>();
	            ResultSetMetaData rsmd=rs.getMetaData();
				String columns="";
				for (int i=1;i<=rsmd.getColumnCount();i++)
				{
					
					if( (i<rsmd.getColumnCount() ) && i>2 )
						columns=columns+rsmd.getColumnLabel(i)+",";
					else if(i>2)
						columns=columns+rsmd.getColumnLabel(i);
					columnList.add(rsmd.getColumnLabel(i));
					
				}
			
	        	while(rs.next())
				{
	        		
					LinkedHashMap<String,String> dataRecord=new LinkedHashMap<String,String>();
					
					
					for(int c=0;c<rsmd.getColumnCount();c++)
					{
					
						if(rs.getString((String)columnList.get(c))!=null )
						 
							dataRecord.put((String)columnList.get(c),rs.getString((String)columnList.get(c)));
						else
							dataRecord.put((String)columnList.get(c),"--");
				
					}
				
					
					
					list.add(dataRecord);
					
				}
	        	iTotalRecords=count;
	        	
	        	
	        		    JsonObject jsonResponse = new JsonObject();		
	        			jsonResponse.addProperty("sEcho", param.sEcho);
	        			jsonResponse.addProperty("iTotalRecords",count);
	        	        jsonResponse.addProperty("iTotalDisplayRecords", count);			
	        	        jsonResponse.addProperty("sColumns",columns);
	        	        Gson gson = new Gson();
	        			jsonResponse.add("aaData", gson.toJsonTree(list));
	        			//System.out.println(list);
	        			
	        			response.setContentType("application/Json");
	        			response.getWriter().print(jsonResponse.toString());
	        			
	        	
	        	
	            
			}
			catch(Exception e)
			{
				e.printStackTrace();
			}
			
		}catch(Exception e){
			e.printStackTrace();
			logger.error("ERROR", e);
		}
		
	
	
	}
	
	public static Map<String, Object> jsonToMap(String t) throws JSONException {

       Map map = new HashMap<String, String>();
        JSONObject jObject = new JSONObject(t);
        Iterator<?> keys = jObject.keys();

        while( keys.hasNext() ){
            String key = (String)keys.next();
            String value = jObject.getString(key); 
            map.put(key, value);

        }
		return map;
    }

private String  retrieveData(Map<String, Object> sendCriteiraMap) {
		System.out.println("retrieveData retrieveData retrieveData");
		List resultMap=new ArrayList();
		//Connection connection = HistoryConnection.getConnection();
		ResultSet rs=null;
		String fetchQuery=" ";
		String card_no=(String)sendCriteiraMap.get("card_no");
		String tableName=(String)sendCriteiraMap.get("reconModule");
		String referenceNo=(String)sendCriteiraMap.get("referenceNo");
		String MATCH_TYPE=(String)sendCriteiraMap.get("MATCH_TYPE");
		String MAIN_REV_IND=(String)sendCriteiraMap.get("MAIN_REV_IND");
		String FROM_DATE=(String)sendCriteiraMap.get("FROM_DATE");
		String TO_DATE=(String)sendCriteiraMap.get("TO_DATE");
		
		if(tableName.equalsIgnoreCase("ONUS_POS_CREDIT_RECON")|| tableName.equalsIgnoreCase("ONUS_POS_PAYROL_RECON")
				|| tableName.equalsIgnoreCase("ACQ_MPOS_CRDS_RECON")|| tableName.equalsIgnoreCase("ACQ_UPOS_CRDS_RECON") 
				|| tableName.equalsIgnoreCase("ACQ_VPOS_CRDS_RECON")|| tableName.equalsIgnoreCase("ISSUER_VATM_CREDIT_RECON")
				|| tableName.equalsIgnoreCase("ISSUER_MPOS_CREDIT_RECON")|| tableName.equalsIgnoreCase("ISSUER_VPOS_CREDIT_RECON")
				|| tableName.equalsIgnoreCase("ISSUER_MATM_CREDIT_RECON")){ //CARD NUMBER AS I002_NUMBER 
			
			if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
				
				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
					fetchQuery="  ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%'  AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
					fetchQuery="  I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y'  AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(referenceNo.isEmpty() && card_no.isEmpty()){
					fetchQuery="  ACTIVE_INDEX='Y'  AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}
				else{
	        	 fetchQuery=" I037_RET_REF_NUM='"+referenceNo+"' AND I002_NUMBER LIKE'%"+card_no +"%'  AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
	        	}
				
				
				
			}else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
					fetchQuery="  ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%'  AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
					fetchQuery="  I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(referenceNo.isEmpty() && card_no.isEmpty()){
					fetchQuery="  ACTIVE_INDEX='Y'  AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}
				else{
	        	 fetchQuery=" I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%'  AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
	        	}
				
			}
			else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
				
				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
					fetchQuery="  ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
					fetchQuery="  I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(referenceNo.isEmpty() && card_no.isEmpty()){
					fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND  TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}
				else{
	        	 fetchQuery=" I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
	        	}
			}else{
				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
					fetchQuery="  ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
					fetchQuery="  I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}else if(referenceNo.isEmpty() && card_no.isEmpty()){
					fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
				}
				else{
	        	 fetchQuery=" I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
	        	}
			}
			/* WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
			 * if(referenceNo.isEmpty()){
				fetchQuery="  ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
			}else if(card_no.isEmpty()){
				fetchQuery="  I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
			}else if(referenceNo.isEmpty() && card_no.isEmpty()){
				fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
			}
			else{
        	 fetchQuery=" I037_RET_REF_NUM='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND I002_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        	}*/
        }
    	else if(tableName.equalsIgnoreCase("PAYROL_RECON")){
    		if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
        			fetchQuery=" ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}
    		else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
    			    fetchQuery=" ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}
    		
    		else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    			    fetchQuery=" ACTIVE_INDEX='Y' AND  MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
    		}
    		else{
    			    fetchQuery=" ACTIVE_INDEX='Y' AND  MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
    		}
    		
    		
    		/* WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
    		 * if(card_no.isEmpty()){
    			fetchQuery=" ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}else{
    		 fetchQuery=" ACTIVE_INDEX='Y' AND  MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}*/
    	}
    	else if(tableName.equalsIgnoreCase("ACQ_QPAY_CRDS_RECON")){
    		
    		if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    			if(referenceNo.isEmpty()){
       			 fetchQuery=" ACTIVE_INDEX='Y' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
       		       }else{
       	    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
           		}
    		}
    		else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
    			if(referenceNo.isEmpty()){
       			     fetchQuery=" ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
       		       }else{
       	    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
           		}
    		}
    		else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    			if(referenceNo.isEmpty()){
       			    fetchQuery=" ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
       		       }else{
       	    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
           		}
    		}
    		else{
    			if(referenceNo.isEmpty()){
      			     fetchQuery=" ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
      		       }else{
       		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
      		       }
       		}
    		
    		
    		/*    WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
    		 * if(referenceNo.isEmpty()){
    			 fetchQuery=" ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}else{
    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRAN_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}*/
    	}
    	else if(tableName.equalsIgnoreCase("ISSUER_NATM_DEBIT_RECON") ||tableName.equalsIgnoreCase("ISSUER_NPOS_DEBIT_RECON")){// CARD_NUMBER AND RETR_REF_NO
    		
    		if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    			      if(referenceNo.isEmpty() && (!card_no.isEmpty())){
    			    	  fetchQuery="  ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";  
    			      }else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
    			    	  fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
    	    			}
    			      else if(referenceNo.isEmpty() && card_no.isEmpty()){
    			    	  fetchQuery="  ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	  
    			      }
    			      else{
    			    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    			    	  }
    		}
    		else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
    			if(referenceNo.isEmpty() && (!card_no.isEmpty())){
			    	  fetchQuery="  ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";  
			      }else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
			    	  fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
	    			}
			      else if(referenceNo.isEmpty() && card_no.isEmpty()){
			    	  fetchQuery="  ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	  
			      }
			      else{
			    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
			    	  }
    		}
    		else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    			if(referenceNo.isEmpty() && (!card_no.isEmpty())){
			    	  fetchQuery="  ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";  
			      }else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
			    	  fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
	    			}
			      else if(referenceNo.isEmpty() && card_no.isEmpty()){
			    	  fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	  
			      }
			      else{
			    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
			    	  }
    		}
    		else{
    			
    			if(referenceNo.isEmpty() && (!card_no.isEmpty())){
    				 fetchQuery="  ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    			}
    			else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
    				 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    			}
    			else if(referenceNo.isEmpty() && card_no.isEmpty()){
    				 fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    			}
    			else{
    			 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    			}
       		}
    		
    		/*     WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
    		 * if(referenceNo.isEmpty()){
    			fetchQuery=" ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}else if(card_no.isEmpty()){
    			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
    		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
    			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}
			else{
    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NUMBER LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}*/
    	}
		
    	else{ // CARD NUMBER AS PAN
    		if(tableName.equalsIgnoreCase("ONUS_ATM_DEBIT_RECON")||tableName.equalsIgnoreCase("ONUS_ATM_DEPOSIT_RECON")||
    		   tableName.equalsIgnoreCase("ATM_RECON")){
    			
    			if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery="  ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}	
  			      }
    			else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery="  ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
        			fetchQuery=" ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
        			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
        		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
        			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}
    			else{
        		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		    }
    			}
    			else{
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    				
    			}
    			
    			
    			/*    WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
    			 * if(referenceNo.isEmpty()){
        			fetchQuery=" ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}else if(card_no.isEmpty()){
        			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
        		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
        			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}
    			else{
        		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND PAN LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}*/
    			
    		}else if(tableName.equalsIgnoreCase("ACQ_NATM_CRDS_RECON") || tableName.equalsIgnoreCase("ACQ_MATM_CRDS_RECON") ||
    				 tableName.equalsIgnoreCase("ACQ_UATM_CRDS_RECON")|| tableName.equalsIgnoreCase("ACQ_VATM_CRDS_RECON") ||
    				 tableName.equalsIgnoreCase("ONUS_ATM_CREDIT_RECON") || tableName.equalsIgnoreCase("ONUS_ATM_PAYROL_RECON") ||
    				 tableName.equalsIgnoreCase("ONUS_POS_DEBIT_RECON")){//ONUS_ATM_PAYROL_RECON, ONUS_POS_DEBIT_RECON
    			
    			if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    				if(referenceNo.isEmpty()){
            			fetchQuery=" ACTIVE_INDEX='Y'  AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
    				if(referenceNo.isEmpty()){
            			fetchQuery=" ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    				if(referenceNo.isEmpty()){
            			fetchQuery=" ACTIVE_INDEX='Y'  AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else{
    				if(referenceNo.isEmpty()){
            			fetchQuery=" ACTIVE_INDEX='Y'  AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}	
    			}
    			
    			/*   WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
    			 * if(referenceNo.isEmpty()){
        			fetchQuery=" ACTIVE_INDEX='Y'  AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}
    			else{
        		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}*/
    		}

    		else{
    			
    			if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery="  ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else if((MATCH_TYPE.equals(0) || MATCH_TYPE.equals("0")) && (!(MAIN_REV_IND.equals(0)) || (!(MAIN_REV_IND.equals("0"))))){
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery=" ACTIVE_INDEX='Y' AND  MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else if(!((MATCH_TYPE.equals(0)) || (MATCH_TYPE.equals("0"))) && (MAIN_REV_IND.equals(0) || MAIN_REV_IND.equals("0"))){
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND  TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			else{
    				if(referenceNo.isEmpty() && (!card_no.isEmpty())){
            			fetchQuery=" ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}else if(card_no.isEmpty() && (!referenceNo.isEmpty())){
            			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
            		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
            			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
        			else{
            		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
            		}
    			}
    			
    			/*     WITHOUT MATCH_TYPE AND MAIN_REV_IND CONDITOINS
    			 * if(referenceNo.isEmpty()){
        			fetchQuery=" ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}else if(card_no.isEmpty()){
        			fetchQuery="  RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";	
        		}else if(referenceNo.isEmpty() && card_no.isEmpty()){
        			fetchQuery="  ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}
    			else{
        		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND CARD_NO LIKE'%"+card_no +"%' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
        		}*/
    		}
    		
    		
    		/*if(referenceNo.isEmpty()){
    			fetchQuery=" ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}else{
    		 fetchQuery=" RETR_REF_NO='"+referenceNo+"' AND ACTIVE_INDEX='Y' AND MATCH_TYPE='"+MATCH_TYPE+ "' AND MAIN_REV_IND='"+MAIN_REV_IND+ "' AND TRA_DATE BETWEEN '"+FROM_DATE+"'"+" AND '"+TO_DATE+"'";
    		}*/
    	}
		
		return fetchQuery;
	}
}
