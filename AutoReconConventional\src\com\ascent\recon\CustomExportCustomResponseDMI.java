//----------------------------------------------------------------------
// Isomorphic SmartClient
//
// CustomExportCustomResponseDMI implementation for a Custom Export example
//
// This example shows one way to insert your own logic into SmartClient's 
// normal client/server flow to export entirely arbitrary data without a 
// front-end component supplying the data.
//
//----------------------------------------------------------------------

package com.ascent.recon;

import java.io.File;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.ascent.service.dto.User;
import com.google.common.io.Files;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.log.Logger;
import com.isomorphic.rpc.RPCManager;
import com.isomorphic.servlet.RequestContext;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;

public class CustomExportCustomResponseDMI {

    private static Logger log = new Logger(DSRequest.class.getName());

    public static void customExport(RPCManager rpc, HttpServletResponse response)
    throws Exception
    {
    	
        long l1=System.currentTimeMillis();
    	// setting doCustomResponse() notifies the RPCManager that we'll bypass RPCManager.send
        // and instead write directly to the servletResponse output stream
        int ii=1;
        rpc.doCustomResponse();
        RequestContext.setNoCacheHeaders(response);
        DSRequest i= rpc.getDSRequest();
        Map m= i.getValues();
        System.out.println("ColNames "+m);
        System.out.println("File criteria" + m.get("DataS")+" "+m.get("filterCriteria"));
      String fileName=  (String) m.get("exportFilename");
      System.out.println("export file name *******************************:" +  fileName);
        @SuppressWarnings("unchecked")
		Map<String, String> c1=  (Map<String, String>)m.get("filterCriteria");
         
        System.out.println("all fields from web"+m.get("fields"));
     
		@SuppressWarnings("unchecked")
		Map <String, String>colMap=(Map<String, String>)(m.get("fields"));
        Set colNames=colMap.keySet();
        DSRequest req = new DSRequest((String)m.get("DataS"), "fetch");
        req.setCriteria("userId", (User)i.getHttpServletRequest().getSession().getAttribute("userId"));
        if(!(c1==null))
        {
        	System.out.println("crit contain null");
        req.setCriteria(c1);
        
        }
        @SuppressWarnings("unchecked")
		List <Map<String, String>>results = req.execute().getDataList();
        System.out.println("List size"+results.size());
       
        /*if((results.size()>0))
        {*/
        	if((results.size()>=35000))
        	{
        		System.out.println("from large export block");
        		ExportLargeFile( rpc,  response);
        	}else
        	{
        		
        	
        //System.out.println(results);
        System.out.println("**********************************************************************************");
       // System.out.println(results.size());
     //   System.out.println(results);
        Rectangle pageSize = new Rectangle(5400f, 5400f);
		Document document = new Document(pageSize);
		//int i=1;
		PdfPTable table=null;
		int srNo=1;
        //*****************************************************************************************
		File file= new File("E:\\EXPORETED_GRID_PDF_FILES");
		File file1= new File("E:/EXPORETED_GRID_PDF_FILES/test.pdf");
		if(!file.exists())
		{
			
			//file.createNewFile();
			file.mkdir();
			System.out.println("CREATING NEW FILE ");
		}
		if(!file1.exists()){
			
			file1.createNewFile();
		}
		int c=1;
        table= new PdfPTable(colNames.size()+1);
        PdfWriter.getInstance(document, new FileOutputStream(file1));
        document.open();
        table.addCell("SR NO");
			
        for(Object c11:colNames)
			{
				String key1=(String) c11;
				table.addCell(key1.toString());
			}
			table.setHeaderRows(1);

			PdfPCell[] cells = table.getRow(0).getCells();
			for (int j = 0; j < cells.length; j++) 
			{
				cells[j].setBackgroundColor(BaseColor.GRAY);
			}
		
			
			if(results.size() == 0) {
				Map<String, String> map = new HashMap<String, String>();
				for (Object column : colNames) {
					map.put(column.toString(), null);
				}
				results.add(map);
			}
			
			Iterator i2 = results.iterator();
        
		while(i2.hasNext())
        {
        	//if(c==){break;}
        	
            @SuppressWarnings("unchecked")
			Map <String, String>record = (Map<String, String>)i2.next();// one record
            //log.warn("converted record no"+ii+++"size"+results.size());
				Iterator iter=colNames.iterator();
				table.addCell(Integer.toString(srNo++));
				while (iter.hasNext()) 
				{
					String key3=(String) iter.next();
					if(record.get(key3)==null)
					{
						table.addCell(" ");
				
					}else
					{
					table.addCell("" + String.valueOf(record.get(key3)));
			
					}
					
				}
		
				c++;
        }
        System.out.println("Adding datato table");
        //table.flushContent();
        document.add(table);
        System.out.println("Add finish");
        document.close();
        long t2=System.currentTimeMillis();
        response.setContentType("application/pdf");
		response.addHeader("content-disposition", "attachment; filename="+ fileName+".pdf");
		response.setContentLengthLong(file1.length());
		log.warn("about to fetch data"+ii);
     
        System.out.println("*************************Count time ");
        long t3=System.currentTimeMillis();
        byte[] bytes = Files.toByteArray(file1);
        long t4=System.currentTimeMillis();
        System.out.println("time takes by bytes concvertion"+(t4-t3));
        ServletOutputStream os = response.getOutputStream();
        os.write(bytes);
        os.flush();
        System.out.println("Total time taken by programm"+(t2-l1));
        return ;
        }
        /*}	
        else{
        	response.sendError( response.SC_PAYMENT_REQUIRED);
        }*/
        
}
    
    public static void ExportLargeFile(RPCManager rpc, HttpServletResponse response )
    	    throws Exception
    	    {
    	    	
    	        long l1=System.currentTimeMillis();
    	    	// setting doCustomResponse() notifies the RPCManager that we'll bypass RPCManager.send
    	        // and instead write directly to the servletResponse output stream
    	        int ii=1;
    	        rpc.doCustomResponse();
    	        RequestContext.setNoCacheHeaders(response);
    	        DSRequest i= rpc.getDSRequest();
    	        Map m= i.getValues();
    	        System.out.println("ColNames "+m);
    	        System.out.println("File criteria" + m.get("DataS")+" "+m.get("filterCriteria"));
    	        //response.setContentType("text/plain");
    	        @SuppressWarnings("unchecked")
    			Map<String, String> c1=  (Map<String, String>)m.get("filterCriteria");
    	     
    	        	// dsResponse.setStatus(DSResponse.STATUS_SUCCESS);
    	             //rpc.send(dsRequest, dsResponse);
    	            // rpc.sendFailure(null, "Invalid response");
    	             //rpc.send(i, response);
    	             System.out.println("block called");
    	        
    	        System.out.println("all fields from web"+m.get("fields"));
    	     
    			@SuppressWarnings("unchecked")
    			Map <String, String>colMap=(Map<String, String>)(m.get("fields"));
    	        Set colNames=colMap.keySet();
    	        DSRequest req = new DSRequest((String)m.get("DataS"), "fetch");
    	        if(!(c1==null))
    	        {
    	        	System.out.println("crit contain null");
    	        req.setCriteria(c1);
    	        
    	        }
    	        @SuppressWarnings("unchecked")
    			List <Map<String, String>>results = req.execute().getDataList();
    	        System.out.println("List size"+results.size());
    	       
    	        if(results.size()>0)
    	        {
    	        //System.out.println(results);
    	        System.out.println("**********************************************************************************");
    	       // System.out.println(results.size());
    	     //   System.out.println(results);
    	        Rectangle pageSize = new Rectangle(5400f, 5400f);
    			Document document = new Document(pageSize);
    			//int i=1;
    			PdfPTable table=null;
    			int srNo=1;
    	        //*****************************************************************************************
    			File file= new File("E:\\EXPORETED_GRID_PDF_FILES");
    			File file1= new File("E:/EXPORETED_GRID_PDF_FILES/test.pdf");
    			if(!file.exists())
    			{
    				
    				//file.createNewFile();
    				file.mkdir();
    				System.out.println("CREATING NEW FILE ");
    			}
    			if(file1.exists()){
    				file1.createNewFile();
    			}
    			int c=1;
    	        table= new PdfPTable(colNames.size()+1);
    	        PdfWriter.getInstance(document, new FileOutputStream(file1));
    	        document.open();
    	        table.addCell("SR NO");
    				
    	        for(Object c11:colNames)
    				{
    					String key1=(String) c11;
    					table.addCell(key1.toString());
    				}
    				table.setHeaderRows(1);

    				PdfPCell[] cells = table.getRow(0).getCells();
    				for (int j = 0; j < cells.length; j++) 
    				{
    					cells[j].setBackgroundColor(BaseColor.GRAY);
    				}
    			
    				Iterator i2 = results.iterator();
    	        
    				while(i2.hasNext())
    	        {
    	        	//if(c==100000){break;}
    	        	
    	            @SuppressWarnings("unchecked")
    				Map <String, String>record = (Map<String, String>)i2.next();// one record
    	            //log.warn("converted record no"+ii+++"size"+results.size());
    					Iterator iter=colNames.iterator();
    					table.addCell(Integer.toString(srNo++));
    					while (iter.hasNext()) 
    					{
    						String key3=(String) iter.next();
    						if(record.get(key3)==null)
    						{
    							table.addCell(" ");
    					
    						}else
    						{
    						table.addCell("" + String.valueOf(record.get(key3)));
    				
    						}
    						
    					}
    			
    				if(c%2000==0)
    				{
    					 document.add(table);
    					 table.flushContent();
    					 table=null;
    					System.gc();
    					 table= new PdfPTable(colNames.size()+1);
    					// c=1;
    				}
    					 //table.flushContent();		
    					c++;
    	        }
    	        System.out.println("Adding datato table");
    	        //table.flushContent();
    	        document.add(table);
    	        System.out.println("Add finish");
    	        document.close();
    	        long t2=System.currentTimeMillis();
    	        response.setContentType("application/pdf");
    			response.addHeader("content-disposition", "attachment; filename=report.pdf");
    			response.setContentLengthLong(file1.length());
    			log.warn("about to fetch data"+ii);
    	     
    	        System.out.println("*************************Count time ");
    	        long t3=System.currentTimeMillis();
    	        byte[] bytes = Files.toByteArray(file1);
    	        long t4=System.currentTimeMillis();
    	        System.out.println("time takes by bytes concvertion"+(t4-t3));
    	        ServletOutputStream os = response.getOutputStream();
    	        os.write(bytes);
    	        os.flush();
    	        System.out.println("Total time taken by programm"+(t2-l1));
    	        return ;
    	        }
    	        else{
    	        	response.sendError( response.SC_PAYMENT_REQUIRED);
    	        }
    	        
    	}
    
    
    
   
    
    
    
    
    
    
    
}