package com.ascent.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Privileges implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3241339451954305460L;
	List<Privilege> privilegeList = new ArrayList<Privilege>();
	Map<Integer, Privilege> privilegeMap = new HashMap<Integer, Privilege>();
	Map<String, Privilege> privilegeWithRoleNameMap = new HashMap<String, Privilege>();

	public void init() {
		if (privilegeList != null) {
			for (Privilege privilege : this.privilegeList) {
				privilege.init();
				privilegeMap.put(privilege.getPrivilegeId(), privilege);
				privilegeWithRoleNameMap.put(privilege.getRole(), privilege);

			}
		}
	}

	public Privilege getPrivilege(Integer id) {
		return privilegeMap.get(id);
	}

	public Privilege getPrivilege(String roleName) {
		return privilegeWithRoleNameMap.get(roleName);
	}

	public List<Privilege> getPrivilegeList() {
	 
		return privilegeList;
	}

	public void setPrivilegeList(List<Privilege> previlelageList) {
		this.privilegeList = previlelageList;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((privilegeList == null) ? 0 : privilegeList.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Privileges other = (Privileges) obj;
		if (privilegeList == null) {
			if (other.privilegeList != null)
				return false;
		} else if (!privilegeList.equals(other.privilegeList))
			return false;
		return true;
	}

}
