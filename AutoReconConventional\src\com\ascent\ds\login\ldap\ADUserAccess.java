package com.ascent.ds.login.ldap;


import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;

import javax.naming.Context;
import javax.naming.NameAlreadyBoundException;
import javax.naming.NameNotFoundException;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.BasicAttribute;
import javax.naming.directory.BasicAttributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.ModificationItem;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.LdapContext;

import org.apache.log4j.Logger;


//import com.fss.jdbcConn.utils.ConfigFileUtil;


/*******************************************************************************
 * This class is created for authenticating the Directory Server using LDAP
 * protocol
 * 
 * 
 */
public class ADUserAccess implements LDAPInterface {
	private static final Logger logger = Logger.getLogger(ADUserAccess.class);

	/***************************************************************************
	 * This method is used for authenticating the user name and password
	 * connecting Directory Server
	 * 
	 * @param userName
	 * @param password
	 * @return
	 */

	public boolean authenticateUser(String userName, String password,
			String domain) {

		boolean authStatus = false;
		try {

			LDAPConnection ldapCon = new LDAPConnection();
			
			ldapCon.setDomain(domain);
			
			//SAtish Added
			//ldapContext = ldapCon.getContext(userName,password,getDomain());
			String domain1=ldapCon.getDomain();
			
			authStatus = getContext(userName, password, ldapCon.getDomain());

			if (authStatus) {

				System.out.println("User Successfully Athenticated...");
			}

			else

				System.out.println("In correct User or Password ");

		} catch (Exception e) {
			logger.error("Error in authenticateUser:"+e.getMessage());

		}

		return authStatus;

	}

	/***************************************************************************
	 * This method is used for getting the connection
	 * 
	 * @return
	 */
	public LDAPConnection LDAPConnection() {

		return new LDAPConnection();
	}

	/***************************************************************************
	 * This method is used for authenticating the user name and password
	 * connecting Directory Server under a domain
	 * 
	 * @param userName
	 * @param password
	 * @return
	 */
	public boolean authenticateUser(String userName, String password) {

		boolean authStatus = false;
		try {

			authStatus = getContext(userName, password);

			if (authStatus)
				System.out.println("User Successfully Athenticated...");
			else
				System.out.println("In correct User or Password ");

		} catch (Exception e) {
			logger.error("Error in authenticateUser:"+e.getMessage());

		}

		return authStatus;

	}

	/***************************************************************************
	 * This method is used for changing the password of the user after
	 * validating with the old password
	 * 
	 * @param userName
	 * @param oldPassword
	 * @param newPassword
	 * @return
	 */
	public boolean changePassword(String userName, String oldPassword,
			String newPassword, String domain) {

		LdapContext ldapContext = null;
		boolean status = false;

		try {

			if (authenticateUser(userName, oldPassword, domain))

			{
				LDAPConnection ldapCon = new LDAPConnection();
				
				ldapCon.setDomain(domain);
				
				ldapContext = ldapCon.getContext(ldapCon.getDomain());
				
				//ldapContext = getContext(domain);

				Attributes attrs = new BasicAttributes(true);

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(domain).append(
								",DC=com");

				// String usrName = "CN=" + userName + ",CN=Users,DC=" + domain
				// + ",DC=com";

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);

				ModificationItem[] mods = new ModificationItem[1];

				// New Password
				mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
						new BasicAttribute("unicodePwd", LDAPUtil
								.getPassword(newPassword)));

				// Perform the update
				ldapContext.modifyAttributes(sbusrName.toString(), mods);

				status = true;
				System.out.println("Change Password Successfully completed..");
			} else {
				System.out.println("User Authentication Failed..");
				status = false;
			}
		} catch (NamingException e) {
			logger.error("Error in changePassword:"+e.getMessage());
			System.err
					.println("Problem while changing password for user name: "
							+ userName + ":" + e);

			status = false;
		} finally {
			closeConnection(ldapContext);

		}
		return status;
	}


	/***************************************************************************
	 * This method is used for forcily changing the password of the user 
	 * @param userName
	 * @param oldPassword
	 * @param newPassword
	 * @return
	 */
	public boolean forcechangePassword(String userName, 
			String newPassword, String domain) { 

		LdapContext ldapContext = null;
		boolean status = false;

		try {

			if (checkUserExist(userName, domain))

			{

				//ldapContext = getContext(domain);
                LDAPConnection ldapCon = new LDAPConnection();
				
				ldapCon.setDomain(domain);
				
				ldapContext = ldapCon.getContext(ldapCon.getDomain());

				Attributes attrs = new BasicAttributes(true);

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(domain).append(
								",DC=com");

				// String usrName = "CN=" + userName + ",CN=Users,DC=" + domain
				// + ",DC=com";

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);

				ModificationItem[] mods = new ModificationItem[1];

				// New Password
				mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
						new BasicAttribute("unicodePwd", LDAPUtil
								.getPassword(newPassword)));

				// Perform the update
				ldapContext.modifyAttributes(sbusrName.toString(), mods);

				status = true;
				System.out.println("Force Change Password Successfully completed..");
			} else {
				System.out.println("User doesn't exists..");
				status = false;
			}
		} catch (NamingException e) {
			logger.error("Error in forcechangePassword:"+e.getMessage());
			System.err
					.println("Problem while changing password for user name: "
							+ userName + ":" + e);

			status = false;
		} finally {
			closeConnection(ldapContext);

		}
		return status;
	}
	/***************************************************************************
	 * This method is used for changing the password of the user after
	 * validating with the old password
	 * 
	 * @param userName
	 * @param oldPassword
	 * @param newPassword
	 * @return
	 */
	public boolean changePassword(String userName, String oldPassword,
			String newPassword) {

		LdapContext ldapContext = null;
		boolean status = false;

		try {

			if (authenticateUser(userName, oldPassword))

			{

				ldapContext = getContext(getDomain());

				Attributes attrs = new BasicAttributes(true);

				// String usrName = "CN=" + userName + ",CN=Users,DC="
				// + getDomain() + ",DC=com";

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(getDomain()).append(
								",DC=com");

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);

				ModificationItem[] mods = new ModificationItem[1];

				// New Password
				mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
						new BasicAttribute("unicodePwd", LDAPUtil
								.getPassword(newPassword)));

				// Perform the update
				ldapContext.modifyAttributes(sbusrName.toString(), mods);

				status = true;

				System.out.println("Change Password Successfully completed..");
			} else {

				System.out.println("User Authentication Failed..");
				status = false;

			}
		} catch (NamingException e) {
			logger.error("Error in changePassword:"+e.getMessage());
			System.err
					.println("Problem while changing password for user name: "
							+ userName + ":" + e);

			status = false;
		} finally {
			closeConnection(ldapContext);

		}
		return status;

	}

	/***************************************************************************
	 * This method is used for changing the password of the user forcefully
	 * 
	 * @param userName
	 * @param oldPassword
	 * @param newPassword
	 * @return
	 */
	public boolean changePassword(String userName, String newPassword) {

		LdapContext ldapContext = null;
		boolean status = false;

		try {

			if (checkUserExist(userName, getDomain()))

			{

				ldapContext = getContext(getDomain());

				Attributes attrs = new BasicAttributes(true);

				// String usrName = "CN=" + userName + ",CN=Users,DC="
				// + getDomain() + ",DC=com";

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(getDomain()).append(
								",DC=com");

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);

				ModificationItem[] mods = new ModificationItem[1];

				// New Password
				mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
						new BasicAttribute("unicodePwd", LDAPUtil
								.getPassword(newPassword)));

				// Perform the update
				ldapContext.modifyAttributes(sbusrName.toString(), mods);

				status = true;

				System.out.println("Forceful Change Password Successfully completed..");
			} else {

				System.out.println("User Doesn't exists..");
				status = false;

			}
		} catch (NamingException e) {
			logger.error("Error in changePassword:"+e.getMessage());
			System.err
					.println("Problem while changing password for user name: "
							+ userName + ":" + e);

			status = false;
		} finally {
			closeConnection(ldapContext);

		}
		return status;

	}

	/***************************************************************************
	 * This method is used for creating group under a specific domain
	 * 
	 * @param groupName
	 * @param domain
	 * @return
	 */
	public boolean createGroup(String groupName, String domain) {

		LdapContext ldapContext = null;
		@SuppressWarnings("unused")
		Context result = null;

		boolean status = false;

		try {

			
			//setDomain(domain);
			
			LDAPConnection ldapCon = new LDAPConnection();
			
			ldapCon.setDomain(domain);
			
			ldapContext = ldapCon.getContext(ldapCon.getDomain());

			Attributes attrs = new BasicAttributes(true);

			attrs.put("objectClass", "Group");
			attrs.put("name", groupName);
			attrs.put("samAccountName", groupName);
			// attrs.put("groupType", "Admin");

			// groupName = "CN=" + groupName+ ",CN=Users,OU=Security
			// Groups,DC=testdomain,DC=com";

			// groupName = "CN=" + groupName + ",CN=Users,DC=" + domain
			// + ",DC=com";

			StringBuffer sbgrpName = new StringBuffer();

			sbgrpName.append("CN=").append(groupName).append(",CN=Users,DC=")
					.append(ldapCon.getDomain()).append(",DC=com");

			groupName = sbgrpName.toString();

			try {
				result = ldapContext.createSubcontext(groupName, attrs);

			} catch (NamingException e) {
				logger.error("Error in createGroup:"+e.getMessage());
				System.err.println("Problem adding user to group: " + e);

				status = false;
			}

			status = true;
			System.out.println("Group created successfully..");
		} catch (Exception e) {

			logger.error("Error in createGroup:"+e.getMessage());
			status = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for creating group under a specific domain
	 * 
	 * @param groupName
	 * @return
	 */
	public boolean createGroup(String groupName) {

		LdapContext ldapContext = null;
		@SuppressWarnings("unused")
		Context result = null;

		boolean status = false;

		try {

			ldapContext = getContext(getDomain());

			Attributes attrs = new BasicAttributes(true);

			attrs.put("objectClass", "Group");
			attrs.put("name", groupName);
			attrs.put("samAccountName", groupName);
			// attrs.put("groupType", "Admin");

			// groupName = "CN=" + groupName + ",CN=Users,DC=" + getDomain()
			// + ",DC=com";

			StringBuffer sbgrpName = new StringBuffer();

			sbgrpName.append("CN=").append(groupName).append(",CN=Users,DC=")
					.append(getDomain()).append(",DC=com");

			groupName = sbgrpName.toString();

			try {
				result = ldapContext.createSubcontext(groupName, attrs);

			} catch (NamingException e) {
				logger.error("Error in createGroup:"+e.getMessage());
				System.err.println("Problem adding user to group: " + e);

				status = false;
			}

			status = true;
			System.out.println("Group created successfully..");
		} catch (Exception e) {

			logger.error("Error in createGroup:"+e.getMessage());
			status = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for searching the user
	 * 
	 * @param userName
	 * @param domain
	 * @return
	 */

	@SuppressWarnings("unchecked")
	public boolean checkUserExist(String userName, String domain) {

		boolean status = false;

		LdapContext ldapContext = null;
		Map userList = null;

		String returnedAtts[] = { "givenName", "mail", "userPrincipalName" };
		String searchFilter = "(& (userPrincipalName=" + userName
				+ ")(objectClass=user))";

		// Create the search controls
		SearchControls searchCtls = new SearchControls();
		searchCtls.setReturningAttributes(returnedAtts);

		// Specify the search scope
		searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE);

		int existCount = 0;

		try {
		//	setDomain(domain);
			
			LDAPConnection ldapCon = new LDAPConnection();
			
			ldapCon.setDomain(domain);
			
			//ldapContext = ldapCon.getContext(ldapCon.getDomain());
			ldapContext = ldapCon.getContext(ldapCon.getDomain());
			// Search objects in GC using filters

			ResourceBundle resourceBundle = ResourceBundle.getBundle("LDAPResources_AD");
			
			//ldapCon.setSearchBase("DC="+domain+","+ResourceBundleUtil.getString(ConfigFileUtil.getProperty("LDAP_SERVER_TYPE"),"searchbase"));
			ldapCon.setSearchBase("DC="+domain+","+resourceBundle.getString("searchbase"));
			
			NamingEnumeration answer = ldapContext.search(ldapCon.getSearchBase(),
					searchFilter, searchCtls);

			while (answer.hasMoreElements()) {
				SearchResult sr = (SearchResult) answer.next();
				Attributes attrs = sr.getAttributes();
				userList = new HashMap();
				if (attrs != null) {

					NamingEnumeration ne = attrs.getAll();
					while (ne.hasMore()) {
						Attribute attr = (Attribute) ne.next();

						if (attr.getID().equalsIgnoreCase("userPrincipalName"))

							++existCount;

					}
					ne.close();
				}

			}
			if (existCount > 0)
				status = true;

			else
				status = false;

		} catch (NamingException ex) {
			logger.error("Error in checkUserExist:"+ex.getMessage());

		} finally {
			closeConnection(ldapContext);
		}

		return status;
	}

	/***************************************************************************
	 * This method is used for getting the list of users under a group and a
	 * domain
	 * 
	 * @param groupName
	 * @param domain
	 * @return userList
	 */

	@SuppressWarnings("unchecked")
	public Map getUsersList(String domain, String groupName) {

		LdapContext ldapContext = null;
		Map userList = null;

		String returnedAtts[] = { "givenName", "mail", "userPrincipalName" };
		String searchFilter = "(&(objectClass=user))";

		// Create the search controls
		SearchControls searchCtls = new SearchControls();
		searchCtls.setReturningAttributes(returnedAtts);

		// Specify the search scope
		searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE);

		try {
			setDomain(domain);
			ldapContext = getContext();
			// Search objects in GC using filters

			NamingEnumeration answer = ldapContext.search(getSearchBase(),
					searchFilter, searchCtls);
			while (answer.hasMoreElements()) {
				SearchResult sr = (SearchResult) answer.next();
				Attributes attrs = sr.getAttributes();
				userList = new HashMap();
				if (attrs != null) {

					NamingEnumeration ne = attrs.getAll();
					while (ne.hasMore()) {
						Attribute attr = (Attribute) ne.next();

						System.out.println("ID:" + attr.getID() + " \n Name:"
								+ attr.get());
						userList.put(attr.getID(), attr.get());

					}
					ne.close();
				}

			}
		} catch (NamingException ex) {
			logger.error("Error in getUsersList:"+ex.getMessage());

		} finally {
			closeConnection(ldapContext);
		}

		return userList;
	}

	/***************************************************************************
	 * This method is used for getting the list of users under a group
	 * 
	 * @param groupName
	 * @return userList
	 */
	@SuppressWarnings("unchecked")
	public Map getUsersList(String groupName) {

		LdapContext ldapContext = null;
		Map userList = null;

		String returnedAtts[] = { "givenName", "mail", "userPrincipalName" };
		String searchFilter = "(&(objectClass=user))";

		// Create the search controls
		SearchControls searchCtls = new SearchControls();
		searchCtls.setReturningAttributes(returnedAtts);

		// Specify the search scope
		searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE);

		try {
			ldapContext = getContext(getDomain());
			// Search objects in GC using filters

			NamingEnumeration answer = ldapContext.search(getSearchBase(),
					searchFilter, searchCtls);
			while (answer.hasMoreElements()) {
				SearchResult sr = (SearchResult) answer.next();
				Attributes attrs = sr.getAttributes();
				userList = new HashMap();
				if (attrs != null) {

					NamingEnumeration ne = attrs.getAll();
					while (ne.hasMore()) {
						Attribute attr = (Attribute) ne.next();

						System.out.println("ID:" + attr.getID() + " \n Name:"
								+ attr.get());
						userList.put(attr.getID(), attr.get());

					}
					ne.close();
				}

			}
		} catch (NamingException ex) {
			logger.error("Error in getUsersList:"+ex.getMessage());

		} finally {
			closeConnection(ldapContext);
		}

		return userList;
	}

	/***************************************************************************
	 * This method is used for removing group under a specific domain
	 * 
	 * @param groupName
	 * @param domain
	 * @return
	 */
	public boolean removeGroup(String groupName, String domain) {

		LdapContext ldapContext = null;
		@SuppressWarnings("unused")
		Context result = null;
		boolean status = false;

		try {

			setDomain(domain);
			ldapContext = getContext();

			// groupName = "CN=" + groupName + ",CN=Users,DC=" + getDomain()
			// + ",DC=com";

			StringBuffer sbgrpName = new StringBuffer();

			sbgrpName.append("CN=").append(groupName).append(",CN=Users,DC=")
					.append(getDomain()).append(",DC=com");

			groupName = sbgrpName.toString();

			try {
				ldapContext.destroySubcontext(groupName);

			} catch (NamingException e) {
				logger.error("Error in removeGroup:"+e.getMessage());
				System.err.println("Problem adding user to group: " + e);

				status = false;
			}

			status = true;
			System.out.println("Group removed successfully..");
		} catch (Exception e) {

			logger.error("Error in removeGroup:"+e.getMessage());
			status = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return status;

	}

	/***************************************************************************
	 * This method is used for removing group under a specific domain
	 * 
	 * @param groupName
	 * @return
	 */
	public boolean removeGroup(String groupName) {

		LdapContext ldapContext = null;
		@SuppressWarnings("unused")
		Context result = null;
		boolean status = false;

		try {

			ldapContext = getContext(getDomain());

			// groupName = "CN=" + groupName + ",CN=Users,DC=" + getDomain()
			// + ",DC=com";

			StringBuffer sbgrpName = new StringBuffer();

			sbgrpName.append("CN=").append(groupName).append(",CN=Users,DC=")
					.append(getDomain()).append(",DC=com");

			groupName = sbgrpName.toString();

			try {
				ldapContext.destroySubcontext(groupName);

			} catch (NamingException e) {
				logger.error("Error in removeGroup:"+e.getMessage());
				System.err.println("Problem adding user to group: " + e);

				status = false;
			}

			status = true;
			System.out.println("Group removed successfully..");
		} catch (Exception e) {

			logger.error("Error in removeGroup:"+e.getMessage());
			status = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for removing user under a specific domain
	 * 
	 * @param userName
	 * @param domain
	 * @return
	 */

	public boolean removeUser(String userName, String domain) {

		LdapContext ldapContext = null;
		@SuppressWarnings("unused")
		Context result = null;
		boolean status = false;

		try {
			setDomain(domain);
			ldapContext = getContext();

			// userName = "CN=" + userName + ",CN=Users,DC=" + getDomain()
			// + ",DC=com";

			StringBuffer sbusrName = new StringBuffer();

			sbusrName.append("CN=").append(userName).append(",CN=Users,DC=")
					.append(getDomain()).append(",DC=com");

			try {
				ldapContext.destroySubcontext(sbusrName.toString());

			} catch (NamingException e) {
				logger.error("Error in removeUser:"+e.getMessage());
				System.err.println("Problem adding user to group: " + e);

				status = false;
			}

			status = true;
			System.out.println("User Removed Successfully..");
		} catch (Exception e) {

			logger.error("Error in removeUser:"+e.getMessage());

		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for removing user
	 * 
	 * @param userName
	 * @return
	 */
	public boolean removeUser(String userName) {

		LdapContext ldapContext = null;
		@SuppressWarnings("unused")
		Context result = null;
		boolean status = false;

		try {

			ldapContext = getContext(getDomain());

			// userName = "CN=" + userName + ",CN=Users,DC=" + getDomain()
			// + ",DC=com";

			StringBuffer sbusrName = new StringBuffer();

			sbusrName.append("CN=").append(userName).append(",CN=Users,DC=")
					.append(getDomain()).append(",DC=com");

			try {
				ldapContext.destroySubcontext(sbusrName.toString());

			} catch (NamingException e) {
				logger.error("Error in removeUser:"+e.getMessage());
				System.err.println("Problem adding user to group: " + e);

				status = false;
			}

			status = true;
			System.out.println("User Removed Successfully..");
		} catch (Exception e) {

			logger.error("Error in removeUser:"+e.getMessage());

		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for creating user under a specific domain
	 * 
	 * @param userName
	 * @param mailID
	 * @param phoneNumber
	 * @param domain
	 * @return
	 */
	public boolean createUser(String userName, String mailID,
			String phoneNumber, String domain) {

		LdapContext ldapContext = null;

		@SuppressWarnings("unused")
		Context result = null;

		boolean status = false;

		try {

			//setDomain(domain);
			//ldapContext = getContext();
			 LDAPConnection ldapCon = new LDAPConnection();
				
				ldapCon.setDomain(domain);
				
				//ldapContext = ldapCon.getContext(ldapCon.getDomain());
				ldapContext = ldapCon.getContext(ldapCon.getDomain());
			status = checkUserExist(userName, ldapCon.getDomain());

			if (status == false) {

				Attributes attrs = new BasicAttributes(true);

				// String usrName = "CN=" + userName + ",CN=Users,DC=" +
				// getDomain()
				// + ",DC=com";

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(ldapCon.getDomain()).append(
								",DC=com");

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);
				attrs.put("cn", userName);
				attrs.put("givenName", userName);
				// attrs.put("sn", "Ravi");

				attrs.put("displayName", userName);
				attrs.put("description", "Research");
				attrs.put("userPrincipalName", userName);
				attrs.put("mail", mailID);
				attrs.put("telephoneNumber", phoneNumber);

				try {
					result = ldapContext.createSubcontext(sbusrName.toString(),
							attrs);
					status = true;

				} catch (NameAlreadyBoundException e) {
					logger.error("Error in createUser:"+e.getMessage());
					status = false;
				}

				System.out.println("User Created Successfully..");
			}

			else {
				status = false;
				System.out.println("User Already Exists..");
			}

		} catch (NamingException e) {
			logger.error("Error in createUser:"+e.getMessage());
			System.err.println("Problem creating object: " + e);

			status = false;
		} finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for creating user
	 * 
	 * @param userName
	 * @param mailID
	 * @param phoneNumber
	 * @return
	 */
	public boolean createUser(String userName, String mailID, String phoneNumber) {

		LdapContext ldapContext = null;

		@SuppressWarnings("unused")
		Context result = null;

		boolean status = false;

		try {

			ldapContext = getContext(getDomain());
			status = checkUserExist(userName, getDomain());

			if (status == false) {

				Attributes attrs = new BasicAttributes(true);

				// String usrName = "CN=" + userName + ",CN=Users,DC=" +
				// getDomain()
				// + ",DC=com";

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(getDomain()).append(
								",DC=com");

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);
				attrs.put("cn", userName);
				attrs.put("givenName", userName);

				attrs.put("displayName", userName);
				attrs.put("description", "Research");
				attrs.put("userPrincipalName", userName);
				attrs.put("mail", mailID);
				attrs.put("telephoneNumber", phoneNumber);

				try {

					result = ldapContext.createSubcontext(sbusrName.toString(),
							attrs);
					status = true;

				} catch (NameAlreadyBoundException e) {
					logger.error("Error in createUser:"+e.getMessage());
					status = false;
				}

			} else {
				status = false;
				System.out.println("User Already Exists..");
			}

		}

		catch (NamingException e) {
			logger.error("Error in createUser:"+e.getMessage());
			System.err.println("Problem creating object: " + e);
			status = false;
		} finally {
			closeConnection(ldapContext);

		}

		return status;
	}

	/***************************************************************************
	 * This method is used for creating user under a specific domain
	 * 
	 * @param userName
	 *            (Cannot be changed)
	 * @param mailID (
	 *            Can be changed)
	 * @param phoneNumber
	 *            (Can be changed)
	 * @param domain
	 * @return
	 */
	public boolean modifyUser(String userName, String mailID,
			String phoneNumber, String domain) {

		LdapContext ldapContext = null;

		@SuppressWarnings("unused")
		Context result = null;

		boolean status = false;

		try {

			setDomain(domain);
			ldapContext = getContext();

			StringBuffer sbusrName = new StringBuffer();

			sbusrName.append("CN=").append(userName).append(",CN=Users,DC=")
					.append(getDomain()).append(",DC=com");

			try {
				// result = ldapContext.createSubcontext(sbusrName.toString(),
				// attrs);

				ModificationItem mods[] = new ModificationItem[2];

				mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
						new BasicAttribute("mail", mailID));

				mods[1] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
						new BasicAttribute("telephoneNumber", phoneNumber));

				ldapContext.modifyAttributes(sbusrName.toString(), mods);

				status = true;

			} catch (NameAlreadyBoundException e) {
				logger.error("Error in modifyUser:"+e.getMessage());
				status = false;
			}

			System.out.println("User Created Successfully..");
		}

		catch (NamingException e) {
			logger.error("Error in modifyUser:"+e.getMessage());
			System.err.println("Problem creating object: " + e);

			status = false;
		} finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for creating user under a specific group and domain
	 * 
	 * @param userName
	 * @param mailID
	 * @param phoneNumber
	 * @param groupName
	 * @param domain
	 * @return
	 */
	public boolean userCreateAddGroup(String userName, String password,
			String mailID, String phoneNumber, String groupName, String domain) {

		LdapContext ldapContext = null;

		boolean userStatus = false;
		boolean groupStatus = false;
		String grpName = null;

		try {

			//setDomain(domain);
			userStatus = userCreateWithPassword(userName, mailID, password,
					phoneNumber, domain);
            LDAPConnection ldapCon = new LDAPConnection();
			
			ldapCon.setDomain(domain);
			
			//ldapContext = ldapCon.getContext(ldapCon.getDomain());
			ldapContext = ldapCon.getContext(ldapCon.getDomain());
			//ldapContext = getContext(domain);

			if (userStatus)

			{

				Attributes attrs = new BasicAttributes(true);

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);
				attrs.put("cn", userName);
				attrs.put("givenName", userName);

				// Create the context

				// userName = "CN=" + userName + ",CN=Users,DC=" + getDomain()
				// + ",DC=com";

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(ldapCon.getDomain()).append(
								",DC=com");

				// grpName = "CN=" + groupName + ",CN=Users,DC=" + getDomain()
				// + ",DC=com";

				StringBuffer sbgrpName = new StringBuffer();

				sbgrpName.append("CN=").append(groupName).append(
						",CN=Users,DC=").append(ldapCon.getDomain()).append(",DC=com");

				try {
					// result = context.createSubcontext(userName,
					// attrs);

					ModificationItem member[] = new ModificationItem[1];

					// Add user to Group
					member[0] = new ModificationItem(DirContext.ADD_ATTRIBUTE,
							new BasicAttribute("member", sbusrName.toString()));

					// attrs.put("objectClass", "group");

					try {

						ldapContext.modifyAttributes(sbgrpName.toString(),
								member);
						groupStatus = true;
					} catch (NameNotFoundException e) {
						groupStatus=createGroup(groupName, ldapCon.getDomain());
						ldapContext.modifyAttributes(sbgrpName.toString(),
								member);
						//groupStatus = true;
					}

					System.out
							.println("User:" + sbusrName.toString()
									+ "added to Group:" + groupName
									+ " successfully..");

					//groupStatus = true;
				} catch (NamingException e) {
					logger.error("Error in userCreateAddGroup:"+e.getMessage());
					System.err.println("Problem adding user to group: " + e);

					groupStatus = false;
				}
			} else {

				System.out.println("Problem occurred while creating user..");
				groupStatus = false;

			}

		} catch (Exception e) {

			logger.error("Error in userCreateAddGroup:"+e.getMessage());
			groupStatus = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return groupStatus;
	}

	/***************************************************************************
	 * This method is used for modifying user under a specific group and domain
	 * 
	 * @param userName
	 * @param mailID
	 * @param phoneNumber
	 * @param groupName
	 * @param domain
	 * @return
	 */
	public boolean userModifyAddGroup(String userName, String password,
			String mailID, String phoneNumber, String groupName, String domain) {

		LdapContext ldapContext = null;

		boolean userStatus = false;
		boolean groupStatus = false;
		boolean result = false;

		try {

			setDomain(domain);

			userStatus = authenticateUser(userName, password);

			ldapContext = getContext(domain);

			if (userStatus)

			{

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(getDomain()).append(
								",DC=com");

				// grpName = "CN=" + groupName + ",CN=Users,DC=" + getDomain()
				// + ",DC=com";

				StringBuffer sbgrpName = new StringBuffer();

				sbgrpName.append("CN=").append(groupName).append(
						",CN=Users,DC=").append(getDomain()).append(",DC=com");

				try {

					userStatus = modifyUser(userName, mailID, phoneNumber,
							domain);

					if (userStatus) {

						ModificationItem member[] = new ModificationItem[1];

						// Add user to Group
						member[0] = new ModificationItem(
								DirContext.REPLACE_ATTRIBUTE,
								new BasicAttribute("member", sbusrName
										.toString()));

						// attrs.put("objectClass", "group");

						try {

							ldapContext.modifyAttributes(sbgrpName.toString(),
									member);
							groupStatus = true;
						} catch (NameNotFoundException e) {
							logger.error("Error in userModifyAddGroup:"+e.getMessage());
							System.out.println("Group Not found..");
						}

						System.out.println("User:" + sbusrName.toString()
								+ "added to Group:" + groupName
								+ " successfully..");

						groupStatus = true;
					} else {
						groupStatus = false;
					}

				} catch (NamingException e) {
					logger.error("Error in userModifyAddGroup:"+e.getMessage());
					System.err.println("Problem adding user to group: " + e);

					groupStatus = false;
				}
			} else {

				System.out.println("Problem occurred while creating user..");
				groupStatus = false;

			}

		} catch (Exception e) {

			logger.error("Error in userModifyAddGroup:"+e.getMessage());
			groupStatus = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return groupStatus;
	}

	/***************************************************************************
	 * This method is used for creating user under a specific group
	 * 
	 * @param userName
	 * @param mailID
	 * @param phoneNumber
	 * @param groupName
	 * @return
	 */
	public boolean userCreateAddGroup(String userName, String password,
			String mailID, String phoneNumber, String groupName) {

		LdapContext ldapContext = null;

		boolean userStatus = false;
		boolean groupStatus = false;
		String grpName = null;

		try {

			userStatus = userCreateWithPassword(userName, mailID, password,
					phoneNumber);

			ldapContext = getContext(getDomain());

			if (userStatus)

			{

				Attributes attrs = new BasicAttributes(true);

				attrs.put("objectClass", "user");
				attrs.put("samAccountName", userName);
				attrs.put("cn", userName);
				attrs.put("givenName", userName);
				// Create the context

				// userName = "CN=" + userName + ",CN=Users,DC=" + getDomain()
				// + ",DC=com";

				StringBuffer sbusrName = new StringBuffer();

				sbusrName.append("CN=").append(userName)
						.append(",CN=Users,DC=").append(getDomain()).append(
								",DC=com");

				// grpName = "CN=" + groupName + ",CN=Users,DC=" + getDomain()
				// + ",DC=com";

				StringBuffer sbgrpName = new StringBuffer();

				sbgrpName.append("CN=").append(groupName).append(
						",CN=Users,DC=").append(getDomain()).append(",DC=com");

				try {
					// result = context.createSubcontext(userName,
					// attrs);

					ModificationItem member[] = new ModificationItem[2];

					// Add user to Group
					member[0] = new ModificationItem(DirContext.ADD_ATTRIBUTE,
							new BasicAttribute("member", sbusrName.toString()));

					member[1] = new ModificationItem(
							DirContext.REPLACE_ATTRIBUTE, new BasicAttribute(
									"groupScope", sbusrName.toString()));

					// attrs.put("objectClass", "group");
					try {

						ldapContext.modifyAttributes(sbgrpName.toString(),
								member);
						groupStatus = true;
					} catch (NameNotFoundException e) {
						createGroup(groupName, getDomain());
						ldapContext.modifyAttributes(sbgrpName.toString(),
								member);
					}

					System.out.println("User:" + userName + "added to Group:"
							+ groupName + " successfully..");

					groupStatus = true;

				} catch (NamingException e) {
					logger.error("Error in userCreateAddGroup:"+e.getMessage());
					System.err.println("Problem adding user to group: " + e);

					groupStatus = false;
				}
			} else {

				System.out.println("Problem occurred while creating user..");
				groupStatus = false;

			}

		} catch (Exception e) {

			logger.error("Error in userCreateAddGroup:"+e.getMessage());
			groupStatus = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return groupStatus;
	}

	/***************************************************************************
	 * This method is used for creating user with password under a specific
	 * domain
	 * 
	 * @param userName
	 * @param mailID
	 * @param password
	 * @param phoneNumber
	 * @param domain
	 * @return
	 */
	public boolean userCreateWithPassword(String userName, String mailID,
			String password, String phoneNumber, String domain) {

		LdapContext ldapContext = null;
		boolean status = false;

		try {
			 LDAPConnection ldapCon = new LDAPConnection();
				
				ldapCon.setDomain(domain);
				
				//ldapContext = ldapCon.getContext(ldapCon.getDomain());
				ldapContext = ldapCon.getContext(ldapCon.getDomain());
			//setDomain(domain);
			//ldapContext = getContext();

			if (status == false) {

				status = createUser(userName, mailID, phoneNumber, domain);

				if (status) {
					try
					{

					ModificationItem[] mods = new ModificationItem[2];

					mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute("unicodePwd", LDAPUtil
							.getPassword(password)));
					mods[1] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute("userAccountControl", Integer.toString(LDAPConstants.UF_NORMAL_ACCOUNT + LDAPConstants.UF_PASSWORD_EXPIRED)));

					// String usrName = "CN=" + userName + ",CN=Users,DC="
					// + getDomain() + ",DC=com";

					StringBuffer sbusrName = new StringBuffer();

					sbusrName.append("CN=").append(userName).append(
							",CN=Users,DC=").append(ldapCon.getDomain()).append(
							",DC=com");

					// Perform the update
					ldapContext.modifyAttributes(sbusrName.toString(), mods);
					status = true;
					System.out
							.println("User and password created successfully..");
					}
					catch(Exception e)
					{
						logger.error("Error in userCreateWithPassword:"+e.getMessage());
						removeUser(userName);
					}
				
				}else {
					status = false;
				}
			} else {
				status = false;
				System.out.println("User Already Exists..");
			}

		} catch (Exception e) {

			logger.error("Error in userCreateWithPassword:"+e.getMessage());
			status = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	/***************************************************************************
	 * This method is used for creating user with password
	 * 
	 * @param userName
	 * @param mailID
	 * @param password
	 * @param phoneNumber
	 * @return
	 */
	public boolean userCreateWithPassword(String userName, String mailID,
			String password, String phoneNumber) {

		LdapContext ldapContext = null;
		boolean status = false;

		try {

			ldapContext = getContext(getDomain());

			status = checkUserExist(userName, getDomain());

			if (status == false) {

				status = createUser(userName, mailID, phoneNumber);

				if (status) {
					
					try
					{
					ModificationItem[] mods = new ModificationItem[2];

					mods[0] = new ModificationItem(
							DirContext.REPLACE_ATTRIBUTE, new BasicAttribute(
									"unicodePwd", LDAPUtil
											.getPassword(password)));
					mods[1] = new ModificationItem(
							DirContext.REPLACE_ATTRIBUTE,
							new BasicAttribute(
									"userAccountControl",
									Integer
											.toString(LDAPConstants.UF_NORMAL_ACCOUNT
													+ LDAPConstants.UF_PASSWORD_EXPIRED)));

					// String usrName = "CN=" + userName + ",CN=Users,DC="
					// + getDomain() + ",DC=com";

					StringBuffer sbusrName = new StringBuffer();

					sbusrName.append("CN=").append(userName).append(
							",CN=Users,DC=").append(getDomain()).append(
							",DC=com");
						System.out.println("SB USERNAME"+sbusrName);
					// Perform the update
					ldapContext.modifyAttributes(sbusrName.toString(), mods);

					status = true;
					System.out
							.println("User and password created successfully..");

				} 
					catch(Exception e)
					{
						logger.error("Error in userCreateWithPassword:"+e.getMessage());
						removeUser(userName);
					
					}
				}
						else {
					
					status = false;
				}
			} else {
				status = false;
				System.out.println("User already Exists");
			}

		} catch (Exception e) {

			logger.error("Error in userCreateWithPassword:"+e.getMessage());
			status = false;
		}

		finally {
			closeConnection(ldapContext);

		}
		return status;
	}

	public LdapContext getContext() {

		return LDAPConnection().getContext();
	}

	public boolean getContext(String user, String password, String domain) {

		return LDAPConnection().getContext(user, password, domain);
	}

	public boolean getContext(String user, String password) {

		return LDAPConnection().getContext(user, password);
	}

	public LdapContext getContext(String domain) {
		return LDAPConnection().getContext(domain);
	}

	public void closeConnection(LdapContext lc) {

		LDAPConnection().closeConnection(lc);

	}

	public String getDomain() {
		return LDAPConnection().getDomain();
	}

	public void setDomain(String domain) {
		LDAPConnection().setDomain(domain);
	}

	public String getSearchBase() {
		return LDAPConnection().getSearchBase();
	}

	public static void main(String[] args) {

		ADUserAccess a = new ADUserAccess();

		// Create user

		boolean status = false;

		//status = a.createUser("Test145555", "<EMAIL>", "811 181 8816");

		/*if (status)
			System.out.println("User Created successfully..");

		else
			System.out.println("User Not Created..");*/
	//	status = a.userCreateWithPassword("metest9", "<EMAIL>", "test@123", "9790405358", "testdomain");
		//status = a.authenticateUser("abhi", "abhilash","localhost");
		// status = a.authenticateUser("vdn3249", "Dilse@2050");
		System.out.println("status::"+status);
		 /*   status =a.forcechangePassword("333", "test@123", "testdomain");
				 if (status)
				 System.out.println("User Created with password successfully..");
				 else`````````
				 System.out.println("User Not Created..");*/
				 /*a.changePassword("Test13",  "password@123#",  "password@123#");
				
					System.out.println("Status:" + status);
					status = a.createGroup("TestGroup12");
					 if (status)
					 System.out.println("Group created successfully..");
					 else
					System.out.println("Group creation unsuccessful..");
					 a.getUsersList("TestGroup");
					 status = a.userModifyAddGroup("Test13", "password@123#",
							  "<EMAIL>", "811 181 8819", "TestGroup12", "testdomain");
							  
							 if (status)
							  
							  System.out.println("User modified to group successfully.."); else
							  System.out.println("User modification to group failed..");*/

	}
}
