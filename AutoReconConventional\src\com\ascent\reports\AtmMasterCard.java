package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class AtmMasterCard {
	
	private static Logger logger = LogManager.getLogger(AtmMasterCard.class.getName());
	
	private static final String ATMMASTERCARD_INTERNAL_RECONCILED_RECON = "ATMMASTERCARD_INTERNAL_RECONCILED_RECON";
	private static final String ATMMASTERCARD_EXTERNAL_RECONCILED_RECON = "ATMMASTERCARD_EXTERNAL_RECONCILED_RECON";
	private static final String ATMMASTERCARD_INTERNAL_UNRECONCILED_RECON = "ATMMASTERCARD_INTERNAL_UNRECONCILED_RECON";
	private static final String ATMMASTERCARD_EXTERNAL_UNRECONCILED_RECON = "ATMMASTERCARD_EXTERNAL_UNRECONCILED_RECON";
	private static final String ATMMASTERCARD_INTERNAL_SUPPRESS_RECON = "ATMMASTERCARD_INTERNAL_SUPPRESS_RECON";
	private static final String ATMMASTERCARD_EXTERNAL_SUPPRESS_RECON = "ATMMASTERCARD_EXTERNAL_SUPPRESS_RECON";
	private static final String ATMMASTERCARD_AGING_RECON = "ATMMASTERCARD_AGING_RECON";
	private static final String ATMMASTERCARD_INTERNAL_DRCR = "ATMMASTERCARD_INTERNAL_DRCR";
	private static final String ATMMASTERCARD_EXTERNAL_DRCR = "ATMMASTERCARD_EXTERNAL_DRCR";
	private static final String ATMMASTERCARD_RECONCILE_DRCR = "ATMMASTERCARD_RECONCILE_DRCR";
	private static final String ATMMASTERCARD_UNRECONCILE_DRCR = "ATMMASTERCARD_UNRECONCILE_DRCR";
	
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {
		
		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		//String driver = bundle.getString("driver");
		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> AtmMasterCardInternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CARD NUM", rset.getString(2));
				map.put("ACTUAL REF NUM", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("TXN REF NUM", rset.getString(6));
				map.put("TRAN PARTICULAR", rset.getString(7));
				map.put("TRAN AMOUNT", rset.getString(8));
				map.put("DRCR IND", rset.getString(9));
				map.put("TRAN RMKS", rset.getString(10));
				map.put("COMMENTS", rset.getString(11));
				map.put("VERSION", rset.getString(12));
				map.put("ACTIVE INDEX", rset.getString(13));
				map.put("WORKFLOW STATUS", rset.getString(14));
				map.put("UPDATED ON", rset.getString(15));
				map.put("CREATED ON", rset.getString(16));
				map.put("RECON STATUS", rset.getString(17));
				map.put("RECON ID", rset.getString(18));
				map.put("ACTIVITY COMMENTS", rset.getString(19));
				map.put("MAIN REV IND", rset.getString(20));
				map.put("OPERATION", rset.getString(21));
				map.put("FILE NAME", rset.getString(22));
			    map.put("BUSINESS AREA", rset.getString(23));
				
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> AtmMasterCardExternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("MSG TYPE IND", rset.getString(2));
				map.put("SWITCH SER NUM", rset.getString(3));
				map.put("PROCESSOR ACQ", rset.getString(4));
				map.put("PROCESSOR ID", rset.getString(5));
				map.put("TRN DATE", rset.getString(6));
				map.put("TRN TIME", rset.getString(7));
				map.put("PAN LENGTH", rset.getString(8));
				map.put("PRIMARY ACC NUM", rset.getString(9));
				map.put("PROCESSING CODE", rset.getString(10));
				map.put("TRACE NUM1", rset.getString(11));
				map.put("MERCHANT TYPE", rset.getString(12));
				map.put("POS ENTRY", rset.getString(13));
				map.put("REF NUMBER", rset.getString(14));
				map.put("ACQ INST ID", rset.getString(15));
				map.put("TERMINAL ID", rset.getString(16));
				map.put("BRAND", rset.getString(17));
				map.put("ADVICE REASON CODE", rset.getString(18));
				map.put("INTRACUR AGRMNT CODE", rset.getString(19));
				map.put("AUTHORIZATION ID", rset.getString(20));
				map.put("CUR CODE TRN", rset.getString(21));
				map.put("IMP DEC TRN", rset.getString(22));
			    map.put("COMPTD AMT TRN LOCAL", rset.getString(23));
			    map.put("COMPTD AMT TRN LOCAL DR CR IND", rset.getString(24));
			    map.put("CASH BACK AMT LOCAL", rset.getString(25));
				map.put("CASH BACK AMT LOCAL DR CR IND", rset.getString(26));
				map.put("ACCESS FEE LOCAL", rset.getString(27));
				map.put("ACCESS FEE LOCAL DR CR IND", rset.getString(28));
				map.put("CUR CODE STMNT", rset.getString(29));
				map.put("IMPLIED DEC STMNT", rset.getString(30));
				map.put("CONVERSION RATE STMNT", rset.getString(31));
				map.put("COMPTD AMT STMNT", rset.getString(32));
				map.put("COMPTD AMOUNT STMNT DR CR IND", rset.getString(33));
				map.put("INTRCHNG FEE", rset.getString(34));
				map.put("INTRCHNG FEE DR CR IND", rset.getString(35));
				map.put("SER LEVEL IND", rset.getString(36));
				map.put("RESP CODE", rset.getString(37));
				map.put("FILLER1", rset.getString(38));
				map.put("POSITIVE ID IND", rset.getString(39));
				map.put("ATM SURCHRGE FREE PRGM ID", rset.getString(40));
				map.put("CROSS BORDER IND", rset.getString(41));
				map.put("CROSS BORDER CUR INDD", rset.getString(42));
				map.put("VISA INTR SER ASS IND", rset.getString(43));
				map.put("REQ AMT TRN LOCAL", rset.getString(44));
				map.put("FILLER2", rset.getString(45));
				map.put("TRACE NUM ADJ TRNS", rset.getString(46));
				map.put("FILLER3", rset.getString(47));
				map.put("COMMENTS", rset.getString(48));
				map.put("VERSION", rset.getString(49));
				map.put("ACTIVE INDEX", rset.getString(50));
				map.put("WORKFLOW STATUS", rset.getString(51));
				map.put("UPDATED ON", rset.getString(52));
				map.put("CREATED ON", rset.getString(53));
				map.put("RECON STATUS", rset.getString(54));
				map.put("RECON ID", rset.getString(55));
				map.put("TACTIVITY COMMENTS", rset.getString(56));
				map.put("MAIN REV IND", rset.getString(57));
				map.put("OPERATION", rset.getString(58));
				map.put("FILE NAME", rset.getString(59));
				map.put("BUSINESS AREA", rset.getString(60));
				map.put("RESP CODE1", rset.getString(61));
				
				
				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> AtmMasterCardInternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CARD NUM", rset.getString(2));
				map.put("ACTUAL REF NUM", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("TXN REF NUM", rset.getString(6));
				map.put("TRAN PARTICULAR", rset.getString(7));
				map.put("TRAN AMOUNT", rset.getString(8));
				map.put("DRCR IND", rset.getString(9));
				map.put("TRAN RMKS", rset.getString(10));
				map.put("COMMENTS", rset.getString(11));
				map.put("VERSION", rset.getString(12));
				map.put("ACTIVE INDEX", rset.getString(13));
				map.put("WORKFLOW STATUS", rset.getString(14));
				map.put("UPDATED ON", rset.getString(15));
				map.put("CREATED ON", rset.getString(16));
				map.put("RECON STATUS", rset.getString(17));
				map.put("RECON ID", rset.getString(18));
				map.put("ACTIVITY COMMENTS", rset.getString(19));
				map.put("MAIN REV IND", rset.getString(20));
				map.put("OPERATION", rset.getString(21));
				map.put("FILE NAME", rset.getString(22));
			    map.put("BUSINESS AREA", rset.getString(23));
			    map.put("AGE", rset.getString(24));
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> AtmMasterCardExternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("MSG TYPE IND", rset.getString(2));
				map.put("SWITCH SER NUM", rset.getString(3));
				map.put("PROCESSOR ACQ", rset.getString(4));
				map.put("PROCESSOR ID", rset.getString(5));
				map.put("TRN DATE", rset.getString(6));
				map.put("TRN TIME", rset.getString(7));
				map.put("PAN LENGTH", rset.getString(8));
				map.put("PRIMARY ACC NUM", rset.getString(9));
				map.put("PROCESSING CODE", rset.getString(10));
				map.put("TRACE NUM1", rset.getString(11));
				map.put("MERCHANT TYPE", rset.getString(12));
				map.put("POS ENTRY", rset.getString(13));
				map.put("REF NUMBER", rset.getString(14));
				map.put("ACQ INST ID", rset.getString(15));
				map.put("TERMINAL ID", rset.getString(16));
				map.put("BRAND", rset.getString(17));
				map.put("ADVICE REASON CODE", rset.getString(18));
				map.put("INTRACUR AGRMNT CODE", rset.getString(19));
				map.put("AUTHORIZATION ID", rset.getString(20));
				map.put("CUR CODE TRN", rset.getString(21));
				map.put("IMP DEC TRN", rset.getString(22));
			    map.put("COMPTD AMT TRN LOCAL", rset.getString(23));
			    map.put("COMPTD AMT TRN LOCAL DR CR IND", rset.getString(24));
			    map.put("CASH BACK AMT LOCAL", rset.getString(25));
				map.put("CASH BACK AMT LOCAL DR CR IND", rset.getString(26));
				map.put("ACCESS FEE LOCAL", rset.getString(27));
				map.put("ACCESS FEE LOCAL DR CR IND", rset.getString(28));
				map.put("CUR CODE STMNT", rset.getString(29));
				map.put("IMPLIED DEC STMNT", rset.getString(30));
				map.put("CONVERSION RATE STMNT", rset.getString(31));
				map.put("COMPTD AMT STMNT", rset.getString(32));
				map.put("COMPTD AMOUNT STMNT DR CR IND", rset.getString(33));
				map.put("INTRCHNG FEE", rset.getString(34));
				map.put("INTRCHNG FEE DR CR IND", rset.getString(35));
				map.put("SER LEVEL IND", rset.getString(36));
				map.put("RESP CODE", rset.getString(37));
				map.put("FILLER1", rset.getString(38));
				map.put("POSITIVE ID IND", rset.getString(39));
				map.put("ATM SURCHRGE FREE PRGM ID", rset.getString(40));
				map.put("CROSS BORDER IND", rset.getString(41));
				map.put("CROSS BORDER CUR INDD", rset.getString(42));
				map.put("VISA INTR SER ASS IND", rset.getString(43));
				map.put("REQ AMT TRN LOCAL", rset.getString(44));
				map.put("FILLER2", rset.getString(45));
				map.put("TRACE NUM ADJ TRNS", rset.getString(46));
				map.put("FILLER3", rset.getString(47));
				map.put("COMMENTS", rset.getString(48));
				map.put("VERSION", rset.getString(49));
				map.put("ACTIVE INDEX", rset.getString(50));
				map.put("WORKFLOW STATUS", rset.getString(51));
				map.put("UPDATED ON", rset.getString(52));
				map.put("CREATED ON", rset.getString(53));
				if(rset.getString(54)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(54));
				map.put("RECON ID", rset.getString(55));
				map.put("TACTIVITY COMMENTS", rset.getString(56));
				map.put("MAIN REV IND", rset.getString(57));
				map.put("OPERATION", rset.getString(58));
				map.put("FILE NAME", rset.getString(59));
				map.put("BUSINESS AREA", rset.getString(60));
				map.put("RESP CODE1", rset.getString(61));
				map.put("AGE", rset.getString(62));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> AtmMasterCardInternalSuppress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_INTERNAL_SUPPRESS_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CARD NUM", rset.getString(2));
				map.put("ACTUAL REF NUM", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("TXN REF NUM", rset.getString(6));
				map.put("TRAN PARTICULAR", rset.getString(7));
				map.put("TRAN AMOUNT", rset.getString(8));
				map.put("DRCR IND", rset.getString(9));
				map.put("TRAN RMKS", rset.getString(10));
				map.put("COMMENTS", rset.getString(11));
				map.put("VERSION", rset.getString(12));
				map.put("ACTIVE INDEX", rset.getString(13));
				map.put("WORKFLOW STATUS", rset.getString(14));
				map.put("UPDATED ON", rset.getString(15));
				map.put("CREATED ON", rset.getString(16));
				map.put("RECON STATUS", rset.getString(17));
				map.put("RECON ID", rset.getString(18));
				map.put("ACTIVITY COMMENTS", rset.getString(19));
				map.put("MAIN REV IND", rset.getString(20));
				map.put("OPERATION", rset.getString(21));
				map.put("FILE NAME", rset.getString(22));
			    map.put("BUSINESS AREA", rset.getString(23));
			    
			    map.put("VERIFIER USER ID", rset.getString(24));
				map.put("VERIFIER COMMENTS", rset.getString(25));
				map.put("MAKER USER ID", rset.getString(26));
				map.put("MAKER COMMENTS", rset.getString(27));
				
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> AtmMasterCardExternalSuppress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_EXTERNAL_SUPPRESS_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("MSG TYPE IND", rset.getString(2));
				map.put("SWITCH SER NUM", rset.getString(3));
				map.put("PROCESSOR ACQ", rset.getString(4));
				map.put("PROCESSOR ID", rset.getString(5));
				map.put("TRN DATE", rset.getString(6));
				map.put("TRN TIME", rset.getString(7));
				map.put("PAN LENGTH", rset.getString(8));
				map.put("PRIMARY ACC NUM", rset.getString(9));
				map.put("PROCESSING CODE", rset.getString(10));
				map.put("TRACE NUM1", rset.getString(11));
				map.put("MERCHANT TYPE", rset.getString(12));
				map.put("POS ENTRY", rset.getString(13));
				map.put("REF NUMBER", rset.getString(14));
				map.put("ACQ INST ID", rset.getString(15));
				map.put("TERMINAL ID", rset.getString(16));
				map.put("BRAND", rset.getString(17));
				map.put("ADVICE REASON CODE", rset.getString(18));
				map.put("INTRACUR AGRMNT CODE", rset.getString(19));
				map.put("AUTHORIZATION ID", rset.getString(20));
				map.put("CUR CODE TRN", rset.getString(21));
				map.put("IMP DEC TRN", rset.getString(22));
			    map.put("COMPTD AMT TRN LOCAL", rset.getString(23));
			    map.put("COMPTD AMT TRN LOCAL DR CR IND", rset.getString(24));
			    map.put("CASH BACK AMT LOCAL", rset.getString(25));
				map.put("CASH BACK AMT LOCAL DR CR IND", rset.getString(26));
				map.put("ACCESS FEE LOCAL", rset.getString(27));
				map.put("ACCESS FEE LOCAL DR CR IND", rset.getString(28));
				map.put("CUR CODE STMNT", rset.getString(29));
				map.put("IMPLIED DEC STMNT", rset.getString(30));
				map.put("CONVERSION RATE STMNT", rset.getString(31));
				map.put("COMPTD AMT STMNT", rset.getString(32));
				map.put("COMPTD AMOUNT STMNT DR CR IND", rset.getString(33));
				map.put("INTRCHNG FEE", rset.getString(34));
				map.put("INTRCHNG FEE DR CR IND", rset.getString(35));
				map.put("SER LEVEL IND", rset.getString(36));
				map.put("RESP CODE", rset.getString(37));
				map.put("FILLER1", rset.getString(38));
				map.put("POSITIVE ID IND", rset.getString(39));
				map.put("ATM SURCHRGE FREE PRGM ID", rset.getString(40));
				map.put("CROSS BORDER IND", rset.getString(41));
				map.put("CROSS BORDER CUR INDD", rset.getString(42));
				map.put("VISA INTR SER ASS IND", rset.getString(43));
				map.put("REQ AMT TRN LOCAL", rset.getString(44));
				map.put("FILLER2", rset.getString(45));
				map.put("TRACE NUM ADJ TRNS", rset.getString(46));
				map.put("FILLER3", rset.getString(47));
				map.put("COMMENTS", rset.getString(48));
				map.put("VERSION", rset.getString(49));
				map.put("ACTIVE INDEX", rset.getString(50));
				map.put("WORKFLOW STATUS", rset.getString(51));
				map.put("UPDATED ON", rset.getString(52));
				map.put("CREATED ON", rset.getString(53));
				map.put("RECON STATUS", rset.getString(54));
				map.put("RECON ID", rset.getString(55));
				map.put("TACTIVITY COMMENTS", rset.getString(56));
				map.put("MAIN REV IND", rset.getString(57));
				map.put("OPERATION", rset.getString(58));
				map.put("FILE NAME", rset.getString(59));
				map.put("BUSINESS AREA", rset.getString(60));
				map.put("RESP CODE1", rset.getString(61));
				
				 map.put("VERIFIER USER ID", rset.getString(62));
					map.put("VERIFIER COMMENTS", rset.getString(63));
					map.put("MAKER USER ID", rset.getString(64));
					map.put("MAKER COMMENTS", rset.getString(65));
				
				
				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> AtmMasterCardAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_AGING_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	
	public List<Map<String, Object>> atmmastercardInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmmastercardExternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_EXTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmmastercardReconcileDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_RECONCILE_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmmastercardUnreconcileDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMMASTERCARD_UNRECONCILE_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public static void main(String[] args) {
		AtmMasterCard c = new AtmMasterCard();
		
		c.AtmMasterCardInternalReconsiledMethod("2018-01-01", "2018-10-01");
		c.AtmMasterCardInternalUnReconsiledMethod("2018-01-01", "2018-10-01");
		c.AtmMasterCardExternalReconsiledMethod("2018-01-01", "2018-10-01");
		c.AtmMasterCardExternalUnReconsiledMethod("2018-01-01", "2018-10-01");
		c.AtmMasterCardInternalSuppress("2018-01-01", "2018-10-01");
		c.AtmMasterCardExternalSuppress("2018-01-01", "2018-10-01");
		c.AtmMasterCardAgingMethod();
		c.atmmastercardInternalDrcr("2018-01-01", "2018-10-01");
		c.atmmastercardExternalDrcr("2018-01-01", "2018-10-01");
	}

}
