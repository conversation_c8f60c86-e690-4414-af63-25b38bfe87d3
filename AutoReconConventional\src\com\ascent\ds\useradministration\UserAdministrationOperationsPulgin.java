package com.ascent.ds.useradministration;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.administration.AuthorizationBean;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.ds.login.PasswordPolicy;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.Role;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class UserAdministrationOperationsPulgin extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = -2875895744205343328L;
	private static final String GET_MAX_ROLE_ID = "GET_MAX_ROLE_ID";
	private static final String ERR_MASSAGE = "Transaction not submitted for Approval , Try Again..!!";
	private static final String GET_MAX_DEP_ID = "GET_MAX_DEP_ID";
	private static final String INSERT_PASS_POLICY_QRY = "INSERT_PASS_POLICY";
	private static final String UPDATE_PASS_POLICY_QRY = "UPDATE_PASS_POLICY";
	private static final String DELETE_PASS_POLICY_QRY = "DELETE_PASS_POLICY";
	private static final String DEACTIVATE_ALL_PASS_POLICY = "DEACTIVATE_ALL_PASS_POLICY";
	
	private static Logger logger = LogManager.getLogger(AddUserAdminPlugin.class);

	public UserAdministrationOperationsPulgin() {
		
	}

	Connection connection = null;
	private Role role;

	public DSResponse executeFetch(final DSRequest dsRequest) {

		DSResponse dsResponse = new DSResponse();
		Map<Object, Object> criteriaMap = dsRequest.getValues();
		
		HttpSession httpSession = dsRequest.getHttpServletRequest().getSession();
		String userSelectedBusinessArea = (String) httpSession.getAttribute("user_selected_business_area");
		String userSelectedRecon = (String) httpSession.getAttribute("user_selected_recon");
		User user = (User) httpSession.getAttribute("userId");
		int serverPort=dsRequest.getHttpServletRequest().getServerPort();
		String contextPath=	dsRequest.getHttpServletRequest().getContextPath();
		criteriaMap.put("serverPort", serverPort);
		criteriaMap.put("contextPath", contextPath);
		
		Map<String, Object> processData = processData(criteriaMap, user, userSelectedBusinessArea, userSelectedRecon);
		dsResponse.setData(processData);
		return dsResponse;
	}

	public Map<String, Object> processData(Map<Object, Object> criteriaMap, User user, String userSelectedBusinessArea,
			String userSelectedRecon) {

		boolean successFlag = false;
		boolean successFlagEdit = false;
		boolean successFlagdelete = false;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		AuthorizationBean authorizationBean = new AuthorizationBean();
		List<Map<String, Object>> record = new ArrayList<Map<String, Object>>();
		String tableName = (String) criteriaMap.get("tableName");
		String dsName = (String) criteriaMap.get("dsName");
		String action = (String) criteriaMap.get("action");
		Map<String, Object> recordMap = (Map<String, Object>) criteriaMap.get("data");
		System.out.println(recordMap);
		Integer roleId = 0, maxDeptId = 0;
		recordMap.put("status", PENDING_APPROVAL);
		recordMap.put("active_index", "Y");
		recordMap.put("created_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
		recordMap.put("updated_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
		Calendar calendar = Calendar.getInstance();
		CustomerDao customerDao = new CustomerDao();
		PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
		Integer pwdAge = loadPasswordPolicy.getPwdMaxAge();
		calendar.add(Calendar.MONTH, pwdAge);
		recordMap.put("pwd_exp_date", new Timestamp(calendar.getTimeInMillis()));
		
		recordMap.put("serverPort", criteriaMap.get("serverPort"));
		recordMap.put("contextPath", criteriaMap.get("contextPath"));
		record.add(recordMap);
		resultMap.put(TABLE_NAME, tableName);
		resultMap.put(DS_NAME, dsName);
		resultMap.put("userSelectedBusinessArea", userSelectedBusinessArea);
		resultMap.put("userSelectedRecon", userSelectedRecon);
		resultMap.put(SELECTED_RECORDS, record);
		resultMap.put("action", action);

		if (tableName.equalsIgnoreCase(departments_table)) {
			if (action.equalsIgnoreCase(SAVE)) {
				System.out.println("table is departments " + tableName);
				maxDeptId = this.getMaxDeptId();
				recordMap.put("dept_id", "DEPT_" + maxDeptId);
				recordMap.put("version", null);
				authorizationBean.saveDepartments(resultMap, user);

			} else if (action.equalsIgnoreCase(UPDATE)) {
				System.out.println("table is departments " + tableName);
				recordMap.put("dept_id", recordMap.get("dept_id"));
				authorizationBean.saveDepartments(resultMap, user);
			} else {
				recordMap.put("dept_id", recordMap.get("dept_id"));
				authorizationBean.saveDepartments(resultMap, user);

			}
		} else if (tableName.equalsIgnoreCase(roles_table)) {
			if (action.equalsIgnoreCase(SAVE)) {
				roleId = this.getMaxRole();
				recordMap.put("roleid", roleId);
				recordMap.put("version", null);
				authorizationBean.saveRoles(resultMap, user);
			} else if (action.equalsIgnoreCase(UPDATE)) {
				recordMap.put("roleid", recordMap.get("roleid"));
				authorizationBean.saveRoles(resultMap, user);
			} else {
				recordMap.put("roleid", recordMap.get("roleid"));
				authorizationBean.saveRoles(resultMap, user);
			}

		} else if (tableName.equalsIgnoreCase(users_table)) {
			if (action.equalsIgnoreCase(SAVE)) 
			{
				recordMap.put("version", null);
				System.out.println("resultMap   "+resultMap);
				
				List<Map<String, Object>> resultDataList = (List) resultMap.get(SELECTED_RECORDS);
				Map map = resultDataList.get(0);
				
				boolean flagupper = true;
				boolean flagnumber = true;
				boolean flagSpecial = true;
				//LdapUser
				if(map.get("isLdapUser").equals("N")) {
					
					String pwd = map.get("password").toString();
					if(loadPasswordPolicy.getIsUpperCaseAllowed().equals("Y"))
					{
						Pattern textPattern = Pattern.compile("^(?=.*[A-Z]).+$");
						flagupper = textPattern.matcher(pwd).matches();
						
					} if(loadPasswordPolicy.getIsNumbersAllowed().equals("Y"))
					{
						Pattern textPattern = Pattern.compile("^(?=.*\\d).+$");
						flagnumber = textPattern.matcher(pwd).matches();
						
					} if(loadPasswordPolicy.getIsSpecialCharsAllowed().equals("Y"))
					{
						Pattern special = Pattern.compile ("["+loadPasswordPolicy.getSpecialChars()+"]");
						//flagSpecial = textPattern.matcher(pwd).matches();
						 Matcher hasSpecial = special.matcher(pwd);
						 flagSpecial = hasSpecial.find();
					}
					if(flagupper == false) {
						resultMap.put(STATUS, "Password Should Contain Atleast One UppserCase...!");
					} if(flagnumber == false) {
						resultMap.put(STATUS, "Password Should Contain Atleast One Number...!");
					} if(flagSpecial == false) {
						resultMap.put(STATUS, "Password Should Contain Atleast One Special Character...!");
					}
				}
				if(flagupper == true && flagnumber == true && flagSpecial == true){
					successFlag = authorizationBean.saveUsers(resultMap, user);

					if (successFlag == true) {
						resultMap.put(STATUS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
					} else {
						resultMap.put(STATUS, ERR_MASSAGE);
					}
				}
				

			} else if (action.equalsIgnoreCase(UPDATE)) {

				recordMap.put("version", criteriaMap.get("version"));
				recordMap.put("password", criteriaMap.get("password"));
				
				recordMap.put("updated_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				
				successFlagEdit = authorizationBean.saveUsers(resultMap, user);
				if (successFlagEdit == true) {
					resultMap.put(STATUS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				} else {
					resultMap.put(STATUS, ERR_MASSAGE);
				}
				/*
				 * boolean smsNotification ; if
				 * (recordMap.get("sms_notification").equals("Y")) {
				 * smsNotification = true;
				 * recordMap.put("sms_notification",smsNotification);
				 * 
				 * } else { smsNotification = false;
				 * recordMap.put("sms_notification",smsNotification); } boolean
				 * emailNotification ; if
				 * ((boolean)recordMap.get("email_notification").equals("Y")) {
				 * emailNotification = true;
				 * recordMap.put("email_notification",emailNotification); } else
				 * { emailNotification = false;
				 * recordMap.put("email_notification",emailNotification);
				 * 
				 * }
				 */
				
				
				
			} else {
				recordMap.put("version", recordMap.get("version"));
				successFlagdelete = authorizationBean.saveUsers(resultMap, user);
				if (successFlagdelete == true) {
					resultMap.put(STATUS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				} else {
					resultMap.put(STATUS, ERR_MASSAGE);
				}
			}

			if (successFlag) {
				// System.out.println("Submited succesfully"+successFlag);
			} else {
				// System.out.println("}}}}}}}}}}}}}}}}}}}}}}}"+successFlag);
			}
			if (successFlagEdit) {
				// System.out.println("Updated Succesfully
				// succesfully"+successFlagEdit);
			} else {
				// System.out.println("}}}}}}}}}}}}}}}}}}}}}}}"+successFlagEdit);
			}
			if (successFlagdelete) {
				// System.out.println("Deleted succesfully"+successFlagdelete);
			} else {
				// System.out.println("}}}}}}}}}}}}}}}}}}}}}}}"+successFlagdelete);
			}

		}
		else if(tableName.equalsIgnoreCase(password_policy)){
			@SuppressWarnings("unchecked")
			Map<String, Object> policyRecordMap = (Map<String, Object>) criteriaMap.get("data");
		//	System.out.println("password policy"+policyRecordMap);
			
			if(action!=null&&action.equalsIgnoreCase("SAVE")){
				System.out.println("SAVIND PASSWORD POLICY");
				//DELETED 
				if(criteriaMap.containsKey("data")){
					Map<String, Object> dataMap=(Map<String, Object>)criteriaMap.get("data");
				if(dataMap.get("use_policy")!=null&&dataMap.get("use_policy").toString().equalsIgnoreCase("Y")){
					updateAllPasswordPolicy(dataMap);
					savePasswordPolicy(dataMap);
				}else{
					savePasswordPolicy(dataMap);
				}
				
				}
				
			}else if(action!=null&&action.equalsIgnoreCase("UPDATE")){
				System.out.println("UPDATE PASSWORD POLICY");
		
				if(criteriaMap.containsKey("data")){
					Map<String, Object> dataMap=(Map<String, Object>)criteriaMap.get("data");
				if(dataMap.get("use_policy")!=null&&dataMap.get("use_policy").toString().equalsIgnoreCase("Y")){
					updateAllPasswordPolicy(dataMap);
					updatePasswordPolicy(dataMap);
				}else{
					//updateAllPasswordPolicy(policyRecordMap);
					updatePasswordPolicy(dataMap);
				}
				}
				
			}else if(action!=null&&action.equalsIgnoreCase("DELETE")){
				deletePasswordPolicy(policyRecordMap);
			}
			
		}

		return resultMap;
	}

	public Integer getMaxRole() {
		Integer newRoleId = null;
		this.role = new Role();
		CustomerDao cDao = new CustomerDao();
		Map<String, Object> paramValues = new HashMap<String, Object>();
		try {
			Map<String, Object> IdMap = cDao.getDataBaseMap(GET_MAX_ROLE_ID, paramValues);
			System.out.println(IdMap + "88");
			Integer integer = 0;
			if ((Integer) IdMap.get("maxId") != null) {
				integer = (Integer) IdMap.get("maxId");

			}
			int existingRoleId = integer;
			newRoleId = existingRoleId + 1;

			this.role.setRoleId(newRoleId);
			System.out.println(role.getRoleId());
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return newRoleId;

	}

	public Integer getMaxDeptId() {
		Integer newRoleId = null;
		this.role = new Role();
		CustomerDao cDao = new CustomerDao();
		Map<String, Object> paramValues = new HashMap<String, Object>();
		try {
			Map<String, Object> IdMap = cDao.getDataBaseMap(GET_MAX_DEP_ID, paramValues);
			System.out.println(IdMap + "88");
			Integer integer = 0;
			if (IdMap.get("maxId") == null) {
				int existingRoleId = integer;
				newRoleId = existingRoleId + 1;

			} else {
				integer = (Integer) IdMap.get("maxId");
				int existingRoleId = integer;
				newRoleId = existingRoleId + 1;

			}

		} catch (SQLException e) {
			 
			e.printStackTrace();
		}
		return newRoleId;

	}

	
	// added by ankush 7-10-2017
	public void savePasswordPolicy(Map<String, Object> valueMap) {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Connection connection = DbUtil.getConnection();
		InsertRegulator insertRegulator = new InsertRegulator();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;

		try {
			query = queries.getQueryConf(INSERT_PASS_POLICY_QRY);
		} catch (Exception e) {

			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);
		setMapData(valueMap);
		int rowsAffected = insertRegulator.insert(connection, parmMap);
		//System.out.println("rowsAffected:--- to add updatePasswordPolicy :--- " + rowsAffected);

		// dbCurdRUC.insert(parmMap);
	}
	
	public void updatePasswordPolicy(Map<String, Object> valueMap) {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Connection connection = DbUtil.getConnection();
		InsertRegulator insertRegulator = new InsertRegulator();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;

		try {
			query = queries.getQueryConf(UPDATE_PASS_POLICY_QRY);
		} catch (Exception e) {

			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);
		setMapData(valueMap);
		int rowsAffected = insertRegulator.insert(connection, parmMap);
		System.out.println("rowsAffected:--- to add updatePasswordPolicy :--- " + rowsAffected);

		// dbCurdRUC.insert(parmMap);
	}
	
	public void updateAllPasswordPolicy(Map<String, Object> valueMap) {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Connection connection = DbUtil.getConnection();
		InsertRegulator insertRegulator = new InsertRegulator();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;

		try {
			query = queries.getQueryConf(DEACTIVATE_ALL_PASS_POLICY);
		} catch (Exception e) {

			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);
		setMapData(valueMap);
		int rowsAffected = insertRegulator.insert(connection, parmMap);
		System.out.println("rowsAffected:--- to add updatePasswordPolicy :--- " + rowsAffected);

		// dbCurdRUC.insert(parmMap);DEACTIVATE_ALL_PASS_POLICY
	}
	
	public void deletePasswordPolicy(Map<String, Object> valueMap) {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Connection connection = DbUtil.getConnection();
		InsertRegulator insertRegulator = new InsertRegulator();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;
        //assert query==null:"queries is not configured";
		try {
			query = queries.getQueryConf(DELETE_PASS_POLICY_QRY);
		} catch (Exception e) {

			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);
		setMapData(valueMap);
		int rowsAffected = insertRegulator.insert(connection, parmMap);
		System.out.println("rowsAffected:--- to add updatePasswordPolicy :--- " + rowsAffected);

		// dbCurdRUC.insert(parmMap);
	}
	
	public Map<String, Object> setMapData(Map<String, Object> valueMap) {
		
	   if(valueMap!=null&&valueMap.get("version")!=null){
		   Integer value=new Integer(valueMap.get("version").toString());
		   valueMap.put("version", value+1);
	   }else{
		   valueMap.put("version", 1);
	   }
		
		valueMap.put("Active_Index", "Y");
		valueMap.put("status", "APPROVED");
		valueMap.put("workflowStatus", "N");
		valueMap.put("activity_comments", "Created By Admin");
 		return valueMap;
		
	}
		
}
