package com.date.util;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateForamtConversion {
	public static java.sql.Date parseDate(String d){
		SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yy");
		java.util.Date date1 = null;
		try {
			date1 = sdf.parse(d);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		java.sql.Date sqlStartDate = new java.sql.Date(date1.getTime());  
	    
	    return sqlStartDate;
	}
	
	public static java.sql.Date parseDateEJ(String d){
		SimpleDateFormat dateFormat = new SimpleDateFormat("yy/MM/dd");
	    Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(d);
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
	
	
	
	public static java.sql.Date parseDateEJMMddyy(String d){
		SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yy");
	    Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(d);
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
	
	public static java.sql.Date parseDatemon(String d){
		SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
	    Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(d);
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
	
	public static java.sql.Date getConvertedSqlDate(Date d){
		if(d==null) return null;
		else return new java.sql.Date(d.getTime());
	}
	
	public static String  formateDateMMddyy(String date){
		SimpleDateFormat dateFormat = new SimpleDateFormat("yy/MM/dd");
		SimpleDateFormat dateFormat2 = new SimpleDateFormat("MM/dd/yy");
		
		  Date parsedDate=null;;
		    
			try {
				parsedDate = dateFormat.parse(date);
				
			} catch (ParseException e) {
				
				e.printStackTrace();
			}
			return dateFormat2.format(parsedDate);
					
	}
	
	
	public static String  formateDateMMddyyyy(String date){
		SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
		SimpleDateFormat dateFormat2 = new SimpleDateFormat("MM/dd/yy");
		
		  Date parsedDate=null;;
		    
			try {
				parsedDate = dateFormat.parse(date);
				
			} catch (ParseException e) {
				
				e.printStackTrace();
			}
			return dateFormat2.format(parsedDate);
					
	}
	public static java.sql.Date  formateDateMMddyyyyhhmmsss(String date){
		//26/03/2017 0:00
		SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy h:mm");
	
	 Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(date);
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
	
	
	
	public static java.sql.Date  formateDateDDMMYY(String date){
		//26/03/2017 0:00
		SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yy");
	
	 Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(date.trim());
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
}
