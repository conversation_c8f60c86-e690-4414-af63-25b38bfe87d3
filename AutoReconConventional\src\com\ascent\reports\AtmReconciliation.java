package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;


/**
 * <AUTHOR>
 *
 */
public class AtmReconciliation {

	private static final String ATM_RECONCILATION_INTERNAL_RECON = "ATM_RECONCILATION_INTERNAL_RECON";
	private static final String ATM_RECONCILATION_INTERNAL_UNRECON = "ATM_RECONCILATION_INTERNAL_UNRECON";

	private static final String ATM_RECONCILATION_EXTERNAL_RECON = "ATM_RECONCILATION_EXTERNAL_RECON";
	private static final String ATM_RECONCILATION_EXTERNAL_UNRECON = "ATM_RECONCILATION_EXTERNAL_UNRECON";

	private static final String ATM_RECONCILATION_SUPPRESS_INTERNAL = "ATM_RECONCILATION_SUPPRESS_INTERNAL";
	private static final String ATM_RECONCILATION_SUPPRESS_EXTERNAL = "ATM_RECONCILATION_SUPPRESS_EXTERNAL";
	
	private static final String ATM_AGING_REPORT = "ATM_AGING_REPORT";
	
	private static final String ATM_INTERNAL_DRCR = "ATM_INTERNAL_DRCR";
	
	
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {  // Data base connection method 

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> atmReconcileInternal(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_RECONCILATION_INTERNAL_RECON);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CUSTOMER ACCT", rset.getString(2));
				map.put("ATM ACCOUNT", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT1", rset.getString(7));
				map.put("ATM ACCOUNT1", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("BUSINESS AREA", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> atmUnReconcileInternal(String fromDate, String toDate) { //Atm internal method for Unreconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_RECONCILATION_INTERNAL_UNRECON);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CUSTOMER ACCT", rset.getString(2));
				map.put("ATM ACCOUNT", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT1", rset.getString(7));
				map.put("ATM ACCOUNT1", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("BUSINESS AREA", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				map.put("AGE", rset.getString(30));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> atmReconcileExternal(String fromDate, String toDate) { //Atm External Method for Recociled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_RECONCILATION_EXTERNAL_RECON);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("XNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CHIPCARD", rset.getString(11));
				map.put("CARDNUMBER", rset.getString(12));
				map.put("ACCOUNTNO1", rset.getString(13));
				map.put("ACCOUNTNO2", rset.getString(14));
				map.put("AMOUNT", rset.getString(15));
				map.put("NOTEDETAILS", rset.getString(16));
				map.put("CARDTAKEN", rset.getString(17));
				map.put("CARDCAPTURE", rset.getString(18));
				map.put("NOTESPRESENTED", rset.getString(19));
				map.put("NOTESTAKEN", rset.getString(20));
				map.put("NOTESRETRACT", rset.getString(21));
				map.put("RESPONSECODE", rset.getString(22));
				map.put("RESPONSEDESC", rset.getString(23));
				map.put("HARDWARESTATUS", rset.getString(24));
				map.put("COMMENTS", rset.getString(25));
				map.put("VERSION", rset.getString(26));
				map.put("ACTIVE INDEX", rset.getString(27));
				map.put("WORKFLOW STATUS", rset.getString(28));
				map.put("UPDATED ON", rset.getString(29));
				map.put("CREATED ON", rset.getString(30));
				map.put("RECON STATUS", rset.getString(31));
				map.put("RECON ID", rset.getString(32));
				map.put("ACTIVITY COMMENTS", rset.getString(33));
				map.put("MAIN REV IND", rset.getString(34));
				map.put("OPERATION", rset.getString(35));
				map.put("BUSINESS AREA", rset.getString(36));
				map.put("FILE NAME", rset.getString(37));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> atmUnReconcileExternal(String fromDate, String toDate) { //Atm External Method for UnRecociled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_RECONCILATION_EXTERNAL_UNRECON);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("XNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CHIPCARD", rset.getString(11));
				map.put("CARDNUMBER", rset.getString(12));
				map.put("ACCOUNTNO1", rset.getString(13));
				map.put("ACCOUNTNO2", rset.getString(14));
				map.put("AMOUNT", rset.getString(15));
				map.put("NOTEDETAILS", rset.getString(16));
				map.put("CARDTAKEN", rset.getString(17));
				map.put("CARDCAPTURE", rset.getString(18));
				map.put("NOTESPRESENTED", rset.getString(19));
				map.put("NOTESTAKEN", rset.getString(20));
				map.put("NOTESRETRACT", rset.getString(21));
				map.put("RESPONSECODE", rset.getString(22));
				map.put("RESPONSEDESC", rset.getString(23));
				map.put("HARDWARESTATUS", rset.getString(24));
				map.put("COMMENTS", rset.getString(25));
				map.put("VERSION", rset.getString(26));
				map.put("ACTIVE INDEX", rset.getString(27));
				map.put("WORKFLOW STATUS", rset.getString(28));
				map.put("UPDATED ON", rset.getString(29));
				map.put("CREATED ON", rset.getString(30));
				if(rset.getString(31)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(31));
				map.put("RECON ID", rset.getString(32));
				map.put("ACTIVITY COMMENTS", rset.getString(33));
				map.put("MAIN REV IND", rset.getString(34));
				map.put("OPERATION", rset.getString(35));
				map.put("BUSINESS AREA", rset.getString(36));
				map.put("FILE NAME", rset.getString(37));
				map.put("AGE", rset.getString(38));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> atmReconcileSuppressInternal(String fromDate, String toDate) {  // ATM Suppress Internal Method 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(ATM_RECONCILATION_SUPPRESS_INTERNAL);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CUSTOMER ACCT", rset.getString(2));
				map.put("ATM ACCOUNT", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT1", rset.getString(7));
				map.put("ATM ACCOUNT1", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("BUSINESS AREA", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				map.put("VERIFIER USER ID", rset.getString(30));
				map.put("VERIFIER COMMENTS", rset.getString(31));
				map.put("MAKER USER ID", rset.getString(32));
				map.put("MAKER COMMENTS", rset.getString(33));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> atmReconcileSuppressExternal(String fromDate, String toDate) { //ATM Suppress External Method
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(ATM_RECONCILATION_SUPPRESS_EXTERNAL);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("XNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CHIPCARD", rset.getString(11));
				map.put("CARDNUMBER", rset.getString(12));
				map.put("ACCOUNTNO1", rset.getString(13));
				map.put("ACCOUNTNO2", rset.getString(14));
				map.put("AMOUNT", rset.getString(15));
				map.put("NOTEDETAILS", rset.getString(16));
				map.put("CARDTAKEN", rset.getString(17));
				map.put("CARDCAPTURE", rset.getString(18));
				map.put("NOTESPRESENTED", rset.getString(19));
				map.put("NOTESTAKEN", rset.getString(20));
				map.put("NOTESRETRACT", rset.getString(21));
				map.put("RESPONSECODE", rset.getString(22));
				map.put("RESPONSEDESC", rset.getString(23));
				map.put("HARDWARESTATUS", rset.getString(24));
				map.put("COMMENTS", rset.getString(25));
				map.put("VERSION", rset.getString(26));
				map.put("ACTIVE INDEX", rset.getString(27));
				map.put("WORKFLOW STATUS", rset.getString(28));
				map.put("UPDATED ON", rset.getString(29));
				map.put("CREATED ON", rset.getString(30));
				map.put("RECON STATUS", rset.getString(31));
				map.put("RECON ID", rset.getString(32));
				map.put("ACTIVITY COMMENTS", rset.getString(33));
				map.put("MAIN REV IND", rset.getString(34));
				map.put("OPERATION", rset.getString(35));
				map.put("BUSINESS AREA", rset.getString(36));
				map.put("FILE NAME", rset.getString(37));
				
				map.put("VERIFIER USER ID", rset.getString(38));
				map.put("VERIFIER COMMENTS", rset.getString(39));
				map.put("MAKER USER ID", rset.getString(40));
				map.put("MAKER COMMENTS", rset.getString(41));
				
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> AtmAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");
	
		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_AGING_REPORT);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public static void main(String[] args) {
		AtmReconciliation c = new AtmReconciliation();
		c.atmReconcileInternal("2018-01-01", "2018-10-01");
		c.atmUnReconcileInternal("2018-01-01", "2018-10-01");
		c.atmReconcileExternal("2018-01-01", "2018-10-01");
		c.atmUnReconcileExternal("2018-01-01", "2018-10-01");
		c.atmReconcileSuppressInternal("2018-01-01", "2018-10-01");
		c.atmReconcileSuppressExternal("2018-01-01", "2018-10-01");
		c.atmInternalDrcr("2018-01-01", "2018-10-01");
		c.AtmAgingMethod();

	}

}
