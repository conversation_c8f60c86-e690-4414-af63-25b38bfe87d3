package com.ascent.persistance;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class DeleteRegulator implements DbRegulatorConstants, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5339120816953536964L;

	private static Logger logger = LogManager.getLogger(DeleteRegulator.class.getName());

	public DeleteRegulator() {

	}

	public int delete(Map<String, Object> args, Query deleteQuery) {

		Connection connection = null;
		PreparedStatement deletePstmt = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;

		int rowsAffected = 0;
		try {
			connection = DbUtil.getConnection();
			String deleteQry = deleteQuery.getQueryString();

			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);

			if (paramValuemap == null) {
				paramValuemap = args;
			}

			deletePstmt = connection.prepareStatement(deleteQry);

		
			paramTypeMap = deleteQuery.getQueryParamTypeMap();
			paramList = deleteQuery.getQueryParamList();

			int index = 1;
			for (String param : paramList) {
				deletePstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
				index++;
			}

			rowsAffected = deletePstmt.executeUpdate();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(deletePstmt);
			RegulatorUtil.closeConnection(connection);
		}
		return rowsAffected;

	}

	public int delete(Connection connection, Map<String, Object> args) {

		PreparedStatement deletePstmt = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		int rowsAffected = 0;
		try {
			String deleteQry = (String) args.get(DELETE_QRY);
			String deleteQryParams = (String) args.get(DELETE_QRY_PARAMS);
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			deletePstmt = connection.prepareStatement(deleteQry);
			
			if (deleteQryParams != null && !deleteQryParams.isEmpty()) {
				
				String[] paramArrayWithType = deleteQryParams.split(",");
				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
				
			}

			int index = 1;
			for (String param : paramList) {
				deletePstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
				index++;
			}

			rowsAffected = deletePstmt.executeUpdate();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(deletePstmt);
			// closeConnection(connection);
		}
		return rowsAffected;

	}

	public int delete(PreparedStatement deletePstmt, Map<String, Object> args, String paramStringWithType) {
		int rowsAffected = 0;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;

		
		try {
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			if (paramValuemap == null) {
				paramValuemap = args;
			}
			
			if (paramStringWithType != null && !paramStringWithType.isEmpty()) {
				
				String[] paramArrayWithType = paramStringWithType.split(",");
				
				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
				
			}

			int index = 1;
			
			for (String param : paramList) {
				deletePstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
				index++;
			}

			rowsAffected = deletePstmt.executeUpdate();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(deletePstmt);
		}
		return rowsAffected;

	}
	// Delete methods
	// --End------------------------------------------------------------------------

	public static void main(String[] args) {
		DeleteRegulator dbCurdRUC = new DeleteRegulator();
		Connection connection = null;

		Map<String, Object> params = new HashMap<String, Object>();

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		try {

			Query query = queries.getQueryConf("name");
			connection = DbUtil.getConnection();

			/*
			 * DbCursor ascentResultSet = dbCurdRUC.load(connection, query,
			 * params); boolean reachedEnd = false;
			 * 
			 * List<Map<String, Object>> records =
			 * ascentResultSet.getNextBatch();
			 */

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
}
