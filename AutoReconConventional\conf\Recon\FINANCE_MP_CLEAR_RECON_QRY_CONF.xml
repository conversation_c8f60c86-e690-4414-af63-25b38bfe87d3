<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">
	
	<query id="17">
        <name>MP_<PERSON><PERSON><PERSON>_INSERT_QRY</name>
		<targetTables>FINANCE_MP_CLEAR_RECON</targetTables>
        <queryString>
				
		INSERT INTO FINANCE_MP_CLEAR_RECON
		           (SID,RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,REFERENCE,WORKFLOW_STATUS,SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
		           ,MATCH_TYPE,ACTIVE_INDEX,USER_ID,UPDATED_ON,CREATED_ON,COMMENTS,RULE_NAME,ACTIVITY_STATUS,OPERATION
		           ,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS,ID)
		     VALUES
		           (?,?,?,?,?,?,?,?,?,?,
				    ?,?,?,?,?,?,?,?,?,?,
					?,?,?,?)
  
	</queryString>
		<queryParam>
		   SID@BIGINT,REC<PERSON>_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,REFERENCE@VARCHAR,
		   WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR
		   ,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR
           ,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,ID@BIGINT
		</queryParam>
    </query>
     	
	<query id="18">
        <name>FINANCE_MP_CLEAR_RECON_UPSTREAM_QRY</name>
		<targetTables>FIN_MP_CLEAR_CBS_STG,FIN_MP_CLEAR_EXT_STG</targetTables>
        <queryString>
			SELECT * FROM(
			select 'CBS' AS RECON_SIDE,SID,PAYMENT_REFERENCE  AS REFERENCE,
			TRAN_AMT AS TRA_AMT,TRAN_DATE  AS TRA_DATE,'' AS DEB_CRE_IND,'FIN_MP_CLEAR_CBS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
			 FROM FIN_MP_CLEAR_CBS_STG WITH (NOLOCK) WHERE  (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'

	        UNION ALL

		SELECT 'EXT' AS RECON_SIDE,SID,ID AS REFERENCE,AMOUNT AS TRA_AMT,
		SETTLEMENT_DATE AS TRA_DATE,'' AS DEB_CRE_IND,'FIN_MP_CLEAR_EXT_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
	 	FROM FIN_MP_CLEAR_EXT_STG WITH (NOLOCK) WHERE  (RECON_ID IS NULL OR RECON_STATUS='AU') and STATE = 'ACSC' and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'
  ) AS A  ORDER BY REFERENCE,TRA_DATE,TRA_AMT 
		</queryString>
		<queryParam>
				
		</queryParam>
    </query>
    	


<query id="2">
        <name>FIN_MP_CLEAR_CBS_STG_AUDIT_INSERT_QRY</name>
		<targetTables>FIN_MP_CLEAR_CBS_STG_AUDIT</targetTables>
        <queryString>
		   INSERT INTO FIN_MP_CLEAR_CBS_STG_AUDIT
           (SID,PAYMENT_REFERENCE,CHN_REQ_DATE,BD_STATUS,CUST_GSM_NUM,SENDER_BANK,DEBIT_ACCT_NUMBER,DEBIT_ACCT_NAME
           ,STAFF_FLAG,CREDIT_ACCT_NUMBER,CREDIT_ACCT_NAME,PYMNT_TYPE,REVERSED_FLAG,TRAN_AMT,TRAN_ID,TRAN_DATE
           ,VALUE_DATE,TRAN_POST_FLG,WALLET_TRANSFER_WITHIN_BD,CUST_TYPE_CHRG,FINACLE_DATE_TIME,START_DATE_TIME
           ,END_DATE_TIME,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS
           ,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?)	 
			 
			 </queryString>
		<queryParam>
			
				 SID@BIGINT,PAYMENT_REFERENCE@VARCHAR,CHN_REQ_DATE@DATE,BD_STATUS@VARCHAR,CUST_GSM_NUM@VARCHAR,SENDER_BANK@VARCHAR,
			DEBIT_ACCT_NUMBER@VARCHAR,DEBIT_ACCT_NAME@VARCHAR
           ,STAFF_FLAG@VARCHAR,CREDIT_ACCT_NUMBER@VARCHAR,CREDIT_ACCT_NAME@VARCHAR,PYMNT_TYPE@VARCHAR,REVERSED_FLAG@VARCHAR,
		   TRAN_AMT@DECIMAL,TRAN_ID@VARCHAR,TRAN_DATE@DATE
           ,VALUE_DATE@DATE,TRAN_POST_FLG@VARCHAR,WALLET_TRANSFER_WITHIN_BD@VARCHAR,CUST_TYPE_CHRG@VARCHAR,
		   FINACLE_DATE_TIME@TIMESTAMP,START_DATE_TIME@TIMESTAMP
           ,END_DATE_TIME@TIMESTAMP,COMMENTS@VARCHAR,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,
		   UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR
           ,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR 
		</queryParam>
    </query>
   <query id="2">
        <name>FIN_MP_CLEAR_EXT_STG_AUDIT_INSERT_QRY</name>
		<targetTables>FIN_ONS_CBS_STG</targetTables>
        <queryString>
	INSERT INTO FIN_MP_CLEAR_EXT_STG_AUDIT
           (SID,SETTLEMENT_DATE,SESSION_SEQ,CURRENCY,PARTICIPANT,SETTLEMENTRETRY,ID,TYPE,AMOUNT,STATE,REASON
           ,ADDITIONAL_INFO,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS
           ,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?)	
			 </queryString>
		<queryParam>
			SID@BIGINT,SETTLEMENT_DATE@DATE,SESSION_SEQ@VARCHAR,CURRENCY@VARCHAR,PARTICIPANT@VARCHAR,SETTLEMENTRETRY@VARCHAR,
		   ID@VARCHAR,TYPE@VARCHAR,AMOUNT@DECIMAL,STATE@VARCHAR,REASON@VARCHAR
           ,ADDITIONAL_INFO@VARCHAR,COMMENTS@VARCHAR,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,
		   UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR
           ,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR
		</queryParam>
    </query>
		
    

	<query id="17">
        <name>PAYMENT_ORDER_RECON_UPDATE_QRY</name>
		<targetTables>PAYMENT_ORDER_RECON</targetTables>
        <queryString>
				UPDATE ONUS_ATM_DEBIT_RECON SET
					ID=?,TRA_AMT=?,TRA_DATE=?,DEB_CRE_IND=?,TRA_CUR=?,CHECK_NO=?,WORKFLOW_STATUS=?,SOURCE_TARGET=?,
					MAIN_REV_IND=?,RECON_ID=?,VERSION=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,CREATED_ON=?,COMMENTS=?,
					SUPPORTING_DOC_ID=?,RULE_NAME=?,ACTIVITY_STATUS=?,OPERATION=?,STATUS=?,BUSINESS_AREA=?,ACTIVITY_COMMENTS=?
				WHERE SID=? AND RECON_SIDE=?
		
  
	</queryString>
		<queryParam>
				ID@BIGINT,SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TRA_CUR@VARCHAR,
		CHECK_NO@VARCHAR,WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,
		MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,
		SUPPORTING_DOC_ID@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,
		ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
    </query>

</queries>