package com.ascent.ds.login.ldap;


/***
 * This class is created for creating utility methods
 * <AUTHOR>
 *
 */

public class LDAPUtil {

	/***
	 * This method is used for generating the uniquecode value of the password
	 * @param newPass
	 * @return
	 */
	
	public static byte[]  getPassword(String newPass) {
		String quotedPassword = "\"" + newPass + "\"";

		char unicodePwd[] = quotedPassword.toCharArray();
		byte pwdArray[] = new byte[unicodePwd.length * 2];
		for (int i = 0; i < unicodePwd.length; i++) {
			pwdArray[i * 2 + 1] = (byte) (unicodePwd[i] >>> 8);
			pwdArray[i * 2 + 0] = (byte) (unicodePwd[i] & 0xff);
		}
		return pwdArray;
	}
}
