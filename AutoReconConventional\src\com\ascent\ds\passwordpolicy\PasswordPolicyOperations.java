package com.ascent.ds.passwordpolicy;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.persistance.UpdateRegulator;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class PasswordPolicyOperations extends BasicDataSource {

	/**
	 * 
	 */
	private static final long serialVersionUID = 112222222L;


	private static final String INSERT_PASS_POLICY_QRY = "INSERT_PASS_POLICY_QRY";
	private static final String UPDATE_PASS_POLICY_QRY = "UPDATE_PASS_POLICY_QRY";
	private static final String DELETE_PASS_POLICY_QRY = "DELETE_PASS_POLICY_QRY";


	static Connection connection = null;
	PreparedStatement preparedStatement = null;
	ResultSet resultSet = null;

	public PasswordPolicyOperations() {
	}

	public DSResponse executeFetch(final DSRequest request) throws Exception {

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();

		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();

		DSResponse dsResponse = new DSResponse();

		Map requestParams = request.getCriteria();
		String action = (String) requestParams.get("action");

		if (action != null && action.equals("editPasswordPolicy")) {
			/*Query queryConf = queryConfs.getQueryConf(UPDATE_PASS_POLICY_QRY);
			List<Map<String, Object>> sourceNameList = null;

			dsResponse.setData(sourceNameList);*/
			System.out.println("edit data"+requestParams);
			return dsResponse;
		}
		if (action != null && action.equals("deletePasswordPolicy")) {
		/*	Query queryConf = queryConfs.getQueryConf(DELETE_PASS_POLICY_QRY);
			String selectedGeography = String.valueOf(requestParams.get("selectedGeography"));

			List<Map<String, Object>> businessAreaList = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
*/
			System.out.println("delete data"+requestParams);
			return dsResponse;

		} else if (action != null && action.equals("insertPasswordPolicy")) {

			/*String selectedBusinessArea = String.valueOf(requestParams.get("selectedBusinessArea"));
			String selectedGeography = String.valueOf(requestParams.get("selectedGeogrphy"));
			Query queryConf = queryConfs.getQueryConf(INSERT_PASS_POLICY_QRY);

			Map<String, Object> params = new HashMap<String, Object>();

			params.put("business_area", selectedBusinessArea);
			params.put("geography_name", selectedGeography);

			List<Map<String, Object>> data = loadRegulator.loadCompleteData(params, queryConf);*/
		//	dsResponse.setData(data);
			System.out.println("insert data"+requestParams);
			return dsResponse;
		}
		return dsResponse;

	}


	

}
