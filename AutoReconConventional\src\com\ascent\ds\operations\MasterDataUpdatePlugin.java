package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;


public class MasterDataUpdatePlugin extends BasicDataSource implements PagesConstants{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6349900930944369781L;
	
	
	private static Logger logger = LogManager.getLogger(MasterDataUpdatePlugin.class.getName());
	static Connection connection=null;
	public static final String GET_ALL_IDs_FROM_TABLE="select MAX(ID) from ";
	public static final String PARAM_VALUE_MAP = "PARAM_VALUE_MAP";
	public static final String INSERT_QRY_PARAMS = "INSERT_QRY_PARAMS";
	public static final String INSERT_QRY = "INSERT_QRY";

	public MasterDataUpdatePlugin(){
		
		logger.trace("MASTER DATA UPDATE PLUGIN.....");
	}
	 Map<String, Object> result = null;
	 
	public DSResponse executeFetch (DSRequest request){
		
		 DSResponse dsResponse= new DSResponse();
	 Map criteriaMap= request.getValues();
	//TODO: is user authorized utility to verify user priviliges
	
	HttpSession httpSession = request.getHttpServletRequest().getSession(); 
	String reconName=(String)httpSession.getAttribute("user_selected_recon");
	String businesArea=(String)httpSession.getAttribute("user_selected_business_area");
	User user = (User) httpSession.getAttribute("userId");
	String userId = user.getUserId();
	List<Map<String, Object>> selectedRecords=null;
	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		dsResponse.setData(result);
		return dsResponse;
	}
	 
		 Map<String,Object> records =(Map<String,Object>) criteriaMap.get("record");
		 if(criteriaMap.get("tableName").equals("TBL_MASTER_DATA"))
		 {
		 Date date=(Date)((Map)criteriaMap.get("record")).get("AD_FROM_DATE");
			Date date1=(Date)((Map)criteriaMap.get("record")).get("AD_TO_DATE");
			 java.sql.Date d1=new java.sql.Date(date.getTime());
			 java.sql.Date d2=new java.sql.Date(date1.getTime());
			records.put("AD_FROM_DATE", d1);
			records.put("AD_TO_DATE", d2);
		 }		
		 System.out.println(records);
		 
		
		String action="Master Data Update Operation";
		String tableName=(String) criteriaMap.get("tableName");
		String dsName=(String) criteriaMap.get("dsName");
		selectedRecords = new ArrayList<Map<String, Object>>();
		
		
		boolean updateFlag=updateValuesTxn(records , tableName);
		selectedRecords.add(records);
		Map<String,Object> masterDataUpdateArgs=new HashMap<String, Object>();
		masterDataUpdateArgs.put(SELECTED_RECORDS, selectedRecords);
		masterDataUpdateArgs.put(TABLE_NAME, tableName);
		masterDataUpdateArgs.put(DS_NAME, dsName);
		masterDataUpdateArgs.put(ACTION,action);
		masterDataUpdateArgs.put(USER_ID, userId);
		masterDataUpdateArgs.put(BUSINES_AREA, businesArea);
		masterDataUpdateArgs.put(RECON_NAME, reconName);
		
		
		System.out.println("updated----   "+updateFlag);
		//process(masterDataUpdateArgs);
		
		dsResponse.setData(result); 
		return dsResponse;
		
	}
private Map<String, Object>  process(Map<String,Object> masterDataUpdateArgs){
	try {
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();

		String userId = (String) masterDataUpdateArgs.get(USER_ID);
		String tableName = (String) masterDataUpdateArgs.get(TABLE_NAME);
		String dsName = (String) masterDataUpdateArgs.get(DS_NAME);
		masterDataUpdateArgs.put(PERSIST_CLASS, MASTER_DATA_UPDATE_OPREATION_PLUGIN_CLASS_NAME);
		activityDataMap.put("activity_data", masterDataUpdateArgs);
	//	String moduleName=(String) suppressArgs.get(MODULE);
	    UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;
			String businessArea = (String) masterDataUpdateArgs.get(BUSINES_AREA);
			String reconName = (String) masterDataUpdateArgs.get(RECON_NAME);
			String comments = (String) masterDataUpdateArgs.get(COMMENTS);
			String moduleName=(String)masterDataUpdateArgs.get(ACTION);
			userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
					MASTER_DATA_UPDATE_OPERATION, activityDataMap, activityStatus, comments);

			updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				}
		else{
			result = persist(activityDataMap, APPROVED, connection);
		}
			
					}catch(Exception e){
						updateResultStatus(result,FAILED,OPERATION_FAILED);
						e.printStackTrace();
					}finally{
						
						DbUtil.closeConnection(connection);
						
					}
	return result;
			
	}
	
	public static boolean updateValuesTxn(Map<String,Object> records,String tableName){
		int updateStatus=0;
		connection = DbUtil.getConnection();
		PreparedStatement preparedStatement= null;
if(tableName.equals("TBL_MASTER_DATA"))
{
	
		Date AD_FROM_DATE=(Date)records.get("AD_FROM_DATE");
		Date AD_TO_DATE=(Date)records.get("AD_TO_DATE");
		
final String updateStatusAndVersionQuery ="update "+tableName+ " set AL_COMPANY_CODE=?, AL_BRANCH_CODE=?, AL_CY_CODE=?, AL_GL_CODE=?, AL_CIF_NO=?, AL_SL_NO=?, AD_FROM_DATE=?, AD_TO_DATE=?, INTEGERATIN_NAME=?, TRA_STATUS=? where ID=?  "; 
		try {
		
			
			System.out.println(updateStatusAndVersionQuery);
			preparedStatement=connection.prepareStatement(updateStatusAndVersionQuery);
			
			preparedStatement.setString(1, (records.get("AL_COMPANY_CODE"))+"");
			preparedStatement.setString(2, (records.get("AL_BRANCH_CODE"))+"");
			preparedStatement.setString(3, (records.get("AL_CY_CODE"))+"");
			preparedStatement.setString(4, (records.get("AL_GL_CODE"))+"");
			preparedStatement.setString(5, (records.get("AL_CIF_NO"))+"");
			preparedStatement.setString(6, (records.get("AL_SL_NO"))+"");
					
			preparedStatement.setDate(7,(java.sql.Date) AD_FROM_DATE);
			
			preparedStatement.setDate(8,(java.sql.Date) AD_TO_DATE); 
			preparedStatement.setString(9,(String)records.get("INTEGERATIN_NAME")); 
			preparedStatement.setString(10,records.get("TRA_STATUS")+""); 
			preparedStatement.setString(11,records.get("ID")+""); 
		 updateStatus=preparedStatement.executeUpdate();
			
			
		} catch (SQLException e) {
			 e.printStackTrace();
		}finally{
			DbUtil.closePreparedStatement(preparedStatement);
			DbUtil.closeConnection(connection);
		}
}
else
{
	
	final String updateStatusAndVersionQuery ="update "+tableName+ " set BRANCH_CODE=?, CURRENCY_CODE=?, GL_CODE=?, CIF_NO=?, SL_NO=?, AREA=?  where ID=?  "; 
	try {

		
		System.out.println(updateStatusAndVersionQuery);
		preparedStatement=connection.prepareStatement(updateStatusAndVersionQuery);
		
		preparedStatement.setString(1, records.get("BRANCH_CODE")+"");
		preparedStatement.setString(2,records.get("CURRENCY_CODE")+"");
		preparedStatement.setString(3,records.get("GL_CODE")+""); 
		preparedStatement.setString(4,records.get("CIF_NO")+""); 
		preparedStatement.setString(5,records.get("SL_NO")+""); 
		preparedStatement.setString(6,records.get("AREA")+""); 
	
		preparedStatement.setString(7,records.get("ID")+""); 
	 updateStatus=preparedStatement.executeUpdate();
		
		
	} catch (SQLException e) {
		 e.printStackTrace();
	}finally{
		DbUtil.closePreparedStatement(preparedStatement);
		DbUtil.closeConnection(connection);
	}	
}
		
			
		 if(updateStatus==1){
			 System.out.println("Updated value successfully");
			 return true;
			 
		 }else{
			 System.out.println("Not Updated value");
			 return false;
		 }
		
	 }
private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}

public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {
	connection=DbUtil.getConnection();
	 System.out.println("*****************"+activityDataMap);
	 Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
	 String comment=(String) activityDataMap.get("comment");
	 System.out.println(comment+"-------"+activityRecordsMap);
	 InsertRegulator insertRegulator= new InsertRegulator(); 
		Statement statement = null;
  		ResultSet resultSet=null;
	 int insertStatus=0;
		Map<String, Object> args =new HashMap<String, Object>();
	 try
	 {
		if(APPROVED.equalsIgnoreCase(status)){
          String tableName=(String) activityRecordsMap.get(TABLE_NAME);
          List<Map<String,Object>> selectedRecords=(List<Map<String,Object>>) activityRecordsMap.get(SELECTED_RECORDS);
          
          for (Map<String,Object> recordMap:selectedRecords){

        	  long newID=0;
      
        	   
      	
      		 
      			statement = connection.createStatement();
      			resultSet=statement.executeQuery(GET_ALL_IDs_FROM_TABLE+tableName);
      			
      			
      			 if(resultSet.next()){
      				 
      				 newID=resultSet.getLong(1);
      				 newID++;
      			 }
      			 recordMap.put("ID", newID);
        	
        	  Query insertQuery= OperationsUtil.getInsertQueryConf(tableName, connection);
              String inserQueryString=insertQuery.getQueryString();
              String insertQueryParams=insertQuery.getQueryParam();
        	  System.out.println("Record MAp before updating********  "+recordMap);
       	   args.put(INSERT_QRY, inserQueryString);
       	   args.put(INSERT_QRY_PARAMS, insertQueryParams);
       	   args.put(PARAM_VALUE_MAP, recordMap);
       	insertStatus=insertRegulator.insert(connection,args);
       		System.out.println(insertStatus+" SUCCESS, INSERTED RECORD");
      	 
          }
         }
	 }
	 catch(Exception e){
		 logger.error(e);
		 e.printStackTrace();
	 }finally{
		 DbUtil.closeResultSet(resultSet);
		 try {
			statement.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		 DbUtil.closeConnection(connection);
		 
	 }
	
	
		 
	return result;
}

}
