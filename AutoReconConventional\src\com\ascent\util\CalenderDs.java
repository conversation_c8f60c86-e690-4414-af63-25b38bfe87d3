package com.ascent.util;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class CalenderDs extends BasicDataSource{

	/**
	 * <AUTHOR>
	 * USE OF THIS CLASS  IS TO  KEEP DATES IN SESSION FOR MANAGE(DISPLAY)
	 *  ALL GRIDS DATA BASED ON SESSION CONTAING DATES
	 *  (IN THIS CLASS WE ARE  GETTING DATES FROM USER AND STORING THAT DATES INTO THE SESSION. 
	 *   WHENEVER  USER  CHOOSE DATES BASED ON USER SELECTED DATES WE HAVE TO SHOW DATA
	 *   IN ALL GRID FOR THAT HERE WE ARE MAINTAINING DATES INTO THE SESSION OBJECT.) 
	 */
	private static final long serialVersionUID = 1L;
	
public DSResponse executeFetch(final DSRequest dsRequest){
	
	DSResponse dsResponse= new DSResponse();// CREATEING DSRESPONSE OBJECT TO GIVE RESPONSE BACK TO THE JSP form .
	
		
	
	
	
	HttpSession httpSession = dsRequest.getHttpServletRequest().getSession();//GETTING SESSION OBJECT
	List <Map<String,Object>> list= new ArrayList<Map<String,Object>>();//CREATING LIST OBJECT FOR GETTING DATA FROM SESSION
		 if(dsRequest.getValues()!=null && !dsRequest.getValues().isEmpty()){
			 Map dateCriteriaMap=dsRequest.getValues();//GETTING CRITERIA IN STRING FORMAT FROM USER TO MANAGE DATES 
				String fromDateinString =String.valueOf(dateCriteriaMap.get("fromDate"));
				String toDateinString   =String.valueOf(dateCriteriaMap.get("toDate"));

			 //CONVERTING STRING DATE VALUES TO UTIL DATE
			 java.util.Date fromDate2=( java.util.Date)dateCriteriaMap.get("fromDate");
				java.util.Date toDate2=( java.util.Date)dateCriteriaMap.get("toDate");
				//CONVERTING UTIL DATES TO SQL DATES
			    java.sql.Date fromDate = new java.sql.Date(fromDate2.getTime());
			    java.sql.Date toDate = new java.sql.Date(toDate2.getTime());

			    
				
				System.out.println("fromDate"+fromDate+"  toDate"+toDate);
				
				// SAVING  SQL FORMATS DATES INTO THE SESSION 
			 	httpSession.setAttribute("fromDate",fromDate);
				httpSession.setAttribute("toDate",toDate);
				
				System.out.println("..................Dates from Session ................");
				System.out.println(httpSession.getAttribute("fromDate"));
				System.out.println(httpSession.getAttribute("toDate"));
				
				// GETTING DATES FROM SESSION AND SENDING BACK TO THE JSP PAGE 
				
					java.sql.Date formDate=(Date) httpSession.getAttribute("fromDate");
					java.sql.Date  toDatetoCalender =(Date) httpSession.getAttribute("toDate");
				Map<String,Object> dateMap=new HashMap<String,Object>();
				//MAP IS TO STORE DATES
				dateMap.put("dateFromCalenderFromDate",formDate);
				dateMap.put("dateFromCalenderToDate",toDatetoCalender);
				//ADDING MAP OF DATES TO LIST 
				//list.add(dateMap);
				// STORING LIST OF MAP TO RESPONSE OBJECT .
				dsResponse.setData(dateMap);
				return dsResponse;
		 }else 
		 {
			 System.out.println("...............Request is null........... ");
			 System.out.println(httpSession.getAttribute("fromDate"));
				System.out.println(httpSession.getAttribute("toDate"));
				// GETTING DATES FROM SESSION AND SENDING BACK TO THE JSP PAGE 
				java.sql.Date formDate=(Date) httpSession.getAttribute("fromDate");
				  java.sql.Date  toDatetoCalender =(Date) httpSession.getAttribute("toDate");
				Map<String,Object> dateMap=new HashMap<String,Object>();//MAP IS TO STORE DATES
		dateMap.put("dateFromCalenderFromDate",formDate);
		dateMap.put("dateFromCalenderToDate",toDatetoCalender);
		//ADDING MAP OF DATES TO LIST 		
		//list.add(dateMap);
		// STORING LIST OF MAP TO RESPONSE OBJECT .
				dsResponse.setData(dateMap);
		 
		 return dsResponse;
		 
		 }
	}
	
	

}
