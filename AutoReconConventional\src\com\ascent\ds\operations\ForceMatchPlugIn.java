package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ForceMatchPlugIn extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = -6870261822605048138L;

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		System.out.println("force match  plug in");
		List list = null;
		List l = new ArrayList();
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();

		User user = (User) httpSession.getAttribute("userId");

		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		// TODO: is user authorized utility to verify user priviliges.

		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");
		Map<String, List> selectedRecords = (Map) reqCriteria.get("selectedRecords");
		
		String reconDataSource = (String) reqCriteria.get("reconDataSource");
		String centrifugal = (String) reqCriteria.get("centrifugal");
		List basestylelist = (List) reqCriteria.get("basestylelist");
		String integrationName = (String) reqCriteria.get("integrationName");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String moduleName = (String) reqCriteria.get("moduleName");
		List<Map<String, Object>> recordsList = (List<Map<String, Object>>) reqCriteria.get("recordsList");
		List dsList = (List) reqCriteria.get("dsName");
		
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
		String centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
		Map glEntryMap=null;
		
		if(reqCriteria.get("glEntryMap")!=null)
			System.out.println("force match gl entry plug in");
		 glEntryMap=(Map) reqCriteria.get("glEntryMap");
		
		
		System.out.println(glEntryMap);
		
		List<Object> workflowIds = new ArrayList<Object>();
		StringBuilder commentSb = new StringBuilder();
		/*
		 * for(int h=0;h<selectedRecords.size();h++){ list=(List)
		 * selectedRecords.get(h); if (list != null) {
		 * 
		 * StringBuilder commentSb = new StringBuilder(); List<Object>
		 * workflowIds = new ArrayList<Object>(); for (int
		 * s=0;s<list.size();s++) { Map rec=(Map) list.get(s); l.add(rec);
		 * 
		 * if ((rec != null && rec.get("WORKFLOW_STATUS") != null &&
		 * "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec
		 * != null && rec.get("WORKFLOW_STATUS") != null &&
		 * "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
		 * 
		 * }else{ workflowIds.add(rec.get(SID)); } } } }
		 */

		for (Map<String, Object> rec : recordsList) {
			if ((rec != null && rec.get("WORKFLOW_STATUS") != null
					&& "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))
					|| (rec != null && rec.get("WORKFLOW_STATUS") != null
							&& "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {

			} else {
				workflowIds.add(rec.get("RECON_ID"));
			}

		}
		String commentPrefix = null;
		if (workflowIds.size() > 0) {
			result = new HashMap<String, Object>();
			// String commentPrefix = null;
			if (workflowIds.size() == 1) {
				commentSb.append("Selected record with RECON_ID ");
			} else if (workflowIds.size() > 1) {
				commentSb.append("Selected records with RECON_IDs ");
			}
			for (Object obj : workflowIds) {
				if (commentSb.length() != 0) {
					commentSb.append(",");
				}
				commentSb.append(obj);
			}
			commentPrefix = commentPrefix + commentSb.toString() + " are already Under WorkFlow";
			updateResultStatus(result, FAILED, commentPrefix);
			response.setData(result);
			return response;
		}

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsList);
		paramsMap.put("basestylelist", basestylelist);
		paramsMap.put("recordsList", recordsList);
		paramsMap.put("reconDataSource", reconDataSource);
		paramsMap.put("glEntryMap", glEntryMap);
		
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		if(centrifugalAmountField!=null){
			paramsMap.put("centrifugalAmountField",centrifugalAmountField);
		}
		result = process(paramsMap);

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);

		return result;
	}

	public Map<String, Object> reject() {
		return null;
	}

	public Map<String, Object> approve() {
		return null;
	}

	// will Submit the operation basis on user credintials
	@SuppressWarnings("finally")
	private Map<String, Object> process(Map<String, Object> forceMatchArgs) {
		Connection connection = null;
		Map<String, Object> result = null;
		try {
			connection = DbUtil.getConnection();
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			String userId = (String) forceMatchArgs.get(USER_ID);
			List dsName = (List) forceMatchArgs.get(DS_NAME);
			forceMatchArgs.put(PERSIST_CLASS, FORCE_MATCH_PLUGIN_CLASS_NAME);
			activityDataMap.put("activity_data", forceMatchArgs);

			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

			if (userAdminManager.isUserUnderWorkflow(user)) {
				result = new HashMap<String, Object>();

				String activityStatus = PENDING_APPROVAL;

				String businessArea = (String) forceMatchArgs.get(BUSINES_AREA);
				String reconName = (String) forceMatchArgs.get(RECON_NAME);
				String comments = (String) forceMatchArgs.get(COMMENTS);
				String moduleName = (String) forceMatchArgs.get(MODULE);
				userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
						FORCE_MATCH_OPERATION, activityDataMap, activityStatus, comments);

				String requesterComments = userId + " : " + comments;
				String integrationName = (String) forceMatchArgs.get(INTEGRATION_NAME);
				LoadRegulator loadRegulator = new LoadRegulator();
				InsertRegulator insertRegulator = new InsertRegulator();
				// Recon Data Sorces will get through DS_NAME key from front end
				String reconDataSource = (String) (forceMatchArgs.get("reconDataSource"));
				String reconTableName = reconDataSource.substring(0, reconDataSource.length() - 14);
				Map<String, List> selectedRecords = (Map<String, List>) forceMatchArgs.get(SELECTED_RECORDS);
				List<Map<String, Object>> recordsList = (List<Map<String, Object>>) forceMatchArgs.get("recordsList");
				PreparedStatement selectAuditStmt = null;
				PreparedStatement auditInsertPstmt = null;
				PreparedStatement reconDataSelectPstmt = null;
				PreparedStatement stagingDataSelectPstmt = null;
				PreparedStatement auditDataInsertPstmt = null;
				PreparedStatement stagingDataUpdatePstmt = null;
				PreparedStatement reconDataUpdatePstmt = null;
				try {

					long version = 0;
					long sid = 0;

					for (String key : selectedRecords.keySet()) {
						List<Map<String, Object>> l = (List<Map<String, Object>>) selectedRecords.get(key);
						for (Map<String, Object> selectedRec : l) {
							String stgTableName = key + "_STG";
							String auditTableName = key + "_STG_AUDIT";
							
							//FINANCE -  ONS
							if(key.equalsIgnoreCase("CBS") ){	
									stgTableName="FIN_ONS_CBS_STG";
									auditTableName="FIN_ONS_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CBO") ){	
									stgTableName="FIN_ONS_CBO_STG";
									auditTableName="FIN_ONS_CBO_STG_AUDIT";
								}
							//FINANCE -  MPCLEAR
							else if(key.equalsIgnoreCase("MP_CLEAR_CBS") ){	
									stgTableName="FIN_MP_CLEAR_CBS_STG";
									auditTableName="FIN_MP_CLEAR_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("EXT") ){	
									stgTableName="FIN_MP_CLEAR_EXT_STG";
									auditTableName="FIN_MP_CLEAR_EXT_STG_AUDIT";
								}
							//FINANCE -  SUSPENSE
							else if(key.equalsIgnoreCase("FIN_SUSPENSE_DEBIT") || key.equalsIgnoreCase("FIN_SUSPENSE_CREDIT")){	
									stgTableName="FIN_SUSPENSE_STG";
									auditTableName="FIN_SUSPENSE_STG_AUDIT";
								}
							//CARDS -  ATM MASTER CARD ACQUIRER
							else if(key.equalsIgnoreCase("ATM_MC_CBS") ){	
									stgTableName="CARD_ATM_MC_CBS_STG";
									auditTableName="CARD_ATM_MC_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("ACQ") ){	
									stgTableName="CARD_ATM_MC_ACQ_STG";
									auditTableName="CARD_ATM_MC_ACQ_STG_AUDIT";
								}
							//CARDS -  ATM VISA ACQUIRER
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ACQ_CBS") ){	
									stgTableName="CARD_ATM_VISA_ACQ_CBS_STG";
									auditTableName="CARD_ATM_VISA_ACQ_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ACQ_EXT") ){	
									stgTableName="CARD_ATM_VISA_ACQ_EXT_STG";
									auditTableName="CARD_ATM_VISA_ACQ_EXT_STG_AUDIT";
								}
							//CARDS -  POS NI ACQUIRER
							else if(key.equalsIgnoreCase("CARD_POSNI_CBS") ){	
									stgTableName="CARD_POSNI_CBS_STG";
									auditTableName="CARD_POSNI_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_POSNI_TRAN") ){	
									stgTableName="CARD_POSNI_TRAN_STG";
									auditTableName="CARD_POSNI_TRAN_STG_AUDIT";
								}
							//CARDS -  CREDIT CARD STATEMENT
							else if(key.equalsIgnoreCase("CREDIT_CARD_STATEMENT_CBS") ){	
									stgTableName="CREDIT_CARD_STATEMENT_CBS_STG";
									auditTableName="CREDIT_CARD_STATEMENT_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CREDIT_CARD_STATEMENT_TRAN") ){	
									stgTableName="CREDIT_CARD_STATEMENT_TRAN_STG";
									auditTableName="CREDIT_CARD_STATEMENT_TRAN_STG_AUDIT";
								}
							//CARDS -  ATM/POS VISA ISSUER
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ISS_CBS") ){	
									stgTableName="CARD_ATM_VISA_ISS_CBS_STG";
									auditTableName="CARD_ATM_VISA_ISS_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ISS_INCT") ){	
								stgTableName="CARD_ATM_VISA_ISS_INCT_STG";
								auditTableName="CARD_ATM_VISA_ISS_INCT_STG_AUDIT";
							}
							//CARDS -  OPG BD ACQ
							else if(key.equalsIgnoreCase("ONUS_CBS") ){	
									stgTableName="CARD_ECOM_ONUS_CBS_STG";
									auditTableName="CARD_ECOM_ONUS_CBS_STG_AUDIT";
							}
							else if(key.equalsIgnoreCase("ONUS_SETTL") ){	
								stgTableName="CARD_ECOM_ONUS_SETTL_STG";
								auditTableName="CARD_ECOM_ONUS_SETTL_STG_AUDIT";
							}
							//CARDS -  OPG OTHER BANK ACQ
							else if(key.equalsIgnoreCase("OFFUS_CBS") ){	
									stgTableName="CARD_ECOM_OFFUS_CBS_STG";
									auditTableName="CARD_ECOM_OFFUS_CBS_STG_AUDIT";
							}
							else if(key.equalsIgnoreCase("OFFUS_SETTL") ){	
								stgTableName="CARD_ECOM_OFFUS_SETTL_STG";
								auditTableName="CARD_ECOM_OFFUS_SETTL_STG_AUDIT";
							}
							//CENTRAL OPERATION DEPARTMENT - CDM
							else if(key.equalsIgnoreCase("CO_CDM_CBS") ){	
									stgTableName="CO_CDM_CBS_STG";
									auditTableName="CO_CDM_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_CDM_JOURNAL") ){	
									stgTableName="CO_CDM_JOURNAL_STG";
									auditTableName="CO_CDM_JOURNAL_STG_AUDIT";
								}
							//CENTRAL OPERATION DEPARTMENT - ATM TRANSACTIONS
							else if(key.equalsIgnoreCase("CO_ATM_CBS") ){	
									stgTableName="CO_ATM_CBS_STG";
									auditTableName="CO_ATM_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_ATM_JOURNAL") ){	
									stgTableName="CO_ATM_JOURNAL_STG";
									auditTableName="CO_ATM_JOURNAL_STG_AUDIT";
								}
							//CENTRAL OPERATION DEPARTMENT - ACH
							else if(key.equalsIgnoreCase("CO_ACH_CBS") ){	
									stgTableName="CO_ACH_CBS_STG";
									auditTableName="CO_ACH_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_ACH_CBO") ){	
									stgTableName="CO_ACH_CBO_STG";
									auditTableName="CO_ACH_CBO_STG_AUDIT";
								}
							
							
							sid = (long) selectedRec.get("SID");
							String auditSelectQry = "	select * from " + stgTableName
									+ "  where version=(	select max(version) from " + stgTableName
									+ " where sid =?) and sid=?";
							selectAuditStmt = connection.prepareStatement(auditSelectQry);
							List<Map<String, Object>> auditData = loadRegulator.loadCompleteData(selectedRec,
									selectAuditStmt, "SID@BIGINT,SID@BIGINT");

							Query auditQuery = OperationsUtil.getInsertQueryConf(auditTableName, connection);
							auditDataInsertPstmt = connection.prepareStatement(auditQuery.getQueryString());
							Map paramValueMap = new HashMap();
							for (Map rec : auditData) {

								paramValueMap.put("PARAM_VALUE_MAP", rec);
								insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
							}

							selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));

							version = Integer.parseInt(selectedRec.get("VERSION").toString());
							++version;

							String updateQuery = "UPDATE " + stgTableName
									+ " SET WORKFLOW_STATUS='Y',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
							stagingDataUpdatePstmt = connection.prepareStatement(updateQuery);
							stagingDataUpdatePstmt.setObject(1, version);
							stagingDataUpdatePstmt.setObject(2, requesterComments);
							stagingDataUpdatePstmt.setObject(3,
									new Timestamp(Calendar.getInstance().getTimeInMillis()));
							stagingDataUpdatePstmt.setObject(4, sid);
							int row = stagingDataUpdatePstmt.executeUpdate();

						}

					}
					int mailStatus[]=new int[recordsList.size()];
					for (Map<String, Object> reconRec : recordsList) {
						int i=0;
						Long reconSID = (Long) reconRec.get("SID");
						reconDataUpdatePstmt = connection.prepareStatement("UPDATE " + reconTableName
								+ " SET WORKFLOW_STATUS='Y',UPDATED_ON=?,ACTIVITY_COMMENTS=? WHERE SID=" + reconSID);
						reconDataUpdatePstmt.setObject(1, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						reconDataUpdatePstmt.setObject(2, requesterComments);
						mailStatus[i++]=reconDataUpdatePstmt.executeUpdate();

					}
				/*	boolean flag=true;
					for(int res:mailStatus){
						if(res==0){
							flag=false;
							break;
						}
					}
					if(flag){
						
						try{
							String strquery="select email_id from users where user_name='"+user.getReporting()+"'";
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strquery);
							if(rs.next()){
								String appMailId=rs.getString(1);
								OperationMail mail=	new OperationMail(user.getEmailId(), appMailId);
								mail.sendMail(user.getEmailId(), appMailId);
								mail.sentMail(appMailId, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, recordsList, "FORCE MATCH PENDING FOR APPROVAL");
							}
						}catch(Exception e){
							
						}
					}*/
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
				}
				updateResultStatus(result, SUCCESS, "Transactions submitted for Approval Sucessfully");
				return result;
			} else {

				result = persist(activityDataMap, APPROVED, connection);

			}
		} catch (Exception e) {
			e.printStackTrace();

			updateResultStatus(result, FAILED, "Operation Failed");
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return result;
		}

	}

	public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {

		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator = new LoadRegulator();
		InsertRegulator insertRegulator = new InsertRegulator();
		try {

			Map activityRecordsMap = (Map) activityDataMap.get("activity_data");
			String integrationName = (String) activityRecordsMap.get(INTEGRATION_NAME);
			// List<Map<String, Object>> records = (List<Map<String, Object>>)
			// activityRecordsMap.get(SELECTED_RECORDS);
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();

			connection = DbUtil.getConnection();
			String businessArea = (String) (activityRecordsMap.get(BUSINES_AREA));
			String reconName = (String) (activityRecordsMap.get(RECON_NAME));
			if (APPROVED.equalsIgnoreCase(status)) {

				
				
				String userId = (String) activityDataMap.get("userId");
				String comments = (String) activityDataMap.get("comment");
				String requesterComments = userId + " : " + comments;
				// Recon Data Sorces will get through DS_NAME key from front end
				String reconDataSource = (String) (activityRecordsMap.get("reconDataSource"));
				String reconTableName = reconDataSource.substring(0, reconDataSource.length() - 14);
				Map<String, List> selectedRecords = (Map<String, List>) activityRecordsMap.get(SELECTED_RECORDS);
				List<Map<String, Object>> recordsList = (List<Map<String, Object>>) activityRecordsMap
						.get("recordsList");
				System.out.println("selectedRecords   RAMA APPROVED   "+selectedRecords);
				PreparedStatement selectAuditStmt = null;
				PreparedStatement auditInsertPstmt = null;
				PreparedStatement reconDataSelectPstmt = null;
				PreparedStatement stagingDataSelectPstmt = null;
				PreparedStatement auditDataInsertPstmt = null;
				PreparedStatement stagingDataUpdatePstmt = null;
				PreparedStatement reconDataUpdatePstmt = null;
				PreparedStatement reconDataSelectFromStagingPstmt = null;
				PreparedStatement reconDataInsertPstmt = null;
				String reconTableIdSeqName = reconTableName + "MM_ID_SEQ";
				String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";
				String idseqname = reconTableName + "MM_ID_SEQ";
				PreparedStatement reconTableIdGenPstmt = connection.prepareStatement(reconTableIdGenQry);
				Map<Long, Object> reconcileList = new HashMap<Long, Object>();
				Map<Long, String> unreconcileList = new HashMap<Long, String>();

				try {

					long version = 0;
					long sid = 0;
					long reconid = (long) recordsList.get(0).get("RECON_ID");

					for (String key : selectedRecords.keySet()) {
						List<Map<String, Object>> l = (List<Map<String, Object>>) selectedRecords.get(key);
						for (Map<String, Object> selectedRec : l) {
							String stgTableName = key + "_STG";
							String auditTableName = key + "_STG_AUDIT";
							
							//FINANCE ONS
							if(key.equalsIgnoreCase("CBS") ){	
									stgTableName="FIN_ONS_CBS_STG";
									auditTableName="FIN_ONS_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CBO") ){	
									stgTableName="FIN_ONS_CBO_STG";
									auditTableName="FIN_ONS_CBO_STG_AUDIT";
								}
							//FINANCE MPCLEAR
							else if(key.equalsIgnoreCase("MP_CLEAR_CBS") ){	
									stgTableName="FIN_MP_CLEAR_CBS_STG";
									auditTableName="FIN_MP_CLEAR_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("EXT") ){	
									stgTableName="FIN_MP_CLEAR_EXT_STG";
									auditTableName="FIN_MP_CLEAR_EXT_STG_AUDIT";
								}
							//FINANCE -  SUSPENSE
							else if(key.equalsIgnoreCase("FIN_SUSPENSE_DEBIT") || key.equalsIgnoreCase("FIN_SUSPENSE_CREDIT")){	
									stgTableName="FIN_SUSPENSE_STG";
									auditTableName="FIN_SUSPENSE_STG_AUDIT";
								}
							//CARDS -  ATM MASTER CARD ACQUIRER
							else if(key.equalsIgnoreCase("ATM_MC_CBS") ){	
									stgTableName="CARD_ATM_MC_CBS_STG";
									auditTableName="CARD_ATM_MC_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("ACQ") ){	
									stgTableName="CARD_ATM_MC_ACQ_STG";
									auditTableName="CARD_ATM_MC_ACQ_STG_AUDIT";
								}
							//CARDS -  ATM VISA ACQUIRER
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ACQ_CBS") ){	
									stgTableName="CARD_ATM_VISA_ACQ_CBS_STG";
									auditTableName="CARD_ATM_VISA_ACQ_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ACQ_EXT") ){	
									stgTableName="CARD_ATM_VISA_ACQ_EXT_STG";
									auditTableName="CARD_ATM_VISA_ACQ_EXT_STG_AUDIT";
								}
							//CARDS -  POS NI ACQUIRER
							else if(key.equalsIgnoreCase("CARD_POSNI_CBS") ){	
									stgTableName="CARD_POSNI_CBS_STG";
									auditTableName="CARD_POSNI_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_POSNI_TRAN") ){	
									stgTableName="CARD_POSNI_TRAN_STG";
									auditTableName="CARD_POSNI_TRAN_STG_AUDIT";
								}
							//CARDS -  CREDIT CARD STATEMENT
							else if(key.equalsIgnoreCase("CREDIT_CARD_STATEMENT_CBS") ){	
									stgTableName="CREDIT_CARD_STATEMENT_CBS_STG";
									auditTableName="CREDIT_CARD_STATEMENT_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CREDIT_CARD_STATEMENT_TRAN") ){	
									stgTableName="CREDIT_CARD_STATEMENT_TRAN_STG";
									auditTableName="CREDIT_CARD_STATEMENT_TRAN_STG_AUDIT";
								}
							//CARDS -  ATM/POS VISA ISSUER
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ISS_CBS") ){	
									stgTableName="CARD_ATM_VISA_ISS_CBS_STG";
									auditTableName="CARD_ATM_VISA_ISS_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ISS_INCT") ){	
								stgTableName="CARD_ATM_VISA_ISS_INCT_STG";
								auditTableName="CARD_ATM_VISA_ISS_INCT_STG_AUDIT";
							}
							//CARDS -  OPG BD ACQ
							else if(key.equalsIgnoreCase("ONUS_CBS") ){	
									stgTableName="CARD_ECOM_ONUS_CBS_STG";
									auditTableName="CARD_ECOM_ONUS_CBS_STG_AUDIT";
							}
							else if(key.equalsIgnoreCase("ONUS_SETTL") ){	
								stgTableName="CARD_ECOM_ONUS_SETTL_STG";
								auditTableName="CARD_ECOM_ONUS_SETTL_STG_AUDIT";
							}
							//CARDS -  OPG OTHER BANK ACQ
							else if(key.equalsIgnoreCase("OFFUS_CBS") ){	
									stgTableName="CARD_ECOM_OFFUS_CBS_STG";
									auditTableName="CARD_ECOM_OFFUS_CBS_STG_AUDIT";
							}
							else if(key.equalsIgnoreCase("OFFUS_SETTL") ){	
								stgTableName="CARD_ECOM_OFFUS_SETTL_STG";
								auditTableName="CARD_ECOM_OFFUS_SETTL_STG_AUDIT";
							}
							//CENTRAL OPERATION DEPARTMENT - CDM
							else if(key.equalsIgnoreCase("CO_CDM_CBS") ){	
									stgTableName="CO_CDM_CBS_STG";
									auditTableName="CO_CDM_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_CDM_JOURNAL") ){	
									stgTableName="CO_CDM_JOURNAL_STG";
									auditTableName="CO_CDM_JOURNAL_STG_AUDIT";
								}
							//CENTRAL OPERATION DEPARTMENT - ATM TRANSACTIONS
							else if(key.equalsIgnoreCase("CO_ATM_CBS") ){	
									stgTableName="CO_ATM_CBS_STG";
									auditTableName="CO_ATM_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_ATM_JOURNAL") ){	
									stgTableName="CO_ATM_JOURNAL_STG";
									auditTableName="CO_ATM_JOURNAL_STG_AUDIT";
								}
							//CENTRAL OPERATION DEPARTMENT - ACH
							else if(key.equalsIgnoreCase("CO_ACH_CBS") ){	
									stgTableName="CO_ACH_CBS_STG";
									auditTableName="CO_ACH_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_ACH_CBO") ){	
									stgTableName="CO_ACH_CBO_STG";
									auditTableName="CO_ACH_CBO_STG_AUDIT";
								}
							
							
							sid = (long) selectedRec.get("SID");
							stagingDataSelectPstmt = connection
									.prepareStatement("select * from " + stgTableName + " where SID=" + sid);
							List<String> columnList = new ArrayList<String>();
							ResultSet stagingRs = stagingDataSelectPstmt.executeQuery();
							ResultSetMetaData rsm = stagingRs.getMetaData();
							int columnCount = rsm.getColumnCount();
							Map stagingDataMap = new HashMap();
							while (stagingRs.next()) {
								version = Integer.parseInt(stagingRs.getObject("VERSION").toString());
								// ++version;
								for (int i = 1; i <= columnCount; i++) {
									columnList.add(rsm.getColumnName(i));
									stagingDataMap.put(rsm.getColumnName(i),
											stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
								}

							}

							Query auditQuery = OperationsUtil.getInsertQueryConf(auditTableName, connection);
							auditDataInsertPstmt = connection.prepareStatement(auditQuery.getQueryString());
							Map paramValueMap = new HashMap();

							paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
							selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
							insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
							// version=Integer.parseInt(selectedRec.get("VERSION").toString());
							System.out.println("record inserted to Audit");
							
							
							
							++version;
							// sid=(long) selectedRec.get("SID");
							String updateQuery = "UPDATE " + stgTableName
									+ " SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?,RECON_STATUS=?,RECON_ID=?,ACTIVE_INDEX='Y'  WHERE SID=?";
							stagingDataUpdatePstmt = connection.prepareStatement(updateQuery);
							stagingDataUpdatePstmt.setObject(1, version);
							stagingDataUpdatePstmt.setObject(2, requesterComments);
							stagingDataUpdatePstmt.setObject(3,
									new Timestamp(Calendar.getInstance().getTimeInMillis()));
							stagingDataUpdatePstmt.setObject(4, "MM");
							stagingDataUpdatePstmt.setObject(5, reconid);
							stagingDataUpdatePstmt.setObject(6, sid);
							int row = stagingDataUpdatePstmt.executeUpdate();
//ACTIVE_INDEX='Y' and
							reconDataSelectPstmt = connection.prepareStatement("select * from " + reconTableName
									+ " where  SID=" + sid + " and RECON_SIDE=?");
							reconDataSelectPstmt.setObject(1, key);
							ResultSet reconRs = reconDataSelectPstmt.executeQuery();
							if (reconRs.next()) {
								List versionList = new ArrayList();
								versionList.add(reconRs.getObject("VERSION"));
								versionList.add(key);
								reconcileList.put(sid, versionList);
							} else {
								//unreconcileList.put(sid, key);
								unreconcileList.put(sid, stgTableName);

							}

						}

					}
					/*try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, recordsList, "FORCE MATCH OPERATION APPROVED");
					
						}catch(Exception e){
							e.printStackTrace();
						}
					}catch(Exception e){
						e.printStackTrace();
					}*/
					for (Long reconSid : reconcileList.keySet()) {
						List keyVersionList = (List) reconcileList.get(reconSid);

						int recVersion = Integer.parseInt(keyVersionList.get(0).toString());
						String reconSideWithVersion = (String) keyVersionList.get(1);
						++recVersion;
						reconDataUpdatePstmt = connection.prepareStatement("UPDATE " + reconTableName
								+ " SET ACTIVE_INDEX='Y',WORKFLOW_STATUS='N',RECON_ID=?,STATUS='FORCE MATCH',COMMENTS='MANUAL MATCH',MATCH_TYPE=?,UPDATED_ON=?,USER_ID=?,VERSION=?,ACTIVITY_COMMENTS=?  WHERE SID=? AND RECON_SIDE=?");
						reconDataUpdatePstmt.setObject(1, reconid);
						reconDataUpdatePstmt.setObject(2, "MM");
						reconDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						reconDataUpdatePstmt.setObject(4, userId);
						reconDataUpdatePstmt.setObject(5, recVersion);
						reconDataUpdatePstmt.setObject(6, requesterComments);
						reconDataUpdatePstmt.setObject(7, reconSid);
						reconDataUpdatePstmt.setObject(8, reconSideWithVersion);
						int r = reconDataUpdatePstmt.executeUpdate();

					}
					for (Long unReconSid : unreconcileList.keySet()) {
						String reocnside = unreconcileList.get(unReconSid);

						String queryName = reconTableName + "_" + reocnside;//PAYMENT_ORDER_RECON_IMAL_PAYMENTS_CREDIT
						Query query = queries.getQueryConf(queryName);
						reconDataSelectFromStagingPstmt = connection.prepareStatement(query.getQueryString());
						reconDataSelectFromStagingPstmt.setObject(1, unReconSid);
						ResultSet unreconRs = reconDataSelectFromStagingPstmt.executeQuery();
						ResultSetMetaData unreconRsm = unreconRs.getMetaData();
						int columnCount = unreconRsm.getColumnCount();
						Map<String, Object> reconDataMap = new HashMap<String, Object>();
						while (unreconRs.next()) {

							for (int i = 1; i <= columnCount; i++) {
								System.out.println(unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
								reconDataMap.put(unreconRsm.getColumnName(i),
										unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
							}
						}

						reconDataMap.put("ID",
								PersistanceUtil.generateSeqNo(connection, reconTableIdGenPstmt, reconTableIdSeqName));
						reconDataMap.put("RECON_ID", reconid);
						reconDataMap.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						reconDataMap.put("MATCH_TYPE", "MM");
						// reconDataMap.put("USER_ID", userId);
						reconDataMap.put("ACTIVE_INDEX", "Y");
						reconDataMap.put("WORKFLOW_STATUS", "N");
						// reconDataMap.put("RULE_NAME", "AMOUNTS MATCHED");
						reconDataMap.put("VERSION", 1);
						reconDataMap.put("COMMENTS", "AMOUNTS MATCHED");
						reconDataMap.put("STATUS", "FORCE MATCH");
						reconDataMap.put("SUPPORTING_DOC_ID", "-1");
						reconDataMap.put("ACTIVITY_ID", "-1");
						reconDataMap.put("ACTIVITY_STATUS", "MANUAL");

						reconDataMap.put("CREATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						// reconDataMap.put("COMMENTS", "SYSTEM");
						Query reconQuery = OperationsUtil.getInsertQueryConf(reconTableName, connection);
						reconDataInsertPstmt = connection.prepareStatement(reconQuery.getQueryString());

						Map<String, Object> paramValueMap = new HashMap<String, Object>();
						paramValueMap.put("PARAM_VALUE_MAP", reconDataMap);

						insertRegulator.insert(reconDataInsertPstmt, paramValueMap, reconQuery.getQueryParam());
						
					//	----
						
						

					}
					//---
					if(activityRecordsMap.get("glEntryMap")!=null){
						///////////
						
						Map glEntryMap=(Map) activityRecordsMap.get("glEntryMap");
						Map DEBITCREDITDETAILS=(Map) glEntryMap.get("glEntryData");
						Map CustomerDebitCreditDetails=(Map) glEntryMap.get("glEntryCustomerData");
						System.out.println(DEBITCREDITDETAILS+"-----------------------"+CustomerDebitCreditDetails);
						Query glQuery=OperationsUtil.getInsertQueryConf("GENERATE_GL_ENTRY", connection);
						Map<String,Object> generateGlEntryMap=new HashMap<String,Object>();
						String custTran_Type = (String) DEBITCREDITDETAILS.get("custTranType");
						System.out.println("custTran_Type : " + custTran_Type);
						int custval=0;
						int hostval=0;
						if(custTran_Type.equalsIgnoreCase("Credit")){
							custval=2;
							hostval=1;
						}
						else{
							custval=1;
							hostval=2;
						}
						System.out.println("custval :" + custval);
						
						// ADDING LAST_4_DIGIT_DOC_NO
						
						/*String retrive_4_Digit_Doc_No=DEBITCREDITDETAILS.get("docNum").toString();
						String Last_4_digit_DOC_No="";
						if(retrive_4_Digit_Doc_No.length()>4){
					       	Last_4_digit_DOC_No=retrive_4_Digit_Doc_No.substring(retrive_4_Digit_Doc_No.length()-4);
					        }else{
					        	Last_4_digit_DOC_No=retrive_4_Digit_Doc_No;
					        }*/
						generateGlEntryMap.put("BRANCH_CODE", DEBITCREDITDETAILS.get("branchCode"));
						generateGlEntryMap.put("CUSTOMER", DEBITCREDITDETAILS.get("customerNum"));
						generateGlEntryMap.put("CHECK_DIGIT", DEBITCREDITDETAILS.get("checkDigit"));
						generateGlEntryMap.put("LEDGER_CODE", DEBITCREDITDETAILS.get("ledgerCode"));
						generateGlEntryMap.put("SUB_ACC_CODE", DEBITCREDITDETAILS.get("subAccCode"));
						generateGlEntryMap.put("DEB_CRE_IND",custval );
						generateGlEntryMap.put("TRANSACTION_AMOUNT",DEBITCREDITDETAILS.get("transactionAmount"));
						generateGlEntryMap.put("RECON_ID", reconid);
						generateGlEntryMap.put("BUSINESS_AREA", businessArea);
						generateGlEntryMap.put("RECON_NAME", reconName);
						
						generateGlEntryMap.put("CURR_CODE", DEBITCREDITDETAILS.get("currencyCode"));
						generateGlEntryMap.put("GL_FLAG", "N");
						generateGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
						generateGlEntryMap.put("INSERT_USER", userId);
						generateGlEntryMap.put("REMARKS",  glEntryMap.get("remarks"));
						//generateGlEntryMap.put("REMARKS",  records.get(0).get("REMARKS"));
						generateGlEntryMap.put("REASON",  comments);
						generateGlEntryMap.put("DOCUMENT_ALPHA",  0000 );
						generateGlEntryMap.put("DOCUMENT_NUMBER",   0000);
						//generateGlEntryMap.put("LAST_4_DIGIT_DOC_NO", Last_4_digit_DOC_No);
						
						Map<String,Object> generateOriginalGlEntryMap=new HashMap<String,Object>();
					//	String hostTran_Type = (String) DEBITCREDITDETAILS.get("hostTran_Type");
					//	System.out.println("hostTran_Type : " + hostTran_Type);
						
						/*if(hostTran_Type.equalsIgnoreCase("Credit")){
							hostval=2;
						}
						else{
							hostval=1;
						}*/
						System.out.println("hostval : " + hostval);
						generateOriginalGlEntryMap.put("BRANCH_CODE", CustomerDebitCreditDetails.get("branchCode_Cust"));
						generateOriginalGlEntryMap.put("CUSTOMER",  CustomerDebitCreditDetails.get("customerNum_Cust"));
						generateOriginalGlEntryMap.put("CHECK_DIGIT",CustomerDebitCreditDetails.get("checkDigit_Cust"));
						generateOriginalGlEntryMap.put("LEDGER_CODE", CustomerDebitCreditDetails.get("ledgerCode_Cust"));
						generateOriginalGlEntryMap.put("SUB_ACC_CODE", CustomerDebitCreditDetails.get("subAccCode_Cust"));
						generateOriginalGlEntryMap.put("DEB_CRE_IND",hostval);
						generateOriginalGlEntryMap.put("TRANSACTION_AMOUNT",CustomerDebitCreditDetails.get("transactionAmount_Cust"));
						generateOriginalGlEntryMap.put("RECON_ID", reconid);
						generateOriginalGlEntryMap.put("BUSINESS_AREA", businessArea);
						generateOriginalGlEntryMap.put("RECON_NAME", reconName);
						generateOriginalGlEntryMap.put("CURR_CODE", CustomerDebitCreditDetails.get("currencyCode_Cust"));
						generateOriginalGlEntryMap.put("GL_FLAG",  "N");
						generateOriginalGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
						generateOriginalGlEntryMap.put("INSERT_USER", userId);
						generateOriginalGlEntryMap.put("REMARKS",   glEntryMap.get("remarks"));
						generateOriginalGlEntryMap.put("DOCUMENT_ALPHA",  0000 );
						generateOriginalGlEntryMap.put("DOCUMENT_NUMBER",    0000);
						//generateOriginalGlEntryMap.put("LAST_4_DIGIT_DOC_NO", Last_4_digit_DOC_No);
						 PreparedStatement generateInsertPstmt = connection.prepareStatement(glQuery.getQueryString());
						Map<String, Object> paramValueMap=new HashMap<String, Object>();
						paramValueMap.put("PARAM_VALUE_MAP", generateGlEntryMap);
						insertRegulator.insert(generateInsertPstmt, paramValueMap, glQuery.getQueryParam());
						// logger.trace("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconId);
						System.out.println("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconid);
						 PreparedStatement generateInsertPstmtOrig = connection.prepareStatement(glQuery.getQueryString());
						Map<String, Object> paramValueMapOrig=new HashMap<String, Object>();
						paramValueMapOrig.put("PARAM_VALUE_MAP", generateOriginalGlEntryMap);
						insertRegulator.insert(generateInsertPstmtOrig, paramValueMapOrig, glQuery.getQueryParam());
						//logger.trace("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconId);
						System.out.println("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconid);
						///////////
					}
					
					
					
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
				}
			} else if (REJECTED.equalsIgnoreCase(status)) {  

				String userId = (String) activityDataMap.get("userId");
				String comments = (String) activityDataMap.get("comment");
				String requesterComments = userId + " : " + comments;
				// Recon Data Sorces will get through DS_NAME key from front end
				String reconDataSource = (String) (activityRecordsMap.get("reconDataSource"));
				String reconTableName = reconDataSource.substring(0, reconDataSource.length() - 14);
				Map<String, List> selectedRecords = (Map<String, List>) activityRecordsMap.get(SELECTED_RECORDS);
				List<Map<String, Object>> recordsList = (List<Map<String, Object>>) activityRecordsMap
						.get("recordsList");

				PreparedStatement selectAuditStmt = null;
				PreparedStatement auditInsertPstmt = null;
				PreparedStatement reconDataSelectPstmt = null;
				PreparedStatement stagingDataSelectPstmt = null;
				PreparedStatement auditDataInsertPstmt = null;
				PreparedStatement stagingDataUpdatePstmt = null;
				PreparedStatement reconDataUpdatePstmt = null;
				PreparedStatement reconDataSelectFromStagingPstmt = null;
				PreparedStatement reconDataInsertPstmt = null;
				String reconTableIdSeqName = reconTableName + "MM_ID_SEQ";
				String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";
				String idseqname = reconTableName + "MM_ID_SEQ";
				PreparedStatement reconTableIdGenPstmt = connection.prepareStatement(reconTableIdGenQry);
				List<Long> reconcileList = new ArrayList<Long>();
				Map<Long, String> unreconcileList = new HashMap<Long, String>();
				
				
				/*try{
					String apprUserId=(String) activityDataMap.get("userId");
					String makerId=(String) activityDataMap.get("activity_owner");
					String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
					
					String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
					String appMailId="";
					String makerEmail="";
					
					try{
						Statement st=connection.createStatement();
						ResultSet rs=st.executeQuery(strforApproverquery);
						if(rs.next()){
							
							appMailId=rs.getString(1);
						}
						st=connection.createStatement();
						 rs=st.executeQuery(strforMakerquery);
						if(rs.next()){
							
							makerEmail=rs.getString(1);
						}
						
						OperationMail mail=	new OperationMail(appMailId, makerEmail);
						mail.sendMail(appMailId, makerEmail);
						mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, recordsList, "FORCE MATCH OPERATION REJECTED");
				
					}catch(Exception e){
						e.printStackTrace();
					}
				}catch(Exception e){
					e.printStackTrace();
				}*/
				try {

					long version = 0;
					long sid = 0;
					long reconid = (long) recordsList.get(0).get("RECON_ID");
					System.out.println("selectedRecords   RAMA   "+selectedRecords);
					for (String key : selectedRecords.keySet()) {
						List<Map<String, Object>> l = (List<Map<String, Object>>) selectedRecords.get(key);
						for (Map<String, Object> selectedRec : l) {
							String stgTableName = key + "_STG";
							String auditTableName = key + "_STG_AUDIT";
							
							//FINANCE -  ONS
							if(key.equalsIgnoreCase("CBS") ){	
									stgTableName="FIN_ONS_CBS_STG";
									auditTableName="FIN_ONS_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CBO") ){	
									stgTableName="FIN_ONS_CBO_STG";
									auditTableName="FIN_ONS_CBO_STG_AUDIT";
								}
							//FINANCE -  MPCLEAR
							else if(key.equalsIgnoreCase("MP_CLEAR_CBS") ){	
									stgTableName="FIN_MP_CLEAR_CBS_STG";
									auditTableName="FIN_MP_CLEAR_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("EXT") ){	
									stgTableName="FIN_MP_CLEAR_EXT_STG";
									auditTableName="FIN_MP_CLEAR_EXT_STG_AUDIT";
								}
							//FINANCE -  SUSPENSE
							else if(key.equalsIgnoreCase("FIN_SUSPENSE_DEBIT") || key.equalsIgnoreCase("FIN_SUSPENSE_CREDIT")){	
									stgTableName="FIN_SUSPENSE_STG";
									auditTableName="FIN_SUSPENSE_STG_AUDIT";
								}
							//CARDS -  ATM MASTER CARD ACQUIRER
							else if(key.equalsIgnoreCase("ATM_MC_CBS") ){	
									stgTableName="CARD_ATM_MC_CBS_STG";
									auditTableName="CARD_ATM_MC_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("ACQ") ){	
									stgTableName="CARD_ATM_MC_ACQ_STG";
									auditTableName="CARD_ATM_MC_ACQ_STG_AUDIT";
								}
							//CARDS -  ATM VISA ACQUIRER
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ACQ_CBS") ){	
									stgTableName="CARD_ATM_VISA_ACQ_CBS_STG";
									auditTableName="CARD_ATM_VISA_ACQ_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ACQ_EXT") ){	
									stgTableName="CARD_ATM_VISA_ACQ_EXT_STG";
									auditTableName="CARD_ATM_VISA_ACQ_EXT_STG_AUDIT";
								}
							//CARDS -  POS NI ACQUIRER
							else if(key.equalsIgnoreCase("CARD_POSNI_CBS") ){	
									stgTableName="CARD_POSNI_CBS_STG";
									auditTableName="CARD_POSNI_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_POSNI_TRAN") ){	
									stgTableName="CARD_POSNI_TRAN_STG";
									auditTableName="CARD_POSNI_TRAN_STG_AUDIT";
								}
							//CARDS -  CREDIT CARD STATEMENT
							else if(key.equalsIgnoreCase("CREDIT_CARD_STATEMENT_CBS") ){	
									stgTableName="CREDIT_CARD_STATEMENT_CBS_STG";
									auditTableName="CREDIT_CARD_STATEMENT_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CREDIT_CARD_STATEMENT_TRAN") ){	
									stgTableName="CREDIT_CARD_STATEMENT_TRAN_STG";
									auditTableName="CREDIT_CARD_STATEMENT_TRAN_STG_AUDIT";
								}
							//CARDS -  ATM/POS VISA ISSUER
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ISS_CBS") ){	
									stgTableName="CARD_ATM_VISA_ISS_CBS_STG";
									auditTableName="CARD_ATM_VISA_ISS_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CARD_ATM_VISA_ISS_INCT") ){	
								stgTableName="CARD_ATM_VISA_ISS_INCT_STG";
								auditTableName="CARD_ATM_VISA_ISS_INCT_STG_AUDIT";
							}
							//CARDS -  OPG BD ACQ
							else if(key.equalsIgnoreCase("ONUS_CBS") ){	
									stgTableName="CARD_ECOM_ONUS_CBS_STG";
									auditTableName="CARD_ECOM_ONUS_CBS_STG_AUDIT";
							}
							else if(key.equalsIgnoreCase("ONUS_SETTL") ){	
								stgTableName="CARD_ECOM_ONUS_SETTL_STG";
								auditTableName="CARD_ECOM_ONUS_SETTL_STG_AUDIT";
							}
							//CARDS -  OPG OTHER BANK ACQ
							else if(key.equalsIgnoreCase("OFFUS_CBS") ){	
									stgTableName="CARD_ECOM_OFFUS_CBS_STG";
									auditTableName="CARD_ECOM_OFFUS_CBS_STG_AUDIT";
							}
							else if(key.equalsIgnoreCase("OFFUS_SETTL") ){	
								stgTableName="CARD_ECOM_OFFUS_SETTL_STG";
								auditTableName="CARD_ECOM_OFFUS_SETTL_STG_AUDIT";
							}
							//CENTRAL OPERATION DEPARTMENT - CDM
							else if(key.equalsIgnoreCase("CO_CDM_CBS") ){	
									stgTableName="CO_CDM_CBS_STG";
									auditTableName="CO_CDM_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_CDM_JOURNAL") ){	
									stgTableName="CO_CDM_JOURNAL_STG";
									auditTableName="CO_CDM_JOURNAL_STG_AUDIT";
								}
							//CENTRAL OPERATION DEPARTMENT - ATM TRANSACTIONS
							else if(key.equalsIgnoreCase("CO_ATM_CBS") ){	
									stgTableName="CO_ATM_CBS_STG";
									auditTableName="CO_ATM_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_ATM_JOURNAL") ){	
									stgTableName="CO_ATM_JOURNAL_STG";
									auditTableName="CO_ATM_JOURNAL_STG_AUDIT";
								}
							//CENTRAL OPERATION DEPARTMENT - ACH
							else if(key.equalsIgnoreCase("CO_ACH_CBS") ){	
									stgTableName="CO_ACH_CBS_STG";
									auditTableName="CO_ACH_CBS_STG_AUDIT";
								}
							else if(key.equalsIgnoreCase("CO_ACH_CBO") ){	
									stgTableName="CO_ACH_CBO_STG";
									auditTableName="CO_ACH_CBO_STG_AUDIT";
								}
							
							sid = (long) selectedRec.get("SID");
							stagingDataSelectPstmt = connection
									.prepareStatement("select * from " + stgTableName + " where SID=" + sid);
							List<String> columnList = new ArrayList<String>();
							ResultSet stagingRs = stagingDataSelectPstmt.executeQuery();
							ResultSetMetaData rsm = stagingRs.getMetaData();
							int columnCount = rsm.getColumnCount();
							Map stagingDataMap = new HashMap();
							while (stagingRs.next()) {
								version = Integer.parseInt(stagingRs.getObject("VERSION").toString());
								// ++version;
								for (int i = 1; i <= columnCount; i++) {
									columnList.add(rsm.getColumnName(i));
									stagingDataMap.put(rsm.getColumnName(i),
											stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
								}
							}

							Query auditQuery = OperationsUtil.getInsertQueryConf(auditTableName, connection);
							auditDataInsertPstmt = connection.prepareStatement(auditQuery.getQueryString());
							Map paramValueMap = new HashMap();

							paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
							selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
							insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
							// version=Integer.parseInt(selectedRec.get("VERSION").toString());
							++version;
							// sid=(long) selectedRec.get("SID");
							String updateQuery = "UPDATE " + stgTableName
									+ " SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
							System.out.println("STAGING  updateQuery   "+updateQuery);
							stagingDataUpdatePstmt = connection.prepareStatement(updateQuery);
							stagingDataUpdatePstmt.setObject(1, version);
							stagingDataUpdatePstmt.setObject(2, requesterComments);
							stagingDataUpdatePstmt.setObject(3,
									new Timestamp(Calendar.getInstance().getTimeInMillis()));
							// stagingDataUpdatePstmt.setObject(4, "MM");
							// stagingDataUpdatePstmt.setObject(5, reconid);
							stagingDataUpdatePstmt.setObject(4, sid);
							int row = stagingDataUpdatePstmt.executeUpdate();

						}

					}
					for (Map<String, Object> reconRec : recordsList) {
						Long reconId = (Long) reconRec.get("RECON_ID");

						PreparedStatement reconDataSelectstmt = connection
								.prepareStatement("SELECT * FROM  " + reconTableName + "  WHERE RECON_ID=" + reconId);
						ResultSet recondatars = reconDataSelectstmt.executeQuery();
						while (recondatars.next()) {
							int recversion = Integer.parseInt(recondatars.getObject("VERSION").toString());
							++recversion;
							
							System.out.println("UPDATE " + reconTableName
									+ " SET WORKFLOW_STATUS='N',UPDATED_ON=?,VERSION=?,ACTIVITY_COMMENTS=? WHERE SID="
									+ recondatars.getObject("SID")+" AND RECON_ID=" + reconId);
							
							
							reconDataUpdatePstmt = connection.prepareStatement("UPDATE " + reconTableName
									+ " SET WORKFLOW_STATUS='N',UPDATED_ON=?,VERSION=?,ACTIVITY_COMMENTS=? WHERE SID="
									+ recondatars.getObject("SID")+" AND RECON_ID=" + reconId);
							reconDataUpdatePstmt.setObject(1, new Timestamp(Calendar.getInstance().getTimeInMillis()));
							reconDataUpdatePstmt.setObject(2, recondatars.getObject("VERSION"));
							reconDataUpdatePstmt.setObject(3, requesterComments);
							reconDataUpdatePstmt.executeUpdate();
						}
					}

				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
				}
			} else if ("PENDING".equalsIgnoreCase(status)) {
				/*
				 * 
				 * String tableName=integrationName+"_STG"; String query=
				 * "UPDATE "+tableName+
				 * " set ACTIVE_INDEX='Y',Status='APPROVED',WORKFLOW_STATUS='Y' where SID=?"
				 * ; PreparedStatement updateStmt=null;
				 * 
				 * try{ updateStmt=connection.prepareStatement(query);
				 * 
				 * for(Map<String,Object> rec:records){
				 * updateStmt.setLong(1,(Long)rec.get(SID));
				 * updateStmt.addBatch(); } updateStmt.executeUpdate();
				 * 
				 * }catch(Exception e){ e.printStackTrace(); }
				 * 
				 * 
				 * updateResultStatus(result, FAILED, "Undefined Action");
				 */}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return result;
	}

	private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
					throws ClassNotFoundException, SQLException {
		String QueryParam = insertQueryConf.getQueryParam();
		List<Map<String, Object>> auditData = loadRegulator.loadCompleteData(rec, selectAuditStmt,
				"SID@BIGINT,VERSION@BIGINT");

		if (auditData != null) {
			for (Map<String, Object> auditRec : auditData) {
				Map paramValueMap = new HashMap();
				/*
				 * Long version= (Long) rec.get("VERSION"); ++version;
				 * auditRec.put("VERSION",version);
				 */
				paramValueMap.put("PARAM_VALUE_MAP", auditRec);

				insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
		}

	}

}
