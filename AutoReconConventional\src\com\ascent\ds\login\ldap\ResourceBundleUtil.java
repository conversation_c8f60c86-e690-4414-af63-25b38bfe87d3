package com.ascent.ds.login.ldap;


import java.util.ResourceBundle;

public class ResourceBundleUtil {
	public static ResourceBundle rb;


	public static ResourceBundle getBundle(String type) {
		if (rb == null){
			if(type.equalsIgnoreCase("AD"))
			rb = ResourceBundle.getBundle("ldap.LDAPResources_AD");
			else if(type.equalsIgnoreCase("OD"))
				rb = ResourceBundle.getBundle("ldap.LDAPResources_OD");
		}
		return rb;
	}

	public static String getString(String type,String key) {
		return getBundle(type).getString(key);
	}

	public static boolean containsKey(String type,String key) {
		return getBundle(type).containsKey(key);
	}

	public static void main(String[] args) {
		//ResourceBundleUtil.getBundle();
		//System.out.println("Directory Server"+ResourceBundleUtil.getString("directoryserver"));
	}

}
