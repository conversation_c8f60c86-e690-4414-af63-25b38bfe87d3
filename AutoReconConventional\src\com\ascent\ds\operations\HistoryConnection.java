package com.ascent.ds.operations;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class HistoryConnection {
	private static HistoryConnection instance;
	private Properties bootProperties;
	private Properties dbProperties;
	private Properties applicationProperties;
	/*private Queries etlQueryConfs = null;
	private Integrations etlConfs = null;*/
	private static Logger logger = LogManager.getLogger(HistoryConnection.class);
	private HistoryConnection() throws Exception {

		String bootPropFileName = "boot.properties";
		String dbPropFileName = "db.properties";
		String applicationPropFileName = "application.properties";

		this.bootProperties = new Properties();
		this.dbProperties = new Properties();
		this.applicationProperties = new Properties();

		InputStream inputStream = getClass().getClassLoader().getResourceAsStream(bootPropFileName);

		if (inputStream != null) {
			try {
				this.bootProperties.load(inputStream);

				logger.trace("Loaded bootProperties ");
				for (Object key : this.bootProperties.keySet()) {
					logger.trace(key + " : " + this.bootProperties.get(key));
					this.bootProperties.get(key);
				}
				logger.trace("Sucessfully ");
				String appMode = (String) this.bootProperties.get("APP_MODE");
				try {

					InputStream dbInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + dbPropFileName);

					InputStream appInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + applicationPropFileName);

					if (dbInputStream != null) {
						try {
							this.dbProperties.load(dbInputStream);

							logger.trace("Loaded dbProperties ");
							for (Object key : this.dbProperties.keySet()) {
								logger.trace(key + " : " + this.dbProperties.get(key));
								this.dbProperties.get(key);
							}
							logger.trace("Sucessfully ");

						} catch (Exception e) {
							logger.trace("Unable to load dbProperties ");
							e.printStackTrace();
							throw e;

						}
					} else {
						logger.trace("property file '" + dbPropFileName + "' not found in the classpath");
						throw new FileNotFoundException(
								"property file '" + dbPropFileName + "' not found in the classpath");
					}

					if (appInputStream != null) {

						try {
							this.applicationProperties.load(appInputStream);

							logger.trace("Loaded applicationProperties ");
							for (Object key : this.applicationProperties.keySet()) {
								logger.trace(key + " : " + this.applicationProperties.get(key));

							}
							logger.trace("Sucessfully ");

						} catch (Exception e) {
							logger.trace("Unable to load applicationProperties ");
							e.printStackTrace();
							throw e;

						}

					} else {
						logger.trace("property file '" + applicationPropFileName + "' not found in the classpath");
						throw new FileNotFoundException(
								"property file '" + applicationPropFileName + "' not found in the classpath");
					}

				} catch (Exception e) {
					e.printStackTrace();
					logger.trace("unable to load properties under the APP_MODE " + appMode);
					throw new Exception("unable to load properties under the APP_MODE " + appMode);
				}
			} catch (Exception e) {
				logger.trace("Unable to load bootProperties properties");
				e.printStackTrace();
				throw e;
			}

		} else {
			logger.trace("property file '" + bootPropFileName + "' not found in the classpath");
			throw new FileNotFoundException("property file '" + bootPropFileName + "' not found in the classpath");
		}
		try {
			//loadEtlConf();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			throw e;
		}
	}

	static {
		try {
			instance = new HistoryConnection();
		} catch (Exception e) {
			throw new RuntimeException("Exception occured in creating singleton instance");
		}
	}
	
	public static HistoryConnection getInstance() {
		return instance;
	}

	public Properties getBootProperties() {
		return bootProperties;
	}

	public void setBootProperties(Properties bootProperties) {
		this.bootProperties = bootProperties;
	}

	public Properties getDbProperties() {
		return dbProperties;
	}

	public void setDbProperties(Properties dbProperties) {
		this.dbProperties = dbProperties;
	}

	public Properties getApplicationProperties() {
		return applicationProperties;
	}

	public void setApplicationProperties(Properties applicationProperties) {
		this.applicationProperties = applicationProperties;
	}
	public static Connection getConnection() {
		Connection connection = null;
		try {
			Properties properties = HistoryConnection.getInstance().getDbProperties();

			Class.forName((String) properties.getProperty("history_driver"));
			String url = (String) properties.getProperty("history_url");
			String user = (String) properties.getProperty("history_username");
			String pwd = (String) properties.getProperty("history_password");

			connection = DriverManager.getConnection(url, user, pwd);
			logger.trace("Connected database successfully...");

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("ERROR", e);
		}

		return connection;
	}

}
