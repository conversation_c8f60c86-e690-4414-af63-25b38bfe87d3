package com.ascent.test;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Simple test class to verify log4j2 configuration is working
 */
public class Log4j2Test {
    
    private static final Logger logger = LogManager.getLogger(Log4j2Test.class);
    
    public static void main(String[] args) {
        testLogging();
    }
    
    public static void testLogging() {
        logger.trace("This is a TRACE message");
        logger.debug("This is a DEBUG message");
        logger.info("This is an INFO message");
        logger.warn("This is a WARN message");
        logger.error("This is an ERROR message");
        
        // Test with exception
        try {
            throw new RuntimeException("Test exception for logging");
        } catch (Exception e) {
            logger.error("Exception caught during testing", e);
        }
        
        System.out.println("Log4j2 test completed. Check the log files:");
        System.out.println("- Console output (above)");
        System.out.println("- D:/tomcat1 8.5/apache-tomcat-8.5.99/logs/logs.log");
        System.out.println("- D:/tomcat1 8.5/apache-tomcat-8.5.99/logs/logs2.log");
    }
}
