# The webRoot directory:
# the directory that the servlet engine regards as the place where applications 
# that use the servlet engine should be installed.  Generally, it is safe to leave
# this at the default setting of __AUTODETECT__.  When the SmartClient server is
# started, it logs a message to stdout telling you the autodetected path to your
# webRoot.  If this path is not your actual webRoot, then you'll want to override
# this config parameter here.
#
# Valid values: 
#
# 1. Absolute path to the webRoot directory
#
# 2. Special token:  __AUTODETECT__
#    When this token is used, SmartClient attempts to auto-detect the webRoot using
#    standard servlet APIs.  This may or may not work - depending on your
#    container type and deployment type.  For example, WAR/EAR deployments
#    on some containers never make it to disk, and so the container refuses
#    to provide the webRoot path.
#  
#    If SmartClient cannnot detect the webRoot, it sets the webRoot to
#    __USE_CONTAINER__ (see below).
#
# 3.  Special token: __USE_CONTAINER__
#     When this token is used, SmartClient uses standard servet APIs for accessing
#     filesystem resources.  This is slower than direct file access and, since
#     the servlet APIs provide no mechanism for writing to disk, means that some
#     development tools like the FileAssembler will not work.
#
webRoot: __AUTODETECT__

# if you've moved the isomorphic directory from its default location in webRoot,
# set the root-relative path to it here
#
# For example, if in your deployment the 'isomorphic' dir is in /foo/bar, then set
# then you'll need to set this to foo/bar/isomorphic
isomorphicPathRootRelative: isomorphic

# -------------- PICK DATABASE TO USE --------------------
#
# The SmartClient SDK ships with examples that use a database as the persistence
# layer.  By default, the SDK uses a built-in version of HSQLDB, but you can 
# specify a different database to use here.

# which database do you want to use?  HSQLDB is enabled by default.
#sql.defaultDatabase: HSQLDB

# If you want to use Mysql instead, uncomment the following line
# and comment all other sql.defaultDatabase definitions
#sql.defaultDatabase: Mysql

# If you want to use Oracle instead, uncomment the following line
# and comment all other sql.defaultDatabase definitions
#sql.defaultDatabase: Oracle

# If you want to use Postgres instead, uncomment the following line
# and comment all other sql.defaultDatabase definitions
#sql.defaultDatabase: PostgreSQL

# If you want to use DB2 instead, uncomment the following line
# and comment all other sql.defaultDatabase definitions
#sql.defaultDatabase: DB2

# If you want to use Microsoft SQL Server instead, uncomment the following
# line and comment all other sql.defaultDatabase definitions
sql.defaultDatabase: SQLServer

# If you want to use Firebird instead, uncomment the following line
# and comment all other sql.defaultDatabase definitions
#sql.defaultDatabase: Firebird

# If you want to use Informix instead, uncomment the following line
# and comment all other sql.defaultDatabase definitions
#sql.defaultDatabase: Informix

# If you want to use a different type of database, there is a generic 
# SQL92 driver that you may be able to use.  Scan the client documentation
# for "sqlSettings" and "sqlDataSource" for details of this driver,
# including its limitations

# -------------- SETTINGS FOR HSQLDB --------------------

sql.HSQLDB.database.type: hsqldb
sql.HSQLDB.interface.type: driverManager

sql.HSQLDB.driver: org.hsqldb.jdbcDriver
sql.HSQLDB.driver.url: ******************************************************

# -------------- SETTINGS FOR MYSQL --------------------

# These are the settings for use with the Mysql database.  If you have 
# just done a fresh install of MySQL on the same machine where you are 
# running your servlet engine, the values provided below will probably 
# just work on most platforms.

# Configuration for Mysql Connector/J
sql.Mysql.database.type: mysql
sql.Mysql.database.ansiMode: false

# Driver settings
sql.Mysql.interface.type: dataSource
sql.Mysql.driver: com.mysql.jdbc.jdbc2.optional.MysqlDataSource
sql.Mysql.driver.driverName: mysql

# hostname and port where the database server is installed
sql.Mysql.driver.serverName: localhost
sql.Mysql.driver.portNumber: 3306

# name of the database to use
sql.Mysql.driver.databaseName: isomorphic

# username and password that can create and modify tables in that database
# this user must have the following privileges for the system to function
# properly: create/alter/drop table; insert/update/replace/delete rows.
sql.Mysql.driver.user: root
sql.Mysql.driver.password: 


# -------------- SETTINGS FOR ORACLE --------------------

# These are example settings for use with the Oracle database.
sql.Oracle.database.type: oracle

# hostname and port where the database server is installed
sql.Oracle.driver.serverName: localhost
sql.Oracle.driver.portNumber: 1521

# Driver settings
sql.Oracle.interface.type: driverManager
sql.Oracle.driver: oracle.jdbc.driver.OracleDriver
sql.Oracle.driver.driverName: oracle

# SID of Oracle Database
sql.Oracle.driver.databaseName: isomorphic

# username and password that can create and modify tables in that database
# this user must have the following privileges for the system to function
# properly: create/alter/drop table; create/drop sequences; 
# insert/update/replace/delete rows.
sql.Oracle.driver.user: system
sql.Oracle.driver.password: manager

# -------------- SETTINGS FOR PostgreSQL --------------------

# These are example settings for use with the PostgreSQL database.
sql.PostgreSQL.database.type: postgresql

# hostname and port where the database server is installed
sql.PostgreSQL.driver.serverName: 127.0.0.1
sql.PostgreSQL.driver.portNumber: 5432

# Driver settings
sql.PostgreSQL.interface.type: driverManager
sql.PostgreSQL.driver: org.postgresql.Driver
sql.PostgreSQL.driver.name: Postgresql

# name of the database to use
sql.PostgreSQL.driver.databaseName: ArabBank

# username and password that can create and modify tables in that database
# this user must have the following privileges for the system to function
# properly: create/alter/drop table; create/drop sequences; 
# insert/update/replace/delete rows.
sql.PostgreSQL.driver.user: ARB
sql.PostgreSQL.driver.password: ARB@123

# -------------- SETTINGS FOR DB2 ---------------------------

# These are example settings for use with the DB2 database.
sql.DB2.database.type: db2

# hostname and port where the database server is installed
sql.DB2.driver.serverName: localhost
sql.DB2.driver.portNumber: 50000

# Driver settings
sql.DB2.interface.type: dataSource
sql.DB2.driver: com.ibm.db2.jcc.DB2DataSource
sql.DB2.driver.driverType: 4

# name of the database to use
sql.DB2.driver.databaseName: ISOMORPH

# username and password that can create and modify tables in that database
# this user must have the following privileges for the system to function
# properly: create/alter/drop table; create/drop sequences; 
# insert/update/replace/delete rows.
sql.DB2.driver.user: db2admin
sql.DB2.driver.password:

# -------------- SETTINGS FOR SQLServer ------ --------------

# These are example settings for use with the SQLServer database.
sql.SQLServer.database.type: sqlserver

# Driver settings
sql.SQLServer.interface.type: driverManager
sql.SQLServer.driver: com.microsoft.sqlserver.jdbc.SQLServerDriver

# SQLServer is configured via a jdbc URL params are: 
#                                          hostname :port;             dbName    ;    uid;         password
#sql.SQLServer.driver.url: *******************************************************************************************************
sql.SQLServer.driver.url: ************************************************************************************************************
#sql.SQLServer.driver.url: *****************************************************************************************************

#sql.SQLServer.driver.url: ***********************************************************************************************
#sql.SQLServer.driver.url: ********************************************************************************************
#sql.SQLServer.driver.url: **********************************************************************************************
#sql.SQLServer.driver.url: ***********************************************************************************************
# Note that settings are alightly different if you are using SQL Server 2000 or earlier - 
# see http://forums.smartclient.com/showthread.php?p=11879#post11879

# -------------- SETTINGS FOR Informix ---------------------------

# These are example settings for use with the Informix database.
sql.Informix.database.type: informix

# hostname and port where the database server is installed
sql.Informix.driver.serverName: localhost
sql.Informix.driver.portNumber: 1526

# Driver settings
sql.Informix.interface.type: driverManager
sql.Informix.driver: com.informix.jdbc.IfxDriver
sql.Informix.driver.driverName: informix

# name of the database to use
sql.Informix.driver.databaseName: isomorphic:INFORMIXSERVER=isomorphic

# username and password that can create and modify tables in that database
# this user must have the following privileges for the system to function
# properly: create/alter/drop table; create/drop sequences; 
# insert/update/replace/delete rows.
sql.Informix.driver.user: informix
sql.Informix.driver.password: 

# -------------- SETTINGS FOR Firebird ---------------------------

# These are example settings for use with the Firebird database.
sql.Firebird.database.type: firebirdsql

# hostname and port where the database server is installed
sql.Firebird.driver.serverName: localhost
sql.Firebird.driver.portNumber: 3050

# Driver settings
sql.Firebird.interface.type: driverManager
sql.Firebird.driver: org.firebirdsql.jdbc.FBDriver
sql.Firebird.driver.driverName: firebirdsql

# name of the database to use
sql.Firebird.driver.databaseName: C:\isomorphic.fdb

# username and password that can create and modify tables in that database
# this user must have the following privileges for the system to function
# properly: create/alter/drop table; create/drop sequences; 
# insert/update/replace/delete rows.
sql.Firebird.driver.user: sysdba
sql.Firebird.driver.password: 

# -------------- SETTINGS FOR Intersystems Cache --------------

sql.Cache.driver.user: _SYSTEM
sql.Cache.driver.password: sys
sql.Cache.driver.databaseName: SAMPLES
sql.Cache.driver.portNumber: 1972
sql.Cache.driver.serverName: localhost

# -------------- HIBERNATE SETTINGS --------------------
# Where the system looks for your Hibernate configuration
hibernate.config: $webRoot/WEB-INF/classes/hibernate.cfg.xml

# -------------- SAMPLE JNDI SETTINGS ------------------
# This is the minimal set of properties needed to define a SmartClient/SmartGWT
# database connection to a JNDI resource.  These sample properties assume an 
# Oracle JNDI resource with the name "jndiTest" - such a resource would be 
# configured similar to this in Tomcat:
#
# <Resource name="jdbc/jndiTest"
#                  auth="Container"
#                  type="javax.sql.DataSource"
#                  driverClassName="oracle.jdbc.driver.OracleDriver"
#                  url="jdbc:oracle:thin:@***************:1521:xe"
#                  username="system"
#                  password="manager"
#                  initialSize="5"                 
#                  maxActive="50" />
#
# (Note that the java:comp/env/ prelude in this line is optional - SC/SGWT Server will
# automatically look there if it can't find the resource in the absolute location)
#sql.myOracleConnection.driver.name: java:comp/env/jdbc/jndiTest
#sql.myOracleConnection.database.type: oracle
#sql.myOracleConnection.interface.type: jndi

# -------------- LOADING APP AND DATASOURCE DEFINITIONS --------------------

# Where the system looks for DataSource definition files ([dataSourceId].ds.xml or
# [dataSourceID].ds.js).  It's useful to put all your DataSources in one 
# directory since DataSources are frequently shared between applications.  
# "project.datasources" is also where the DataSource Importer tool looks 
# for available DataSources.
project.datasources: $webRoot/ds,$webRoot/ds/onusDebitAtmDs, $webRoot/ds/adminds, $webRoot/ds/loginds, $webRoot/ds/operationsds, $webRoot/ds/paymentOrder,$webRoot/ds/issuerOnsImal,$webRoot/ds/retailAsset,$webRoot/ds/utilityPaymentsDs,$webRoot/ds/cboFtDs,$webRoot/ds/suspensGlDs,$webRoot/ds/marginGlDs,$webRoot/ds/masterCreditDs,$webRoot/ds/eccCboDs,$webRoot/ds/masterDebitDs,$webRoot/ds/tradeDealDS,$webRoot/ds/FinanceONSDs,$webRoot/ds/FinanceMPCLEARDs,$webRoot/ds/CentralCDMDs,$webRoot/ds/CentralATMDs,$webRoot/ds/CentralACHDs,$webRoot/ds/CardAtmMasterCardAcqDs,$webRoot/ds/CardAtmVisaAcqDs,$webRoot/ds/CardPosNiAcquirer,$webRoot/ds/CardCreditCardStatementDs,$webRoot/ds/CardAtmVisaIssDs,$webRoot/ds/FinanceSuspenseDs,$webRoot/ds/CardEcomOffusDs,$webRoot/ds/CardEcomOnusDs
project.ui: $webRoot/shared/ui, $webRoot/isomorphic/system/reference/inlineExamples/ui
project.apps: $webRoot/shared/app

# $webRoot/ds/acqMasterAtmDs,$webRoot/ds/acqNapsAtmDs,$webRoot/ds/acqNAPS2AtmDs,$webRoot/ds/acqUnionPayPosDs,$webRoot/ds/acqVisaPosDs, $webRoot/ds/atmReconsDs,$webRoot/ds/issuerVisaDebitAtmDs, $webRoot/ds/issuerVisaCreditDs, $webRoot/ds/issuerMasterAtmDs,$webRoot/ds/depositatm, $webRoot/ds/masteratmds, $webRoot/ds/issuerNapsAtmDs,$webRoot/ds/acqVisaAtmDs,$webRoot/ds/acqNapsPosDs,$webRoot/ds/acqMasterPos,$webRoot/ds/acqQPayDs,$webRoot/ds/onusPayrollAtmDs,$webRoot/ds/onusCreditPosDs,$webRoot/ds/acqUnionPayAtmDs,$webRoot/ds/onusDebitPOSDs,$webRoot/ds/onusPayrollPosDs,$webRoot/ds/onusCreditAtmDs,$webRoot/ds/issuerMasterCreditPosDs,$webRoot/ds/issuerNapsDebitPosDs,$webRoot/ds/payrollCardAtmDs, $webRoot/ds/depositDebitCardDs,$webRoot/ds/issuerVisaDebitPosDs,$webRoot/ds/issuerVisaCreditPosDs,


# -------------- SECURE APPLICATION EXAMPLE DEFINITIONS --------------------
authentication.enabled: yes
datasources.pool.enabled :yes
# superuserRole: If specified, user with this role will have access to all 
# dataSource operations regardless of the any requiresRole settings
#authentication.superuserRole: manager

authenticator.authExample:com.isomorphic.datasource.DataSourceAuthenticator
authenticator.authExample.datasource: user
authenticator.authExample.usernameField: username
authenticator.authExample.passwordField: password
authenticator.authExample.saltField: salt
authenticator.authExample.cookieDomain: .smartclient.com
authenticator.authExample.sessionTimeout: 900



# -------------- JMS Configuration for Real Time Messaging  --------------------
# If you comment in the properties below, the simple messaging example will work
# against Active MQ JMS instead.  Be sure to start your ActiveMQ server and double
# check the configuraition below, notably the TCP endpoint of your ActiveMQ server.
# If you make changes here, you'll also need to modify ActiveMQ's jndi.properties
# also located in WEB-INF/classes
#
# You'll also need to drop the ActiveMQ jar into WEB-INF/lib - it's typically
# named apache-activemq-4.1.1.jar or similar.
#
# For additional information, see this forum post:
# http://forums.smartclient.com/showthread.php?p=1785#post1785
#
#messaging.dispatcherImplementer: com.isomorphic.messaging.JMSMessageDispatcher
#
#jndi.messaging.java.naming.factory.initial: org.apache.activemq.jndi.ActiveMQInitialContextFactory
#jndi.messaging.java.naming.provider.url: tcp://localhost:61616
#
#messaging.jms.context: messaging
#messaging.jms.jndiPrefix: 
#messaging.jms.topicConnectionFactory: TopicConnectionFactory


# -------------- Other settings --------------------
# The setting RPCManager.enabledBuiltinMethods enables or disables the BuiltInRPCs - RPC calls
# that are built into the SmartClient Server.  The setting below reflects the framework default
# of enabling only those RPCs that are typically needed in an application.
# 
# See the JavaDoc for com.isomorphic.rpc.BuiltinRPC and com.isomorphic.tools.BuiltinRPC for all
# available builtinRPCs and their behavior.
# 
# Note that many of the BuiltinRPCs are designed for use by tools such as Visual Builder, and
# provide services such as direct access to the file system (for load and save of screens) that
# would be unsafe to expose to untrusted users.
#
#RPCManager.enabledBuiltinMethods: getPdfObject, getPdfObject, xmlToJS, uploadProgressCheck, exportClientData, downloadClientExport

# SECURITY NOTE: DO NOT ENABLE THIS IN PRODUCTION
# The special '*' value enables all builtin RPCs. 
# Do not deploy applications with all builtin RPCs enabled. Some of these are
# development-only tools that have not been audited from a security standpoint.
RPCManager.enabledBuiltinMethods: *
RPCManager.enableBuiltinRPCs: true
# SECURITY: DO NOT ENABLE THIS IN PRODUCTION
# This datasource allows remote deletion and viewing of any file under webRoot.
# It is used by the Visual Builder to load/save views.
FilesystemDataSource.enabled: true
