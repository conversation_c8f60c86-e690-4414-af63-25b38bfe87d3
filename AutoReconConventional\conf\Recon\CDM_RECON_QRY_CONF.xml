<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">
	
	<query id="17">
        <name>CDM_RECON_INSERT_QRY</name>
		<targetTables>CO_CDM_RECON</targetTables>
        <queryString>
			INSERT INTO CO_CDM_RECON
           (ID,SID ,RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,TERMINAL_ID,ACCOUNT_NUM,REF_NUM,TRAN_SEQ
           ,WORKFLOW_STATUS,SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,MATCH_TYPE,ACTIVE_INDEX,USER_ID
           ,UPDATED_ON,CREATED_ON,COMMENTS,RULE_NAME,ACTIVITY_STATUS,OPERATION
           ,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?)
		
	</queryString>
		<queryParam>
			ID@BIGINT,SID@BIGINT ,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TERMINAL_ID@VARCHAR,ACCOUNT_NUM@VARCHAR,REF_NUM@VARCHAR,TRAN_SEQ@VARCHAR
           ,WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR
           ,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR
           ,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,ACTIVITY_COMMENTS@VARCHAR				
		</queryParam>
    </query>
     	
	<query id="18">
        <name>CDM_RECON_UPSTREAM_QRY</name>
		<targetTables>CO_CDM_CBS_STG,CO_CDM_JOURNAL_STG</targetTables>
        <queryString>
				
	SELECT * FROM(
			select 'CBS' AS RECON_SIDE,SID,CDM_ID  AS TERMINAL_ID,CUSTOMER_ACCT AS ACCOUNT_NUM,REPLACE(REFERENCE_NUMBER,'X','*') AS REF_NUM,
			AMOUNT AS TRA_AMT,TRAN_DATE  AS TRA_DATE,
			SUBSTRING(TRAN_PARTICULAR,22,4)  AS TRAN_SEQ ,DRCR AS DEB_CRE_IND,'CO_CDM_CBS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
			 FROM CO_CDM_CBS_STG WITH (NOLOCK)
			 WHERE  (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'

	UNION ALL

		SELECT 'CDM' AS RECON_SIDE,SID,TERMINALID AS TERMINAL_ID,ACCOUNTNO1 AS ACCOUNT_NUM,CARDNUMBER AS REF_NUM,AMOUNT AS TRA_AMT,
		TXNDATETIME AS TRA_DATE,SEQUENCENUMBER AS TRAN_SEQ ,'' AS DEB_CRE_IND,'CO_CDM_JOURNAL_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,BUSINESS_AREA
	 	FROM CO_CDM_JOURNAL_STG WITH (NOLOCK) WHERE  (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'
  ) AS A  ORDER BY TRA_AMT,TERMINAL_ID,ACCOUNT_NUM,REF_NUM,TRAN_SEQ, TRA_DATE 
 
		</queryString>
		<queryParam>
				
		</queryParam>
    </query>
    	
	
    
   <query id="2">
        <name>CO_CDM_CBS_STG_AUDIT_INSERT_QRY</name>
		<targetTables>CO_CDM_CBS_STG_AUDIT</targetTables>
        <queryString>
			INSERT INTO CO_CDM_CBS_STG_AUDIT
           (SID ,CDM_ID,CDM_BRANCH,TRAN_ID,TRAN_DATE,VALUE_DATE,CUSTOMER_ACCT,CDM_ACCOUNT,DRCR,AMOUNT
           ,TRAN_PARTICULAR,REFERENCE_NUMBER,TRAN_REMARKS,TRAN_CRNCY_CODE,REF_CRNCY_CODE,REF_AMT,COMMENTS,VERSION
           ,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID
           ,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?)
			 </queryString>
		<queryParam>
			SID@BIGINT ,CDM_ID@VARCHAR,CDM_BRANCH@VARCHAR,TRAN_ID@VARCHAR,TRAN_DATE@DATE,VALUE_DATE@DATE,CUSTOMER_ACCT@VARCHAR,
		   CDM_ACCOUNT@VARCHAR,DRCR@VARCHAR,AMOUNT@DECIMAL
           ,TRAN_PARTICULAR@VARCHAR,REFERENCE_NUMBER@VARCHAR,TRAN_REMARKS@VARCHAR,TRAN_CRNCY_CODE@VARCHAR,REF_CRNCY_CODE@VARCHAR,
		   REF_AMT@DECIMAL,COMMENTS@VARCHAR,VERSION@INTEGER
           ,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT
           ,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR
		</queryParam>
    </query>
    
    
    
    <query id="2">
        <name>CO_CDM_JOURNAL_STG_AUDIT_INSERT_QRY</name>
		<targetTables>CO_CDM_JOURNAL_STG_AUDIT</targetTables>
        <queryString>
			INSERT INTO CO_CDM_JOURNAL_STG_AUDIT
           (SID,TXNMESSAGES_ID,CREATEDDATE,TXNDATETIME,TXNDATE,TXNTIME,TERMINALID,SEQUENCENUMBER,TXNTYPE_ID
           ,TXNTYPE,CARDNUMBER,ACCOUNTNO1,ACCOUNTNAME,AMOUNT,NOTEDETAILS,CARDTAKEN,CARDCAPTURE,NOTESENCASHED
           ,CASHRETRACT ,RESPONSECODE,RESPONSEDESC,HARDWARESTATUS,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS
           ,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION
           ,FILE_NAME,BUSINESS_AREA)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
			 ?,?,?,?,?,?,?,?,?,?,
			 ?,?,?,?,?,?,?,?,?,?,
			 ?,?,?,?,?)		
		</queryString>
		<queryParam>
			SID@BIGINT,TXNMESSAGES_ID@DECIMAL,CREATEDDATE@DATE,TXNDATETIME@DATE,TXNDATE@VARCHAR,TXNTIME@VARCHAR,TERMINALID@VARCHAR,
			SEQUENCENUMBER@VARCHAR,TXNTYPE_ID@VARCHAR
           ,TXNTYPE@VARCHAR,CARDNUMBER@VARCHAR,ACCOUNTNO1@VARCHAR,ACCOUNTNAME@VARCHAR,AMOUNT@DECIMAL,NOTEDETAILS@VARCHAR,
		   CARDTAKEN@VARCHAR,CARDCAPTURE@VARCHAR,NOTESENCASHED@VARCHAR
           ,CASHRETRACT@VARCHAR ,RESPONSECODE@VARCHAR,RESPONSEDESC@VARCHAR,HARDWARESTATUS@VARCHAR,COMMENTS@VARCHAR,
		   VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR
           ,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,
		   MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR
		</queryParam>
    </query>
		
    
   

	<query id="17">
        <name>PAYMENT_ORDER_RECON_UPDATE_QRY</name>
		<targetTables>PAYMENT_ORDER_RECON</targetTables>
        <queryString>
				UPDATE ONUS_ATM_DEBIT_RECON SET
					ID=?,TRA_AMT=?,TRA_DATE=?,DEB_CRE_IND=?,TRA_CUR=?,CHECK_NO=?,WORKFLOW_STATUS=?,SOURCE_TARGET=?,
					MAIN_REV_IND=?,RECON_ID=?,VERSION=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,CREATED_ON=?,COMMENTS=?,
					SUPPORTING_DOC_ID=?,RULE_NAME=?,ACTIVITY_STATUS=?,OPERATION=?,STATUS=?,BUSINESS_AREA=?,ACTIVITY_COMMENTS=?
				WHERE SID=? AND RECON_SIDE=?
		
  
	</queryString>
		<queryParam>
				ID@BIGINT,SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TRA_CUR@VARCHAR,
		CHECK_NO@VARCHAR,WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,
		MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,
		SUPPORTING_DOC_ID@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,
		ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
    </query>

</queries>