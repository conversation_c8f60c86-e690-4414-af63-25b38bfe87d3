package com.ascent.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

import javax.swing.JOptionPane;

public class TomcatStop {

	/**
	 * @param args
	 */
	private static String getJavaProcesses() {
		String filePath = "sc queryex postgresql-x64-9.2";
		List<String> processList = null;
		Process process;
		try {
			process = Runtime.getRuntime().exec(filePath);

			process.waitFor();
			InputStream in = process.getInputStream();
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			int c = -1;
			while ((c = in.read()) != -1) {
				baos.write(c);
			}
			String response = new String(baos.toByteArray());
			System.out.println(response);
			String processId = null;
			processList = Arrays.asList(response.split("(stop|\\n)"));
			for (String str : processList) {
				if (str.contains("PID")) {
					processId = str.split(":")[1].trim();
					return processId;
				}
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;

	}

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		try {
			/*String processId = getJavaProcesses();
			System.out.println("ProcessId:" + processId);
			if (processId != null && (!"0".equalsIgnoreCase(processId))) {
				Process process = Runtime.getRuntime().exec(
						"cmd /c taskKill /F /IM postgresql-x64-9.2" );
			//	System.exit(0);
			} else {
				System.out.println("Process is not running");
			}*/
			
			Process process = Runtime.getRuntime().exec(
			"cmd /c taskKill /F /IM postgresql-x64-9.2.exe" );

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			System.exit(0);
		}

	}

}
