package com.ascent.banknizwa.source.ej;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Test {

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		String adte="26/03/17";
		System.out.println(new Test().formateDateDDMMYY(adte));

	}
	public static java.sql.Date  formateDateDDMMYY(String date){
		//26/03/2017 0:00
		SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yy");
	
	 Date parsedDate=null;;
	    java.sql.Date timestamp=null;
		try {
			parsedDate = dateFormat.parse(date.trim());
			timestamp = new java.sql.Date(parsedDate.getTime());
		} catch (ParseException e) {
			
			e.printStackTrace();
		}
	    
	    return timestamp;
	}
}
