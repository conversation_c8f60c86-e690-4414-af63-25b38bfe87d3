package com.ascent.ds.operations;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;

import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.integration.util.DbUtil;
import com.ascent.service.dto.User;
import com.ascent.util.AscentAutoReconConstants;
import com.ibm.icu.text.DateFormat;
import com.ibm.icu.text.SimpleDateFormat;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
public class GenerateGLWithPDF extends BasicDataSource{
	private static Logger logger = LogManager.getLogger(GenerateGLWithPDF.class);
	public static final String AMT_FORMATTER="#,###.00";
    public static final String SELECT_QUERY_FOR_ICC_ACQ=" SELECT * FROM GENERATE_GL_ENTRY WHERE Business_Area IN (?, ?) AND Reason IS NULL";
    public static final String SELECT_QUERY=" SELECT * FROM GENERATE_GL_ENTRY WHERE Business_Area=? AND Ledger_Code=?";
	private static final long serialVersionUID = -7043810271738121475L;
    Connection connection=null;
    static PreparedStatement preparedStatement=null;
    File file=null;
    String extPath="";
    static String img_path="";
	public GenerateGLWithPDF()
	{
		
		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		System.out.println("Properties Loaded");
		extPath = (String) appProps.get(AscentAutoReconConstants.GENRATE_GL_ENTRY_PDF);
		img_path=(String)appProps.get(AscentAutoReconConstants.DOHA_LOGO);
		file = new File(extPath);
		logger.trace(" loaded file path "+extPath);
	}
public DSResponse executeFetch(final DSRequest request)
{
	// connection properties
	Connection connection=DbUtil.getConnection();
	ServletContext servletContext= request.getServletContext();
	String path=servletContext.getRealPath("/");
    HttpSession httpSession= request.getHttpServletRequest().getSession();
	String user_selected_recon=(String) httpSession.getAttribute("user_selected_recon");
	User user=(User)httpSession.getAttribute("userId");
	
	String preparedByUser=user.getUserName();
	String approverUser=user.getReporting();
	
	String RECON_NAME="";
	RECON_NAME=RECON_NAME.concat("'").concat(user_selected_recon).concat("'");
	// getting today's date
	DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
	DateFormat dateFormat1 = new SimpleDateFormat("yyyy/MM/dd/hh_mm_ss");
	Date date = new Date();
	String todaysDate=dateFormat.format(date);
	String todaysDateWithTime=dateFormat1.format(date);
	String dateArr[]=todaysDateWithTime.split("/");
	String genratedDateYear=dateArr[0];
	String genratedDateMonth=dateArr[1];
	String genratedDate=dateArr[2];
	String time=dateArr[3];
	//System.out.println(genratedDateYear+""+genratedDateMonth+""+genratedDate+""+time);
	DSResponse response=new DSResponse();
	Map requestMap=request.getValues();
	String LedCodes=(String) requestMap.get("LedCodes");
	File folder= new File(extPath);
	 if(!folder.exists())
	 {
		 
		 folder.mkdir();
		 
	 }
	if(LedCodes.equalsIgnoreCase("no"))
	{
		try{
			connection=DbUtil.getConnection();
			String query=" SELECT Ledger_Code FROM GENERATE_GL_ENTRY WHERE Business_Area=? AND Reason IS NULL";
			preparedStatement=connection.prepareStatement(query);
			preparedStatement.setString(1, "ON US");
			ResultSet rs_onus=preparedStatement.executeQuery();
		//	int count=0;
			List lcodeList=new ArrayList();
			while(rs_onus.next())
			{
			//	count++;
				String led_code=rs_onus.getString("Ledger_Code");
				lcodeList.add(led_code);
			}
			System.out.println(" List : "+lcodeList);
			lcodeList=new ArrayList(new LinkedHashSet(lcodeList));
			List fileNameList=new ArrayList();
			if(lcodeList.size()!=0)
			{
				for(int i=0;i<lcodeList.size();i++)
				{
					String lCode=(String) lcodeList.get(i);
					PreparedStatement pstmt=connection.prepareStatement(SELECT_QUERY);
					pstmt.setString(1, "ON US");
					pstmt.setString(2, lCode);
					ResultSet rs=pstmt.executeQuery();
					List reconIdsList=new ArrayList();
					while(rs.next())
					{
						String reconId=rs.getString("RECON_ID");
						reconIdsList.add(reconId);
					}
				  System.out.println("recon ids list : "+reconIdsList);
				  String fileName="ONUS_PDF_"+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+lCode+")"+".pdf";
					fileNameList.add(fileName);
					String FILE_PATH=extPath+"\\ONUS_PDF_"+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+lCode+")"+".pdf";
					boolean generatePDF = generatePDF(todaysDate,FILE_PATH,path,connection, lCode, reconIdsList,preparedByUser,approverUser);
				}
			}else
			{
				System.out.println("no ledger codes are found");
			}
			preparedStatement=connection.prepareStatement(SELECT_QUERY_FOR_ICC_ACQ);
			preparedStatement.setString(1, "ISSUER");
			preparedStatement.setString(2, "ACQUIRER");
			ResultSet rs_iss_acq=preparedStatement.executeQuery();
			List lcodeListForIssAcq=new ArrayList();
			while(rs_iss_acq.next())
			{
				String led_code=rs_iss_acq.getString("Ledger_Code");
				lcodeListForIssAcq.add(led_code);
			}
			lcodeListForIssAcq=new ArrayList(new LinkedHashSet(lcodeListForIssAcq));
			if(lcodeListForIssAcq.size()!=0)
			{
				for(int i=0;i<lcodeListForIssAcq.size();i++)
				{
					String myQuery=" SELECT * FROM GENERATE_GL_ENTRY WHERE Business_Area IN (?, ?) AND  Ledger_Code=?";
					String lCode=(String) lcodeListForIssAcq.get(i);
					PreparedStatement pstmt=connection.prepareStatement(myQuery);
					pstmt.setString(1, "ISSUER");
					pstmt.setString(2, "ACQUIRER");
					pstmt.setString(3, lCode);
					ResultSet rs=pstmt.executeQuery();
					List reconIdsList=new ArrayList();
					while(rs.next())
					{
						String reconId=rs.getString("RECON_ID");
						reconIdsList.add(reconId);
					}
					String fileName="ISS_ACQ_PDF_"+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+lCode+")"+".pdf";
					fileNameList.add(fileName);
					String FILE_PATH=extPath+"\\ISS_ACQ_PDF_"+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+lCode+")"+".pdf";
					boolean generatePDF = generatePDF(todaysDate,FILE_PATH,path,connection, lCode, reconIdsList,preparedByUser,approverUser);
				}
			}else
			{
				System.out.println("no ledger codes are found in iss and acq");
			}
			Map responseMap=new HashMap();
			responseMap.put("LIST", fileNameList);
			response.setData(responseMap);
			}catch(Exception e)
		{
			e.printStackTrace();
		}finally{
			DbUtil.closeConnection(connection);
		}
		
	}else
	{
		try{
			List selectedRecords=(List)requestMap.get("selectedRecords");
			List ledgerCodes=(List) requestMap.get("ledgerCodes");
			ledgerCodes= new ArrayList(new LinkedHashSet(ledgerCodes));
			List ledgerCodesBackUp=(List) requestMap.get("backUpledgerCodes");
			List reconIdsList=(List) requestMap.get("reconIdsList");
			String b_area=(String) requestMap.get("b_area");
			String back_b_area=(String) requestMap.get("back_b_area");
			if(b_area=="ON US")
			{
				b_area="ONUS_PDF_";
			}else
			{
				b_area="ISS_ACQ_PDF_";
			}
			List filesList=new ArrayList();
			List fileNameList=new ArrayList();
			System.out.println("lcodes list : "+ledgerCodes);
			System.out.println("back up lcodes : "+ledgerCodesBackUp);
			for(int i=0;i<ledgerCodes.size();i++)
			{
				String ledgerCode=String.valueOf( ledgerCodes.get(i));
				PreparedStatement pstmt=connection.prepareStatement(SELECT_QUERY);
				pstmt.setString(1, back_b_area);
				pstmt.setString(2, ledgerCode);
				ResultSet rs=pstmt.executeQuery();
				List r_IdsList=new ArrayList();
				while(rs.next())
				{
					String reconId=rs.getString("RECON_ID");
					r_IdsList.add(reconId);
				}
				 System.out.println("recon ids list : "+r_IdsList);
				  String fileName="ONUS_PDF_"+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+ledgerCode+")"+".pdf";
					fileNameList.add(fileName);
					String FILE_PATH=extPath+"\\ONUS_PDF_"+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+ledgerCode+")"+".pdf";
					boolean generatePDF = generatePDF(todaysDate,FILE_PATH,path,connection, ledgerCode, reconIdsList,preparedByUser,approverUser);
				//String myQuery=" select * from GENERATE_GL_ENTRY where "
				/*
				String ledgerCode=(String) ledgerCodes.get(i);
				System.out.println("single l code : "+ledgerCode);
				int count=0;List rIdList=new ArrayList();
				for(int l=0;l<ledgerCodesBackUp.size();l++)
				{
					if(ledgerCodesBackUp.get(l).equals(ledgerCode))
					{
						count++;
					}
				}
				for(int r=0;r<selectedRecords.size();r++)
				{
					Map map=(Map) selectedRecords.get(r);
					String lcode=(String)map.get("Ledger_Code");
					if(ledgerCode.equalsIgnoreCase(lcode))
					{
						rIdList.add(map.get("RECON_ID"));
					}
				}
				rIdList=new ArrayList(new LinkedHashSet(rIdList));
				int len=rIdList.size()+rIdList.size();
				System.out.println("Led code : "+ledgerCode+" Count : "+count);
				System.out.println("Led Code recon ids : "+rIdList);
				System.out.println("Length : "+len);
				String fileName=b_area+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+ledgerCode+")"+".pdf";
				String FILE_PATH=extPath+"\\"+b_area+genratedDateYear+""+genratedDateMonth+""+genratedDate+"_"+time+"(GL_"+ledgerCode+")"+".pdf";
				boolean generatePDF = generatePDF(todaysDate,FILE_PATH,path,connection,len, ledgerCode, rIdList, preparedByUser, approverUser);
				fileNameList.add(fileName);
			*/}
		Map responseMap=new HashMap();
		responseMap.put("LIST", fileNameList);
		response.setData(responseMap);
		}catch(Exception e)
		{
			e.printStackTrace();
		}finally{
			DbUtil.closeConnection(connection);
		}
		
	}
	
	return response;
	}
public static boolean generatePDF(String todayDate, String FILE_PATH, String img_absolute_path,Connection connection,  String ledgerCode, List reconIdsList,String preparedByUser,String approverUser)
{ 
	Font boldFont = new Font(Font.FontFamily.TIMES_ROMAN, 12, Font.BOLD);
	Font normalFont = new Font(Font.FontFamily.TIMES_ROMAN, 10);
	boolean	flag= true;
	int r=100;
	int g=255;
	int b=200;     
	BaseColor cell_background_color=new BaseColor(r,g,b);
	// NumberFormat format = NumberFormat.getInstance(Locale.FRANCE);
	 DecimalFormat formatter = new DecimalFormat(AMT_FORMATTER);
	System.out.println("Date : "+todayDate+" File Path : "+FILE_PATH+" ledgerCode : "+ledgerCode);
	 Document document = new Document();
	 String led_category=null;
	 if(ledgerCode.equalsIgnoreCase("1016"))
		{
			led_category="NAPS POS SETTLEMENT EXCEPTIONS";
		}else if(ledgerCode.equalsIgnoreCase("1015"))
		{
			led_category="NAPS ATM SETTLEMENT EXCEPTIONS";
		}else if(ledgerCode.equalsIgnoreCase("2247"))
		{
			led_category="VISA DEBIT CARD INTERCHANGE EXCEPTIONS";
		}else if(ledgerCode.equalsIgnoreCase("1482"))
		{
			led_category="MASTERCARD ATM ACQUIRING";
		}else if(ledgerCode.equalsIgnoreCase("1472"))
		{
			led_category="VISA ATM ACQUIRING INTERCHANGE";
		}else if(ledgerCode.equalsIgnoreCase("1002"))
		{
			led_category="ATM Withdrawal Suspense A/C Exceptions";
		}else if(ledgerCode.equalsIgnoreCase("1006"))
		{
			led_category="ATM Cash Deposit Suspense A/C Exceptions";
		}else{
			
			led_category="Not Applicable";
		}
	 try{
		 
			
		// file creation
			 File file = new File(FILE_PATH);
			 file.createNewFile();
			 System.out.println(" file is created");
	         PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(file));
	         document.open();
	         Image image1 = Image.getInstance(img_absolute_path+"/"+img_path);
	         image1.scaleAbsolute(158, 45);
	         image1.setAlignment(1);
	         document.add(image1);
	         
	         PdfPTable table = new PdfPTable(2); // 2 columns.                          //table1
	         table.setWidthPercentage(80); //Width 70%
		     table.setSpacingBefore(10f); //Space before table
		     table.setSpacingAfter(10f);

	         float[] columnWidths = {1.25f, 2.55f };
		     table.setWidths(columnWidths);
		      
	         PdfPCell dohaBank = new PdfPCell(new Paragraph("Doha Bank", boldFont));
	         dohaBank.setBorderColor(BaseColor.BLACK);
	         dohaBank.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell tmm = new PdfPCell(new Paragraph("TMM", normalFont));
	         dohaBank.setBorderColor(BaseColor.BLACK);
	         dohaBank.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell category = new PdfPCell(new Paragraph("Category", boldFont));
	         category.setBorderColor(BaseColor.BLACK);
	         category.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell Adjustment_Entry = new PdfPCell(new Paragraph("Adjustment Entry", normalFont));
	         Adjustment_Entry.setBorderColor(BaseColor.BLACK);
	         Adjustment_Entry.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell ledgercode = new PdfPCell(new Paragraph("Ledger Code", boldFont));
	         ledgercode.setBorderColor(BaseColor.BLACK);
	         ledgercode.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell ledgercode_no = new PdfPCell(new Paragraph(""+ledgerCode, normalFont));
	         ledgercode_no.setBorderColor(BaseColor.BLACK);
	         ledgercode_no.setHorizontalAlignment(Element.ALIGN_LEFT);
	     
	         PdfPCell ledgerCategory = new PdfPCell(new Paragraph("Ledger_Category", boldFont));
	         ledgerCategory.setBorderColor(BaseColor.BLACK);
	         ledgerCategory.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell naps = new PdfPCell(new Paragraph(""+led_category, normalFont));
	         naps.setBorderColor(BaseColor.BLACK);
	         naps.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell date = new PdfPCell(new Paragraph("Date", boldFont));
	         date.setBorderColor(BaseColor.BLACK);
	         date.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	         PdfPCell date_space = new PdfPCell(new Paragraph(""+todayDate, normalFont));
	         date_space.setBorderColor(BaseColor.BLACK);
	         date_space.setHorizontalAlignment(Element.ALIGN_LEFT);
	         
	          table.addCell(dohaBank);
	          table.addCell(tmm);
	          table.addCell(category);
	          table.addCell(Adjustment_Entry);
	          table.addCell(ledgercode);
	          table.addCell(ledgercode_no);
	          table.addCell(ledgerCategory);
	          table.addCell(naps);
	          table.addCell(date);
	          table.addCell(date_space);
	         
	          document.add(table);
			
	          
			     for(int l=0;l<reconIdsList.size();l++)
			     {                                               //for loop
			    	 PdfPTable table1 = new PdfPTable(2); // 2 columns.                  //table2
				     table1.setWidthPercentage(80); //Width 70%  
			    	 System.out.println("recon ids list : "+reconIdsList);
			         String reconId=String.valueOf(reconIdsList.get(l));
			         String query=" SELECT * FROM GENERATE_GL_ENTRY WHERE RECON_ID=? AND Deb_Cre_Ind=?";
			         preparedStatement=connection.prepareStatement(query);
				     preparedStatement.setString(1, reconId);
				     preparedStatement.setString(2, "1");
				     ResultSet rs=preparedStatement.executeQuery();
				     ResultSetMetaData resultSetMetaData = rs.getMetaData();
					  int cols = resultSetMetaData.getColumnCount();
					  List<String> columnNames = new ArrayList<String>();
					  for (int i=1;i<=cols;i++)
					  {
						 String colName = resultSetMetaData.getColumnName(i);
						 columnNames.add(colName);
					  }
					 String deb_acc=""; float amt=0; String debitRemarks="";String cre_acc=""; String tra_reason="";
				     List L_code_list=new ArrayList();
				     while(rs.next())
				     {
				    	 String led_code=rs.getString("Ledger_Code");
				    	 StringBuilder builder= new StringBuilder();
				    	   for(int i=0;i<columnNames.size();i++)
							{
				    		   if(columnNames.get(i).equalsIgnoreCase("Branch_Code"))
								{
									  builder.append(rs.getObject(i+1));
					    			  builder.append("/");
								}else if(columnNames.get(i).equalsIgnoreCase("Customer"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    			 builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Check_Digit"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Ledger_Code"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Sub_Acc_Code"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    		  }
							}
				    	   deb_acc=builder.toString();
				    	   amt=rs.getFloat("Transaction_Amount");
							debitRemarks=rs.getString("Remarks");
				     }
				     String formatAmt=formatter.format(amt);
				     preparedStatement=connection.prepareStatement(query);
				       preparedStatement.setString(1, reconId);
				       preparedStatement.setString(2, "2");
				       ResultSet rs_credit=preparedStatement.executeQuery();
				       while(rs_credit.next())
				       {
				    	   StringBuilder builder= new StringBuilder(); 
				    	   for(int i=0;i<columnNames.size();i++)
							{
								if(columnNames.get(i).equalsIgnoreCase("Branch_Code"))
								{
									 builder.append(rs_credit.getObject(i+1));
					    			  builder.append("/");
								}else if(columnNames.get(i).equalsIgnoreCase("Customer"))
					    		  {
					    			 builder.append(rs_credit.getObject(i+1));
					    			 builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Check_Digit"))
					    		  {
					    			  builder.append(rs_credit.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Ledger_Code"))
					    		  {
					    			  builder.append(rs_credit.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Sub_Acc_Code"))
					    		  {
					    			   builder.append(rs_credit.getObject(i+1));
					    		  }
							}
							cre_acc=builder.toString();
				       }
				       String reason_query=" SELECT * FROM GENERATE_GL_ENTRY WHERE RECON_ID=? AND Reason is not null";
				       preparedStatement=connection.prepareStatement(reason_query);
				       preparedStatement.setString(1, reconId);
				       ResultSet reasonRs=preparedStatement.executeQuery();
				       while(reasonRs.next())
				       {
				    	   tra_reason=reasonRs.getString("Reason");
				       }
			        /*  String query="select * from GENERATE_GL_ENTRY where Ledger_Code=? and Deb_Cre_Ind=? and RECON_ID=?";
			    	  
				       preparedStatement=connection.prepareStatement(query);
				       preparedStatement.setString(1, ledgerCode);
				       preparedStatement.setString(2, "1");
				       preparedStatement.setString(3, reconId);
				       ResultSet rs=preparedStatement.executeQuery();
					     ResultSetMetaData resultSetMetaData = rs.getMetaData();
						  int cols = resultSetMetaData.getColumnCount();
						  List<String> columnNames = new ArrayList<String>();
						  for (int i=1;i<=cols;i++)
						  {
							 String colName = resultSetMetaData.getColumnName(i);
							 columnNames.add(colName);
						  }
						 String deb_acc=""; float amt=0; String debitRemarks=""; String cre_acc=""; String reason="";
				       while(rs.next())
				       {
				    	   StringBuilder builder= new StringBuilder();
				    	   for(int i=0;i<columnNames.size();i++)
							{
				    		   if(columnNames.get(i).equalsIgnoreCase("Branch_Code"))
								{
									  builder.append(rs.getObject(i+1));
					    			  builder.append("/");
								}else if(columnNames.get(i).equalsIgnoreCase("Customer"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    			 builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Check_Digit"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Ledger_Code"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Sub_Acc_Code"))
					    		  {
					    			  builder.append(rs.getObject(i+1));
					    		  }
							}
				    	   deb_acc=builder.toString();
							amt=rs.getFloat("Transaction_Amount");
							debitRemarks=rs.getString("Remarks");
				       }
				       String formatAmt=formatter.format(amt);
				       // result set for credit account
				         
				       ///////// credit account
				       preparedStatement=connection.prepareStatement(query);
				       preparedStatement.setString(1, ledgerCode);
				       preparedStatement.setString(2, "2");
				       preparedStatement.setString(3, reconId);
				       ResultSet rs_credit=preparedStatement.executeQuery();
				       /////
				      
				       while(rs_credit.next())
				       {
				    	   StringBuilder builder= new StringBuilder();
				    	   reason=rs_credit.getString("Remarks");
							for(int i=0;i<columnNames.size();i++)
							{
								if(columnNames.get(i).equalsIgnoreCase("Branch_Code"))
								{
									 builder.append(rs_credit.getObject(i+1));
					    			  builder.append("/");
								}else if(columnNames.get(i).equalsIgnoreCase("Customer"))
					    		  {
					    			 builder.append(rs_credit.getObject(i+1));
					    			 builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Check_Digit"))
					    		  {
					    			  builder.append(rs_credit.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Ledger_Code"))
					    		  {
					    			  builder.append(rs_credit.getObject(i+1));
					    			  builder.append("/");
					    		  }else if(columnNames.get(i).equalsIgnoreCase("Sub_Acc_Code"))
					    		  {
					    			   builder.append(rs_credit.getObject(i+1));
					    		  }
							}
							cre_acc=builder.toString();
				       }*/
				       //end
					   table1.setSpacingAfter(10f);  //space after table

				       float[] columnWidths1 = {1.25f, 2.55f };
					   table1.setWidths(columnWidths1);
					   
			        	  	 PdfPCell debit_acc = new PdfPCell(new Paragraph("Debit Account", boldFont));
			        	  	 debit_acc.setBorderColor(BaseColor.BLACK);
			        	  	 debit_acc.setBackgroundColor(cell_background_color.CYAN);
			        	     debit_acc.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell debit_acc_space = new PdfPCell(new Paragraph(""+deb_acc, normalFont));
					         debit_acc_space.setBorderColor(BaseColor.BLACK);
					         
					         debit_acc_space.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell credit_acc = new PdfPCell(new Paragraph("Credit Account", boldFont));
					         credit_acc.setBorderColor(BaseColor.BLACK);
					         credit_acc.setBackgroundColor(cell_background_color.CYAN);
					         credit_acc.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell credit_acc_space = new PdfPCell(new Paragraph(""+cre_acc, normalFont));
					         credit_acc_space.setBorderColor(BaseColor.BLACK);
					         credit_acc_space.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell amount = new PdfPCell(new Paragraph("Amount", boldFont));
					         amount.setBorderColor(BaseColor.BLACK);
					         amount.setBackgroundColor(cell_background_color.CYAN);
					         amount.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell amount_space = new PdfPCell(new Paragraph(""+formatAmt, normalFont));
					         amount_space.setBorderColor(BaseColor.BLACK);
					         amount_space.setHorizontalAlignment(Element.ALIGN_LEFT);
					     
					         PdfPCell remarks = new PdfPCell(new Paragraph("Remarks", boldFont));
					         remarks.setBorderColor(BaseColor.BLACK);
					         remarks.setBackgroundColor(cell_background_color.CYAN);
					         remarks.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell remarks_space = new PdfPCell(new Paragraph(""+debitRemarks, normalFont));
					         remarks_space.setBorderColor(BaseColor.BLACK);
					         remarks_space.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell transaction = new PdfPCell(new Paragraph("Transaction Reason", boldFont));
					         transaction.setBorderColor(BaseColor.BLACK);
					         transaction.setBackgroundColor(cell_background_color.CYAN);
					         transaction.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
					         PdfPCell transaction_space = new PdfPCell(new Paragraph(""+tra_reason, normalFont));
					         transaction_space.setBorderColor(BaseColor.BLACK);
					         transaction_space.setHorizontalAlignment(Element.ALIGN_LEFT);
					         
								          table1.addCell(debit_acc);
								          table1.addCell(debit_acc_space);
								          table1.addCell(credit_acc);
								          table1.addCell(credit_acc_space);
								          table1.addCell(amount);
								          table1.addCell(amount_space);
								          table1.addCell(remarks);
								          table1.addCell(remarks_space);
								          table1.addCell(transaction);
								          table1.addCell(transaction_space);
								          
								          document.add(table1);
								        
			          }//for loop end
			     
			        PdfPTable table_signature= new PdfPTable(2);                                     //table3
			        table_signature.setWidthPercentage(80); //Width 70%
					
				    float[] columnWidths_space = {1.25f, 2.55f};
				    table_signature.setWidths(columnWidths_space);
				   
				    
				        PdfPCell space = new PdfPCell(new Paragraph("   "));
			            space.setBorderColor(BaseColor.BLACK);
			            space.setHorizontalAlignment(Element.ALIGN_LEFT);
			            
			            PdfPCell name_signature = new PdfPCell(new Paragraph("Name                                     Signature", boldFont));
			            name_signature.setHorizontalAlignment(Element.ALIGN_LEFT);
				   
			            table_signature.addCell(space);
			            table_signature.addCell(name_signature);
			            
						document.add(table_signature);
					    
						String preparedBy = "Prepared By";
					 
						String space_3rdcolumn_level2user="";
						PdfPTable table_name=createTable(preparedBy,preparedByUser,space_3rdcolumn_level2user);	
						
						document.add(table_name);
						
						String CheckedBy = "Checked By";
					 
						String space_3rdcolumn_level1user="";
						PdfPTable table_name1=createTable(CheckedBy, approverUser,space_3rdcolumn_level1user);		   
						document.add(table_name1);
						
						String AuthorizedBy = "Authorized By";
						String space_Auth = "";
						String space_3rdcolumn_Auth="";
						PdfPTable table_name2=createTable(AuthorizedBy,space_Auth,space_3rdcolumn_Auth);		   
						document.add(table_name2);
						
						String PostedBy = "Posted By";
						String space_postedBy = "";
						String space_3rdcolumn_postedby="";
						PdfPTable table_name3=createTable(PostedBy,space_postedBy,space_3rdcolumn_postedby);	
						
						document.add(table_name3);
						
	         document.close();
	         writer.close();
	         
	       //   DbUtil.closeConnection(connection);
	          
	 }catch(Exception e){
		 e.printStackTrace();
	 }
	return flag;
}
public static PdfPTable createTable(String column1,String column2,String column3) throws DocumentException{
	
	Font boldFont = new Font(Font.FontFamily.TIMES_ROMAN, 12, Font.BOLD);
	PdfPTable table_3columns= new PdfPTable(3);// what is this 3                                   
	table_3columns.setWidthPercentage(80); //Width 70%
			
	table_3columns.setSpacingAfter(10f);  //space after table
	
		   float[] columnWidths_space = {0.98f, 1.0f, 1.0f};
		   table_3columns.setWidths(columnWidths_space);
		   
		        PdfPCell column_one = new PdfPCell(new Paragraph(column1, boldFont));
		        column_one.setBorderColor(BaseColor.BLACK);
		        column_one.setMinimumHeight(35f);
		        column_one.setVerticalAlignment(Element.ALIGN_MIDDLE);
		        column_one.setHorizontalAlignment(Element.ALIGN_LEFT);
	            
	            PdfPCell column_two = new PdfPCell(new Paragraph(column2, boldFont));
	            column_two.setBorderColor(BaseColor.BLACK);
	            column_two.setMinimumHeight(35f);
	            column_two.setVerticalAlignment(Element.ALIGN_BOTTOM);
	            column_two.setHorizontalAlignment(Element.ALIGN_LEFT);
		   
	            PdfPCell column_three = new PdfPCell(new Paragraph(column3, boldFont));
	            column_three.setBorderColor(BaseColor.BLACK);
	            column_three.setFixedHeight(9);
	            column_three.setVerticalAlignment(Element.ALIGN_BOTTOM);
	            column_three.setHorizontalAlignment(Element.ALIGN_LEFT);
	            column_three.setMinimumHeight(35f);
	            
	            
		            table_3columns.addCell(column_one);
		            table_3columns.addCell(column_two);
		            table_3columns.addCell(column_three);
		            
	       return table_3columns;
}
}

