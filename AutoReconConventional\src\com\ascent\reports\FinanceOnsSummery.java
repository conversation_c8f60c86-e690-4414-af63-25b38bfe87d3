package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class FinanceOnsSummery {
	
	//private static Logger logger = LogManager.getLogger(FinanceOnsSummery.class.getName());

	private static final String ONS_TOTAL_UNRECONCILE_SUMMERY = "ONS_TOTAL_UNRECONCILE_SUMMERY";
	private static final String ONS_UNRECONCILE_SUMMERY = "ONS_UNRECONCILE_SUMMERY";
	private static final String ONS_SUMMERY_CALCULATION = "ONS_SUMMERY_CALCULATION";
	private static final String ONS_INTERNAL_UNRECONCILE = "ONS_INTERNAL_UNRECONCILE";
	private static final String ONS_EXTERNAL_UNRECONCILE = "ONS_EXTERNAL_UNRECONCILE";
	
	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	
	public List<Map<String, Object>> onsTotalUnreconcileSummery(String date) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_TOTAL_UNRECONCILE_SUMMERY);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, date);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			//logger.debug("list.size()="+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> onsUnReconcileSummery(String date) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_UNRECONCILE_SUMMERY);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, date);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			//logger.debug("list.size()="+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> onsSummaryCalculation(String date) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_SUMMERY_CALCULATION);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, date);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			//logger.debug("list.size()="+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> OnsInternalUnReconcile(String date) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_UNRECONCILE);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, date);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			//logger.debug("list.size()="+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> OnsExternalUnReconcile(String date) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsExternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_UNRECONCILE);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, date);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			//logger.debug("list.size()="+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		while (rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));
			}
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}
	
	
	/*public static void main(String[] args) {
		FinanceSummery c = new FinanceSummery();
		c.onsSummaryCalculation("'2019-09-20'");
	}*/

}
