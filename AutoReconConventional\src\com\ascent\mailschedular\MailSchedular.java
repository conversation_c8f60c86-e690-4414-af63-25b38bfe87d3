package com.ascent.mailschedular;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Properties;

import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.impl.StdSchedulerFactory;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.util.AscentAutoReconConstants;
/**
 * <AUTHOR> (01-02-2016)
 * <AUTHOR>
 *
 */
public class MailSchedular {
	FileInputStream fis;
	Properties properties;
	Scheduler scheduler;
	Trigger triggerIris;
	JobKey jobKey;
	JobDetail job;
	String key;
	int time;
	String cronExpression;

	public MailSchedular() throws SchedulerException {
		// TODO Auto-generated constructor stub
		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		System.out.println(etlMetaInstance);
		Properties appProps = etlMetaInstance.getApplicationProperties();
		cronExpression=(String) appProps.get(AscentAutoReconConstants.cronExpression);
		// System.out.println("helloo sgfvghfyhvyhfvujvuyjfvujfvgui");
	}

	public static void main(String as[]) throws SchedulerException {
		MailSchedular mail = new MailSchedular();
		mail.mailSchedule();
	}

	public void mailSchedule() {

		try {
			scheduler = new StdSchedulerFactory().getScheduler();

			scheduler.start();

			// for iris integration
			new Thread(new Runnable() {
				@Override
				public void run() {

					try {

						// System.out.println((Class)obj[i].getClass());

						System.out.println(Thread.currentThread().getName());
						jobKey = new JobKey("jobKeyIris", "group1");

						job = JobBuilder.newJob(TriggerMail.class).withIdentity("jobKey").build();

						triggerIris = TriggerBuilder.newTrigger().withIdentity("triggerIris", "group1").startNow()
								.withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)).build();
								// SimpleScheduleBuilder.simpleSchedule().withIntervalInSeconds(10).repeatForever()).build();

						// temp=temp+temp;
						scheduler.scheduleJob(job, triggerIris);
						// scheduler.scheduleJob(jobQCB, triggerQCB);
						System.out.println("fire job");

					} catch (Exception e) {
						e.printStackTrace();
					}

				}
			}).start();

		} catch (Exception e) {
			e.printStackTrace();
		}

	}

}
