package com.ascent.service.dto;

import java.io.Serializable;

public class UserController implements Serializable {
	
	private static final long serialVersionUID = 620616419089602101L;
	
	private Roles roles;
	private Departments departments;
	private Features features;
	private Privileges privileges;
	private Users users;
	
    private  PrivilegeDetails privilegeDetails;
    
	public void init() throws Exception {
		System.out.println(this.roles);
		this.roles.init();
		this.departments.init();
	 
		this.privileges.init();
		this.features.init();
		try {
			System.out.println(users+"*******************************");
			users.init(roles, departments, privileges);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new Exception();
		}
	}

	public Roles getRoles() {
		return roles;
	}

	public void setRoles(Roles roles) {
		this.roles = roles;
	}

	public Departments getDepartments() {
		return departments;
	}

	public void setDepartments(Departments departments) {
		this.departments = departments;
	}

	public Privileges getPrivileges() {
		return privileges;
	}

	public void setPrivileges(Privileges privileges) {
		this.privileges = privileges;
	}

	public Users getUsers() {
		return users;
	}

	public void setUsers(Users users) {
		this.users = users;
	}

	public PrivilegeDetails getPrivilegeDetails() {
		return privilegeDetails;
	}

	public void setPrivilegeDetails(PrivilegeDetails privilegeDetails) {
		this.privilegeDetails = privilegeDetails;
	}

	public Features getFeatures() {
		return features;
	}

	public void setFeatures(Features features) {
		this.features = features;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((departments == null) ? 0 : departments.hashCode());
		result = prime * result
				+ ((privileges == null) ? 0 : privileges.hashCode());
		result = prime * result + ((roles == null) ? 0 : roles.hashCode());
		result = prime * result + ((users == null) ? 0 : users.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserController other = (UserController) obj;
		if (departments == null) {
			if (other.departments != null)
				return false;
		} else if (!departments.equals(other.departments))
			return false;
		if (privileges == null) {
			if (other.privileges != null)
				return false;
		} else if (!privileges.equals(other.privileges))
			return false;
		if (roles == null) {
			if (other.roles != null)
				return false;
		} else if (!roles.equals(other.roles))
			return false;
		if (users == null) {
			if (other.users != null)
				return false;
		} else if (!users.equals(other.users))
			return false;
		return true;
	}

}
