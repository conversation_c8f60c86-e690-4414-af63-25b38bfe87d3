
package com.onus.atm.etl;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;


import com.date.util.DateForamtConversion;





public class OnUsAtm {

	private String []atmGlHeader={"BRANCH_CODE","CURRENCY_CODE","GL_CODE","CIF_SUB_NO","SL_NO","OP_NO",
			"LINE_NO","TRANSACTION_DATE","VALUE_DATE","DESCRIPTION","AMOUNT","DECIMAL_POINTS","BRIEF_NAME_ENG",
			"SHORT_NAME_ENG","YTD_FC_BALANCE","YTD_CV_BALANCE","CV_AVAIL_BALANCE","FC_AVAIL_BALANCE","CURRENCY_NAME"};
	Cell 						cell					=	null;
	Row 						row						=	null;
	Connection connection=null;
	PreparedStatement preparedStatementGain=null;
	Workbook workbook =null;
	public static void main(String[] args) {
		File file =new File("D:\\BANK NIZUA\\SampleData for Reconciliaton\\Cards\\ATM ONUS\\ATM GL.csv");
		File file2 =new File("D:\\BANK NIZUA\\SampleData for Reconciliaton\\Cards\\ATM ONUS\\ATM GL.csv");
		OnUsAtm onus=	new OnUsAtm();
		onus.extractFile(file);
		onus.extractFile(file2);

	}
	
	public void extractFile(File file){
		int        	numberOfSheet			=	0;
	
		
		
		try{
			//connection = DBUtilConnection.getConnection();
			FileInputStream fileInputStream = new FileInputStream(file);
	        
			//Get the workbook instance for XLS file 
			//XSSFWorkbook workbook = new XSSFWorkbook(fileInputStream);
			 workbook =  WorkbookFactory.create(fileInputStream);//(fileInputStream);
			//Get the Number of sheet in one file
			numberOfSheet=workbook.getNumberOfSheets();
			 
			//Iterate those many time that much have sheet
			for(int i=0;i<numberOfSheet;i++){
					//XSSFSheet sheet = workbook.getSheetAt(i);
				
				Sheet sheet = workbook.getSheetAt(i);
				
				ledgerDetail(sheet,file.getName());}
				
		}catch(Exception e){
							e.printStackTrace();
		}
		/*finally{
			
			File file2 = new File(appProps.getProperty("PROCESS_PATH") + file.getParentFile().getName());
			boolean is=file2.exists();
			Path movefrom = FileSystems.getDefault().getPath(file.getPath());
			Path target = FileSystems.getDefault().getPath(file2.getPath()+"\\" + file.getName() );
			Path duplicate = FileSystems.getDefault().getPath(file2.getPath() + "\\" + file.getName());
			boolean check = new File(file2, file.getName()).exists();
			try{
			if (check) {
				
				//Files.move(movefrom, target, StandardCopyOption.REPLACE_EXISTING);
			} else {
				if (!file2.exists()) {
					file2.mkdirs();
				}
				//Files.move(movefrom, target, StandardCopyOption.REPLACE_EXISTING);
			}
			}catch(Exception e){
				e.printStackTrace();
			}
		}*/
	}
	
	
	private void ledgerDetail(Sheet sheet,String fileName){
		try{
	//	preparedStatementGain=connection.prepareStatement(QueryConstant.ATM_CDM_GL);
	
		Iterator<Row> rowIterator = sheet.iterator();
		List<Map<String,Object>> list=new ArrayList<Map<String,Object>>();
		int counter=0;
		
		Map<String,Object> m=new LinkedHashMap<String,Object>();
		int count=0;
		String terminalId=null;
		Object date=null;;
		
		while(rowIterator.hasNext()){
			row 				= 	rowIterator.next();
			
			counter++;
			//calling the rowIterate(-) for reading one complete row
			
			
			
					 m =rowIterate(row,m,this.atmGlHeader,1);
					 
					 
					 System.out.println("FX GAIN   "+m);
					 
					String line=(String) m.get("line");
						if(line==null ){
							
							
							continue;
						}else if(line.isEmpty()|| line.equals(""))continue;
					m.remove("line");
					
			
	          
				if(!m.isEmpty()){
				
					m.put("FILE_NAME", fileName);
				if(sheet.getSheetName().equals("CDM GL"))
					m.put("TRANSACTION_TYPE", "DEPOSIT");
					else if(sheet.getSheetName().equals("ATM GL")){
						m.put("TRANSACTION_TYPE", "WITHDRAWAL");
					}
					m=getFormatMap(m);
					m.put("FLAG", "GAIN EXCHANGE");
					//boolean flag=new Validator().ammountValidatorDouble(m, TOTAL_AMOUNT, LOCAL_TOTAL_AMOUNT);
					//if(flag);
           // new	MuscatPersistence().insertData(m, preparedStatementGain, QueryConstant.PARAM_ATM_GL.split(","));
                 	}
				//new	MuscatPersistence().persistTxn(preparedStatement, query, m);
				
			
			}
			
		}
		//System.out.println(count);
	
		catch(Exception e){
			e.printStackTrace();
		}
		
			
		}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	private String rowIterate(Row row){
		Iterator<Cell> 		cellIterator 		= 	row.cellIterator();
		 int counter=0;
		 String 				mandline			=	"";
		
		  while(cellIterator.hasNext()) 
		  {
			  
			  String property=null;
			  cell =  cellIterator.next();
			//  System.out.println("column index  "+cell.getColumnIndex());
			 // cell.setCellType(Cell.CELL_TYPE_STRING);
	
			 // System.out.println(property+"  "+cell.getColumnIndex());
		/*System.out.println(cell.getCellType());
		System.out.println("numeric  "+cell.CELL_TYPE_NUMERIC);
		System.out.println("blanck  "+cell.CELL_TYPE_BLANK);
		System.out.println("boolean  "+cell.CELL_TYPE_BOOLEAN);
		System.out.println("error  "+cell.CELL_TYPE_ERROR);
		System.out.println("formula"+cell.CELL_TYPE_FORMULA);
		System.out.println("string  "+cell.CELL_TYPE_STRING);;*/
		
			  switch(cell.getCellType()) 
			  {
	            case Cell.CELL_TYPE_STRING:
			 // cell.setCellType(Cell.CELL_TYPE_STRING);
	            	
	            		//System.out.println(getFormatDate(cell.getStringCellValue()));
	            		//System.out.println(DateForamtConversion.getConvertedSqlDate(getFormatDate(cell.getStringCellValue())));
	            		
	            	
	            	mandline=mandline+"#"+cell.getStringCellValue();
			  break;
	            case Cell.CELL_TYPE_NUMERIC:
	            	mandline=mandline+"#"+cell.getNumericCellValue();
	                break;
	            case Cell.CELL_TYPE_FORMULA:
	            	// map.put(property, cell.getStringCellValue());
	           // 	System.out.println( cell.getCachedFormulaResultType());
	            //	System.out.println("raw value   "+cell.getCellFormula());
	            	//FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();
	            	
	            	 break;
	            case Cell.CELL_TYPE_BLANK:
	            	
	            	
	            	 break;
			  }
	           
	          /* m.put("BANK_Code", value.substring(0,3));
	           m.put("bankser_no", value.subSequence(4, 6));
	           m.put("Currency", value.subSequence(7, 10));
	           m.put("BANK_name", value.subSequence(11, 5));*/
	        		
	       	
	      
	       	
	       	
	          counter++;    
	       }
			  
			
		  //while(cellIterator.hasNext()) close
		
		 
		  
		 //System.out.println(mandline);
		  
		 
		  return mandline;
	}
	
	private Map<String,Object> rowIterate(Row row,Map<String,Object> map,String[] header,int index){
		
		
		 String 				mandline			=	"";
		 						
		 boolean 				flag				=   false;
		 
		 String 				nLine				= "";
		 //Get iterator to all cells of current row
		 Iterator<Cell> 		cellIterator 		= 	row.cellIterator();
		 int counter=0;
		
		
		  while(cellIterator.hasNext()) 
		  {
			  
			  String property=null;
			  cell =  cellIterator.next();
			//  System.out.println("column index  "+cell.getColumnIndex());
			  if(cell.getColumnIndex()<header.length){
		property = header[cell.getColumnIndex()];
			 // System.out.println(property+"  "+cell.getColumnIndex());
		/*System.out.println(cell.getCellType());
		System.out.println("numeric  "+cell.CELL_TYPE_NUMERIC);
		System.out.println("blanck  "+cell.CELL_TYPE_BLANK);
		System.out.println("boolean  "+cell.CELL_TYPE_BOOLEAN);
		System.out.println("error  "+cell.CELL_TYPE_ERROR);
		System.out.println("formula"+cell.CELL_TYPE_FORMULA);
		System.out.println("string  "+cell.CELL_TYPE_STRING);;*/
		
			  switch(cell.getCellType()) 
			  {
	            case Cell.CELL_TYPE_STRING:
			 // cell.setCellType(Cell.CELL_TYPE_STRING);
	            	if(property.contains("DATE")){
	            		//System.out.println(getFormatDate(cell.getStringCellValue()));
	            		//System.out.println(DateForamtConversion.getConvertedSqlDate(getFormatDate(cell.getStringCellValue())));
	            		map.put(property,DateForamtConversion.getConvertedSqlDate(getFormatDate(cell.getStringCellValue())));
	            	}
	            	else
	            	 map.put(property, cell.getStringCellValue());
			 mandline=mandline+"#"+cell.getStringCellValue();
			  break;
	            case Cell.CELL_TYPE_NUMERIC:
	               getDateAndTime(map, cell, property);
	               mandline=mandline+map.get("TRA_DATE");
	               
	               
	                break;
	            case Cell.CELL_TYPE_FORMULA:
	            	// map.put(property, cell.getStringCellValue());
	           // 	System.out.println( cell.getCachedFormulaResultType());
	            //	System.out.println("raw value   "+cell.getCellFormula());
	            	//FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();
	            	get(cell,cell.getCachedFormulaResultType(),map,property);
	            	 break;
	            case Cell.CELL_TYPE_BLANK:
	            	
	            	 map.put(property,null);
	            	 break;
			  }
	           
	          /* m.put("BANK_Code", value.substring(0,3));
	           m.put("bankser_no", value.subSequence(4, 6));
	           m.put("Currency", value.subSequence(7, 10));
	           m.put("BANK_name", value.subSequence(11, 5));*/
	        		
	       	
	      
	       	
	       	
	          counter++;    
	       }
			  
			
		  //while(cellIterator.hasNext()) close
		
		 
		  
		 //System.out.println(mandline);
		  map.put("line", mandline);
		  }
		  return map;
		  
	}
	
	private Map<String,Object> getMapCell(String line,Map<String,Object> m,String[] column){
		String value[]=line.split("\\|");
		//System.out.println(column.length);
		for(int i=0;i<column.length;i++){
			 if(i<=value.length){
				if(column[i].contains("DATE")) {
					//SimpleDateFormat	 simpleDasteFormat = new SimpleDateFormat("dd-MMM-yyyy");
					
					m.put(column[i],DateForamtConversion.parseDatemon(value[i]));
				 }else if(column[i].contains("TIME")){
					 String time=value[i];
					// System.out.println(line);
					// System.out.println("time ="+value[i] + " "+value[i].isEmpty() +"---  ="+time );
					 if(!value[i].isEmpty() )
						 time=value[i].split(" ")[1];
					 else{
						 time=value[i];
					 }
					
					 m.put(column[i],time);
				 }
				 else
			m.put(column[i],column[i].contains("AMOUNT")?value[i].replaceAll(",", ""): value[i]);
			 }
			 else{
				 m.put(column[i], null);
			 }
		
			
		}
		
		return m;
	}
	
	Map<String,Object> get(Cell c, int type,Map<String,Object> map,String property){
		//FileInputStream fis = new FileInputStream("/somepath/test.xls");
		//Workbook wb = new HSSFWorkbook(fis); //or new XSSFWorkbook("/somepath/test.xls")
		FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
		
		           
		            
		            if (type == cell.CELL_TYPE_STRING) {
		            	// System.out.println (cell.getStringCellValue());
		            	 map.put(property, cell.getStringCellValue());
		           } else if (type == cell.CELL_TYPE_NUMERIC) {
		        	  getDateAndTime(map, c, property);
		           } else if (type == cell.CELL_TYPE_BOOLEAN) {
		        	 //  System.out.println(cell.getBooleanCellValue());
		           } else if (type == cell.CELL_TYPE_FORMULA) {
		               // Re-run based on the formula type
		               get( cell,cell.getCachedFormulaResultType(),map,property);
		           } else {
		              
		           }
		            return map;
	}
		
	Map<String,Object> getDateAndTime(Map<String,Object> map,Cell cell, String property){
		 if (DateUtil.isCellDateFormatted(cell)) 
        {
           
        	
				// SimpleDateFormat	 simpleDasteFormat = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss");//dataElement.get(count)
		// SimpleDateFormat	 simpleDasteFormat = new SimpleDateFormat(dataElement.get(counter-1).getFieldFormat());//dataElement.get(count)
				//String date2=simpleDasteFormat.format(cell.getDateCellValue());
				
				
				if(property.contains("DATE")) {
					//SimpleDateFormat	 simpleDasteFormat = new SimpleDateFormat("dd-MMM-yyyy");
					
					map.put(property,DateForamtConversion.getConvertedSqlDate(cell.getDateCellValue()));
				 }else if(property.contains("TIME")){
					 //String time=date2;
					// System.out.println(date2);
					// System.out.println("time ="+date2 + " "+date2.isEmpty() +"---  ="+time );
					 //if(!date2.isEmpty() )
						// time=date2.split(" ")[1];
					 //else{
					//	 time=date2;
					 }
					
					// map.put(property,time);
				 }
				
				
				
        	 
        else{
        	cell.setCellType(Cell.CELL_TYPE_STRING);
        	
        	map.put(property,cell.getStringCellValue());
        }
		 return map;
	}
	 public static String getData(String data, String token) {
	        String retStr;
	        StringBuilder result = new StringBuilder();
	        if (data != null) {
	            Pattern pattern = Pattern.compile(token);
	            Matcher matcher = pattern.matcher(data);
	            String sep = "";
	            while (matcher.find()) {
	                result.append(String.valueOf(String.valueOf("")) + matcher.group().toString());
	               
	            }
	        }
	        return (retStr = result.toString()).isEmpty() ? null : retStr;
	    }
	private Date getFormatDate(String str){
		SimpleDateFormat sdf=new SimpleDateFormat("dd-MMM-yy");
		SimpleDateFormat sdf2=new SimpleDateFormat("yyyyMMdd");
		Date date=null;
		try{
			date=sdf.parse(str);
		}catch(Exception e){
			try {
				date=sdf2.parse(str);
			} catch (ParseException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		System.out.println("comming");
		}
		return date;
	}
	private Map<String,Object>getFormatMap(Map<String,Object> m){
		try{
		 String seqName="ATMGL";
		 String seqNumQry = "SELECT NEXT VALUE FOR " + seqName + " as sno";
		 PreparedStatement psSequence=connection.prepareStatement(seqNumQry);
		 Long seq=0l;//PersistenceUtil.createSequence(connection, psSequence, seqName);
			seq=Long.valueOf("15"+seq.toString());
		 m.put("SID",seq);
		// m.put("TERMINAL_ID", terminalId);
m.put("COMMENTS",null);
m.put("VERSION",1);
m.put("ACTIVE_INDEX","Y");
m.put("WORKFLOW_STATUS","N");
m.put("UPDATED_ON",new Timestamp(Calendar.getInstance().getTimeInMillis()));
m.put("CREATED_ON",new Timestamp(Calendar.getInstance().getTimeInMillis()));
m.put("RECON_STATUS",null);
m.put("RECON_ID",null);
m.put("ACTIVITY_COMMENTS",null);
m.put("MAIN_REV_IND","MAIN");
m.put("OPERATION","ETL");

		}catch(Exception e){
			e.printStackTrace();;
		}
	return m;
	}
}
