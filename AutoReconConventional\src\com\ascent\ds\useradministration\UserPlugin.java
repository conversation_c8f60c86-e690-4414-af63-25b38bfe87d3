package com.ascent.ds.useradministration;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.administration.AuthorizationBean;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class UserPlugin extends BasicDataSource implements PagesConstants {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4910292328170164676L;
	
	public UserPlugin(){
		
		System.out.println(" USER PLUGINS.....");
	}
	
	public DSResponse executeFetch(final DSRequest dsRequest){
		  DSResponse dsResponse= new DSResponse();
		Map<Object,Object> criteriaMap= dsRequest.getValues(); 
		HttpSession httpSession= dsRequest.getHttpServletRequest().getSession();
		String userSelectedBusinessArea= (String)httpSession.getAttribute("user_selected_business_area");
		String userSelectedRecon=(String)httpSession.getAttribute("user_selected_recon");
		User user = (User) httpSession.getAttribute("userId");
		processData(criteriaMap,user,userSelectedBusinessArea,userSelectedRecon);
	 return dsResponse;
	}
	
	public Map<String,Object> processData(Map<Object,Object> criteriaMap,User user,String userSelectedBusinessArea,String userSelectedRecon){
		 Map<String,Object> resultMap= new HashMap<String, Object>();
		 System.out.println(" process ddata () from usaers plugin ");
		 AuthorizationBean  authorizationBean=new AuthorizationBean();		 
		 List<Map<String, Object>> record= new ArrayList<Map<String, Object>>();
		 String tableName= (String) criteriaMap.get("tableName");
		 String  dsName=(String) criteriaMap.get("dsName");
		 String  action=(String) criteriaMap.get("action");
		 System.out.println(tableName+ "  *** "+dsName+" ^^^ "+action);
		  Map<String,Object> recordMap =(Map<String, Object>) criteriaMap.get("data");//saveRoles
		  System.out.println(" RECORDMAP:---------->>> "+recordMap);
		  
		  System.out.println(tableName+ " sbfdsbfdsfjdsjfdsjfdsjfdsbhfdsfdshkdshfhdskfk");
		boolean email_notification=   (boolean) recordMap.get("email_notification");
		   
		boolean sms_notification=   (boolean) recordMap.get("sms_notification");
		System.out.println(" sms_notification && email******"+email_notification+" sma++++++++++++ "+sms_notification);
		  	recordMap.put("status",PENDING_APPROVAL);
			recordMap.put("active_index","Y");
			recordMap.put("version",null);
			recordMap.put("created_on", new Timestamp(Calendar.getInstance().getTimeInMillis()));
		  record.add(recordMap);
		  resultMap.put(TABLE_NAME, tableName);
		  resultMap.put(DS_NAME, dsName);
		  resultMap.put("userSelectedBusinessArea",userSelectedBusinessArea);
		  resultMap.put("userSelectedRecon",userSelectedRecon);
		  resultMap.put(SELECTED_RECORDS, record);
		  resultMap.put(SAVE,action);
		  System.out.println("resultMap ******* "+ resultMap);  
		 
	 
		     if(tableName.equalsIgnoreCase(users_table)){
			   if(action.equalsIgnoreCase(SAVE)) { 
				   
				   System.out.println(" TABLE=== "+tableName+" OPERATION  is for users   *****************");
				   
				   
			 //authorizationBean.saveUsers(resultMap,user);
			  
			  	
				   
			   }
			   
			     
		   }
		  
			 return resultMap;
	 }
	
	

}
