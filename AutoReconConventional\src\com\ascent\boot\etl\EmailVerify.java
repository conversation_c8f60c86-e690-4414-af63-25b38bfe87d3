package com.ascent.boot.etl;

import java.io.File;
import java.util.ArrayList;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.integration.Integrations;
import com.ascent.custumize.query.Queries;
import com.ascent.util.AscentAutoReconConstants;

public class EmailVerify {
	static ArrayList<String> list = new ArrayList<String>();
	EtlMetaInstance integrationMetaInstance = null;
	Integration integration = null;
	String integrationName;
	static Properties appProps = null;

	public EmailVerify(String integrationName) throws Exception {
		this.integrationMetaInstance = EtlMetaInstance.getInstance();
		this.integrationName = integrationName;
		this.integration = this.integrationMetaInstance
				.getEtlConf(this.integrationName);

	}

	static File file = null;

	public EmailVerify() {
		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();

		// meta data test
		Properties bootProps = etlMetaInstance.getBootProperties();
		Properties dbProps = etlMetaInstance.getDbProperties();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		Integrations integrations = etlMetaInstance.getEtlConfs();
		Queries etlQueryConfs = etlMetaInstance.getEtlQueryConfs();

		// ///
		System.out.println("Properties Loaded");

		String extPath = (String) appProps
				.get(AscentAutoReconConstants.AUTO_RECON_HOME)
				+ (String) appProps
						.get(AscentAutoReconConstants.SFTP_FOLDER_NAME);
		file = new File(extPath);

		list.add("_acq.csv");
		list.add("_auth.csv");
		list.add("_batches.csv");
		list.add("_2279.csv");
		list.add("_1002.csv");
		list.add("_1006.csv");
		list.add("_1015.csv");
		list.add("_1016.csv");
		list.add("_1472.csv");
		list.add("_1482.csv");
		list.add("_2247.csv");
		list.add("_ciso.csv");
		list.add("_ctransactions.csv");
		list.add("_master_aq.001");
		list.add("_miso.csv");
		list.add("_mtransactions.csv");
		list.add("nps_rep_");
		list.add("_switch.csv");
		list.add("_unionpay_aq");
		list.add("_visa_aq.txt");
		list.add("_visa_INCMO");
		list.add("QPAY_16092015.TXT");

	}

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		EmailVerify emailVerify = new EmailVerify();

		try {
			emailVerify.processFile(file);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	public void processFile(File folder) throws Exception {
		File[] listOfFiles = folder.listFiles();

		for (int i = 0; i < listOfFiles.length; i++) {
			if (listOfFiles[i].isFile()) {
				for (int j = 0; j <= list.size(); j++) {
					File file = listOfFiles[i];
					String fileName = file.getName();

					Pattern pattern = Pattern.compile(list.get(j));

					Matcher matcher = pattern.matcher(fileName);
					boolean matchFound = false;
					while (matcher.find()) {

						matchFound = true;

					}

					if (!matchFound) {
						Properties properties = EtlMetaInstance.getInstance()
								.getDbProperties();

						String fromUserName = (String) properties
								.getProperty("fromUserName");
						String fromUserPassword = (String) properties
								.getProperty("fromUserPassword");
						String toMailId = (String) properties
								.getProperty("toMailId");

						SendMailTLS.sendMail(fromUserName, fromUserPassword,
								toMailId, list.get(j));
					}

				}

			} else if (listOfFiles[i].isDirectory()) {
				processFile(listOfFiles[i]);
				System.out.println("Directory " + listOfFiles[i].getName());
			}
		}
	}

	/*
	 * public void processFilePattern(List<String> list) {
	 * 
	 * for (String string : list) { Pattern pattern =
	 * Pattern.compile(this.integration .getFileNamePattern());
	 * 
	 * Matcher matcher = pattern.matcher(string); boolean matchFound = false;
	 * while (matcher.find()) {
	 * 
	 * matchFound = true;
	 * 
	 * }
	 * 
	 * }
	 * 
	 * }
	 */
}
