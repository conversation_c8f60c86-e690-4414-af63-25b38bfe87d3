package com.ascent.ds.login;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class UserAuditLogDs extends BasicDataSource {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5986251282037535037L;

	public DSResponse executeFetch(final DSRequest request) throws Exception {

		DSResponse dsResponse = new DSResponse();

		Map<String, Object> criteriaMap = request.getValues();

		Map<String, Object> userAuditLogsMap = new HashMap<String, Object>();
		Map<String, Object> userParamValueMap = new HashMap<String, Object>();

		HttpSession httpSession = request.getHttpServletRequest().getSession();
		LoginDetails details = new LoginDetails();

		User user = (User) httpSession.getAttribute("userId");
		String user_selected_business_area = (String) httpSession.getAttribute("user_selected_business_area");
		String user_selected_recon = (String) httpSession.getAttribute("user_selected_recon");
		String user_Id = user.getUserId();
		String usersRole = user.getSystemRole();
		String action = (String) criteriaMap.get("ACTION");

		userParamValueMap.put("user_id", user_Id);
		userParamValueMap.put("action", action);
		userParamValueMap.put("date_time", new Timestamp(Calendar.getInstance().getTimeInMillis()));
		userParamValueMap.put("bussiness_area", user_selected_business_area);
		userParamValueMap.put("recon_name", user_selected_recon);
		userParamValueMap.put("user_role", usersRole);
		userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);
		int userAuditLogs = details.userAuditLogs(userAuditLogsMap);
		System.out.println("USER_lOGS added" + userAuditLogs);

		return dsResponse;

	}

}
