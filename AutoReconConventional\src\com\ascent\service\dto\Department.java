package com.ascent.service.dto;

import java.io.Serializable;

public class Department implements Serializable {
	
  /**
	 * 
	 */
	private static final long serialVersionUID = 8699599871227255307L;
/**
	 * 
	 */
 
private Integer id;
  private Integer sno;
  private String deptName; 
  private String deptCategory;
  private String deptLocation;
  private String deptId;
  private Integer version;
  
  
  public void init(){
	  
  }
  
  
public Integer getId() {
	return id;
}
public void setId(Integer id) {
	this.id = id;
}
public String getDeptName() {
	return deptName;
}
public void setDeptName(String deptName) {
	this.deptName = deptName;
}
public String getDeptCategory() {
	return deptCategory;
}
public void setDeptCategory(String deptCategory) {
	this.deptCategory = deptCategory;
}
public String getDeptLocation() {
	return deptLocation;
}
public void setDeptLocation(String deptLocation) {
	this.deptLocation = deptLocation;
}
@Override
public int hashCode() {
	final int prime = 31;
	int result = 1;
	result = prime * result
			+ ((deptCategory == null) ? 0 : deptCategory.hashCode());
	result = prime * result
			+ ((deptLocation == null) ? 0 : deptLocation.hashCode());
	result = prime * result + ((deptName == null) ? 0 : deptName.hashCode());
	result = prime * result + ((id == null) ? 0 : id.hashCode());
	return result;
}
@Override
public boolean equals(Object obj) {
	if (this == obj)
		return true;
	if (obj == null)
		return false;
	if (getClass() != obj.getClass())
		return false;
	Department other = (Department) obj;
	if (deptCategory == null) {
		if (other.deptCategory != null)
			return false;
	} else if (!deptCategory.equals(other.deptCategory))
		return false;
	if (deptLocation == null) {
		if (other.deptLocation != null)
			return false;
	} else if (!deptLocation.equals(other.deptLocation))
		return false;
	if (deptName == null) {
		if (other.deptName != null)
			return false;
	} else if (!deptName.equals(other.deptName))
		return false;
	if (id == null) {
		if (other.id != null)
			return false;
	} else if (!id.equals(other.id))
		return false;
	return true;
}


public String getDeptId() {
	return deptId;
}


public void setDeptId(String deptId) {
	this.deptId = deptId;
}


public Integer getSno() {
	return sno;
}


public void setSno(Integer sno) {
	this.sno = sno;
}


public Integer getVersion() {
	return version;
}


public void setVersion(Integer version) {
	this.version = version;
}
  
  
  
  
}
