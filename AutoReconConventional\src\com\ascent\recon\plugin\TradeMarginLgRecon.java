package com.ascent.recon.plugin;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.recon.ReconMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.custumize.recon.Recon;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.ReconUtil;
import com.ascent.recon.AscentAutoReconPlugin;

public class TradeMarginLgRecon extends AscentAutoReconPlugin {

	private static Logger logger = LogManager.getLogger(TradeMarginLgRecon.class.getName());

	Queries reconQueriesConf = null;

	String reconIdSeqName = null;
	String reconTableIdSeqName = null;

	Query insertQryConf = null;
	Query updateQryConf = null;
	Query upstreamQryConf = null;
	Query selectQryConf = null;

	Connection connection = null;

	ResultSet upstreamRs = null;

	PreparedStatement upstreamPstmt = null;
	PreparedStatement reconInsertPstmt = null;
	PreparedStatement reconUpdatePstmt = null;
	PreparedStatement reconTableIdGenPstmt = null;
	PreparedStatement reconIdGenPstmt = null;
	ReconMetaInstance reconMetaInstance = null;
	String reconTableName = null;
	public static long start = 01;
	Map<String, Map<String, Object>> dynamicPstmtMap = new HashMap<String, Map<String, Object>>();

	public TradeMarginLgRecon() {

		this.reconMetaInstance = ReconMetaInstance.getInstance();

	}

	public static void main(String[] args) {

		TradeMarginLgRecon atmIssRecon = new TradeMarginLgRecon();

	}

	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		int recCnt = 0;
		int batchSize = 20000;
		while (recCnt < batchSize && rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));

			}
			recCnt++;
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}

	public static long totalRetrievedRecs = 0l;

	public Map<String, Object> process(Recon recon) throws Exception {

		Map<String, Object> result = new HashMap<String, Object>();
		totalRetrievedRecs = 0l;
		this.reconQueriesConf = this.reconMetaInstance.getReconQueries(recon.getName());

		this.connection = DbUtil.getConnection();
		
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();

		try {
			start = System.currentTimeMillis();
			insertQryConf = reconQueriesConf.getQueryConf(recon.getReconInsertQueryName());
			upstreamQryConf = reconQueriesConf.getQueryConf(recon.getReconUpstreamQueryName());
			selectQryConf = reconQueriesConf.getQueryConf(recon.getReconUpdateQueryName());

			List<String> upstreamTableNameList = upstreamQryConf.getTargetTableNameList();

			if (upstreamTableNameList == null || upstreamTableNameList.size() < 0) {
				throw new Exception("Please configure the Upstream table names for Upstream query");
			}

			if (insertQryConf.getTargetTableNameList() == null || insertQryConf.getTargetTableNameList().size() <= 0) {
				throw new Exception("Please configure the recon table name for recon insert query");
			}

			reconTableName = insertQryConf.getTargetTableNameList().get(0);

			this.reconIdSeqName = reconTableName + "_SEQ";
			String reconIdGenQry = "SELECT NEXT VALUE FOR " + reconIdSeqName + " as sno";
			this.reconIdGenPstmt = this.connection.prepareStatement(reconIdGenQry);

			this.reconTableIdSeqName = reconTableName + "_ID_SEQ";
			String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";

			this.reconTableIdGenPstmt = this.connection.prepareStatement(reconTableIdGenQry);

			this.reconInsertPstmt = this.connection.prepareStatement(insertQryConf.getQueryString());

			String reconUpdateQryString = "UPDATE " + reconTableName
					+ " SET RECON_ID=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,COMMENTS=?,RULE_NAME=?,ACTIVITY_STATUS=?,STATUS=?"
					+ " WHERE SID=? and RECON_SIDE=?";
			String reconUpdateQryParams = "RECON_ID@BIGINT,MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,COMMENTS@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,STATUS@VARCHAR,SID@BIGINT,RECON_SIDE@VARCHAR";
			this.updateQryConf = new Query();
			this.updateQryConf.setQueryString(reconUpdateQryString);
			this.updateQryConf.setQueryParam(reconUpdateQryParams);
			this.updateQryConf.bootConf();
			this.reconUpdatePstmt = this.connection.prepareStatement(reconUpdateQryString);

			for (String upstreamTable : upstreamTableNameList) {

				String updateQuery = "UPDATE " + upstreamTable
						+ " SET RECON_ID=?,UPDATED_ON=?,VERSION=?,OPERATION=?,RECON_STATUS=? WHERE SID=?";

				String selectQry = "select * from " + upstreamTable + " where SID=?";

				// TODO: plugin to create audit Query
				Query stagingAuditInsertQryConf = reconQueriesConf.getQueryConf(upstreamTable + "_AUDIT_INSERT_QRY");

				PreparedStatement pstmt = connection.prepareStatement(updateQuery);
				PreparedStatement selectPstmt = connection.prepareStatement(selectQry);
				PreparedStatement stagingAuditInsertPstmt = connection
						.prepareStatement(stagingAuditInsertQryConf.getQueryString());

				Map<String, Object> auditPstmtMap = (Map<String, Object>) dynamicPstmtMap.get(upstreamTable);
				if (auditPstmtMap == null) {
					auditPstmtMap = new HashMap<String, Object>();
					dynamicPstmtMap.put(upstreamTable, auditPstmtMap);

				}

				auditPstmtMap.put("STG_AUDIT_INSERT_QUERY_CONF", stagingAuditInsertQryConf);

				auditPstmtMap.put("STG_UPDATE_PSTMT", pstmt);
				auditPstmtMap.put("STG_SLECT_PSTMT", selectPstmt);
				auditPstmtMap.put("STG_AUDIT_INSERT_PSTMT", stagingAuditInsertPstmt);

			}

			try {

				List<String> list = new ArrayList<String>();
				List<java.sql.Date> list1 = new ArrayList<java.sql.Date>();
				Map<String, Object> txn = null;

				List<Map<String, Object>> subGroup = new ArrayList<Map<String, Object>>();

				String upstreamQry = upstreamQryConf.getQueryString();
				String selectQry = selectQryConf.getQueryString();

				PreparedStatement pstmt = connection.prepareStatement(selectQry);

				ResultSet rs = pstmt.executeQuery();

				while (rs.next()) {

					String docNum = rs.getString(1);
					//java.sql.Date date = rs.getDate(2);

					list.add(docNum);
					//list1.add(date);

				}
                /**
                 * DONE BY SHIVAM
                 */
				
				for (int i = 0; i < list.size(); i++) {
					subGroup.clear();

					PreparedStatement unionPstmt = connection.prepareStatement(upstreamQry);
					unionPstmt.setString(1, list.get(i));
					

					ResultSet unionrs = unionPstmt.executeQuery();
					ResultSetMetaData rsmt = unionrs.getMetaData();
					int columns = rsmt.getColumnCount();

					while (unionrs.next()) {
						txn = new HashMap<String, Object>();
						for (int j = 1; j <= columns; j++) {

							txn.put(rsmt.getColumnName(j), unionrs.getObject(j));
						}

						subGroup.add(txn);

					}

					Map<String, List<Map<String, Object>>> recGroup = new HashMap<String, List<Map<String, Object>>>();

					recGroup.put("TM_GL", new ArrayList<Map<String, Object>>());
					recGroup.put("LG", new ArrayList<Map<String, Object>>());

					for (Map<String, Object> rec : subGroup) {

						List<Map<String, Object>> sourceGroup = recGroup.get((String) rec.get("RECON_SIDE"));

						sourceGroup.add(rec);

					}

					if (recGroup.get("TM_GL") != null && ((List<Map<String, Object>>) recGroup.get("TM_GL")).size() > 0) {
						reconCheck(recGroup);
						txn.clear();

					}

				}

			} catch (Exception e) {
				//e.printStackTrace();
				logger.error(e.getMessage(),e);
			}

		} catch (Exception e) {
			
			logger.error(e.getMessage(), e);
		} finally {

			DbUtil.closeResultSet(upstreamRs);

			DbUtil.closePreparedStatement(reconIdGenPstmt);
			for (Map<String, Object> auditStmtMap : dynamicPstmtMap.values()) {

				if (auditStmtMap != null) {
					PreparedStatement pstmtUpdate = (PreparedStatement) auditStmtMap.get("STG_UPDATE_PSTMT");
					PreparedStatement pstmtSelect = (PreparedStatement) auditStmtMap.get("STG_SLECT_PSTMT");
					PreparedStatement pstmtAudit = (PreparedStatement) auditStmtMap.get("STG_AUDIT_INSERT_PSTMT");

					DbUtil.closePreparedStatement(pstmtUpdate);
					DbUtil.closePreparedStatement(pstmtSelect);
					DbUtil.closePreparedStatement(pstmtAudit);

				}

			}
			DbUtil.closePreparedStatement(reconUpdatePstmt);
			DbUtil.closePreparedStatement(reconTableIdGenPstmt);
			DbUtil.closePreparedStatement(reconIdGenPstmt);
			DbUtil.closePreparedStatement(upstreamPstmt);
			DbUtil.closePreparedStatement(reconInsertPstmt);
			DbUtil.closeConnection(connection);
			logger.trace(System.currentTimeMillis() - start);

		}
		return result;
	}

	public void reconCheck(Map<String, List<Map<String, Object>>> recGroup) {

		double dpayAmt = 0.00;
		double gl2279Amt = 0.00;
		double frstAmt = 0.00;
		double sndAmt = 0.00;
		StringBuilder commentsSb = null;
		Date d1 = null;
		Date d2 = null;

		List<Map<String, Object>> dpay = recGroup.get("TM_GL");
		List<Map<String, Object>> gl2279 = recGroup.get("LG");

		for (int i = 0; i < dpay.size(); i++) {

			dpayAmt = ((BigDecimal) dpay.get(i).get("TRA_AMT")).doubleValue();

			frstAmt = frstAmt + dpayAmt;

		}

		for (int i = 0; i < gl2279.size(); i++) {
			gl2279Amt = ((BigDecimal) gl2279.get(i).get("TRA_AMT")).doubleValue();
			sndAmt = sndAmt + gl2279Amt;
		}

		if (frstAmt == sndAmt) {
			ReconUtil.updateStatus(recGroup, ReconUtil.AM, "GL_CODE,CIF_NUM,AMOUNT MATCH RULE SUCCESSFUL",
					"COMP_CODE,DATE,AMOUNT MATCH RULE");
		} else if (sndAmt != 0.00 && frstAmt != sndAmt) {

			String comment = "AMOUNT MISSMATCH BETWEEN ";
			commentsSb = new StringBuilder();

			if (commentsSb.length() != 0) {
				commentsSb.append(",");
			}
			commentsSb.append("LG & TM_GL");
			comment = comment + commentsSb.toString();
			ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment, "AMOUNT MISS MATCH RULE");
		} else {

			commentsSb = new StringBuilder();

			if (gl2279.size() == 0) {
				if (commentsSb.length() != 0) {
					commentsSb.append(ReconUtil.COMMA);
				}

				commentsSb.append("LG");
			}

			String comment = "MISSING TXNS  " + commentsSb.toString();

			ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment, "MISSING TXNS RULE");

		}
		ReconUtil.insertRecon(recGroup, this.connection, this.reconIdGenPstmt, reconIdSeqName, reconTableIdGenPstmt,
				reconTableIdSeqName, reconInsertPstmt, reconUpdatePstmt, insertQryConf, updateQryConf, dynamicPstmtMap,
				reconTableName, "74");
	}

}
