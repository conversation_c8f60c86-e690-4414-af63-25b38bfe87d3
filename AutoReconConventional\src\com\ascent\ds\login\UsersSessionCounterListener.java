package com.ascent.ds.login;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

/**
 * Application Lifecycle Listener implementation class
 * UsersSessionCounterListener
 *
 */
@WebListener
public class UsersSessionCounterListener implements HttpSessionListener {

	/**
	 * @see HttpSessionListener#sessionCreated(HttpSessionEvent)
	 */

	private List sessions = new ArrayList();

	public void sessionCreated(HttpSessionEvent event) {
		// TODO Auto-generated method stub

		HttpSession session = event.getSession();
		sessions.add(session.getId());

		session.setAttribute("counter", this);
	}

	/**
	 * @see HttpSessionListener#sessionDestroyed(HttpSessionEvent)
	 */
	public void sessionDestroyed(HttpSessionEvent event) {
		// TODO Auto-generated method stub
		HttpSession session = event.getSession();
		sessions.remove(session.getId());

		session.setAttribute("counter", this);
	}

	public int getActiveSessionNumber() {
		System.out.println("activatedSessions List" + sessions);
		return sessions.size();
	}
}
