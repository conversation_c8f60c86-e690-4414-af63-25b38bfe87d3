package com.ascent.cod.ach.export;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExportExcelInternalReconcileACH {

	private static Logger logger = LogManager.getLogger(ExportExcelExternalSuppressACH.class.getName());

	public static void main(String[] args) throws IOException {
		//	exportExel();
	}

	public static String exportExcel3(List<Map<String, Object>> internalrecondataList) throws IOException {
		/*public static String exportExcel1(List<Map<String, Object>> unmatchList,String department) throws IOException {*/
		Date date = new Date();
		
		List<Map<String,Object>> internalsuppressdataList1 = new ArrayList<Map<String,Object>>();
		for(Map map11 : internalrecondataList){
			Map<String, Object> dataMapList = new LinkedHashMap<String, Object>();

			dataMapList.put("ACCT NUM",map11.get("ACCT NUM"));
			dataMapList.put("TRANID",map11.get("TRANID"));
			dataMapList.put("TRAN DATE",map11.get("TRAN DATE"));
			dataMapList.put("VALUE DATE",map11.get("VALUE DAT"));
			dataMapList.put("VALUE DATE",map11.get("VALUE DATE"));
			dataMapList.put("TRAN AMOUNT",map11.get("TRAN AMOUNT"));
			dataMapList.put("BANK",map11.get("BANK"));
			dataMapList.put("CREATED ON",map11.get("CREATED ON"));
			dataMapList.put("DRCR",map11.get("DRCR"));
			dataMapList.put("RECON STATUS",map11.get("RECON STATUS"));
			internalsuppressdataList1.add(dataMapList);
		}	
		Set<String> columnNamesSet = internalsuppressdataList1.get(0).keySet();

		String[] columnNames = columnNamesSet.stream().toArray(String[] ::new);

		/*System.out.println("Before: "+columnNamesSet);

		ArrayList<String> myList = new ArrayList<String>(columnNamesSet);
		myList.remove("PERSON");
		myList.remove("business_area");


		columnNames = myList.toArray(new String[0]);

		System.out.println("After: "+Arrays.toString(columnNames));
		System.out.println(myList);
		 */
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Contacts");

		Font headerFont = workbook.createFont();
		CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);

		/*Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.RED.getIndex());

		 */
		// Create a Row
		Row headerRow = sheet.createRow(0);


		for (int i = 0; i < columnNames.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columnNames[i]); 
			cell.setCellStyle(headerCellStyle);
		}

		// Create Other rows and cells with contacts data
		int rowNum = 1;

		// for (Contact contact : contacts) {
		for (int i = 0; i < internalsuppressdataList1.size(); i++) {
			Map<String, Object> map = internalsuppressdataList1.get(i);

			int count = 0;
			Row row = sheet.createRow(rowNum++);

			for (Map.Entry<String, Object> entry : map.entrySet()) {

				row.createCell(count++).setCellValue(entry.getValue() == null ? "" : entry.getValue().toString());
			}
		}

		// Resize all columns to fit the content size
		for (int i = 0; i < columnNames.length; i++) {
			sheet.autoSizeColumn(i);
		}


		File pathFile = new File(System.getProperty("java.io.tmpdir")+"\\COD\\");
		if (!pathFile.exists())
			pathFile.mkdirs();
		else {
			pathFile.delete();
			pathFile.mkdirs();
		}
		String fileName = String.format("ACH_Internal_Reconcile.xlsx", date);
		File file = new File(pathFile + File.separator + fileName);
		
		// Write the output to a file
		FileOutputStream fileOut = new FileOutputStream(file);
		workbook.write(fileOut);
		fileOut.close();

		logger.debug("export done...");

		return file.toString();

	}


}
