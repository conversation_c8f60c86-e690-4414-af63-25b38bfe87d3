package com.ascent.ds.operations;

import java.io.InputStream;
import java.util.Properties;

import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 *
 */

public class MailTrigger {

	private static Logger logger = LogManager.getLogger(MailTrigger.class.getName());

	public static void main(String[] args) {

		String dataToAppendCaseTable_followup = "case created";
		try {
			new MailTrigger().sendEtlExtractionConformationMail(dataToAppendCaseTable_followup,"<EMAIL>");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean sendEtlExtractionConformationMail(String content, String toMail) throws Exception {
		boolean flag = false;
		Properties prop = new Properties();
		Properties properties = System.getProperties();
		
		String propFileName = "LOCAL\\caseManagement.properties";
		InputStream fileextraction = getClass().getClassLoader().getResourceAsStream(propFileName);
		prop.load(fileextraction);

		String from = prop.getProperty("from");
		String username = prop.getProperty("fromUserName");
		String password = prop.getProperty("fromUserPassword");
		String SMTPHost = prop.getProperty("SMTPHost");
		String SMTPPort = prop.getProperty("SMTPPort");
		
		// Setup mail server
		properties.setProperty("mail.smtp.host", SMTPHost);
		properties.setProperty("mail.smtp.auth", "true");
		properties.setProperty("mail.smtp.starttls.enable", "true");
		properties.setProperty("mail.smtp.ssl.trust", "smtp.gmail.com");
		properties.setProperty("mail.smtp.port", SMTPPort);

		Session session = Session.getInstance(properties, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, password);
			}
		});
		
		try {
			// Create a default MimeMessage object.
			MimeMessage message = new MimeMessage(session);

			// Set From: header field of the header.
			message.setFrom(new InternetAddress(from));
			logger.debug("from : "+from);
			//System.out.println("from  :  " + from);

			// Set To: header field of the header.
			message.addRecipient(Message.RecipientType.TO, new InternetAddress(toMail));

			// Create a multipar message
			Multipart multipart = new MimeMultipart();

			message.setSubject("This is the Subject Line!");
			// message.setContent("modal.html","text/html" );
			message.setText("Case Created.....!");

			BodyPart htmlBodyPart = new MimeBodyPart();
			htmlBodyPart.setContent(content, "text/html"); // 5
			multipart.addBodyPart(htmlBodyPart); // mainbody commented
			// Send the complete message parts
			message.setContent(multipart);

			// Send message
			Transport.send(message);
			flag = true;

			//System.out.println("MESSAGE SEND successfully....");
			logger.debug("Mail Send successfully....");
		} catch (MessagingException mex) {
			flag = false;
			logger.info(" mailForCaseRecordsFOLLOWUP()   WHILE SENDING MAIL GIVING PROBLEM    " + mex);
			mex.printStackTrace();
		}
		return flag;
	}

}
