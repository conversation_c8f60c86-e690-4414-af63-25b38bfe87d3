package com.ascent.util;

import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.util.Random;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;

public class PasswordGeneratorUtil {

	private static final String CHAR_LIST = "abcdefghijklmnopqrstuvwxyz@#$&?_*!^ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";

	private static final int RANDOM_STRING_LENGTH = 12;

	private String genratedPwd;

	public PasswordGeneratorUtil() {

		generateRandomString();
	}

	public void generateRandomString() {

		StringBuffer randStr = new StringBuffer();
		for (int i = 0; i < RANDOM_STRING_LENGTH; i++) {
			int number = getRandomNumber();
			char ch = CHAR_LIST.charAt(number);
			randStr.append(ch);

		}
		String genratedPwd = randStr.toString();
		setGenratedPwd(genratedPwd);
	}

	private int getRandomNumber() {
		int randomInt = 0;
		Random randomGenerator = new Random();
		randomInt = randomGenerator.nextInt(CHAR_LIST.length());
		if (randomInt - 1 == -1) {
			return randomInt;
		} else {
			return randomInt - 1;
		}
	}

	public String getGenratedPwd() {

		return genratedPwd;

	}

	public void setGenratedPwd(String genratedPwd) {
		this.genratedPwd = genratedPwd;
	}

	
	
	
	
	
	public static void main(String[] args) throws NoSuchAlgorithmException, InvalidKeySpecException {

		//PasswordGeneratorUtil generator = new PasswordGeneratorUtil();
		String password="Shilpa@12";
		String storedPWD="1000:57605ae61e1099bb60077699cb1a836b:a729b3a20d4bba736ed4223e562e150aef2bbc3bd2e03b18acf8a5f0022a7ee0bfc43f25aaee5704e37e3f8e3786acd339034a15505a99cf77196a5403fc7766";
		String generateStorngPasswordHash = generateStorngPasswordHash(password);
		
		
		boolean validatePassword = validatePassword(password, storedPWD);
		System.out.println(validatePassword); 
		
		
		System.out.println(generateStorngPasswordHash);
		
	}
	
	
	
	public static String generateStorngPasswordHash(String password) throws NoSuchAlgorithmException, InvalidKeySpecException
    {
        int iterations = 1000;
        char[] chars = password.toCharArray();
        byte[] salt = getSalt();
         
        PBEKeySpec spec = new PBEKeySpec(chars, salt, iterations, 64 * 8);
        SecretKeyFactory skf = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1");
        byte[] hash = skf.generateSecret(spec).getEncoded();
        return iterations + ":" + toHex(salt) + ":" + toHex(hash);
    }
     
    private static byte[] getSalt() throws NoSuchAlgorithmException
    {
        SecureRandom sr = SecureRandom.getInstance("SHA1PRNG");
        byte[] salt = new byte[16];
        sr.nextBytes(salt);
        return salt;
    }
     
    private static String toHex(byte[] array) throws NoSuchAlgorithmException
    {
        BigInteger bi = new BigInteger(1, array);
        String hex = bi.toString(16);
        int paddingLength = (array.length * 2) - hex.length();
        if(paddingLength > 0)
        {
            return String.format("%0"  +paddingLength + "d", 0) + hex;
        }else{
            return hex;
        }
    }
    
    public static boolean validatePassword(String originalPassword, String storedPassword) throws NoSuchAlgorithmException, InvalidKeySpecException
    {
        String[] parts = storedPassword.split(":");
        int iterations = Integer.parseInt(parts[0]);
        byte[] salt = fromHex(parts[1]);
        byte[] hash = fromHex(parts[2]);
        System.out.println(originalPassword.toCharArray());
        System.out.println(salt);
        System.out.println(iterations);
        System.out.println(hash.length * 8);
     
        PBEKeySpec spec = new PBEKeySpec(originalPassword.toCharArray(), salt, iterations, hash.length * 8);
        SecretKeyFactory skf = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1");
        byte[] testHash = skf.generateSecret(spec).getEncoded();
        
        System.out.println("testHash :" + testHash);
        
        int diff = hash.length ^ testHash.length;
        for(int i = 0; i < hash.length && i < testHash.length; i++)
        {
            diff |= hash[i] ^ testHash[i];
           
        }
        System.out.println("diff :" + diff);
        return diff == 0;
    }
    private static byte[] fromHex(String hex) throws NoSuchAlgorithmException
    {
        byte[] bytes = new byte[hex.length() / 2];
        for(int i = 0; i<bytes.length ;i++)
        {
            bytes[i] = (byte)Integer.parseInt(hex.substring(2 * i, 2 * i + 2), 16);
        }
        return bytes;
    }
	
}
