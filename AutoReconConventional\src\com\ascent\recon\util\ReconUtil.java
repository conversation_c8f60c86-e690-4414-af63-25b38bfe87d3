package com.ascent.recon.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ReconUtil {

	
	/*public synchronized Integer generateLazySeqNo(Connection connection,PreparedStatement preparedStatement,String seqName) {
		if(connection==null && preparedStatement==null){
			throw new Exception("Neither Connection not Statement available");
		}else if(preparedStatement!=null && !preparedStatement.isClosed()){
			
		try {
			seqNumQry = "SELECT NEXT VALUE FOR " + seqName + "  as sno";

			connectionforSeq = dbCurdRUC.getConnection();
			seqNoPstmt = connectionforSeq.prepareStatement(seqNumQry);

			txnRs = seqNoPstmt.executeQuery();
			if (txnRs.next()) {
				return (int) txnRs.getLong(1);
			}
		} catch (SQLException e) {
			Connection connection = null;
			PreparedStatement createSeqPstmt = null;
			PreparedStatement seqNumPstmtTemp = null;
			String createSeqNumQry = null;

			try {
				connection = dbCurdRUC.getConnection();
				createSeqNumQry = "CREATE  SEQUENCE " + seqName
						+ "   AS BIGINT START WITH 1  INCREMENT BY 1 ";

				createSeqPstmt = connection.prepareStatement(createSeqNumQry);
				createSeqPstmt.executeUpdate();

				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);

				ResultSet rs = seqNumPstmtTemp.executeQuery();
				if (rs.next()) {
					return (int) rs.getLong(1);
				}

			} catch (Exception e1) {
				logger.trace("sequence creation error" + e1);
				e1.printStackTrace();
				logger.error(e1.getMessage(), e1);

			} finally {

				closePreparedStatement(createSeqPstmt);
				closePreparedStatement(seqNumPstmtTemp);
				closeConnection(connection);
			}
		}
		}else{
			
		}
		return (int) 0l;

	}*/
}
