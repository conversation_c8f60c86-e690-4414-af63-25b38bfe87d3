package com.ascent.cod.ach.export;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


public class ExportExcelExternalSuppressACH {
	private static Logger logger = LogManager.getLogger(ExportExcelExternalSuppressACH.class.getName());
	
	public static void main(String[] args) throws IOException {
	//	exportExel();
	}

	public static String exportExcel2(List<Map<String, Object>> externalsuppressDataList) throws IOException {
	/*public static String exportExcel1(List<Map<String, Object>> unmatchList,String department) throws IOException {*/
		Date date = new Date();
		
	    Set<String> columnNamesSet = externalsuppressDataList.get(0).keySet();

		String[] columnNames = columnNamesSet.stream().toArray(String[] ::new);
		
		/*System.out.println("Before: "+columnNamesSet);
		
		ArrayList<String> myList = new ArrayList<String>(columnNamesSet);
		myList.remove("PERSON");
		myList.remove("business_area");
		
		
		columnNames = myList.toArray(new String[0]);
		
		System.out.println("After: "+Arrays.toString(columnNames));
		System.out.println(myList);
		*/
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Contacts");

		Font headerFont = workbook.createFont();
		CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);
		
		/*Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.RED.getIndex());

*/
		// Create a Row
		Row headerRow = sheet.createRow(0);
		
		
		for (int i = 0; i < columnNames.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columnNames[i]); 
			cell.setCellStyle(headerCellStyle);
		}
	
		// Create Other rows and cells with contacts data
		int rowNum = 1;

		// for (Contact contact : contacts) {
		for (int i = 0; i < externalsuppressDataList.size(); i++) {
			Map<String, Object> map = externalsuppressDataList.get(i);
			
			int count = 0;
			Row row = sheet.createRow(rowNum++);

			for (Map.Entry<String, Object> entry : map.entrySet()) {
				
			row.createCell(count++).setCellValue(entry.getValue() == null ? "" : entry.getValue().toString());
			}
		}
	
		// Resize all columns to fit the content size
		for (int i = 0; i < columnNames.length; i++) {
			sheet.autoSizeColumn(i);
		}

		
		File pathFile = new File(System.getProperty("java.io.tmpdir")+"\\COD\\");
		if (!pathFile.exists())
			pathFile.mkdirs();
		String fileName = String.format("ACH_External_UnReconcile.xlsx", date);
		File file = new File(pathFile + File.separator + fileName);
		
		// Write the output to a file
		FileOutputStream fileOut = new FileOutputStream(file);
		workbook.write(fileOut);
		fileOut.close();
	
		logger.debug("export done...");

		return file.toString();
	
		}


}
