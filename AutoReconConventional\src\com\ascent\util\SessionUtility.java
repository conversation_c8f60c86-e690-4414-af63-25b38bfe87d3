package com.ascent.util;

import java.io.IOException;

import javax.faces.context.FacesContext;
import javax.servlet.ServletConfig;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.ascent.service.dto.User;

/**
 * Servlet implementation class SessionUtility
 */
@WebServlet("/SessionUtility")
public class SessionUtility extends HttpServlet {
	private static final long serialVersionUID = 1L;
       private static HttpSession session;
   private static	ServletContext context;
    /**
     * @see HttpServlet#HttpServlet()
     */
    public SessionUtility() {

 
 

    }
@Override
public void init(ServletConfig config) throws ServletException {
	// TODO Auto-generated method stub
	
	
	super.init(config);
}
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
	 session=request.getSession();
	 context=getServletContext();  
	 
	}
	
public static User getLoginUser( ) {
		
		 
		 	
		User user=(User) session.getAttribute("Username");
		return user;
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

	
	}

	public static ServletContext getContext() {
		return context;
	}
	
	public static HttpSession getSession() {
		return session;
	}
}
