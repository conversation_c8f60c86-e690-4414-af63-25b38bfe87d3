package com.ascent.service.dao;

import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.ds.login.PasswordPolicy;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.DbCursor;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.persistance.UpdateRegulator;
import com.ascent.service.dto.Department;
import com.ascent.service.dto.Departments;
import com.ascent.service.dto.Feature;
import com.ascent.service.dto.Features;
import com.ascent.service.dto.Privilege;
import com.ascent.service.dto.PrivilegeDetails;
import com.ascent.service.dto.Privileges;
import com.ascent.service.dto.Role;
import com.ascent.service.dto.Roles;
import com.ascent.service.dto.User;
import com.ascent.service.dto.UserController;
import com.ascent.service.dto.Users;

public class CustomerDao implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7586429524916016048L;

	public boolean update(Connection connection, Object object, String queryName) throws Exception {
		Map<String, Object> dataMap = (Map<String, Object>) object;

		UpdateRegulator updateRegulator = new UpdateRegulator();

		Map<String, Object> parmMap = new HashMap<String, Object>();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();

		Query query = queries.getQueryConf(queryName);
		System.out.println("Data map" + dataMap);
		String queryString = query.getQueryString();
		String queryParam = query.getQueryParam();
		parmMap.put("UPDATE_QRY_PARAMS", queryParam);
		parmMap.put("UPDATE_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", dataMap);

		int rowsAffected = updateRegulator.update(connection, parmMap);
		System.out.println(rowsAffected + " record APPROVED check");
		if (rowsAffected == 0) {

			return false;
		} else {

			return true;
		}
	}

	public boolean insertData(Connection connection, Object object, String queryName) throws Exception {

		InsertRegulator insertRegulator = null;
		try {
			Map<String, Object> dataMap = (Map<String, Object>) object;
			insertRegulator = new InsertRegulator();

			Map<String, Object> parmMap = new HashMap<String, Object>();
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();
			Query query = queries.getQueryConf(queryName);

			String queryString = query.getQueryString();
			String queryParam = query.getQueryParam();

			parmMap.put("INSERT_QRY_PARAMS", queryParam);
			parmMap.put("INSERT_QRY", queryString);
			parmMap.put("PARAM_VALUE_MAP", dataMap);

			int rowsAffected = insertRegulator.insert(connection, parmMap);

			if (rowsAffected == 0) {
				return false;
			} else {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		} catch (Throwable e) {
			e.printStackTrace();
			throw new Exception(e);
		} finally {

		}

	}

	public List<Map<String, Object>> getData(Map<String, Object> dataMap, String queryName)
			throws ClassNotFoundException, SQLException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LoadRegulator loadRegulator = null;
		Connection connection = null;
		AscentWebMetaInstance ascentWebMetaInstance = null;
		try {
			ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			loadRegulator = new LoadRegulator();
			connection = DbUtil.getConnection();
			Query query = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(queryName);

			DbCursor dbCursor = loadRegulator.load(connection, query, dataMap);

			List<Map<String, Object>> tempRecords = dbCursor.getNextBatch();

			while (tempRecords.size() > 0) {
				list.addAll(tempRecords);
				tempRecords.clear();
				tempRecords = dbCursor.getNextBatch();

			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (connection != null && !connection.isClosed())
				connection.close();
		}
		return list;

	}

	public List<Map<String, Object>> getPendingActivityData(Map<String, Object> dataMap, String queryName)
			throws ClassNotFoundException, SQLException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LoadRegulator loadRegulator = null;
		Connection connection = null;
		AscentWebMetaInstance ascentWebMetaInstance = null;
		try {
			ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			loadRegulator = new LoadRegulator();
			connection = DbUtil.getConnection();
			Query query = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(queryName);
			// and recon in('ATM - DEBIT CARD','ATM - CREDIT CARD','ATM -
			// PAYROLL CARD')
			String queryString = query.getQueryString();
			System.out.println(queryString + "+++++++++");

			if (!dataMap.get("recon").equals("''")) {

				queryString = queryString + "and recon in(" + dataMap.get("recon") + ")";
				System.out.println(queryString + "____________________________");
				if (!(queryString.contains("and recon in"))) {
					query.setQueryString(queryString);
				}
				DbCursor dbCursor = loadRegulator.load(connection, query, dataMap);

				List<Map<String, Object>> tempRecords = dbCursor.getNextBatch();

				System.out.println(" *******&&&&&&& &&& TEMP RECORDs" + tempRecords);
				while (tempRecords.size() > 0) {
					list.addAll(tempRecords);
					tempRecords.clear();
					tempRecords = dbCursor.getNextBatch();

				}
			} else {
				list = getData(dataMap, "GetPendigActivityForUser");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (connection != null && !connection.isClosed())
				connection.close();
		}
		return list;

	}

	public List<Map<String, Object>> getPendingCaseManagementData(Map<String, Object> dataMap, String queryName)
			throws ClassNotFoundException, SQLException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LoadRegulator loadRegulator = null;
		Connection connection = null;
		AscentWebMetaInstance ascentWebMetaInstance = null;
		try {
			ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			loadRegulator = new LoadRegulator();
			connection = DbUtil.getConnection();
			Query query = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(queryName);
			// and recon in('ATM - DEBIT CARD','ATM - CREDIT CARD','ATM -
			// PAYROLL CARD')
			String queryString = query.getQueryString();
			System.out.println(queryString + "+++++++++");

			if (!dataMap.get("recon").equals("''")) {

				queryString = queryString + "and recon in(" + dataMap.get("recon") + ")";
				System.out.println(queryString + "____________________________");
				if (!(queryString.contains("and recon in"))) {
					query.setQueryString(queryString);
				}
				DbCursor dbCursor = loadRegulator.load(connection, query, dataMap);

				List<Map<String, Object>> tempRecords = dbCursor.getNextBatch();

				System.out.println(" *******&&&&&&& &&& TEMP RECORDs" + tempRecords);
				while (tempRecords.size() > 0) {
					list.addAll(tempRecords);
					tempRecords.clear();
					tempRecords = dbCursor.getNextBatch();

				}
			} else {
				list = getData(dataMap, "GetPendigCaseManagementForUser");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (connection != null && !connection.isClosed())
				connection.close();
		}
		return list;

	}

	public List<Map<String, Object>> getData(Map<String, Object> dataMap, PreparedStatement preparedStatement,
			String queryParamsWithType) throws ClassNotFoundException, SQLException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LoadRegulator loadRegulator = null;
		Connection connection = null;
		try {

			loadRegulator = new LoadRegulator();

			DbCursor ascentResultSet = loadRegulator.load(preparedStatement, dataMap, queryParamsWithType);

			List<Map<String, Object>> tempRecords = ascentResultSet.getNextBatch();

			while (tempRecords.size() > 0) {
				list.addAll(tempRecords);
				tempRecords.clear();
				tempRecords = ascentResultSet.getNextBatch();

			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (connection != null && !connection.isClosed())
				connection.close();
		}
		return list;

	}

	public void updateDeparments(Connection connection, Map<String, Object> parmMap)
			throws ClassNotFoundException, SQLException {
		try {
			update(connection, parmMap, "updateDeparments");
		} catch (Exception e) {

			e.printStackTrace();
		}

	}

	public void savePrevielege(Map<String, Object> valueMap) {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Connection connection = DbUtil.getConnection();
		InsertRegulator insertRegulator = new InsertRegulator();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;

		try {
			query = queries.getQueryConf("savePrevielege");
		} catch (Exception e) {

			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);

		int rowsAffected = insertRegulator.insert(connection, parmMap);
		System.out.println("rowsAffected:--- to add privilage :--- " + rowsAffected);

		// dbCurdRUC.insert(parmMap);
	}

	public UserController loadAllUserPrivilege() {
		UserController userController = new UserController();

		try {
			userController.setRoles(loadAllRoles());
			userController.setUsers(loadAllUsers());
			userController.setFeatures(loadAllFeatures());

			userController.setDepartments(loadAllDepartMents());
			userController.setPrivileges(loadAllPrivileges());

			userController.init();

		} catch (Exception e) {

			e.printStackTrace();
		}

		return userController;
	}

	public void saveFeatures(Map<String, Object> valueMap) {

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();

		Query query = null;

		try {
			query = queries.getQueryConf("saveFeatures");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);

		int rowsAffected = 0;// dbCurdRUC.insert(parmMap);
	}

	public Map<String, Object> getDataBaseMap(String queryName, Map<String, Object> paramValues) throws SQLException {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		System.out.println();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();// getQueries();
		Query query = null;
		try {
			query = queries.getQueryConf(queryName);
		} catch (Exception e) {

			e.printStackTrace();
		}

		parmMap.put("PARAM_VALUE_MAP", paramValues);
		Connection connection = DbUtil.getConnection();
		LoadRegulator loadRegulator = new LoadRegulator();
		DbCursor load = loadRegulator.load(connection, query, parmMap);

		List<Map<String, Object>> recordsTemp = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> records = load.getNextBatch();

		while (records.size() > 0) {
			// List<Map<String, Object>> recordsTemp = new
			// ArrayList<Map<String,Object>>() ;
			recordsTemp.addAll(records);
			records.clear();
			records = load.getNextBatch();
		}
		if (connection != null && !connection.isClosed()) {
			connection.close();
		}
		if (recordsTemp.size() > 0)
			return recordsTemp.get(0);
		else
			return new HashMap<String, Object>();

	}

	public Privileges loadAllPrivileges() throws Exception {
		Privileges privileges = new Privileges();
		try {

			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			Connection connection = null;

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();
			Query query = queries.getQueryConf("LoadAllPrevieleges");

			Map<String, Object> parmMap = new HashMap<String, Object>();
			Map<String, Object> parmValueMap = new HashMap<String, Object>();
			parmMap.put("PARAM_VALUE_MAP", parmValueMap);

			connection = DbUtil.getConnection();
			LoadRegulator loadRegulator = new LoadRegulator();
			DbCursor dbcursor = loadRegulator.load(connection, query, parmMap);

			List<Map<String, Object>> privilegesRecords = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> recordsTemp = dbcursor.getNextBatch();
			while (recordsTemp.size() > 0) {
				privilegesRecords.addAll(recordsTemp);
				recordsTemp.clear();
				recordsTemp = dbcursor.getNextBatch();
			}
			List<Privilege> list = new ArrayList<Privilege>();

			for (Map<String, Object> map : privilegesRecords) {

				Privilege privilege = new Privilege();

				privilege.setId((Integer) map.get("id"));
				privilege.setPrivilegeName((String) map.get("privilege_name"));
				privilege.setRole((String) map.get("role"));
				privilege.setVersion((Integer) map.get("version"));
				privilege.setStatus((String) map.get("status"));
				// System.out.println((map.get("privilegeId")).getClass()+" :
				// class");
				Integer prvId = 0;
				if (map.get("privilegeId") != null) {
					prvId = Integer.parseInt((String) map.get("privilegeId"));
				}
				privilege.setPrivilegeId((prvId));

				Map<String, PrivilegeDetails> privDetailMap = new HashMap<String, PrivilegeDetails>();

				privilege.setPrivilegeDetailsMap(privDetailMap);

				if (map.get("privilge_details") != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) map.get("privilge_details"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					List listObj = (List) obj;
					bis.close();
					ois.close();
					System.out.println(listObj);
					List<PrivilegeDetails> privilegeDetailsList = new ArrayList<PrivilegeDetails>();
					for (Object privilegeDetails : listObj) {
						Map<String, Object> privilegeDetailsMap = (Map<String, Object>) privilegeDetails;
						System.out.println(privilegeDetailsMap + "privilegeDetailsMap");
						// geography=QATAR, B_Area=ON US, module= Recon,
						// Recon=[ATM - DEBIT CARD], operation=Recon Process,
						// description=dfghfgh,
						// _selection_27=true}privilegeDetailsMap
						/*
						 * module=Recon, geography=QATAR, B_Area=ON US,
						 * Recon=[ATM - DEBIT CARD, ATM - CREDIT CARD],
						 * operation=Recon Process , description=aaaaaaaaaaaaaa,
						 * _selection_29=true}privilegeDetailsMap
						 * 
						 * System.out.println(privilegeDetailsMap);
						 */
						PrivilegeDetails details2 = new PrivilegeDetails();
						/* details2.setVersion((Integer) map.get("version")); */

						details2.setVersion((Integer) map.get("version"));
						details2.setAccesibility("Y");
						details2.setGeography((String) privilegeDetailsMap.get("geography"));
						details2.setModule((String) privilegeDetailsMap.get("module"));
						details2.setBuisinessArea((String) privilegeDetailsMap.get("B_Area"));
						List<String> reconList=null;
						try{
							reconList = (List<String>) privilegeDetailsMap.get("Recon");
						}catch(Exception e){
							e.printStackTrace();
							reconList=new ArrayList<String>();
							reconList.add((String)privilegeDetailsMap.get("Recon"));
						}
						String reconString = "";
						if (reconList != null && !reconList.isEmpty()) {

							for (String string : reconList) {
								reconString = reconString + "@" + string;
							}
						}
						if (!reconString.isEmpty()) {
							reconString = reconString.substring(1, reconString.length());
						}
						List<String> reconListSources = (List<String>) privilegeDetailsMap.get("Sources");

						String sources = "";
						if (reconListSources != null && !reconListSources.isEmpty()) {

							for (String string : reconListSources) {
								sources = sources + "@" + string;
							}
						}

						if (!sources.isEmpty()) {
							sources = sources.substring(1, sources.length());
						}
						details2.setSource(sources);
						details2.setRecon(reconString);
						details2.setOperation((String) privilegeDetailsMap.get("operation"));
						privilegeDetailsList.add(details2);

					}

					privilege.setPrivilegeDetails(privilegeDetailsList);

					// privilege.setPrivilegeDetails(new
					// ArrayList<PrivilegeDetails>());
				} else {
					privilege.setPrivilegeDetails(new ArrayList<PrivilegeDetails>());
				}
				list.add(privilege);
			}

			privileges.setPrivilegeList(list);

		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}
		return privileges;

	}

	/*
	 * public Privileges loadAllPrivileges() throws Exception {
	 * System.out.println("In loadAllPrivileges"); Privileges privileges = new
	 * Privileges(); try {
	 * 
	 * Connection connection = null;
	 * 
	 * AscentWebMetaInstance ascentWebMetaInstance =
	 * AscentWebMetaInstance.getInstance();
	 * 
	 * Queries queries = ascentWebMetaInstance.getWebQueryConfs(); Query query =
	 * queries.getQueryConf("LoadAllPrevieleges");
	 * 
	 * Map<String, Object> parmMap = new HashMap<String, Object>(); Map<String,
	 * Object> parmValueMap = new HashMap<String, Object>();
	 * parmMap.put("PARAM_VALUE_MAP", parmValueMap);
	 * 
	 * connection = DbUtil.getConnection(); LoadRegulator loadRegulator = new
	 * LoadRegulator();
	 * 
	 * DbCursor dbcursor = loadRegulator.load(connection, query, parmMap);
	 * 
	 * List<Map<String, Object>> privilegesRecords = new ArrayList<Map<String,
	 * Object>>(); List<Map<String, Object>> recordsTemp =
	 * dbcursor.getNextBatch(); while (recordsTemp.size() > 0) {
	 * privilegesRecords.addAll(recordsTemp); recordsTemp.clear(); recordsTemp =
	 * dbcursor.getNextBatch(); }
	 * 
	 * List<Privilege> list = new ArrayList<Privilege>();
	 * 
	 * for (Map<String, Object> map : privilegesRecords) {
	 * 
	 * Privilege privilege = new Privilege();
	 * 
	 * privilege.setId((Integer) map.get("id"));
	 * privilege.setPrivilegeName((String) map.get("privilege_name"));
	 * privilege.setRole((String) map.get("role")); Integer prvId =0;
	 * if((String) map.get("privilegeId")!=null){ prvId =
	 * Integer.parseInt((String) map.get("privilegeId")); }else{ prvId =
	 * Integer.parseInt("0"); } privilege.setPrivilegeId((prvId));
	 * 
	 * 
	 * 
	 * 
	 * List<Map<String, Object>> privilegesDetailsRecords = new
	 * ArrayList<Map<String, Object>>();
	 * 
	 * 
	 * List<PrivilegeDetails> details = new ArrayList<PrivilegeDetails>();
	 * Map<String, PrivilegeDetails> privDetailMap = new HashMap<String,
	 * PrivilegeDetails>();
	 * 
	 * privilege.setPrivilegeDetailsMap(privDetailMap); int sno = 1;
	 * 
	 * for (Map<String, Object> detailsMap : privilegesRecords) {
	 * 
	 * PrivilegeDetails privilegeDetails = new PrivilegeDetails();
	 * privilegeDetails.setSno(sno); Integer pId = (Integer)
	 * detailsMap.get("pid"); privilegeDetails.setPid(pId);
	 * privilegeDetails.setModule((String) detailsMap.get("module"));
	 * privilegeDetails.setOperation((String) detailsMap.get("operation"));
	 * privilegeDetails.setAccesibility((String) detailsMap.get("allowedto"));
	 * privilegeDetails.setDescription((String) detailsMap.get("description"));
	 * 
	 * details.add(privilegeDetails);
	 * 
	 * sno++; } privilege.setPrivilegeDetails(details); list.add(privilege);
	 * 
	 * }
	 * 
	 * privileges.setPrivilegeList(list);
	 * 
	 * } catch (Exception e) { e.printStackTrace(); throw new Exception(e); }
	 * return privileges;
	 * 
	 * }
	 */

	public Departments loadAllDepartMents() throws Exception {

		Departments departments = new Departments();

		try {

			Connection connection = null;

			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			LoadRegulator loadRegulator = new LoadRegulator();
			Queries queries = ascentWebMetaInstance.getWebQueryConfs();
			Query query = queries.getQueryConf("LoadAllDepartments");

			Map<String, Object> parmMap = new HashMap<String, Object>();
			Map<String, Object> parmValueMap = new HashMap<String, Object>();
			parmMap.put("PARAM_VALUE_MAP", parmValueMap);

			connection = DbUtil.getConnection();

			DbCursor ascentResultSet = loadRegulator.load(connection, query, parmMap);

			List<Map<String, Object>> departmentsRecordsTemp = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> recordsTemp = ascentResultSet.getNextBatch();

			while (recordsTemp.size() > 0) {
				departmentsRecordsTemp.addAll(recordsTemp);
				recordsTemp.clear();
				recordsTemp = ascentResultSet.getNextBatch();
			}

			List<Department> list = new ArrayList<Department>();
			int sno = 1;

			for (Map<String, Object> map : departmentsRecordsTemp) {
				Department department = new Department();
				department.setSno(sno);
				department.setId((Integer) map.get("id"));
				department.setDeptName((String) map.get("dept_name"));
				department.setDeptId((String) map.get("dept_id"));
				department.setDeptCategory((String) map.get("category"));
				department.setDeptLocation((String) map.get("location"));

				list.add(department);

				departments.setDepartments(list);
				sno++;
			}

		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}
		return departments;

	}

	private Features loadAllFeatures() {

		Connection connection = null;

		Features features = new Features();
		try {

			LoadRegulator loadRegulator = new LoadRegulator();
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();
			Query query = queries.getQueryConf("LoadAllFeatures");

			Map<String, Object> parmMap = new HashMap<String, Object>();
			Map<String, Object> parmValueMap = new HashMap<String, Object>();
			parmMap.put("PARAM_VALUE_MAP", parmValueMap);

			connection = DbUtil.getConnection();
			DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);

			List<Map<String, Object>> rolesRecordsTemp = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> recordsTemp = dbCursor.getNextBatch();
			while (recordsTemp.size() > 0) {
				rolesRecordsTemp.addAll(recordsTemp);
				recordsTemp.clear();
				recordsTemp = dbCursor.getNextBatch();
			}
			List<Feature> list = new ArrayList<Feature>();
			int sno = 1;
			for (Map<String, Object> map : rolesRecordsTemp) {
				Feature feature = new Feature();
				feature.setSno(sno);
				feature.setFeatureDiscription((String) map.get("discription"));
				feature.setFeature((String) map.get("feature"));
				feature.setFeatureId((String) map.get("feature_id"));
				feature.setId((Integer) map.get("id"));

				list.add(feature);

				features.setFeatures(list);
				sno++;
			}

		} catch (Exception exception) {
			exception.printStackTrace();
		}
		return features;

	}

	public Users loadAllUsers() throws Exception {
		Users users = new Users();
		try {

			LoadRegulator loadRegulator = new LoadRegulator();
			Connection connection = null;

			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();
			Query query = queries.getQueryConf("LoadAllUsers");

			Map<String, Object> parmMap = new HashMap<String, Object>();
			Map<String, Object> parmValueMap = new HashMap<String, Object>();
			parmMap.put("PARAM_VALUE_MAP", parmValueMap);

			connection = DbUtil.getConnection();
			DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);

			List<Map<String, Object>> usersRecordsTemp = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> recordsTemp = dbCursor.getNextBatch();
			while (recordsTemp.size() > 0) {
				usersRecordsTemp.addAll(recordsTemp);
				recordsTemp.clear();
				recordsTemp = dbCursor.getNextBatch();
			}
			List<User> list = new ArrayList<User>();
			int sno = 1;
			for (Map<String, Object> map : usersRecordsTemp) {
				User user = new User();
				user.setSno(sno);
				user.setId((Integer) map.get("id"));
				user.setUserId((String) map.get("user_id"));
				user.setUserName((String) map.get("user_name"));
				user.setEmailId((String) map.get("email_id"));
				user.setPhonNo((String) map.get("phon_number"));
				user.setDeptName((String) map.get("dept_name"));
				user.setReporting((String) map.get("reporting"));
				user.setSystemRole((String) map.get("system_role"));
				user.setPassword((String) map.get("password"));
				user.setIsLdapUser(((String) map.get("isLdapUser")));
				user.setApprovalRole((String) map.get("approval_role"));
				user.setPwd_exp_date((Timestamp) map.get("pwd_exp_date"));
				user.setAccount_status((String) map.get("account_status"));
				/*
				 * Object object = map.get("created_on");
				 * 
				 * java.sql.Date createdOnDate=(java.sql.Date)object; Date
				 * date=new Date(createdOnDate.getTime());
				 * user.setCreatedOn(date);
				 */
				/*
				 * boolean tempEmailNot = false; if (((String)
				 * map.get("email_notification")).equals("Y")) { tempEmailNot =
				 * true; } else { tempEmailNot = false; }
				 * user.setEmailNotification(tempEmailNot); boolean tempSmsNot =
				 * false; if (((String)
				 * map.get("sms_notification")).equals("Y")) { tempSmsNot =
				 * true; } else { tempSmsNot = false; }
				 */
				/* user.setSmsNotification(tempSmsNot); */
				user.setBranchLocation((String) map.get("branch_location"));
				user.setApprovalDepartment((String) map.get("approval_department"));
				list.add(user);
				users.setUserList(list);
				sno++;
			}
			// connection.close();

		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}

		return users;

	}
	/*
	 * public boolean delete(Connection connection, Object object, String
	 * tablName) throws ClassNotFoundException, SQLException { LoadRegulator
	 * loadRegulator = new LoadRegulator(); connection = DbUtil.getConnection();
	 * Map<String, Object> dataMap = (Map<String, Object>) object;
	 * AscentWebMetaInstance ascentWebMetaInstance =
	 * AscentWebMetaInstance.getInstance();
	 * 
	 * Map<String, Object> parmMap = new HashMap<String, Object>();
	 * 
	 * Queries queries =ascentWebMetaInstance.getWebQueryConfs();
	 * 
	 * Query query = queries.getQueryConf(tablName);
	 * 
	 * String queryString = query.getQueryString();//getQuerieString(); String
	 * queryParam = query.getQueryParam();//getQuerieParam();
	 * parmMap.put("DELETE_QRY_PARAMS", queryParam); parmMap.put("DELETE_QRY",
	 * queryString); parmMap.put("PARAM_VALUE_MAP", dataMap); // Connection
	 * connection = dbCurdRUC.getConnection(); int rowsAffected =
	 * dbCurdRUC.delete(connection, parmMap); if (rowsAffected == 0) { // return
	 * false; } else { // return true; } }
	 */

	public void saveRoles(Map<String, Object> dataMap) {
		LoadRegulator loadRegulator = new LoadRegulator();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();

		Query query = null;

		try {
			query = queries.getQueryConf("saveRoles");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", dataMap);
		InsertRegulator insertRegulator = new InsertRegulator();
		int rowsAffected = insertRegulator.insert(parmMap, query);
	}

	public void saveDepartments(Map<String, Object> valueMap) {
		LoadRegulator loadRegulator = new LoadRegulator();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();// getQueries();

		Query query = null;

		try {
			query = queries.getQueryConf("saveDepartments");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", valueMap);
		InsertRegulator insertRegulator = new InsertRegulator();
		int rowsAffected = insertRegulator.insert(parmMap, query);
	}

	public List<Object> getDataListByField(String queryName, String fieldName, Map<String, Object> paramValues)
			throws SQLException {
		LoadRegulator loadRegulator = new LoadRegulator();
		Connection connection = null;
		Map<String, Object> parmMap = new HashMap<String, Object>();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;
		try {
			query = queries.getQueryConf(queryName);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		parmMap.put("PARAM_VALUE_MAP", paramValues);
		connection = DbUtil.getConnection();
		DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);

		List<Map<String, Object>> recordsTemp = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> records = dbCursor.getNextBatch();
		while (records.size() > 0) {
			recordsTemp.addAll(records);
			records.clear();
			records = dbCursor.getNextBatch();
		}
		List<Object> list = new ArrayList<Object>();
		for (Map<String, Object> rec : recordsTemp) {
			if (rec.get(fieldName) != null) {
				list.add(rec.get(fieldName));
			}
		}
		if (connection != null && !connection.isClosed()) {
			connection.close();
		}
		return list;
	}

	public boolean delete(Connection connection, Object object, String tablName)
			throws ClassNotFoundException, SQLException {

		Map<String, Object> dataMap = (Map<String, Object>) object;
		LoadRegulator loadRegulator = new LoadRegulator();

		Map<String, Object> parmMap = new HashMap<String, Object>();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();// getQueries();

		Query query = null;
		try {
			query = queries.getQueryConf(tablName);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		parmMap.put("DELETE_QRY_PARAMS", queryParam);
		parmMap.put("DELETE_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", dataMap);
		// Connection connection = dbCurdRUC.getConnection();
		InsertRegulator insertRegulator = new InsertRegulator();
		int rowsAffected = 0;// insertRegulator.delete(connection, parmMap);
		if (rowsAffected == 0) {
			//
			return false;
		} else {
			//
			return true;
		}
	}

	public Roles loadAllRoles() throws Exception {
		Roles roles = new Roles();
		try {

			LoadRegulator loadRegulator = new LoadRegulator();
			Connection connection = null;

			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();

			Query query = queries.getQueryConf("LoadAllRoles");

			Map<String, Object> parmMap = new HashMap<String, Object>();
			Map<String, Object> parmValueMap = new HashMap<String, Object>();
			parmMap.put("PARAM_VALUE_MAP", parmValueMap);

			connection = DbUtil.getConnection();
			DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);

			List<Map<String, Object>> rolesRecordsTemp = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> recordsTemp = dbCursor.getNextBatch();
			while (recordsTemp.size() > 0) {
				rolesRecordsTemp.addAll(recordsTemp);
				recordsTemp.clear();
				recordsTemp = dbCursor.getNextBatch();
			}
			List<Role> list = new ArrayList<Role>();
			int sno = 1;
			for (Map<String, Object> map : rolesRecordsTemp) {
				Role role = new Role();
				role.setSno(sno);

				role.setRoleId((Integer) map.get("roleid"));
				role.setRole((String) map.get("role"));
				role.setDescription((String) map.get("discription"));

				list.add(role);

				sno++;
			}
			roles.setRoles(list);
			// connection.close();

		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}
		return roles;

	}

	/**
	 * @throws Exception
	 * 
	 */

	public List<Map<String, Object>> loadPrivilageDetailsRoleWise(String role) throws Exception {

		Connection connection = null;
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = queries.getQueryConf("loadPrevielegesDetailsRoleWise");

		Map<String, Object> parmMap = new HashMap<String, Object>();
		Map<String, Object> parmValueMap = new HashMap<String, Object>();
		parmValueMap.put("role", role);
		parmMap.put("PARAM_VALUE_MAP", parmValueMap);
		connection = DbUtil.getConnection();
		LoadRegulator loadRegulator = new LoadRegulator();
		DbCursor dbcursor = loadRegulator.load(connection, query, parmMap);
		List<Map<String, Object>> privilegesRecords = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> recordsTemp = dbcursor.getNextBatch();
		while (recordsTemp.size() > 0) {
			privilegesRecords.addAll(recordsTemp);
			recordsTemp.clear();
			recordsTemp = dbcursor.getNextBatch();
		}

		List<Map<String, Object>> returnList = new ArrayList<Map<String, Object>>();

		for (Map<String, Object> map : privilegesRecords) {
			Map<String, Object> mapTofetch = new HashMap<String, Object>();
			ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) map.get("privilge_details"));
			ObjectInputStream ois = new ObjectInputStream(bis);
			Object obj = ois.readUnshared();
			List listObj = (List) obj;
			bis.close();
			ois.close();
			for (Object privilegeDetails : listObj) {
				Map<String, Object> mapTemp = (Map<String, Object>) privilegeDetails;

				mapTemp.put("version", (Integer) map.get("version"));

			}
			mapTofetch.put(role, listObj);
			returnList.add(mapTofetch);
		}

		return returnList;

	}

	public PasswordPolicy loadPasswordPolicy() {

		PasswordPolicy passwordPolicy = null;

		LoadRegulator loadRegulator = new LoadRegulator();
		Connection connection = null;

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query;
		try {
			query = queries.getQueryConf("LOAD_PASSWORD_POLICY");
			Map<String, Object> parmMap = new HashMap<String, Object>();
			Map<String, Object> parmValueMap = new HashMap<String, Object>();
			parmMap.put("PARAM_VALUE_MAP", parmValueMap);

			connection = DbUtil.getConnection();
			DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);

			List<Map<String, Object>> pwdpolicyRecordsTemp = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> recordsTemp = dbCursor.getNextBatch();
			while (recordsTemp.size() > 0) {
				pwdpolicyRecordsTemp.addAll(recordsTemp);
				recordsTemp.clear();
				recordsTemp = dbCursor.getNextBatch();
			}
			// List<PasswordPolicy> list = new ArrayList<PasswordPolicy>();

			for (Map<String, Object> map : pwdpolicyRecordsTemp) {
				passwordPolicy = new PasswordPolicy();
				passwordPolicy.setId((Integer) map.get("id"));
				passwordPolicy.setTitle((String) map.get("title"));
				passwordPolicy.setMinLength((Integer) map.get("minLength"));
				passwordPolicy.setMaxLength((Integer) map.get("maxLength"));
				passwordPolicy.setPwdMaxAge((Integer) map.get("pwdMaxAge"));
				passwordPolicy.setPwdExpiryWarning((Integer) map.get("pwdExpiryWarning"));
				passwordPolicy.setPwdMaxFailure((Integer) map.get("pwdMaxFailure"));
				passwordPolicy.setAllowUserToChangeOwnPwd((String) map.get("allowUserToChangeOwnPwd"));
				passwordPolicy.setIsSpecialCharsAllowed((String) map.get("isSpecialCharsAllowed"));
				passwordPolicy.setIsUpperCaseAllowed((String) map.get("isUpperCaseAllowed"));
				passwordPolicy.setIsNumbersAllowed((String) map.get("isNumbersAllowed"));
				passwordPolicy.setSpecialChars((String) map.get("specialChars"));
				passwordPolicy.setCreated_on((Timestamp) map.get("created_on"));
				passwordPolicy.setUpdated_on((Timestamp) map.get("updated_on"));
				passwordPolicy.setVersion((Integer) map.get("version"));
				passwordPolicy.setActive_index((String) map.get("Active_Index"));
				passwordPolicy.setStatus((String) map.get("status"));
				passwordPolicy.setWorkflow_status((String) map.get("workflowStatus"));
				passwordPolicy.setActivity_comments((String) map.get("activity_comments"));

			}
			// connection.close();

		} catch (Exception e) {

			e.printStackTrace();
		}

		return passwordPolicy;
	}


	public static List<Map<String, Object>> retrieveData(Connection connection, Query queryConf,
			Map<String, Object> paramValueMap) throws Exception {

		PreparedStatement pstmt = null;
		ResultSet txnRs = null;

		List<Map<String, Object>> data = new ArrayList<Map<String, Object>>();

		try {
			pstmt = connection.prepareStatement(queryConf.getQueryString());
			int index = 1;
			Map<String, Integer> paramTypeMap = queryConf.getQueryParamTypeMap();

			if (queryConf.getQueryParamList() != null && paramTypeMap != null) {

				for (String param : queryConf.getQueryParamList()) {
					pstmt.setObject(index, paramValueMap.get(param),
							paramTypeMap.get(param));
					index++;
				}

			}



			ResultSetMetaData resultSetMetaData = pstmt.getMetaData();
			int noOfColumns = resultSetMetaData.getColumnCount();

			List<String> columnNames = new ArrayList<String>();
			for (int i = 1; i <= noOfColumns; i++) {
				String columnName = resultSetMetaData.getColumnName(i);
				columnNames.add(columnName);
			}
			txnRs = pstmt.executeQuery();
			while (txnRs.next()) {
				Map<String, Object> record = new HashMap<String, Object>();
				for (int i = 0; i < columnNames.size(); i++) {
					record.put(columnNames.get(i), txnRs.getObject(i + 1));
				}
				data.add(record);

			}
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {

			DbUtil.closeResultSet(txnRs);
			DbUtil.closePreparedStatement(pstmt);

		}
		return data;

	}

	public int setLoginStatus(Map<String, Object> loginMap)
	{
		boolean flag=false;
		Connection con = null;
		PreparedStatement ps = null;

		int result = 0;

		//String query="insert into USERS_LOGIN_STATUS values(?,?,?,?,?)";
		String query= "update USERS_LOGIN_STATUS set login_flag=?, session=?, session_created=?, session_last_accesed=? where user_id=?";
		//String query= "update USERS_LOGIN_STATUS set login_flag=?, session=?, session_created=?, session_last_accesed=? where user_id=?";

		System.out.println("query   "+query);

		try {
			//con = jdbcTemplate.getDataSource().getConnection();
			con= DbUtil.getConnection();



			ps = con.prepareStatement(query);
			ps.setString(1, (String) loginMap.get("login_flag"));
			ps.setString(2, (String) loginMap.get("session"));
			ps.setLong(3, (Long) loginMap.get("session_created"));
			ps.setLong(4, (Long) loginMap.get("session_last_accesed"));
			ps.setString(5, (String) loginMap.get("user_id"));


			result = ps.executeUpdate();

		} catch (Exception e) {
			e.printStackTrace();
		}
		finally
		{
			try {
				con.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return result;
	}

	public Map<String, Object> checkLoginStatus(Map<String, Object> loginMap)
	{
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs= null;
		Map<String, Object> login_status = new HashMap<String, Object>();

		//String query="insert into USERS_LOGIN_STATUS values(?,?,?,?,?)";
		String query= "select * from USERS_LOGIN_STATUS where user_id=?";
		//String query= "update USERS_LOGIN_STATUS set login_flag=?, session=?, session_created=?, session_last_accesed=? where user_id=?";

		System.out.println("query   "+query);

		try {
			//con = jdbcTemplate.getDataSource().getConnection();
			con= DbUtil.getConnection();

			ps = con.prepareStatement(query);
			ps.setString(1, (String) loginMap.get("user_id"));

			rs = ps.executeQuery();

			while(rs.next())
			{
				login_status.put("login_flag", rs.getString(2));
				login_status.put("session", rs.getString(3));
				login_status.put("session_created",rs.getLong(4));
				login_status.put("session_last_accesed", rs.getLong(5));	
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		finally
		{
			try {
				con.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return login_status;
	}

	public List<String> fetchUsers()
	{
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs= null;
		List<String> userList = new ArrayList<String>();

		String query= "select * from USERS_LOGIN_STATUS";
		System.out.println("query   "+query);

		try {
			con= DbUtil.getConnection();

			ps = con.prepareStatement(query);

			rs = ps.executeQuery();

			while(rs.next())
			{
				userList.add(rs.getString(1));
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		finally
		{
			try {
				con.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return userList;
	}

	public int addUser(String user_id)
	{
		Connection con = null;
		PreparedStatement ps = null;
		int result = 0;

		String query="insert into USERS_LOGIN_STATUS values(?,?,?,?,?)";
		System.out.println("query   "+query);

		try {
			con= DbUtil.getConnection();

			ps = con.prepareStatement(query);
			ps.setString(1, user_id);
			ps.setString(2, "F");
			ps.setObject(3, null);
			ps.setObject(4, null);
			ps.setString(5, null);

			result = ps.executeUpdate();

		} catch (Exception e) {
			e.printStackTrace();
		}
		finally
		{
			try {
				con.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return result;
	}




}

