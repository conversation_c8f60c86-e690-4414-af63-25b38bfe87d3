package com.ascent.ds.login;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.UpdateRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.ascent.util.PasswordGeneratorUtil;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ChangeUserPasswordDs extends BasicDataSource {

	private static final long serialVersionUID = 6393151324376312193L;
	private static final String CHANGE_USER_PASSWORD_QUERY = "CHANGE_USER_PASSWORD_QUERY";

	public ChangeUserPasswordDs() {

	}

	public DSResponse executeFetch(final DSRequest request) {

		DSResponse dsResponse = new DSResponse();

		Map<String, Object> criteriaMap = request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");

		Map<String, Object> respMap = changeUserPassword(criteriaMap, user);

		dsResponse.setData(respMap);
		return dsResponse;
	}

	public Map<String, Object> changeUserPassword(Map<String, Object> criteriaMap, User user) {

		Map<String, Object> respMap = new HashMap<String, Object>();

		String userName = (String) criteriaMap.get("userName");
		String oldPassword = (String) criteriaMap.get("oldPassword");
		String newPassword = (String) criteriaMap.get("newPassword");
		String confirmPassword = (String) criteriaMap.get("confirmPassword");

		String msg = "";
		String loggredInUserId = user.getUserId();

		if (userName != null && userName.equals(user.getUserName())) {

			if (oldPassword != null && oldPassword.equals(user.getPassword())) {

				if (newPassword != null && !newPassword.equals(oldPassword)
						&& !newPassword.equals(user.getPassword())) {

					if (newPassword != null && confirmPassword != null && newPassword.equals(confirmPassword)) {

						Map<String, Object> params = new HashMap<String, Object>();
						AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
						try {
							Map<String, Object> args = new HashMap<String, Object>();
							Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
									.getQueryConf(CHANGE_USER_PASSWORD_QUERY);
							String UPDATE_QRY = queryConf.getQueryString();
							String UPDATE_QRY_PARAMS = queryConf.getQueryParam();

							Map<String, Object> PARAM_VALUE_MAP = new HashMap<String, Object>();
							
							// START PASSWORD ENCRIPTION POLICY IS ADDED --28-05-2017
							
							PasswordGeneratorUtil passwordgeneratorutil = new PasswordGeneratorUtil();
							passwordgeneratorutil.generateRandomString();
							//String password = passwordgeneratorutil.getGenratedPwd();
							System.out.println("password :" + newPassword);
							//String hashRandomPassword = passwordgeneratorutil.generateStorngPasswordHash(newPassword);
							PARAM_VALUE_MAP.put("password", newPassword);
							
							// END PASSWORD ENCRIPTION POLICY IS ADDED --28-05-2017	
							//PARAM_VALUE_MAP.put("password", newPassword);
							Calendar calendar = Calendar.getInstance();
							PARAM_VALUE_MAP.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
							CustomerDao  customerDao = new CustomerDao();
							PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
							Integer pwdAge = loadPasswordPolicy.getPwdMaxAge();
							calendar.add(Calendar.MONTH, pwdAge);
							PARAM_VALUE_MAP.put("pwd_exp_date", new Timestamp(calendar.getTimeInMillis()));
							PARAM_VALUE_MAP.put("user_id", loggredInUserId);

							args.put("UPDATE_QRY", UPDATE_QRY);
							args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAMS);
							args.put("PARAM_VALUE_MAP", PARAM_VALUE_MAP);

							UpdateRegulator updateRegulator = new UpdateRegulator();
							int updated = updateRegulator.update(args);

							if (updated >= 1) {

								msg = user.getUserId() + " Your password Has Been Changed Successfully..!!";

								respMap.put("Success", msg);
								return respMap;
							} else {
								msg = user.getUserId() + " Your password not changed ,Please try again ..!!";

								respMap.put("MASSAGE", msg);
								return respMap;
							}

						} catch (Exception e) {

							e.printStackTrace();
						}

					} else {

						msg = "NEW PASSWORD & CONFIRM PASSWORD SHOULD BE SAME";
						respMap.put("MASSAGE", msg);
						return respMap;
					}

				} else {
					msg = "NEW PASSWORD  & OLD PASSWORD Should Not Be Same";
					respMap.put("MASSAGE", msg);
					return respMap;
				}
			} else {

				msg = "OLD PASSWORD Not Matched , Please Enter Again";
				respMap.put("MASSAGE", msg);
				return respMap;
			}

		} else {
			msg = " Something Went Wrong Please Try again..!!";
			respMap.put("MASSAGE", msg);
			return respMap;
		}
		return respMap;

	}

}
