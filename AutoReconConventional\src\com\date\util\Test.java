package com.date.util;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


public class Test {

	public static String getFormatDate(String str,String time) throws ParseException {
		String dateStr = str+" "+time;
		String formattedDate2=null;
		String dateStr2 = str+"  8:00:00:000AM";
		DateFormat readFormat = new SimpleDateFormat( "MM/dd/yy hh:mm:ss:SSSaa");
		DateFormat writeFormat = new SimpleDateFormat( "yyyy-MM-dd HH:mm");
		DateFormat getFormat = new SimpleDateFormat( "MM/dd/yy");
		Date date = null;
		Date date1 = null;
		try {
		    date = readFormat.parse(dateStr);
		    date1 = readFormat.parse(dateStr2);
		} catch (ParseException e) {
		    e.printStackTrace();
		}

		if (date != null) {
		    String formattedDate = writeFormat.format(date);
		    String formattedDate1 = writeFormat.format(date1);
		   // System.out.println(formattedDate+"    "+formattedDate1);
		    Date d2=writeFormat.parse(formattedDate);
		    Date d3=writeFormat.parse(formattedDate1);
		    if(d2.compareTo(d3)>0){
		    	Calendar cal = Calendar.getInstance();
		    	cal.setTime(d2);
		    	cal.add(Calendar.DATE, 1);
		    	d2=cal.getTime();
		    	formattedDate2=getFormat.format(d2);
		    }else{
		    	formattedDate2=getFormat.format(d2);
		    }
		    System.out.println(d2+"    "+d3);
		   // System.out.println(d2.compareTo(d3));
		}
		return formattedDate2;
	}
	
	
	public static String getFormatDateEj(String str,String time) throws ParseException {
		String dateStr = str+" "+time+":00";
		String formattedDate2=null;
		String dateStr2 = str+"  8:00:00";
		DateFormat readFormat = new SimpleDateFormat( "yy/MM/dd HH:mm:ss");
		DateFormat writeFormat = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss");
		DateFormat getFormat = new SimpleDateFormat( "yy/MM/dd");
		Date date = null;
		Date date1 = null;
		try {
		    date = readFormat.parse(dateStr);
		    date1 = readFormat.parse(dateStr2);
		} catch (ParseException e) {
		    e.printStackTrace();
		}

		if (date != null) {
		    String formattedDate = writeFormat.format(date);
		    String formattedDate1 = writeFormat.format(date1);
		    //System.out.println(formattedDate+"    "+formattedDate1);
		    Date d2=writeFormat.parse(formattedDate);
		    Date d3=writeFormat.parse(formattedDate1);
		    if(d2.compareTo(d3)>0){
		    	Calendar cal = Calendar.getInstance();
		    	cal.setTime(d2);
		    	cal.add(Calendar.DATE, 1);
		    	d2=cal.getTime();
		    	formattedDate2=getFormat.format(d2);
		    }else{
		    	formattedDate2=getFormat.format(d2);
		    }
		   // System.out.println(d2+"    "+d3);
		   // System.out.println(d2.compareTo(d3));
		}
		return formattedDate2;
	}

	
	
	
	public static String getFormatDateEj(String str) throws ParseException {
		String dateStr = str+":00";
		String formattedDate2=null;
		String dateStr2 = str+"  8:00:00";
		DateFormat readFormat = new SimpleDateFormat( "yy/MM/dd HH:mm:ss");
		DateFormat writeFormat = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss");
		DateFormat getFormat = new SimpleDateFormat( "yy/MM/dd");
		Date date = null;
		Date date1 = null;
		try {
		    date = readFormat.parse(dateStr);
		    date1 = readFormat.parse(dateStr2);
		} catch (ParseException e) {
		    e.printStackTrace();
		}

		if (date != null) {
		    String formattedDate = writeFormat.format(date);
		    String formattedDate1 = writeFormat.format(date1);
		    //System.out.println(formattedDate+"    "+formattedDate1);
		    Date d2=writeFormat.parse(formattedDate);
		    Date d3=writeFormat.parse(formattedDate1);
		    if(d2.compareTo(d3)>0){
		    	Calendar cal = Calendar.getInstance();
		    	cal.setTime(d2);
		    	cal.add(Calendar.DATE, 1);
		    	d2=cal.getTime();
		    	formattedDate2=getFormat.format(d2);
		    }else{
		    	formattedDate2=getFormat.format(d2);
		    }
		   // System.out.println(d2+"    "+d3);
		   // System.out.println(d2.compareTo(d3));
		}
		return formattedDate2;
	}
	
	public static String getTime(String time,String time3) throws ParseException{
		//String time = "02:30PM";

		DateFormat sdf = new SimpleDateFormat("hh:mm:ss:SSSaa");
		DateFormat write = new SimpleDateFormat("HH:mm");
		Date date = sdf.parse(time);
		Date date2=write.parse(write.format(date));
		String time2=write.format(date2);
		System.out.println("Time: " + write.format(date2));
		if(time2.compareTo(time3)==0){
			System.out.println("0");
		}
		else if(time2.compareTo(time3)<0){
			System.out.println("<1");
			
		}
		else if(time2.compareTo(time3)>0){
			System.out.println(">1");
		}
		return time2;
	}


	
}
