/*package com.ascent.persistance;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class InsertRegulator implements DbRegulatorConstants, Serializable {

	private static final long serialVersionUID = 1882053126044571170L;
	private static Logger logger = LogManager.getLogger(InsertRegulator.class.getName());

	public InsertRegulator() {

	}

	public int insert(Map<String, Object> args, Query insertQueryConf) {
		Connection connection = null;
		PreparedStatement insertPstmt = null;
		int rowsSaved = 0; 
		
		try {
			
			connection = DbUtil.getConnection();
			
			String insertQry = insertQueryConf.getQueryString();

			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			
			if (paramValuemap == null) {
				paramValuemap = args;
			}

			insertPstmt = connection.prepareStatement(insertQry);

			Map<String, Integer> paramTypeMap = null;
			List<String> paramList = null;
			
			paramTypeMap = insertQueryConf.getQueryParamTypeMap();
			paramList = insertQueryConf.getQueryParamList();

			int index = 1;
			
			for (String param : paramList) {
				insertPstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
				index++;
			}
			rowsSaved = insertPstmt.executeUpdate();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(insertPstmt);
			RegulatorUtil.closeConnection(connection);
		}
		return rowsSaved;

	}

	public int insert(Connection connection, Map<String, Object> args) {

		PreparedStatement insertPstmt = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		int rowsSaved = 0;
		try {
			String insertQry = (String) args.get(INSERT_QRY);
			String insertQryParams = (String) args.get(INSERT_QRY_PARAMS);
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			
			insertPstmt = connection.prepareStatement(insertQry);
			if (insertQryParams != null && !insertQryParams.isEmpty()) {

				String[] paramArrayWithType = insertQryParams.split(",");

				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
				
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				
				}
			}

			int index = 1;

			if (paramList != null && !paramList.isEmpty()) {
				for (String param : paramList) {

					insertPstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
					index++;
				}
			}
			
			rowsSaved = insertPstmt.executeUpdate();

		} catch (Exception e) {

			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(insertPstmt);
			// closeConnection(connection);
		}
		return rowsSaved;
	}

	public int insert(PreparedStatement insertPstmt, Map<String, Object> args,String insertQryParams) {
		
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		int rowsSaved = 0;
		try {
			
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);

			if (insertQryParams != null && !insertQryParams.isEmpty()) {
			
				String[] paramArrayWithType = insertQryParams.split(",");
				
				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
			}

			int index = 1;
			
			for (String param : paramList) {
				System.out.println(param +"----"+paramValuemap.get(param));
				insertPstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
				index++;
				System.out.println(param +"----"+paramValuemap.get(param));
			}
			
			rowsSaved = insertPstmt.executeUpdate();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(insertPstmt);
		}

		return rowsSaved;
	}

	public static void main(String[] args) {
		InsertRegulator dbCurdRUC = new InsertRegulator();
		Connection connection = null;

		Map<String, Object> params = new HashMap<String, Object>();

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		try {

			Query query = queries.getQueryConf("name");
			connection = DbUtil.getConnection();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
}
*/
package com.ascent.persistance;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class InsertRegulator implements DbRegulatorConstants, Serializable {

	private static final long serialVersionUID = 1882053126044571170L;
	private static Logger logger = LogManager.getLogger(InsertRegulator.class.getName());

	public InsertRegulator() {

	}

	public int insert(Map<String, Object> args, Query insertQueryConf) {
		Connection connection = null;
		PreparedStatement insertPstmt = null;
		int rowsSaved = 0; 
		
		try {
			
			connection = DbUtil.getConnection();
			
			String insertQry = insertQueryConf.getQueryString();
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			
			if (paramValuemap == null) {
				paramValuemap = args;
			}

			insertPstmt = connection.prepareStatement(insertQry);

			Map<String, Integer> paramTypeMap = null;
			List<String> paramList = null;
			
			paramTypeMap = insertQueryConf.getQueryParamTypeMap();
			paramList = insertQueryConf.getQueryParamList();

			int index = 1;
			
			for (String param : paramList) {
				insertPstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
				index++;
			}
			rowsSaved = insertPstmt.executeUpdate();

		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(insertPstmt);
			RegulatorUtil.closeConnection(connection);
		}
		return rowsSaved;

	}

	public int insert(Connection connection, Map<String, Object> args) {

		PreparedStatement insertPstmt = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		int rowsSaved = 0;
		try {
			String insertQry = (String) args.get(INSERT_QRY);
			String insertQryParams = (String) args.get(INSERT_QRY_PARAMS);
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			
			insertPstmt = connection.prepareStatement(insertQry);
			if (insertQryParams != null && !insertQryParams.isEmpty()) {

				String[] paramArrayWithType = insertQryParams.split(",");

				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
				
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				
				}
			}

			int index = 1;

			if (paramList != null && !paramList.isEmpty()) {
				for (String param : paramList) {
					//System.out.println(index+","+ paramValuemap.get(param)+","+ paramTypeMap.get(param));
					insertPstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
					index++;
				}
			}
			
			rowsSaved = insertPstmt.executeUpdate();

		} catch (Exception e) {

			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(insertPstmt);
			// closeConnection(connection);
		}
		return rowsSaved;
	}

	public int insert(PreparedStatement insertPstmt, Map<String, Object> args,String insertQryParams) {
		
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		
		int rowsSaved = 0;
		try {
			
			Map<String, Object> paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			if((paramValuemap.get("DR_CR_IND"))!=null){
				
				paramValuemap.put("DEB_CRE_IND", paramValuemap.get("DR_CR_IND"));
			}
			if((paramValuemap.get("STAN"))!=null){
				
				paramValuemap.put("SYS_TRACE_AUDIT_NO", paramValuemap.get("STAN"));
			}

			if (insertQryParams != null && !insertQryParams.isEmpty()) {
			
				String[] paramArrayWithType = insertQryParams.split(",");
				
				if (paramArrayWithType != null && paramArrayWithType.length != 0) {
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}
			}

			int index = 1;
			
			for (String param : paramList) {
				
			
				

				if((paramValuemap.get(param)!=null) && (paramTypeMap.get(param)==91)){
					

					
					paramValuemap.put(param,new java.sql.Date(((java.util.Date)paramValuemap.get(param)).getTime()));
			}else if((paramValuemap.get(param)!=null) && (paramTypeMap.get(param)==93)){
				paramValuemap.put(param,new java.sql.Date(((java.util.Date)paramValuemap.get(param)).getTime()));
			}
				
				//System.out.println(index+","+paramValuemap.get(param.toUpperCase())+","+paramTypeMap.get(param));
				insertPstmt.setObject(index, paramValuemap.get(param.toUpperCase()), paramTypeMap.get(param));
				index++;
				
				
				
			}
			
			rowsSaved = insertPstmt.executeUpdate();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			RegulatorUtil.closePreparedStatement(insertPstmt);
		}
System.out.println("rowsSaved   "+rowsSaved);
		return rowsSaved;
	}

	public static void main(String[] args) {
		InsertRegulator dbCurdRUC = new InsertRegulator();
		Connection connection = null;

		Map<String, Object> params = new HashMap<String, Object>();

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		try {

			Query query = queries.getQueryConf("name");
			connection = DbUtil.getConnection();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
}
