<?xml version="1.0" encoding="UTF-8"?>

<configuration status="error">
    <!--The first definition of all appender-->
    <appenders>
        <!--The output console configuration-->
        <Console name="Console" target="SYSTEM_OUT">
            <!--The console output only level and above the level of the information (onMatch), directly to the other(onMismatch)-->
            <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
            <!--This all know is the output log format-->
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>
        </Console>
        <!--Document will print out all the information, the log every time you run the program will automatically clear, determined by the append property, this is also very useful, suitable for temporary test-->
        <File name="log" fileName="./../log/WatchDog.log" append="false">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>
        </File>

        <!--This will print all the information, each size is more than size, then the size size of the log will automatically in the following year month was built according to the folder and file compression, as-->
        <RollingFile name="RollingFile" fileName="./../WatchDogLogs/WatchDog.log" 
                     filePattern="WatchDog-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %class{36} %L %M - %msg%xEx%n"/>
            <SizeBasedTriggeringPolicy size="5MB"/>
        </RollingFile>
    </appenders>
    <!--Then the definition of logger, only the definition of logger and the introduction of the appender, the appender will take effect-->
    <loggers>
        <!--Create a default root logger-->
        <root level="trace">
            <appender-ref ref="RollingFile"/>
            <appender-ref ref="Console"/>
           
        </root>

    </loggers>
</configuration>