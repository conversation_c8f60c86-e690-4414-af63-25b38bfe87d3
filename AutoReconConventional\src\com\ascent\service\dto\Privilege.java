package com.ascent.service.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Privilege implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1430799999302869257L;
	private Integer id;
	private String privilegeName; 
	private Integer privilegeId;
	private String role;
	private String status;
	private Integer	version;
	private List<PrivilegeDetails> privilegeDetails;
	private Map<String,PrivilegeDetails> privilegeDetailsMap=new HashMap<String, PrivilegeDetails>();
	
	
	
	public void init(){
		if(privilegeDetails!=null){
			for(PrivilegeDetails details:this.privilegeDetails){
				//"Upstream","Recon","Reports","Activity Manager","Master Data","User Administration"
				if(details.getModule()!=null&&details.getModule().equalsIgnoreCase("Upstream")){
					privilegeDetailsMap.put(details.getModule()+":"+details.getSource(), details);
				}else if(details.getModule()!=null&&details.getModule().equalsIgnoreCase("Reports")){
					privilegeDetailsMap.put(details.getModule(), details);
				}else if(details.getModule()!=null&&details.getModule().equalsIgnoreCase("Activity Manager")){
					privilegeDetailsMap.put(details.getModule(), details);
				}
               else if(details.getModule()!=null&&details.getModule().equalsIgnoreCase("User Administration")){
            	   privilegeDetailsMap.put(details.getModule(), details);
				}else{
					privilegeDetailsMap.put(details.getGeography()+":"+details.getModule()+":"+details.getBuisinessArea()+":"+details.getRecon()+":"+details.getOperation(), details);
				}
				}
		}
	}
	
	public boolean accessibility(String moduleName,String operation){
		PrivilegeDetails privilegeDetails=(PrivilegeDetails)privilegeDetailsMap.get(moduleName+":"+operation);
		if(privilegeDetails!=null){
			
			String access=privilegeDetails.getAccesibility();
			if(access!=null && "Y".equalsIgnoreCase(access)){
				return true;
			}else{
				return false;
			}
			
		}else{
			return false;
		}
		
	}

	
	
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPrivilegeName() {
		return privilegeName;
	}

	public void setPrivilegeName(String privilegeName) {
		this.privilegeName = privilegeName;
	}

	 

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public List<PrivilegeDetails> getPrivilegeDetails() {
		return privilegeDetails;
	}

	public void setPrivilegeDetails(List<PrivilegeDetails> privilegeDetails) {
		this.privilegeDetails = privilegeDetails;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime
				* result
				+ ((privilegeDetails == null) ? 0 : privilegeDetails.hashCode());
		result = prime * result
				+ ((getPrivilegeId() == null) ? 0 : getPrivilegeId().hashCode());
		result = prime * result
				+ ((privilegeName == null) ? 0 : privilegeName.hashCode());
		result = prime * result + ((role == null) ? 0 : role.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Privilege other = (Privilege) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (privilegeDetails == null) {
			if (other.privilegeDetails != null)
				return false;
		} else if (!privilegeDetails.equals(other.privilegeDetails))
			return false;
		if (getPrivilegeId() == null) {
			if (other.getPrivilegeId() != null)
				return false;
		} else if (!getPrivilegeId().equals(other.getPrivilegeId()))
			return false;
		if (privilegeName == null) {
			if (other.privilegeName != null)
				return false;
		} else if (!privilegeName.equals(other.privilegeName))
			return false;
		if (role == null) {
			if (other.role != null)
				return false;
		} else if (!role.equals(other.role))
			return false;
		return true;
	}

	public Map<String, PrivilegeDetails> getPrivilegeDetailsMap() {
		return privilegeDetailsMap;
	}

	public void setPrivilegeDetailsMap(
			Map<String, PrivilegeDetails> privilegeDetailsMap) {
		this.privilegeDetailsMap = privilegeDetailsMap;
	}

	public Integer getPrivilegeId() {
		return privilegeId;
	}

	public void setPrivilegeId(Integer long1) {
		this.privilegeId = long1;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}
	
	
	
	 
}
