package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class FinanceMPclearUPDATE {
	
	private static Logger logger = LogManager.getLogger(FinanceMPclearUPDATE.class.getName());
	
	private static final String MPCLEAR_INTERNAL_RECONCILED_UPDATE_RECON = "MPCLEAR_INTERNAL_RECONCILED_UPDATE_RECON";
	private static final String MPCLEAR_EXTERNAL_RECONCILED_UPDTE_RECON = "MPCLEAR_EXTERNAL_RECONCILED_UPDATE_RECON";
	private static final String MPCLEAR_INTERNAL_UNRECONCILED_UPDATE_RECON = "MPCLEAR_INTERNAL_UNRECONCILED_UPDATE_RECON";
	private static final String MPCLEAR_EXTERNAL_UNRECONCILED_UPDATE_RECON = "MPCLEAR_EXTERNAL_UNRECONCILED_UPDATE_RECON";
	private static final String MPCLEAR_INTERNAL_SUPPRESS_UPDATE_RECON = "MPCLEAR_INTERNAL_SUPPRESS_UPDATE_RECON";
	private static final String MPCLEAR_EXTERNAL_SUPPRESS_UPDATE_RECON = "MPCLEAR_EXTERNAL_SUPPRESS_UPDATE_RECON";
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {
		
		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		//String driver = bundle.getString("driver");
		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> MpClearInternalReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_INTERNAL_RECONCILED_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("PAYMENT REFERENCE", rset.getString(2));
				map.put("CHN REQ DATE", rset.getString(3));
				map.put("BD STATUS", rset.getString(4));
				map.put("CUST GSM NUM", rset.getString(5));
				map.put("SENDER BANK", rset.getString(6));
				map.put("DEBIT ACCT NUMBER", rset.getString(7));
				map.put("DEBIT ACCT NAME", rset.getString(8));
				map.put("STAFF FLAG", rset.getString(9));
				map.put("CREDIT ACCT NUMBER", rset.getString(10));
				map.put("CREDIT ACCT NAME", rset.getString(11));
				map.put("PYMNT TYPE", rset.getString(12));
				map.put("REVERSED FLAG", rset.getString(13));
				map.put("TRAN AMT", rset.getString(14));
				map.put("TRAN ID", rset.getString(15));
				map.put("TRAN DATE", rset.getString(16));
				map.put("VALUE DATE", rset.getString(17));
				map.put("TRAN POST FLG", rset.getString(18));
				map.put("WALLET TRANSFER WITHIN BD", rset.getString(19));
				map.put("CUST TYPE CHRG", rset.getString(20));
				map.put("FINACLE DATE TIME", rset.getString(21));
				map.put("START DATE TIME", rset.getString(22));
				map.put("END DATE TIME", rset.getString(23));
				map.put("COMMENTS", rset.getString(24));
				map.put("VERSION", rset.getString(25));
				map.put("ACTIVE INDEX", rset.getString(26));
				map.put("WORKFLOW STATUS", rset.getString(27));
				map.put("UPDATED ON", rset.getString(28));
				map.put("CREATED ON", rset.getString(29));
				map.put("RECON STATUS", rset.getString(30));
				map.put("RECON ID", rset.getString(31));
				map.put("ACTIVITY COMMENTS", rset.getString(32));
				map.put("MAIN REV IND", rset.getString(33));
				map.put("OPERATION", rset.getString(34));
				map.put("FILE NAME", rset.getString(35));
				map.put("BUSINESS AREA", rset.getString(36));



				list.add(map);
				//logger.debug("MpClearInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> MpClearExternalReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf( MPCLEAR_EXTERNAL_RECONCILED_UPDTE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("SETTLEMENT DATE", rset.getString(2));
				map.put("SESSION SEQ", rset.getString(3));
				map.put("CURRENCY", rset.getString(4));
				map.put("PARTICIPANT", rset.getString(5));
				map.put("SETTLEMENTRETRY", rset.getString(6));
				map.put("ID", rset.getString(7));
				map.put("TYPE", rset.getString(8));
				map.put("AMOUNT", rset.getString(9));
				map.put("STATE", rset.getString(10));
				map.put("REASON", rset.getString(11));
				map.put("ADDITIONAL INFO", rset.getString(12));
				map.put("COMMENTS", rset.getString(13));
				map.put("VERSION", rset.getString(14));
				map.put("ACTIVE INDEX", rset.getString(15));
				map.put("WORKFLOW STATUS", rset.getString(16));
				map.put("UPDATED ON", rset.getString(17));
				map.put("CREATED ON", rset.getString(18));
				map.put("RECON STATUS", rset.getString(19));
				map.put("RECON ID", rset.getString(20));
				map.put("ACTIVITY COMMENTS", rset.getString(21));
				map.put("MAIN REV IND", rset.getString(22));
				map.put("OPERATION", rset.getString(23));
				map.put("FILE NAME", rset.getString(24));
				map.put("BUSINESS AREA", rset.getString(25));
			
				
				list.add(map);
				//logger.debug("MpClearExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> MpClearInternalUnReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_INTERNAL_UNRECONCILED_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("PAYMENT REFERENCE", rset.getString(2));
				map.put("CHN REQ DATE", rset.getString(3));
				map.put("BD STATUS", rset.getString(4));
				map.put("CUST GSM NUM", rset.getString(5));
				map.put("SENDER BANK", rset.getString(6));
				map.put("DEBIT ACCT NUMBER", rset.getString(7));
				map.put("DEBIT ACCT NAME", rset.getString(8));
				map.put("STAFF FLAG", rset.getString(9));
				map.put("CREDIT ACCT NUMBER", rset.getString(10));
				map.put("CREDIT ACCT NAME", rset.getString(11));
				map.put("PYMNT TYPE", rset.getString(12));
				map.put("REVERSED FLAG", rset.getString(13));
				map.put("TRAN AMT", rset.getString(14));
				map.put("TRAN ID", rset.getString(15));
				map.put("TRAN DATE", rset.getString(16));
				map.put("VALUE DATE", rset.getString(17));
				map.put("TRAN POST FLG", rset.getString(18));
				map.put("WALLET TRANSFER WITHIN BD", rset.getString(19));
				map.put("CUST TYPE CHRG", rset.getString(20));
				map.put("FINACLE DATE TIME", rset.getString(21));
				map.put("START DATE TIME", rset.getString(22));
				map.put("END DATE TIME", rset.getString(23));
				map.put("COMMENTS", rset.getString(24));
				map.put("VERSION", rset.getString(25));
				map.put("ACTIVE INDEX", rset.getString(26));
				map.put("WORKFLOW STATUS", rset.getString(27));
				map.put("UPDATED ON", rset.getString(28));
				map.put("CREATED ON", rset.getString(29));
				map.put("RECON STATUS", rset.getString(30));
				map.put("RECON ID", rset.getString(31));
				map.put("ACTIVITY COMMENTS", rset.getString(32));
				map.put("MAIN REV IND", rset.getString(33));
				map.put("OPERATION", rset.getString(34));
				map.put("FILE NAME", rset.getString(35));
				map.put("BUSINESS AREA", rset.getString(36));
				map.put("AGE", rset.getString(37));


				list.add(map);
				//logger.debug("MpClearInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	
	public List<Map<String, Object>> MpClearExternalUnReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_EXTERNAL_UNRECONCILED_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("SETTLEMENT DATE", rset.getString(2));
				map.put("SESSION SEQ", rset.getString(3));
				map.put("CURRENCY", rset.getString(4));
				map.put("PARTICIPANT", rset.getString(5));
				map.put("SETTLEMENTRETRY", rset.getString(6));
				map.put("ID", rset.getString(7));
				map.put("TYPE", rset.getString(8));
				map.put("AMOUNT", rset.getString(9));
				map.put("STATE", rset.getString(10));
				map.put("REASON", rset.getString(11));
				map.put("ADDITIONAL INFO", rset.getString(12));
				map.put("COMMENTS", rset.getString(13));
				map.put("VERSION", rset.getString(14));
				map.put("ACTIVE INDEX", rset.getString(15));
				map.put("WORKFLOW STATUS", rset.getString(16));
				map.put("UPDATED ON", rset.getString(17));
				map.put("CREATED ON", rset.getString(18));
				if(rset.getString(19)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(19));
				map.put("RECON ID", rset.getString(20));
				map.put("ACTIVITY COMMENTS", rset.getString(21));
				map.put("MAIN REV IND", rset.getString(22));
				map.put("OPERATION", rset.getString(23));
				map.put("FILE NAME", rset.getString(24));
				map.put("BUSINESS AREA", rset.getString(25));
				map.put("AGE", rset.getString(26));
			

				list.add(map);
				//logger.debug("MpClearInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> MpClearInternalSuppressUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_INTERNAL_SUPPRESS_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("PAYMENT REFERENCE", rset.getString(2));
				map.put("CHN REQ DATE", rset.getString(3));
				map.put("BD STATUS", rset.getString(4));
				map.put("CUST GSM NUM", rset.getString(5));
				map.put("SENDER BANK", rset.getString(6));
				map.put("DEBIT ACCT NUMBER", rset.getString(7));
				map.put("DEBIT ACCT NAME", rset.getString(8));
				map.put("STAFF FLAG", rset.getString(9));
				map.put("CREDIT ACCT NUMBER", rset.getString(10));
				map.put("CREDIT ACCT NAME", rset.getString(11));
				map.put("PYMNT TYPE", rset.getString(12));
				map.put("REVERSED FLAG", rset.getString(13));
				map.put("TRAN AMT", rset.getString(14));
				map.put("TRAN ID", rset.getString(15));
				map.put("TRAN DATE", rset.getString(16));
				map.put("VALUE DATE", rset.getString(17));
				map.put("TRAN POST FLG", rset.getString(18));
				map.put("WALLET TRANSFER WITHIN BD", rset.getString(19));
				map.put("CUST TYPE CHRG", rset.getString(20));
				map.put("FINACLE DATE TIME", rset.getString(21));
				map.put("START DATE TIME", rset.getString(22));
				map.put("END DATE TIME", rset.getString(23));
				map.put("COMMENTS", rset.getString(24));
				map.put("VERSION", rset.getString(25));
				map.put("ACTIVE INDEX", rset.getString(26));
				map.put("WORKFLOW STATUS", rset.getString(27));
				map.put("UPDATED ON", rset.getString(28));
				map.put("CREATED ON", rset.getString(29));
				map.put("RECON STATUS", rset.getString(30));
				map.put("RECON ID", rset.getString(31));
				map.put("ACTIVITY COMMENTS", rset.getString(32));
				map.put("MAIN REV IND", rset.getString(33));
				map.put("OPERATION", rset.getString(34));
				map.put("FILE NAME", rset.getString(35));
				map.put("BUSINESS AREA", rset.getString(36));


				map.put("VERIFIER USER ID", rset.getString(37));
				map.put("VERIFIER COMMENTS", rset.getString(38));
				map.put("MAKER USER ID", rset.getString(39));
				map.put("MAKER COMMENTS", rset.getString(40));
				list.add(map);
				//logger.debug("MpClearInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> MpClearExternalSuppressUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_EXTERNAL_SUPPRESS_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("SETTLEMENT DATE", rset.getString(2));
				map.put("SESSION SEQ", rset.getString(3));
				map.put("CURRENCY", rset.getString(4));
				map.put("PARTICIPANT", rset.getString(5));
				map.put("SETTLEMENTRETRY", rset.getString(6));
				map.put("ID", rset.getString(7));
				map.put("TYPE", rset.getString(8));
				map.put("AMOUNT", rset.getString(9));
				map.put("STATE", rset.getString(10));
				map.put("REASON", rset.getString(11));
				map.put("ADDITIONAL INFO", rset.getString(12));
				map.put("COMMENTS", rset.getString(13));
				map.put("VERSION", rset.getString(14));
				map.put("ACTIVE INDEX", rset.getString(15));
				map.put("WORKFLOW STATUS", rset.getString(16));
				map.put("UPDATED ON", rset.getString(17));
				map.put("CREATED ON", rset.getString(18));
				map.put("RECON STATUS", rset.getString(19));
				map.put("RECON ID", rset.getString(20));
				map.put("ACTIVITY COMMENTS", rset.getString(21));
				map.put("MAIN REV IND", rset.getString(22));
				map.put("OPERATION", rset.getString(23));
				map.put("FILE NAME", rset.getString(24));
				map.put("BUSINESS AREA", rset.getString(25));
			
				map.put("VERIFIER USER ID", rset.getString(26));
				map.put("VERIFIER COMMENTS", rset.getString(27));
				map.put("MAKER USER ID", rset.getString(28));
				map.put("MAKER COMMENTS", rset.getString(29));
				
				list.add(map);
				//logger.debug("MpClearExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	

	public static void main(String[] args) {
		FinanceMPclearUPDATE c = new FinanceMPclearUPDATE();
		c.MpClearInternalReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.MpClearInternalUnReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.MpClearExternalReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.MpClearExternalUnReconsiledUpdateMethod("2018-01-01", "2018-10-01");
	c.MpClearInternalSuppressUpdateMethod("2018-01-01", "2018-10-01");
	c.MpClearExternalSuppressUpdateMethod("2018-01-01", "2018-10-01");
		
		
	}

}
