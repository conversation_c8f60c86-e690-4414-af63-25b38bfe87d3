package com.ascent.ds.operations;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.integration.util.DbUtil;
import com.ascent.util.AscentAutoReconConstants;
import com.ibm.icu.text.DateFormat;
import com.ibm.icu.text.SimpleDateFormat;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GenerateGl{

	//private static Logger logger = LogManager.getLogger(GenerateGl.class);
	
	private static final long serialVersionUID = 1L;
	private static final String HEADER_STARTER_VALUES = "9999";
	private static final String HEADER_END_VALUES = "000000000000000000000000";
	private static final String SELECT_QUERY = " SELECT * FROM GENERATE_GL_ENTRY WHERE Business_Area IN (?,?) AND GL_FLAG='N'";
	private static final String ISS_ACC_QUERY = " SELECT * FROM GENERATE_GL_ENTRY WHERE Business_Area IN (?, ?) AND GL_FLAG='N'";
	Connection connection = null;
	Statement stmt = null;
	PreparedStatement pstmt = null;
	PreparedStatement ps = null;
	String folderPath = "";
	File folder = null;

	public GenerateGl() {

		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		System.out.println("Properties Loaded");
		folderPath = (String) appProps.get(AscentAutoReconConstants.GENRATE_GL_ENTRY);
		folder = new File(folderPath);
		
		//logger.trace("GENRATE GL");
	}

	public void bulkFileGeneration() {
		//DSResponse response = new DSResponse();
		try {
			// String b_area="ON US";
		 
			//// get the today date
			DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd/hh_mm_ss");
			StringBuilder builder = new StringBuilder();
			Date date = new Date();
			String todaysDate = dateFormat.format(date);
			String dateArr[] = todaysDate.split("/");
			String genratedDateYear = dateArr[0];
			String genratedDateMonth = dateArr[1];
			String genratedDate = dateArr[2];
		 
			builder.append(HEADER_STARTER_VALUES); // appending 9999 to header
			builder.append(genratedDate); // appending dd
			builder.append(genratedDateMonth); // appending mm
			builder.append(genratedDateYear); // appending yyyy
			builder.append(HEADER_END_VALUES); // appending 24 zeros
			//////
			String FILE_PATH = folderPath;
			genrateGlFile(FILE_PATH, builder, genratedDateYear, genratedDateMonth, genratedDate);

		} catch (Exception e) {
			e.printStackTrace();
		}

		//return response;
	}

	public boolean genrateGlFile(String FILE_PATH, StringBuilder builder, String yyyy, String mm, String dd) throws Exception {
		try {

			String fileName_ONUS = FILE_PATH + "\\" + "ATM_MODULE_bulk_" + yyyy + mm + dd + ".dat";
			String fileName_ISSUER_ACCQUIRER = FILE_PATH + "\\" + "ISS_ACQ_bulk_" + yyyy + mm + dd + ".dat";
			System.out.println(builder.toString());
			folder = new File(folderPath);
			File onusFile = new File(fileName_ONUS);
			File issAccFile = new File(fileName_ISSUER_ACCQUIRER);
			if (!folder.exists()) {

				folder.mkdir();
			}

			if (!onusFile.exists()) {
				onusFile.createNewFile();
				if (!issAccFile.exists()) {

					issAccFile.createNewFile();
				} else {
					System.out.println("issuer aquirer file is exits");
				}
			} else {
				System.out.println("onus file is exits");
			}

			/// header for onus file
			FileWriter fwOfOnusFile = new FileWriter(onusFile.getAbsoluteFile());
			BufferedWriter bwOfOnusFile = new BufferedWriter(fwOfOnusFile);
			bwOfOnusFile.write(builder.toString());
			bwOfOnusFile.newLine();
			// header for iss_acc file
			FileWriter fwOfIssAccFile = new FileWriter(issAccFile.getAbsoluteFile());
			BufferedWriter bwOfIssAccFile = new BufferedWriter(fwOfIssAccFile);
			bwOfIssAccFile.write(builder.toString());
			bwOfIssAccFile.newLine();
			// bwOfIssAccFile.close();
			connection = DbUtil.getConnection();
			pstmt = connection.prepareStatement(SELECT_QUERY);
			pstmt.setString(1, "ON US");
			pstmt.setString(2, "ATM MODULE");
			ResultSet rs = pstmt.executeQuery();
			ResultSetMetaData resultSetMetaData = rs.getMetaData();
			int cols = resultSetMetaData.getColumnCount();
			List<String> columnNames = new ArrayList<String>();
			for (int i = 1; i <= cols; i++) {
				String colName = resultSetMetaData.getColumnName(i);
				columnNames.add(colName);
			}
			ps = connection.prepareStatement(ISS_ACC_QUERY);
			ps.setString(1, "ISSUER");
			ps.setString(2, "ACQUIRER");
			ResultSet resultSet = ps.executeQuery();
			while (rs.next()) {
				StringBuilder builder2 = new StringBuilder();
				long reconId=rs.getLong("RECON_ID");
				for (int i = 0; i < columnNames.size(); i++) {
					if (columnNames.get(i).equalsIgnoreCase("Branch_Code")) {
						String newBCode = "" + rs.getObject(i + 1);
						int len = newBCode.length();
						if(len!=4)
						{
							for (int b = 0; b < 4 - len; b++) {
								newBCode = "0" + newBCode;
							}
						}
						
						builder2.append(newBCode);
					} else if (columnNames.get(i).equalsIgnoreCase("Customer")) {
						String customerStr = "" + rs.getObject(i + 1);
						int len = customerStr.length();
						if(len!=7)
						{
							for (int cus = 0; cus < 7 - len; cus++) {
								customerStr = "0" + customerStr;
							}
						}
						
						builder2.append(customerStr);
					} else if (columnNames.get(i).equalsIgnoreCase("Check_Digit")) {
						builder2.append(rs.getObject(i + 1));
					} else if (columnNames.get(i).equalsIgnoreCase("Ledger_Code")) {
						String led_code=""+rs.getObject(i + 1);
						int len=led_code.length();
						if(len!=4)
						{
							for(int l=0;l<4-len;l++)
							{
								led_code="0"+led_code;
							}
							
						}
						builder2.append(led_code);
					} else if (columnNames.get(i).equalsIgnoreCase("Sub_Acc_Code")) {
						String s_a_code = "" + rs.getObject(i + 1);
						int len = s_a_code.length();
						if(len!=3)
						{
							for (int s = 0; s < 3 - len; s++) {
								s_a_code = "0" + s_a_code;
							}
						}
						
						builder2.append(s_a_code);
					} else if (columnNames.get(i).equalsIgnoreCase("Deb_Cre_Ind")) {
						builder2.append(rs.getObject(i + 1));
					} else if (columnNames.get(i).equalsIgnoreCase("Transaction_Amount")) {

						String t_amt = "" + rs.getObject(i + 1);
						int len = t_amt.length();
						if(len!=15)
						{
							for (int t = 0; t < 15 - len; t++) {
								t_amt = "0" + t_amt;
							}
						}
					
						builder2.append(t_amt);
					} else if (columnNames.get(i).equalsIgnoreCase("Document_Number")) {
						String d_no = "0" + rs.getObject(i + 1);
						int len = d_no.length();
						if(len!=11)
						{
							for (int d = 0; d < 11 - len; d++) {
								d_no = "0" + d_no;
							}
						}
						
						builder2.append(d_no);
					} else if (columnNames.get(i).equalsIgnoreCase("Document_Alpha")) {
						String da = (String) rs.getObject(i + 1);
						int len = da.length();
						if(len!=4)
						{
							for (int d = 0; d < 4 - len; d++) {
								da = "0" + da;
							}
						}
						
						builder2.append(da);
					} else if (columnNames.get(i).equalsIgnoreCase("Remarks")) {
						builder2.append(rs.getObject(i + 1));
					}

				}
				bwOfOnusFile.write(builder2.toString());
				bwOfOnusFile.newLine();
				
				String updateGL_FlagQuery="update GENERATE_GL_ENTRY set GL_FLAG='Y' where RECON_ID= "+reconId;
				PreparedStatement preparedStatement= connection.prepareStatement(updateGL_FlagQuery);
				int update_status = preparedStatement.executeUpdate();
				
				System.out.println(update_status);
			}
			bwOfOnusFile.close();
			while (resultSet.next()) {
				StringBuilder builder2 = new StringBuilder();
				long reconId=resultSet.getLong("RECON_ID");
				for (int i = 0; i < columnNames.size(); i++) {

					if (columnNames.get(i).equalsIgnoreCase("Branch_Code")) {
						String newBCode = "" + resultSet.getObject(i + 1);
						int len = newBCode.length();
						for (int b = 0; b < 4 - len; b++) {
							newBCode = "0" + newBCode;
						}
						builder2.append(newBCode);
					} else if (columnNames.get(i).equalsIgnoreCase("Customer")) {
						String customerStr = "" + resultSet.getObject(i + 1);
						int len = customerStr.length();
						for (int cus = 0; cus < 7 - len; cus++) {
							customerStr = "0" + customerStr;
						}
						builder2.append(customerStr);
					} else if (columnNames.get(i).equalsIgnoreCase("Check_Digit")) {
						builder2.append(resultSet.getObject(i + 1));
					} else if (columnNames.get(i).equalsIgnoreCase("Ledger_Code")) {
						String ledCode=""+resultSet.getObject(i + 1);
						int len=ledCode.length();
						for(int l = 0; l<4-len; l++)
						{
							ledCode = "0" + ledCode;
						}
						builder2.append(ledCode);
					} else if (columnNames.get(i).equalsIgnoreCase("Sub_Acc_Code")) {
						String s_a_code = "" + resultSet.getObject(i + 1);
						int len = s_a_code.length();
						for (int s = 0; s < 3 - len; s++) {
							s_a_code = "0" + s_a_code;
						}
						builder2.append(s_a_code);
					} else if (columnNames.get(i).equalsIgnoreCase("Deb_Cre_Ind")) {
						builder2.append(resultSet.getObject(i + 1));
					} else if (columnNames.get(i).equalsIgnoreCase("Transaction_Amount")) {

						String t_amt = "" + resultSet.getObject(i + 1);
						int len = t_amt.length();
						for (int t = 0; t < 15 - len; t++) {
							t_amt = "0" + t_amt;
						}
						builder2.append(t_amt);
					} else if (columnNames.get(i).equalsIgnoreCase("Document_Number")) {
						String d_no = "0" + resultSet.getObject(i + 1);
						int len = d_no.length();
						for (int d = 0; d < 11 - len; d++) {
							d_no = "0" + d_no;
						}
						builder2.append(d_no);
					} else if (columnNames.get(i).equalsIgnoreCase("Document_Alpha")) {
						String da = (String) resultSet.getObject(i + 1);
						int len = da.length();
						for (int d = 0; d < 4 - len; d++) {
							da = "0" + da;
						}
						builder2.append(da);
					} else if (columnNames.get(i).equalsIgnoreCase("Remarks")) {
						builder2.append(resultSet.getObject(i + 1));
					}

				}
				bwOfIssAccFile.write(builder2.toString());
				bwOfIssAccFile.newLine();
				
				String updateGL_FlagQuery="update GENERATE_GL_ENTRY set GL_FLAG='Y' where RECON_ID= "+reconId;
				PreparedStatement preparedStatement= connection.prepareStatement(updateGL_FlagQuery);
				int update_status = preparedStatement.executeUpdate();
				
				System.out.println(update_status);
			}
			if(!resultSet.isClosed()){
				DbUtil.closeResultSet(resultSet);
			}
			bwOfIssAccFile.close();
		} catch (Exception e) {
			  throw new Exception(e);
		} finally {
			System.out.println("finally block");
			 DbUtil.closeConnection(connection);
			
		}

		return true;
 }

}
