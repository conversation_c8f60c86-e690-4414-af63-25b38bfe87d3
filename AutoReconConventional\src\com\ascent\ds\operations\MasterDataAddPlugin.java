package  com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;


public class MasterDataAddPlugin  extends BasicDataSource implements PagesConstants {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 1955506874650248027L;
	private static Logger logger = LogManager.getLogger(MasterDataAddPlugin.class.getName());
	public static final String GET_ALL_SIDs_FROM_TABLE="select MAX(ID) from ";
	public static final String PARAM_VALUE_MAP = "PARAM_VALUE_MAP";
	public static final String INSERT_QRY_PARAMS = "INSERT_QRY_PARAMS";
	public static final String INSERT_QRY = "INSERT_QRY";
	static Connection connection=null;
	public MasterDataAddPlugin(){
		
		logger.trace(" MasterDataAddPlugin********* ");
	}
	 Map<String, Object> result = null;
 
	public DSResponse executeFetch (DSRequest request){
		List<Map<String, Object>> selectedRecords=null;
		 DSResponse dsResponse= new DSResponse();
		
		 
	Map criteriaMap= request.getValues();
	//TODO: is user authorized utility to verify user priviliges
	
	HttpSession httpSession = request.getHttpServletRequest().getSession(); 
	String reconName=(String)httpSession.getAttribute("user_selected_recon");
	String businesArea=(String)httpSession.getAttribute("user_selected_business_area");
	User user = (User) httpSession.getAttribute("userId");
	String userId = user.getUserId();
	
	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		dsResponse.setData(result);
		return dsResponse;
	}
	System.out.println(" criteria Map :----"+criteriaMap); 
	System.out.println(" table Name :- " +criteriaMap.get("tableName"));
	
		 Map<String,Object> records =(Map<String,Object>) criteriaMap.get("record");
		 if(criteriaMap.get("tableName").equals("TBL_MASTER_DATA"))
		 {
		 		try{
		 Date date=(Date)((Map)criteriaMap.get("record")).get("AD_FROM_DATE");
			Date date1=(Date)((Map)criteriaMap.get("record")).get("AD_TO_DATE");
			 java.sql.Date d1=new java.sql.Date(date.getTime());
			 java.sql.Date d2=new java.sql.Date(date1.getTime());
			records.put("AD_FROM_DATE", d1);
			records.put("AD_TO_DATE", d2);
		 		}
		 		catch(Exception e)
		 		{
		 			e.printStackTrace();
		 		}
		 }
		 // String comments=(String)records.get("ACTIVITY_COMMENTS");
		// String comments=(String) records.get("comments");
		String action="Master Data Add Operation";
		String tableName=(String) criteriaMap.get("tableName");
		String dsName=(String) criteriaMap.get("dsName");
		records=getData(records, tableName);
		
		selectedRecords =new ArrayList<Map<String, Object>>();
		selectedRecords.add(records);
		
		 
		Map<String,Object> masterDataAddArgs=new HashMap<String, Object>();
			masterDataAddArgs.put(SELECTED_RECORDS, selectedRecords);
			masterDataAddArgs.put(TABLE_NAME, tableName);
			masterDataAddArgs.put(DS_NAME, dsName);
			masterDataAddArgs.put(ACTION,action);
			masterDataAddArgs.put(USER_ID, userId);
			masterDataAddArgs.put(BUSINES_AREA, businesArea);
			masterDataAddArgs.put(RECON_NAME, reconName);
			//masterDataAddArgs.put(COMMENTS, comments);
		
		
		process(masterDataAddArgs);
		dsResponse.setData(result); 
		return dsResponse;
		
	}
	
public static Map<String,Object> getData( Map<String,Object> records,String tableName){
	long version=1;
		connection=DbUtil.getConnection();
		Statement statement = null;
		ResultSet resultSet=null;
		try {
			statement = connection.createStatement();
			resultSet=statement.executeQuery(GET_ALL_SIDs_FROM_TABLE+tableName);
			long newSID=0;
			
			 if(resultSet.next()){
				 
				 newSID=resultSet.getLong(1);
				 newSID++;
			 }
			
			 records.put("ID", newSID);
			// records.put("VERSION",version);
			 //records.put("ACTIVE_INDEX","Y");
			 records.put("STATUS", "NEW");
			 records.put("WORKFLOW_STATUS", "N");
			 records.put("UPDATED_ON",new Timestamp(Calendar.getInstance().getTimeInMillis()));
			 
		} catch (SQLException e) {
			logger.error(e);
			System.err.println(e);
		}finally{
			DbUtil.closeResultSet(resultSet);
			try {
				statement.close();
			} catch (SQLException e) {
			
				e.printStackTrace();
			}
			DbUtil.closeConnection(connection);
		}
		 return records;	
}	
private Map<String, Object>  process(Map<String,Object> masterDataAddArgs){
	try {
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();
System.out.println(masterDataAddArgs);
		String userId = (String) masterDataAddArgs.get(USER_ID);
		String tableName = (String) masterDataAddArgs.get(TABLE_NAME);
		String dsName = (String) masterDataAddArgs.get(DS_NAME);
		masterDataAddArgs.put(PERSIST_CLASS, MASTER_DATA_ADD_OPREATION_PLUGIN_CLASS_NAME);
		activityDataMap.put("activity_data", masterDataAddArgs);
	//	String moduleName=(String) suppressArgs.get(MODULE);
	    UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;
			String businessArea = (String) masterDataAddArgs.get(BUSINES_AREA);
			String reconName = (String) masterDataAddArgs.get(RECON_NAME);
			String comments = (String) masterDataAddArgs.get(COMMENTS);
			String moduleName=(String)masterDataAddArgs.get(ACTION);
			userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
					MASTER_DATA_ADD_OPERATION, activityDataMap, activityStatus, comments);

			updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
						}
		else{
			result = persist(activityDataMap, APPROVED, connection);
		}
			
					}catch(Exception e){
						updateResultStatus(result,FAILED,OPERATION_FAILED);
						e.printStackTrace();
					}finally{
						
						DbUtil.closeConnection(connection);
						
					}
	return result;
			
	}

private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}

public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {
	connection=DbUtil.getConnection();
	 System.out.println("*****************"+activityDataMap);
	 Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
	 String comment=(String) activityDataMap.get("comment");
	 System.out.println(comment+"-------"+activityRecordsMap);
	 InsertRegulator insertRegulator= new InsertRegulator(); 
	 
	 int insertStatus=0;
		Map<String, Object> args =new HashMap<String, Object>();
	 try
	 {
		if(APPROVED.equalsIgnoreCase(status)){
           String tableName=(String) activityRecordsMap.get(TABLE_NAME);
           List<Map<String,Object>> selectedRecords=(List<Map<String,Object>>) activityRecordsMap.get(SELECTED_RECORDS);
           Query insertQuery= OperationsUtil.getInsertQueryConf(tableName, connection);
           System.out.println(insertQuery+"insertQuery");
           String inserQueryString=insertQuery.getQueryString();
           String insertQueryParams=insertQuery.getQueryParam();
           System.out.println(inserQueryString+" "+insertQueryParams);
          
           for (Map<String,Object> recordMap:selectedRecords){
        	   recordMap.put("ACTIVITY_COMMENTS",comment);
        	   args.put(INSERT_QRY, inserQueryString);
        	   args.put(INSERT_QRY_PARAMS, insertQueryParams);
        	   args.put(PARAM_VALUE_MAP, recordMap);
        	insertStatus=insertRegulator.insert(connection,args);
        		System.out.println(insertStatus+" SUCCESS, INSERTED RECORD");
           }
          }
	 }
	 catch(Exception e){
		 logger.error(e);
	 }finally{
		 
		 DbUtil.closeConnection(connection);
		 
	 }
	 return result;
}

 
}
