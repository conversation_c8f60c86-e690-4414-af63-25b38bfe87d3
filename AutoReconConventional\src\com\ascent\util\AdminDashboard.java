package com.ascent.util;

import java.sql.Connection;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.ascent.integration.util.DbUtil;

public class AdminDashboard {
	

	public List<Map<String, Object>> getReportValues(String first,String second) throws ParseException {
		// List<String> matchtypes = getMatchtypes();
		Connection connection = DbUtil.getConnection();
		PreparedStatement pstmt3=null;
		PreparedStatement pstmt1=null;
		PreparedStatement pstmt=null;
		PreparedStatement pstmt2 =null;
     
		List<String> tableList = getTableList();
		List<Map<String, Object>> totalReconsList = new ArrayList<Map<String, Object>>();
		
		for (String reconName : tableList) {

			try {

				LinkedHashMap<String, Object> reconMap = new LinkedHashMap<String, Object>();
				
				String automatch=null;
				 String autounmatch=null;
				  String manualmatch =null;
				  String manualunmatch=null;
				reconMap.put("cardType", reconName);

				//connection = dbConnection();
			if("ACQ_QPAY_CRDS_RECON".equalsIgnoreCase(reconName)){
			 automatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where MATCH_TYPE='AM' and TRAN_DATE between '" + first + "' and '" + second + "') / "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName 
						+ " WITH  (NOLOCK) where TRAN_DATE between '" + first + "' and '" + second + "')),1) "
						+ "FROM " + reconName + " WITH  (NOLOCK) WHERE MATCH_TYPE='AM'  and TRAN_DATE between '" + first + "' and '" + second + "'";
			  autounmatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where MATCH_TYPE='AU' and TRAN_DATE between '" + first + "' and '" + second + "') / "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where TRAN_DATE between '" + first + "' and '" + second + "')),1) "
						+ "FROM " + reconName + " WITH  (NOLOCK)  WHERE MATCH_TYPE='AU' and TRAN_DATE between '" + first + "' and '" + second + "'";
			   manualmatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where MATCH_TYPE='MM' and TRAN_DATE between '" + first + "' and '" + second + "') / "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK)  where TRAN_DATE between '" + first + "' and '" + second + "')),1) "
						+ "FROM " + reconName + " WITH  (NOLOCK) WHERE MATCH_TYPE='MM' and TRAN_DATE between '" + first + "' and '" + second + "'";
			   manualunmatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK)  where MATCH_TYPE='MU' and TRAN_DATE between '" + first + "' and '" + second + "') / "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where TRAN_DATE between '" + first + "' and '" + second + "')),1) "
						+ "FROM " + reconName + " WITH  (NOLOCK)  WHERE MATCH_TYPE='MU' and TRAN_DATE between '" + first + "' and '" + second + "'";
			}else{
				 automatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where MATCH_TYPE='AM' and TRA_DATE between '" + first + "' and '" + second + "') / "
						+ "(SELECT count(MATCH_TYPE) FROM "
						+ reconName
						+ " WITH  (NOLOCK) where TRA_DATE between '" + first + "' and '" + second + "')),1) "
						+ "FROM " + reconName + " WITH  (NOLOCK) WHERE MATCH_TYPE='AM'  and TRA_DATE between '" + first + "' and '" + second + "'";
				 autounmatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
							+ "(SELECT count(MATCH_TYPE) FROM "
							+ reconName
							+ " WITH  (NOLOCK) where MATCH_TYPE='AU' and TRA_DATE between '" + first + "' and '" + second + "') / "
							+ "(SELECT count(MATCH_TYPE) FROM "
							+ reconName
							+ " WITH  (NOLOCK) where TRA_DATE between '" + first + "' and '" + second + "')),1) "
							+ "FROM " + reconName + " WITH  (NOLOCK) WHERE MATCH_TYPE='AU' and TRA_DATE between '" + first + "' and '" + second + "'";
				 manualmatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
							+ "(SELECT count(MATCH_TYPE) FROM "
							+ reconName
							+ " WITH  (NOLOCK)  where MATCH_TYPE='MM' and TRA_DATE between '" + first + "' and '" + second + "') / "
							+ "(SELECT count(MATCH_TYPE) FROM "
							+ reconName
							+ " WITH  (NOLOCK)  where TRA_DATE between '" + first + "' and '" + second + "')),1) "
							+ "FROM " + reconName + " WITH  (NOLOCK)  WHERE MATCH_TYPE='MM' and TRA_DATE between '" + first + "' and '" + second + "'";
				 manualunmatch = "SELECT  distinct match_type,convert(VARCHAR,convert(MONEY,100.0 * "
							+ "(SELECT count(MATCH_TYPE) FROM "
							+ reconName
							+ " WITH  (NOLOCK)  where MATCH_TYPE='MU' and TRA_DATE between '" + first + "' and '" + second + "') / "
							+ "(SELECT count(MATCH_TYPE) FROM "
							+ reconName
							+ " WITH  (NOLOCK)  where TRA_DATE between '" + first + "' and '" + second + "')),1) "
							+ "FROM " + reconName + " WITH  (NOLOCK)  WHERE MATCH_TYPE='MU' and TRA_DATE between '" + first + "' and '" + second + "'";
				

			}
				 pstmt3 = connection.prepareStatement(automatch);

				ResultSet dispautomatch = pstmt3.executeQuery();
				if (!dispautomatch.next()) {
					// handle NULL field value
					reconMap.put("AMPer", 0);
				} else {
					reconMap.put("AMPer", dispautomatch.getString(2));

				}

				 pstmt1 = connection.prepareStatement(autounmatch);

				ResultSet dispautounmatch = pstmt1.executeQuery();

				if (!dispautounmatch.next()) {
					// handle NULL field value
					reconMap.put("AUPer", 0);
				} else {
					reconMap.put("AUPer", dispautounmatch.getString(2));

				}
				
				 pstmt = connection.prepareStatement(manualmatch);

				ResultSet dispmanualmatch = pstmt.executeQuery();
				if (!dispmanualmatch.next()) {
					// handle NULL field value
					reconMap.put("MMPer", 0);
				}
				else  {
					reconMap.put("MMPer", dispmanualmatch.getString(2));

				}

				 pstmt2 = connection.prepareStatement(manualunmatch);

				ResultSet dispmanualunmatch = pstmt2.executeQuery();
				if (!dispmanualunmatch.next()) {
					// handle NULL field value
					reconMap.put("MUPer", 0);
				} else {
					reconMap.put("MUPer", dispmanualunmatch.getString(2));

				}

				totalReconsList.add(reconMap);
				System.out.println("map :" + reconMap);

			} catch (Exception e) {
				e.printStackTrace();
			}
			finally{
				DbUtil.closePreparedStatement(pstmt);
				DbUtil.closePreparedStatement(pstmt1);
				DbUtil.closePreparedStatement(pstmt2);
				DbUtil.closePreparedStatement(pstmt3);
				
			
			}
		}
		System.out.println(totalReconsList);

		return totalReconsList;

	}

	public List<String> getTableList() {
		Connection connection = DbUtil.getConnection();
		PreparedStatement pstmt=null;
		List<String> tablList = new ArrayList<String>();
		String DISPLAYID = "select * from TABLE_LIST WITH  (NOLOCK) "; 

		try {
			 pstmt = connection.prepareStatement(DISPLAYID);

			ResultSet rs = pstmt.executeQuery();

			while (rs.next()) {

				tablList.add(rs.getString(1));

			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		finally{
			try {
				DbUtil.closePreparedStatement(pstmt);
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
				
			}
					
		}
		// System.out.println(terminalIds);
		System.out.println(tablList);
		return tablList;

	}

	public List<String> getMatchtypes() {
		Connection connection = DbUtil.getConnection();
		PreparedStatement pstmt=null;
		List<String> mathchtypess = new ArrayList<String>();
		String DISPLAYID = "select distinct MATCH_TYPE from ONUS_ATM_DEBIT_RECON WITH  (NOLOCK) "; //
		
		try {
			 pstmt = connection.prepareStatement(DISPLAYID);

			ResultSet rs = pstmt.executeQuery();

			while (rs.next()) {

				mathchtypess.add(rs.getString(1));

			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		finally{
			try {
				DbUtil.closePreparedStatement(pstmt);
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			
			}
					
		}
		// System.out.println(terminalIds);

		return mathchtypess;

	}

	public Map<String, Object> getActivitiesCount() {
		Connection connection = DbUtil.getConnection();
		PreparedStatement pstmt=null;
		Map<String, Object> activitiesmap = new HashMap<String, Object>();
		
		String DISPLAYID = "select count(*) from recon_activity_flow WITH  (NOLOCK)  where recently_updated_on between CAST(GETDATE()-1 AS DATE) and CAST(GETDATE() AS DATE)  and status='PENDING'"; //

	//	connection = dbConnection();
		try {
		 pstmt = connection.prepareStatement(DISPLAYID);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				activitiesmap.put("ACTIVITES_PENDING", rs.getObject(1));
			}
		
		} catch (SQLException e) {

			e.printStackTrace();
		}
		finally{
			try {
				DbUtil.closePreparedStatement(pstmt);
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
				
			}
					
		}

		return activitiesmap;

	}
	public Map<String, Object> getApprovedActivitiesCount() {

		Map<String, Object> approvedactivitiesmap = new HashMap<String, Object>();
		
		String DISPLAYID = "select count(*) from recon_activity_flow WITH  (NOLOCK)  where recently_updated_on between CAST(GETDATE()-1 AS DATE) and CAST(GETDATE() AS DATE)  and status='APPROVED'"; //
		Connection connection = DbUtil.getConnection();
		PreparedStatement pstmt=null;
	//	connection = dbConnection();
		try {
			 pstmt = connection.prepareStatement(DISPLAYID);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				approvedactivitiesmap.put("ACTIVITES_APPROVED", rs.getObject(1));
			}
			
		} catch (SQLException e) {

			e.printStackTrace();
		}
		finally{
			try {
				DbUtil.closePreparedStatement(pstmt);
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
				
			}
					
		}

		return approvedactivitiesmap;

	}
	public Map<String, Object> getRecentActivitiesCount() {

		Map<String, Object> recentactivitiesmap = new HashMap<String, Object>();
		
		String DISPLAYID = "select count(*) from recon_activity_flow WITH  (NOLOCK)  where recently_updated_on between CAST(GETDATE()-1 AS DATE) and CAST(GETDATE() AS DATE)"; //

		Connection connection = DbUtil.getConnection();
		try {
			PreparedStatement pstmt = connection.prepareStatement(DISPLAYID);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				recentactivitiesmap.put("RECENT_ACTIVITES", rs.getObject(1));
			}
			System.out.println("approvedactivitiesmap   ******** "+recentactivitiesmap);
		} catch (SQLException e) {

			e.printStackTrace();
		}

		return recentactivitiesmap;

	}
	
	
}
