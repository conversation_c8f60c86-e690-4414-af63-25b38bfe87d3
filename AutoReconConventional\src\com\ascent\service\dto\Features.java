package com.ascent.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Features implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2637363412018805855L;
	private List<Feature> features=new ArrayList<Feature>();
	Map<Integer,Feature> featurMap=new HashMap<Integer, Feature>();
	Map<String,Feature> featureWithNameMap=new HashMap<String, Feature>();
 
	
	public void init(){
		if(this.features!=null){
			for(Feature feature:this.features){
				feature.init();
				featurMap.put(feature.getId(), feature);
				featureWithNameMap.put(feature.getFeature(), feature);
			}
		}
	}
	
	
	public List<Feature> getFeatures() {
		return features;
	}

	public void setFeatures(List<Feature> features) {
		this.features = features;
	}


	public Feature getFeature(Integer id) {
		return featurMap.get(id);
	}

	public Feature getFeature(String feature) {
		return featureWithNameMap.get(feature);
	}


	 
}
