package com.ascent.ds.operations;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.recon.ReconProcessController;
import com.ascent.ds.login.LoginDetails;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ProcessStarter extends BasicDataSource implements PagesConstants {

	/**
	 * Class is used for the getting the particular recon name from session and
	 * send control to start process activity.
	 */

	private static Logger logger = LogManager.getLogger(ProcessStarter.class);
	private static final long serialVersionUID = 5682482541827522062L;

	public static final String user_selected_recon = "user_selected_recon";
	public static final String SUCCESS_MSG = "success_msg";

	private static LoginDetails details = null;

	public ProcessStarter() {
		logger.info(" ProcessStarter constructor....");

	}

	public DSResponse executeFetch(final DSRequest dsRequest) {
		DSResponse dsResponse = new DSResponse();

		Map<String, Object> records = dsRequest.getValues();
		String b_area = (String) records.get("B_Area");
		String recon = (String) records.get("Recon");
System.out.println("Values "+b_area+" "+recon);
		 
		 
		ProcessStarter.processStarter(recon);

		Map<String, Object> respMap = new HashMap<String, Object>();

		respMap.put(SUCCESS_MSG, "Process Started Sucessfully..");
		dsResponse.setData(respMap);
		return dsResponse;
	}

	/**
	 * 
	 * @param userSelectedRecon
	 *            method to used to validate the process based on user selected
	 *            recon
	 * 
	 */
	private static void processStarter(String userSelectedRecon) {
		ReconProcessController processController = null;
		details = new LoginDetails();
		try {
			List<Map<String, Object>> reconnameList = details.getAllReconName();
			for (Map<String, Object> reconMap : reconnameList) {
				if (userSelectedRecon.equals(reconMap.get("recon_name"))) {
					System.out.println("++++++++++++++++++++++++++++");
					processController = new ReconProcessController();
					processController.boot(userSelectedRecon);// passing
																// selected
																// recon name to
																// boot method

				} else {
					logger.trace("Not Available");

				}

			}

		} catch (Exception e) {

			e.printStackTrace();
		}
	}

}
