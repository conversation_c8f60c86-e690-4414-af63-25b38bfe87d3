package com.ascent.banknizwa.source.ej;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;



import com.ascent.banknizwa.metainstance.EtlMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.integration.enrichment.AscentEnrichmentPlugin;
import com.ascent.integration.util.DbUtil;


public class EJEnrichment implements AscentEnrichmentPlugin {
	private static Logger logger = LogManager.getLogger(EJEnrichment.class.getName());

	private static Queries queries = null;
	public static List<String> irisReversalCodes = new ArrayList<String>();
	public static List<String> irisMainCodes = new ArrayList<String>();

	private static List<String> onusAuthorizerArrList = new ArrayList<String>();
	private static List<String> onusAcquiringChannelIdList = new ArrayList<String>();

	private static List<String> issuerAuthorizerArrList = new ArrayList<String>();
	private static List<String> issuerAcquiringChannelIdList = new ArrayList<String>();

	private static List<String> acquirerAuthorizerArrList = new ArrayList<String>();
	private static List<String> acquirerAcquiringChannelIdList = new ArrayList<String>();
	private static List<String> atmChannelProcCodeList = new ArrayList<String>();
	public static List<String> dohaBankAcquiringChannelIdList = new ArrayList<String>();
	public static List<String> debitCardBinsList = new ArrayList<String>();
	public static List<String> creditCardBinsList = new ArrayList<String>();

	public static List<String> payrollCardBinList = new ArrayList<String>();

	public static Map<String, Object> channelIdMap = new HashMap<String, Object>();

	public static Map<String, Object> currencyCodeMap = new HashMap<String, Object>();
	public static Map<String, Object> currencySymbolMap = new HashMap<String, Object>();

	public static Map<String, Object> channelDesMap = new HashMap<String, Object>();

	public static Map<String, Object> creditMap = new HashMap<String, Object>();
	public static Map<String, Object> debitMap = new HashMap<String, Object>();
	public static Map<String, Object> payrolMap = new HashMap<String, Object>();

	private static Map<String, Object> pcTrnCodeMap = new HashMap<String, Object>();
	public static Map<String, Object> pcTrandesMap = new HashMap<String, Object>();

	private static Map<String, Object> respCodeMap = new HashMap<String, Object>();

	private static Map<String, Object> glMap = new HashMap<String, Object>();
	private static Map<String, Object> glAllData = new HashMap<String, Object>();

	private static Map<String, Object> glwithdrawal = new HashMap<String, Object>();
	private static Map<String, Object> gldeposit = new HashMap<String, Object>();

	private static Map<String, Object> chanserno = new HashMap<String, Object>();

	public static Map<String, Object> binMap = new HashMap<String, Object>();

	static {/*

		irisMainCodes.add("000");
		irisMainCodes.add("976");

		irisReversalCodes.add("036");
		irisReversalCodes.add("037");

		irisReversalCodes.add("967");

		irisReversalCodes.add("994");
		irisReversalCodes.add("995");
		irisReversalCodes.add("996");
		Connection connection = null;
		ResultSet rs = null;
		PreparedStatement channelPstmt = null;
		PreparedStatement binPstmt = null;

		PreparedStatement processCodePstmt = null;
		PreparedStatement responseCodePstmt = null;

		PreparedStatement chanelserPstmt = null;
		PreparedStatement glPsmt = null;
		PreparedStatement glSusPsmt = null;

		PreparedStatement currencyPstmt = null;

		try {
			connection = DbUtil.getConnection();
			queries = EtlMetaInstance.getInstance().getEtlQueryConfs();

			String selChannelQry = queries.getQueryConf("MDT_CHANNELS").getQueryString();

			channelPstmt = connection.prepareStatement(selChannelQry);
			rs = channelPstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("CHANNEL_NAME");
				String chanel_id = rs.getString("CHANNEL_ID");
				String channel_des = rs.getString("CHANNEL_DESCRIPTION");
				channelIdMap.put(name, chanel_id);
				channelDesMap.put(name, channel_des);

			}
			// --CURRENCY CONFIGURATION QRY DETAILS.
			String currencyData = queries.getQueryConf("MDT_IRIS_CURRENCY").getQueryString();

			currencyPstmt = connection.prepareStatement(currencyData);
			rs = currencyPstmt.executeQuery();

			while (rs.next()) {

				String code = rs.getString("CODE");
				String decimal = rs.getString("DECIMALS");
				String symbol = rs.getString("SYMBOL");

				currencyCodeMap.put(code, decimal);
				currencySymbolMap.put(code, symbol);

			}

			String selCardBin = queries.getQueryConf("MDT_CARD_BIN").getQueryString();

			binPstmt = connection.prepareStatement(selCardBin);
			rs = binPstmt.executeQuery();

			while (rs.next()) {

				if ("CREDIT CARD".equalsIgnoreCase(rs.getString("CARD_TYPE"))) {
					String name = rs.getString("PRODUCT_TYPE");
					String bin = rs.getString("BIN");
					creditMap.put(name, bin);

				}

				if ("DEBIT CARD".equalsIgnoreCase(rs.getString("CARD_TYPE"))) {
					String name = rs.getString("PRODUCT_TYPE");
					String bin = rs.getString("BIN");
					debitMap.put(name, bin);

				}
				if ("PAYROL CARD".equalsIgnoreCase(rs.getString("CARD_TYPE"))) {
					String name = rs.getString("PRODUCT_TYPE");
					String bin = rs.getString("BIN");
					payrolMap.put(name, bin);

				}

			}
			binMap.put("CREDIT_CARD", creditMap);
			binMap.put("DEBIT_CARD", debitMap);
			binMap.put("PAYROL_CARD", payrolMap);

			String selProcessQry = queries.getQueryConf("MDT_PROCESS_CODES").getQueryString();

			processCodePstmt = connection.prepareStatement(selProcessQry);
			rs = processCodePstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("TRAN_NAME");
				String tran_code = rs.getString("TRAN_CODE");
				String tran_des = rs.getString("TRAN_DESCRIPTION");
				pcTrnCodeMap.put(name, tran_code);
				pcTrandesMap.put(name, tran_des);

			}

			String selResponseQry = queries.getQueryConf("RESPONSE_ATM").getQueryString();
			responseCodePstmt = connection.prepareStatement(selResponseQry);
			rs = responseCodePstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("NAME");
				String tran_code = rs.getString("CODE");
				respCodeMap.put(name, tran_code);

			}

			String selGlQry = queries.getQueryConf("GL_ACC").getQueryString();

			glPsmt = connection.prepareStatement(selGlQry);
			rs = glPsmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("ACCOUNT_NAME");
				String tran_code = rs.getString("SUB_ACC");

				String tran[] = { rs.getString("BRANCH_CODE"), rs.getString("CURRENCY"), rs.getString("CUST_NO"),
						rs.getString("SUB_ACC"), rs.getString("LEDGER"), rs.getString("CURENCY_CODE"),
						rs.getString("CODE") };
				glMap.put(name, tran_code);
				glAllData.put(name, tran);

			}

			String selSusGlQry = queries.getQueryConf("GL_SUS_ACC").getQueryString();

			glSusPsmt = connection.prepareStatement(selSusGlQry);
			rs = glSusPsmt.executeQuery();

			while (rs.next()) {

				if ("withdrawal".equalsIgnoreCase(rs.getString("TRAN_DES"))) {

					String name = rs.getString("ATM_LOCATION");

					String tran[] = { rs.getString("ATM"), rs.getString("BRANCH_CODE"), rs.getString("CUST_ID"),
							rs.getString("CUR"), rs.getString("LEDGER"), rs.getString("TXN_TYPE"),
							rs.getString("SUB_ACC_CODE") };
					glwithdrawal.put(name, tran);

				}
				if ("deposit".equalsIgnoreCase(rs.getString("TRAN_DES"))) {

					String name = rs.getString("ATM_LOCATION");

					String tran[] = { rs.getString("ATM"), rs.getString("BRANCH_CODE"), rs.getString("CUST_ID"),
							rs.getString("CUR"), rs.getString("LEDGER"), rs.getString("TXN_TYPE"),
							rs.getString("SUB_ACC_CODE") };
					gldeposit.put(name, tran);

				}
			}

			String selChanelSerno = queries.getQueryConf("CHANN_SERO_ACC").getQueryString();

			chanelserPstmt = connection.prepareStatement(selChanelSerno);
			rs = chanelserPstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("CHANNELNAME");
				String serno = rs.getString("SERNO");
				chanserno.put(name, serno);

			}

			String onusAuthi = queries.getQueryConf("ONUS_AUTH").getQueryString();
			PreparedStatement onusAuthPstmt = connection.prepareStatement(onusAuthi);
			rs = onusAuthPstmt.executeQuery();
			while (rs.next()) {

				onusAuthorizerArrList.add(rs.getString(1));
				onusAcquiringChannelIdList.add(rs.getString(1));
				dohaBankAcquiringChannelIdList.add(rs.getString(1));

			}

			*//**  comment by shivam
			 * Business Area (ISSUER):
			 * 
			 * AUTHORIZER = 50 or 101 and ACQUIRING_CHANNEL_ID = 7 or 28
			 *//*

			
			 * 
			 * CHANNEL_NAME in('IRIS','CTL')
			 * 
			 * CHANNEL_NAME in('VISA','NAPS')
			 

			String issAuthorizer = queries.getQueryConf("ISS_AUTH").getQueryString();

			PreparedStatement issAuthPstmt = connection.prepareStatement(issAuthorizer);
			rs = issAuthPstmt.executeQuery();
			while (rs.next()) {

				issuerAuthorizerArrList.add(rs.getString(1));
				acquirerAuthorizerArrList.add(rs.getString(1));
			}

			String issAuthorizerChannel = queries.getQueryConf("ISS_ACQ_AUTH").getQueryString();

			PreparedStatement issAuthChannelPstmt = connection.prepareStatement(issAuthorizerChannel);
			rs = issAuthChannelPstmt.executeQuery();
			while (rs.next()) {
				issuerAcquiringChannelIdList.add(rs.getString(1));
				acquirerAcquiringChannelIdList.add(rs.getString(1));
			}

			*//**
			 * Business Area (ACQUIRER): AUTHORIZER = 28 or 7 or 40 or 46 and
			 * ACQUIRING_CHANNEL_ID = 1 or 50 or 101 or 9898 or 9999
			 *//*

			*//**
			 * ATM PROC CODES: PROC_CODE = 01 or 73 POS PROC CODE : = 00
			 * 
			 * deposites :21, 25, 76 & 86
			 *//*

			
			 * TRAN_NAME in('Withdrawal','Bill payment','Cash Deposit','Cardless
			 * Deposit','E Voucher')
			 

			String atmChannel = queries.getQueryConf("PROC_ATM").getQueryString();
			PreparedStatement atmChannelPstmt = connection.prepareStatement(atmChannel);
			rs = atmChannelPstmt.executeQuery();
			while (rs.next()) {
				atmChannelProcCodeList.add(rs.getString(1));

			}

			*//**
			 * DOHA BANK
			 * 
			 * ACQUIRING_CHANNEL_ID = 1 or 50 or 101 or 9898 or 9999
			 *//*

			*//**
			 * DebitCard BIN = 434141 and 484823 and 428246
			 *//*

			for (String keys : creditMap.keySet()) {
				String value = (String) creditMap.get(keys);
				creditCardBinsList.add(value);
			}

			for (String keys : debitMap.keySet()) {
				String value = (String) debitMap.get(keys);
				debitCardBinsList.add(value);
			}

			for (String keys : payrolMap.keySet()) {
				String value = (String) payrolMap.get(keys);
				payrollCardBinList.add(value);
			}

		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		} finally {

			DbUtil.closeResultSet(rs);
			DbUtil.closePreparedStatement(channelPstmt);
			DbUtil.closePreparedStatement(binPstmt);
			DbUtil.closePreparedStatement(processCodePstmt);
			DbUtil.closePreparedStatement(responseCodePstmt);
			DbUtil.closePreparedStatement(chanelserPstmt);
			DbUtil.closePreparedStatement(glPsmt);
			DbUtil.closePreparedStatement(glSusPsmt);
		}

	*/}

	public Map<String, Object> enrich(Map<String, Object> args) {

	/*	String business_area = "";

		String acquiring_channel_id = (String) args.get("ACQUIRING_CHANNEL_ID");
		String authorizer = (String) args.get("AUTHORIZER");
		if (onusAuthorizerArrList.contains(authorizer) && onusAcquiringChannelIdList.contains(acquiring_channel_id)) {
			business_area = "ONUS";
		} else if (issuerAuthorizerArrList.contains(authorizer)
				&& issuerAcquiringChannelIdList.contains(acquiring_channel_id)) {
			business_area = "ISSUER";
		} else if (acquirerAuthorizerArrList.contains(authorizer)
				&& acquirerAcquiringChannelIdList.contains(acquiring_channel_id)) {
			business_area = "ACQUIRER";
		}
		args.put("BUSINESS_AREA", business_area);

		String term_id = (String) args.get("C_ACCEP_TERM_ID");
		if (term_id != null && !term_id.isEmpty() && term_id.length() == 4) {
			String cust_id = term_id.substring(0, 3);
			String sub_acc = term_id.substring(3, 4);
			args.put("CUST_ID", cust_id);
			args.put("SUB_ACC_CODE", sub_acc);
		}

		String proc_code_first_2 = (String) args.get("PROC_CODE_FIRST_2");

		String channel = "";
		if (atmChannelProcCodeList.contains(proc_code_first_2)) {
			channel = "ATM";
		}
		if ("00".equalsIgnoreCase(proc_code_first_2)) {
			channel = "POS";
		}

		args.put("CHANNEL", channel);

		String curry_code = (String) args.get("CURR_CODE_TRAN");
		if (curry_code != null && !curry_code.isEmpty() && "634".equalsIgnoreCase(curry_code)) {

			args.put("GEOGRAPHY", "QATAR");
		}

		String network = "";

		if (dohaBankAcquiringChannelIdList.contains(acquiring_channel_id)) {
			network = "DOHA BANK";

		} else if ("0028".equalsIgnoreCase(acquiring_channel_id)) {
			network = "NAPS";
		} else if ("0007".equalsIgnoreCase(acquiring_channel_id)) {
			network = "VISA";
		} else if ("0040".equalsIgnoreCase(authorizer)) {
			network = "MASTER";
		} else if ("0046".equalsIgnoreCase(authorizer)) {
			network = "UP";
		}

		args.put("NETWORK", network);

		String bin = (String) args.get("BIN");
		if (debitCardBinsList.contains(bin)) {
			args.put("CARD_TYPE", "DEBIT CARD");
		}
		if (creditCardBinsList.contains(bin)) {
			args.put("CARD_TYPE", "CREDIT CARD");
		}
		if (payrollCardBinList.contains(bin)) {
			args.put("CARD_TYPE", "PAYROLL CARD");
		}

		String date_loc_tran = (String) args.get("DATE_LOC_TRAN");
		try {
			Date tempDate = (new SimpleDateFormat("yyyyMMdd")).parse(date_loc_tran);
			date_loc_tran = (new SimpleDateFormat("yyyy-MM-dd")).format(tempDate);
			args.put("DATE_LOC_TRAN", date_loc_tran);
		} catch (ParseException e) {

			e.printStackTrace();
		}
		String time_loc_tran = (String) args.get("TIME_LOC_TRAN");
		String tempTime = "";
		if (time_loc_tran != null && time_loc_tran.length() == 6) {
			tempTime = time_loc_tran.substring(0, 2) + ":" + time_loc_tran.substring(2, 4) + ":"
					+ time_loc_tran.substring(4, 6);
			args.put("TIME_LOC_TRAN", tempTime);
		} else {
			tempTime = "00:00:00";
			args.put("TIME_LOC_TRAN", "00:00:00");
		}

		if (date_loc_tran != null && !(date_loc_tran.trim()).isEmpty() && !(tempTime.trim()).isEmpty()) {
			String dateTimeString = date_loc_tran + " " + tempTime;
			try {

				args.put("DATE_TIME_LOC_TRAN", dateTimeString);
				Long timeInMillis = ((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(dateTimeString)).getTime();
				args.put("TIME_IN_MILLIS", timeInMillis);
			} catch (Exception e) {

				logger.error(e.getMessage(),e);
			}

		}

		String resp_code = (String) args.get("RESP_CODE");

		if (irisMainCodes.contains(resp_code)) {
			args.put("MAIN_REV_IND", "MAIN");

		}
		if (irisReversalCodes.contains(resp_code)) {
			args.put("MAIN_REV_IND", "REVERSAL");

		}

		String currcySett = (String) args.get("CURR_CODE_SETT");

		if (currcySett != null && !currcySett.isEmpty()) {
			Double amount_sett = 0.0d;

			String value = (String) currencyCodeMap.get(currcySett);
			String curSymbol = (String) currencySymbolMap.get(currcySett);
			amount_sett = (Double) args.get("AMT_SETT");
			args.put("TRAN_CUR", curSymbol);

			if (value != null && !value.isEmpty()) {
				if ("3".equalsIgnoreCase(value.trim())) {
					amount_sett = amount_sett / 1000;
				} else if ("2".equalsIgnoreCase(value.trim())) {
					amount_sett = amount_sett / 100;

				}
				args.put("AMT_SETT", amount_sett);
			}

		}
		String curryTran = (String) args.get("CURR_CODE_TRAN");
		if (curryTran != null && !curryTran.isEmpty()) {
			Double atmBase = 0.0d;
			Double amtran = (Double) args.get("AMT_TRAN");

			String amt_tran_base = (String) args.get("AMT_TRAN_BASE");
			if (amt_tran_base != null && !amt_tran_base.isEmpty()) {
				atmBase = Double.parseDouble(amt_tran_base);
			}

			String value = (String) currencyCodeMap.get(curryTran);
			String curSymbol = (String) currencySymbolMap.get(curryTran);

			if (value != null && !value.isEmpty() && atmBase != null && amtran != null) {
				if ("3".equalsIgnoreCase(value.trim())) {
					atmBase = atmBase / 1000;
					amtran = amtran / 1000;
				} else if ("2".equalsIgnoreCase(value.trim())) {
					atmBase = atmBase / 100;
					amtran = amtran / 100;

				}
				args.put("AMT_TRAN", amtran);
				args.put("AMT_TRAN_BASE", atmBase);
			}

		}

		String deb_cre_indicator = (String) args.get("PROC_CODE_FIRST_2");
		if ("01".equalsIgnoreCase(deb_cre_indicator) && "000".equalsIgnoreCase(resp_code)
				|| "82".equalsIgnoreCase(deb_cre_indicator) && "000".equalsIgnoreCase(resp_code)) {
			args.put("DEB_CRE_INDI", "DEBIT");
		}
		if ("01".equalsIgnoreCase(deb_cre_indicator) && "036".equalsIgnoreCase(resp_code)
				|| "82".equalsIgnoreCase(deb_cre_indicator) && "036".equalsIgnoreCase(resp_code)) {
			args.put("DEB_CRE_INDI", "CREDIT");
		}

		if ("01".equalsIgnoreCase(deb_cre_indicator) && "037".equalsIgnoreCase(resp_code)
				|| "82".equalsIgnoreCase(deb_cre_indicator) && "037".equalsIgnoreCase(resp_code)) {
			args.put("DEB_CRE_INDI", "CREDIT");
		}

		if ("21".equalsIgnoreCase(deb_cre_indicator) && "000".equalsIgnoreCase(resp_code)
				|| "25".equalsIgnoreCase(deb_cre_indicator) && "000".equalsIgnoreCase(resp_code)) {
			args.put("DEB_CRE_INDI", "DEBIT");
		}
		if ("21".equalsIgnoreCase(deb_cre_indicator) && "036".equalsIgnoreCase(resp_code)
				|| "25".equalsIgnoreCase(deb_cre_indicator) && "036".equalsIgnoreCase(resp_code)) {
			args.put("DEB_CRE_INDI", "CREDIT");
		}

		if ("21".equalsIgnoreCase(deb_cre_indicator) && "037".equalsIgnoreCase(resp_code)
				|| "25".equalsIgnoreCase(deb_cre_indicator) && "037".equalsIgnoreCase(resp_code)) {
			args.put("DEB_CRE_INDI", "CREDIT");
		}*///comment by shivam06-06-2017

		
		return args;
	}

}
