package com.ascent.scheduler;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Message.RecipientType;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.birt.chart.event.InteractionEvent;

/**
 * <AUTHOR>
 *
 */

public class MailTrigger {

	private static Logger logger = LogManager.getLogger(MailTrigger.class.getName());

	public static void main(String[] args) {

	}

	public boolean sendMailConformation(Map<String, Object> mailData) throws Exception {
		boolean flag = false;
		Properties prop = new Properties();
		Properties properties = System.getProperties();

		String propFileName = "mail.properties";
		InputStream fileextraction = getClass().getClassLoader().getResourceAsStream(propFileName);
		prop.load(fileextraction);

		String username = prop.getProperty("fromUserName");
		String password = prop.getProperty("fromUserPassword");
		String SMTPHost = prop.getProperty("SMTPHost");
		String SMTPPort = prop.getProperty("SMTPPort");

		// Setup mail server
		properties.setProperty("mail.smtp.host", SMTPHost);
		properties.setProperty("mail.smtp.auth", "true");
		/*properties.setProperty("mail.smtp.starttls.enable", "true");
		properties.setProperty("mail.smtp.ssl.trust", "smtp.gmail.com");*/
/*		properties.setProperty("mail.smtp.ssl.trust", "smtp.ascentbusiness.com");*/
		properties.setProperty("mail.smtp.port", SMTPPort);

		Session session = Session.getInstance(properties, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, password);
			}
		});

		try {
			// Create a default MimeMessage object.
			MimeMessage message = new MimeMessage(session);

			// Set From: header field of the header.
			message.setFrom(new InternetAddress(username));
			logger.debug("from : " + username);

			// Set To: header field of the header.//toMail to mail_id//kaushal
			message.addRecipient(Message.RecipientType.TO, new InternetAddress(mailData.get("mail_id").toString()));
			
			/*message.addRecipient(Message.RecipientType.TO, new InternetAddress(mailData.get("MAIL_ID").toString()));*/

		


			/*// Set Cc: header field of the header.//Coment By kaushal
			@SuppressWarnings("unchecked")
			List<String> ccMailList = (List<String>) mailData.get("ccMail");

			if (ccMailList != null) {
				for (String ccMail : ccMailList) {
					message.addRecipient(RecipientType.CC, new InternetAddress(ccMail));
				}
			}*/

			// Set Subject: header field
			message.setSubject(mailData.get("recon") + "Recon_UnMatched Data ");

			// Create the message part
			BodyPart messageBodyPart = new MimeBodyPart();

			// Now set the actual message
			messageBodyPart.setText(mailData.get("content").toString());

			// Create a multipart message
			Multipart multipart = new MimeMultipart();

			// Set text message part
			multipart.addBodyPart(messageBodyPart);

			// Part two is attachment
			MimeBodyPart mimeBodyPart = new MimeBodyPart();
			mimeBodyPart.attachFile(mailData.get("fileName").toString());
			multipart.addBodyPart(mimeBodyPart);

			// Send the complete message parts
			message.setContent(multipart);

			// Send message
	        Transport.send(message);
			flag = true;
			logger.debug("Mail Send successfully....");
			
		} catch (MessagingException mex) {
			flag = false;
			logger.info(" MAIL FOLLOWUP()   WHILE SENDING MAIL GIVING PROBLEM " + mex);
			mex.printStackTrace();
		}
		return flag;
	}

}
