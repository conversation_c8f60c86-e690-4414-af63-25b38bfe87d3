package com.ascent.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpSession;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GetAssignedCasesForUserClass extends BasicDataSource{
	
	private static final long serialVersionUID = 1L;

	public DSResponse executeFetch(final DSRequest request)throws Exception
	{
		DSResponse response=new DSResponse();

		try{
			HttpSession httpSession = request.getHttpServletRequest().getSession();
			User user=(User) httpSession.getAttribute("userId");
			ArrayList<Map<String, Object>> assignedCases  =(ArrayList<Map<String, Object>>) getAssignedCasesForUser(user, "GET_ASSIGNED_CASES_BY_USER");
			response.setData(assignedCases);
		} 
		catch(Exception e)
		{
			e.printStackTrace();
		}
		return response;
	}
	
	public List<Map<String, Object>> getAssignedCasesForUser(User user, String queryName)
			throws ClassNotFoundException, SQLException, IOException {
		
		CustomerDao customerDao = new CustomerDao();
		
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("assigned_to", user.getUserId());
		
		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, queryName);
		
		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data.get("activity_data") != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();

					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}
		return activityDataList;
	}
}
