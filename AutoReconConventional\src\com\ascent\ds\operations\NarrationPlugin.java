package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.omg.PortableInterceptor.SUCCESSFUL;

import com.ascent.integration.util.DbUtil;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class NarrationPlugin extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = 1L;
@SuppressWarnings("unchecked")
public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		Map<String, Object> result = new HashMap<String, Object>();
		DSResponse response = new DSResponse();
		Map<Object, Object> reqCriteria =request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");

		if (user == null) {
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		
		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String moduleName =(String)reqCriteria.get("moduleName");
		String dsName=(String)reqCriteria.get("dsName");
		String centrifugalAmountField="";
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
				if((moduleName.equalsIgnoreCase("RECON")) && reqCriteria.get("centrifugalAmount")!=null){
				 centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
				}else{
					centrifugalAmountField="0.00";
				}
		System.out.println("action--"+action+"dsName--"+dsName+"  moduleName--"+moduleName+"centrifugalAmountField--"+centrifugalAmountField+"comments--"+comments+"\n"+selectedRecords);
		
       /* Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsName);
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		if(centrifugalAmountField!=null){
		paramsMap.put("centrifugalAmountField",centrifugalAmountField);
		}*/
		
		String reconTableName = dsName.substring(0, dsName.length() - 8);
		Connection con=DbUtil.getConnection();
		PreparedStatement pstmt=con.prepareStatement("UPDATE "+reconTableName+" SET COMMENTS=?,UPDATED_ON=?,VERSION=?,USER_ID=?  WHERE RECON_ID=?");
		for(Map record : selectedRecords){
			Long reconid=(Long) record.get("RECON_ID");
			String recordComments=(String) record.get("COMMENTS");
			String stringVersion=record.get("VERSION").toString();
			long version=Long.parseLong(stringVersion);
			++version;
			recordComments=recordComments+" ,"+comments;
			pstmt.setObject(1,recordComments );
			pstmt.setObject(2,new Timestamp(Calendar.getInstance().getTimeInMillis()) );
			pstmt.setObject(3,version );
			pstmt.setObject(4,userId);
			pstmt.setObject(5,reconid );
			
			pstmt.addBatch();
			
		}
		
	int[] rows=	pstmt.executeBatch();
	if(rows.length>=1){
	result.put(STATUS, "Records Updated");
	result.put(COMMENT, "Sucessfully");
	}
		
		response.setData(result);
		return response;
	}

	

	/*private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);
		return result;
	}*/
}
