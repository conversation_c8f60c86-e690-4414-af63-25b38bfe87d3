package com.ascent.ds.operations;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.administration.AuthorizationBean;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.persistance.UpdateRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class EscalationMatrix extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = 1L;
	
	private static final String CHECK_ESCALATION_MATRIX = "CHECK_ESCALATION_MATRIX";
	private static final String SAVE_ESCALATION_MATRIX = "SAVE_ESCALATION_MATRIX";
	private static final String UPDATE_ESCALATION_MATRIX = "UPDATE_ESCALATION_MATRIX";
	private static final String DELETE_ESCALATION_MATRIX = "DELETE_ESCALATION_MATRIX";

	private static Logger logger = LogManager.getLogger(EscalationMatrix.class.getName());

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	LoadRegulator loadRegulator = new LoadRegulator();
	
	public DSResponse executeFetch(final DSRequest request) throws Exception {


		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		String userSelectedBArea= (String)httpSession.getAttribute("user_selected_business_area");
		String userSelectedRecon=(String)httpSession.getAttribute("user_selected_recon");
		
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		DSResponse dsResponse = new DSResponse();
		Map<?, ?> requestParams = request.getCriteria();
		String action = (String) requestParams.get("value");
		@SuppressWarnings("unchecked")
		Map<String, Object> paramValueMap = (Map<String, Object>) requestParams.get("record");
		Map<String, Object> activityDataMap = new HashMap<String, Object>();
		String responseMsg = null;
		

		// Add EscalationMatrix
		if (action != null && action.equals("add")) {

			List<Map<String, Object>> count = new ArrayList<Map<String, Object>>();
			Query queryConf;
			try {
				queryConf = queryConfs.getQueryConf(CHECK_ESCALATION_MATRIX);
				count = loadRegulator.loadCompleteData(paramValueMap, queryConf);
			} catch (Exception e1) {
				e1.printStackTrace();
			}

			if (Integer.parseInt(count.get(0).get("COUNT").toString()) == 0) {
				
				paramValueMap.put("ACTIVE_INDEX", "Y");
				paramValueMap.put("VERSION", 1);
				
				if (userAdminManager.isUserUnderWorkflow(user)) {
					paramValueMap.put(DS_NAME, requestParams.get("dsName"));
					activityDataMap.put("VERSION", 1);
					activityDataMap.put("activity_data", paramValueMap);
					setUpEscalationMatrixDataToPersist(action,activityDataMap);
					String comments = "Escalation for "+paramValueMap.get("ESCALATED_PERSON");
					userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
							ESCALATION_MATRIX, OPERATION_NEW, activityDataMap, PENDING_APPROVAL, comments);
					responseMsg = "Escalation submitted for Approval Sucessfully";
				}
				else {
					
					responseMsg = saveEscalation(connection, paramValueMap);
				}
				
			}
			else {
				responseMsg = "Escalation already existed";
			}
			
			dsResponse.setData(responseMsg);
		}

		// Update EscalationMatrix
		if (action != null && action.equals("update")) {

			if (userAdminManager.isUserUnderWorkflow(user)) {
				setUpEscalationMatrixDataToPersist(action,paramValueMap);
				paramValueMap.put(DS_NAME, requestParams.get("dsName"));
				activityDataMap.put("activity_data", paramValueMap);
				String comments = "Escalation for "+paramValueMap.get("ESCALATED_PERSON");
				userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
						ESCALATION_MATRIX, OPERATION_EDIT, activityDataMap, PENDING_APPROVAL, comments);
				responseMsg = "Escalation submitted for Approval Sucessfully";
			}
			else {
				UpdateRegulator updateRegulator = new UpdateRegulator();
				Queries queries = ascentWebMetaInstance.getWebQueryConfs();
				Query query = null;
	
				try {
					query = queries.getQueryConf(UPDATE_ESCALATION_MATRIX);
				} catch (Exception e) {
	
					e.printStackTrace();
				}
	
				String queryString = query.getQueryString();// getQuerieString();
				String queryParam = query.getQueryParam();// getQuerieParam();
				Map<String, Object> parmMap = new HashMap<String, Object>();
				parmMap.put("UPDATE_QRY", queryString);
				parmMap.put("UPDATE_QRY_PARAMS", queryParam);
				parmMap.put("PARAM_VALUE_MAP", paramValueMap);
				int rowsAffected = updateRegulator.update(parmMap);
				logger.debug("rowsAffected : " + rowsAffected);
	
				if (rowsAffected > 0) {
					responseMsg = "Escalation Updated successfully";
				} else {
					responseMsg = "Error while Updating Escalation";
				}
			}
			dsResponse.setData(responseMsg);
		}

		// Delete EscalationMatrix
		if (action != null && action.equals("delete")) {

			paramValueMap.put("ACTIVE_INDEX", "N");
			
			if (userAdminManager.isUserUnderWorkflow(user)) {
				setUpEscalationMatrixDataToPersist(action,paramValueMap);
				paramValueMap.put(DS_NAME, requestParams.get("dsName"));
				activityDataMap.put("activity_data", paramValueMap);
				String comments = "Escalation for "+paramValueMap.get("ESCALATED_PERSON");
				userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
						ESCALATION_MATRIX, OPERATION_DELETE, activityDataMap, PENDING_APPROVAL, comments);
				responseMsg = "Escalation submitted for Approval Sucessfully";
			}
			else {
				UpdateRegulator updateRegulator = new UpdateRegulator();
				Queries queries = ascentWebMetaInstance.getWebQueryConfs();
				Query query = null;
	
				try {
					query = queries.getQueryConf(DELETE_ESCALATION_MATRIX);
				} catch (Exception e) {
	
					e.printStackTrace();
				}
	
				String queryString = query.getQueryString();// getQuerieString();
				String queryParam = query.getQueryParam();// getQuerieParam();
				Map<String, Object> parmMap = new HashMap<String, Object>();
				parmMap.put("UPDATE_QRY", queryString);
				parmMap.put("UPDATE_QRY_PARAMS", queryParam);
				parmMap.put("PARAM_VALUE_MAP", paramValueMap);
				int rowsAffected = updateRegulator.update(parmMap);
				logger.debug("rowsAffected : " + rowsAffected);
	
				if (rowsAffected > 0) {
					responseMsg = "Escalation Deleted successfully";
				} else {
					responseMsg = "Error while Deleting Escalation";
				}
			}

			dsResponse.setData(responseMsg);
		}

		connection.close();
		return dsResponse;
	}
	
	
	private void setUpEscalationMatrixDataToPersist(String action, Map<String, Object> dataMap) {
		Map<String, Object> map = (Map<String, Object>) dataMap.get("activity_data");

		updateVersion(map);
		updateVersion(dataMap);

		dataMap.put(PERSIST_CLASS, ESCALATIONMATRIX_CLASS_NAME);
		map.put(PERSIST_CLASS, ESCALATIONMATRIX_CLASS_NAME);
	}
	
	public void updateVersion(Map<String, Object> data) {
		Long version = Long.parseLong(data.get("VERSION").toString());
		if (version == null) {
			data.put("VERSION", 1);
		} else {
			version = version + 1;
			data.put("VERSION", version);
		}
	}
	
	
	public void activityPersistEscalation(Map<String, Object> dataMap, String actStatus, boolean isNew, boolean isWorkflow,
			Connection connection) throws Exception {
		
		if("APPROVED".endsWith(actStatus)){
			Map<String, Object> activity_data = (Map<String, Object>) dataMap.get("activity_data");
			saveEscalation(connection, activity_data);
		}
	}
	
	public String saveEscalation(Connection connection, Map<String, Object> paramValueMap){
		
		String responseMsg = null;

		InsertRegulator insertRegulator = new InsertRegulator();
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		Query query = null;

		try {
			query = queries.getQueryConf(SAVE_ESCALATION_MATRIX);
		} catch (Exception e) {

			e.printStackTrace();
		}

		String queryString = query.getQueryString();// getQuerieString();
		String queryParam = query.getQueryParam();// getQuerieParam();
		Map<String, Object> parmMap = new HashMap<String, Object>();
		parmMap.put("INSERT_QRY_PARAMS", queryParam);
		parmMap.put("INSERT_QRY", queryString);
		parmMap.put("PARAM_VALUE_MAP", paramValueMap);
		int rowsAffected = insertRegulator.insert(connection, parmMap);
		logger.debug("rowsAffected : " + rowsAffected);

		if (rowsAffected > 0) {
			responseMsg = "Escalation added successfully";
		} else {
			responseMsg = "Error while adding Escalation";
		}
		
		return responseMsg;
	}

}
