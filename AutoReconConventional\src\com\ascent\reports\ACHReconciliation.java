package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class ACHReconciliation {

	private static final String ACH_INTERNAL_RECONCILE_REPORT = "ACH_INTERNAL_RECONCILE_REPORT";
	private static final String ACH_INTERNAL_UNRECONCILE_REPORT = "ACH_INTERNAL_UNRECONCILE_REPORT";

	private static final String ACH_EXTERNAL_RECONCILE_REPORT = "ACH_EXTERNAL_RECONCILE_REPORT";
	private static final String ACH_EXTERNAL_UNRECONCILE_REPORT = "ACH_EXTERNAL_UNRECONCILE_REPORT";

	private static final String ACH_SUPPRESS_INTERNAL_REPORT = "ACH_SUPPRESS_INTERNAL_REPORT";
	private static final String ACH_SUPPRESS_EXTERNAL_REPORT = "ACH_SUPPRESS_EXTERNAL_REPORT";
	private static final String ACH_AGING_REPORT = "ACH_AGING_REPORT";
	
	private static final String ACH_INTERNAL_DRCR = "ACH_INTERNAL_DRCR";
	private static final String ACH_EXTERNAL_DRCR = "ACH_EXTERNAL_DRCR";
	
	private static final String ACH_SUMMARY = "ACH_SUMMARY";
	
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> achInternalReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_INTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("DRCR", rset.getString(3));
				map.put("EXTRACTION DATE", rset.getString(4));
				map.put("CHANNEL", rset.getString(5));
				map.put("END TO END ID", rset.getString(6));
				map.put("TRANID", rset.getString(7));
				map.put("TRAN DATE", rset.getString(8));
				map.put("VALUE DATE", rset.getString(9));
				map.put("TRAN AMOUNT", rset.getString(10));
				map.put("FILENAME", rset.getString(11));
				map.put("TAG 20", rset.getString(12));
				map.put("TAG 21", rset.getString(13));
				map.put("BANK", rset.getString(14));
				map.put("MSG GENERATED", rset.getString(15));
				map.put("COMMENTS", rset.getString(16));
				map.put("VERSION", rset.getString(17));
				map.put("ACTIVE INDEX", rset.getString(18));
				map.put("WORKFLOW STATUS", rset.getString(19));
				map.put("UPDATED ON", rset.getString(20));
				map.put("CREATED ON", rset.getString(21));
				map.put("RECON STATUS", rset.getString(22));
				map.put("RECON ID", rset.getString(23));
				map.put("ACTIVITY COMMENTS'", rset.getString(24));
				map.put("MAIN REV IND", rset.getString(25));
				map.put("OPERATION", rset.getString(26));
				map.put("BUSINESS AREA", rset.getString(27));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> achInternalUnReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_INTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("DRCR", rset.getString(3));
				map.put("EXTRACTION DATE", rset.getString(4));
				map.put("CHANNEL", rset.getString(5));
				map.put("END TO END ID", rset.getString(6));
				map.put("TRANID", rset.getString(7));
				map.put("TRAN DATE", rset.getString(8));
				map.put("VALUE DATE", rset.getString(9));
				map.put("TRAN AMOUNT", rset.getString(10));
				map.put("FILENAME", rset.getString(11));
				map.put("TAG 20", rset.getString(12));
				map.put("TAG 21", rset.getString(13));
				map.put("BANK", rset.getString(14));
				map.put("MSG GENERATED", rset.getString(15));
				map.put("COMMENTS", rset.getString(16));
				map.put("VERSION", rset.getString(17));
				map.put("ACTIVE INDEX", rset.getString(18));
				map.put("WORKFLOW STATUS", rset.getString(19));
				map.put("UPDATED ON", rset.getString(20));
				map.put("CREATED ON", rset.getString(21));
				map.put("RECON STATUS", rset.getString(22));
				map.put("RECON ID", rset.getString(23));
				map.put("ACTIVITY COMMENTS'", rset.getString(24));
				map.put("MAIN REV IND", rset.getString(25));
				map.put("OPERATION", rset.getString(26));
				map.put("BUSINESS AREA", rset.getString(27));
				map.put("AGE", rset.getString(28));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> achExternalReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_EXTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("STATUS", rset.getString(2));
				map.put("DIRECTION", rset.getString(3));
				map.put("TYPE", rset.getString(4));
				map.put("TRANSACTION ID", rset.getString(5));
				map.put("END TO END ID", rset.getString(6));
				map.put("INSTRUCTION ID", rset.getString(7));
				map.put("BATCH", rset.getString(8));
				map.put("CURRENCY", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("INSTRUCTING PARTICIPANT", rset.getString(11));
				map.put("INSTRUCTING BRANCH", rset.getString(12));
				map.put("INSTRUCTED PARTICIPANT", rset.getString(13));
				map.put("INSTRUCTED BRANCH", rset.getString(14));
				map.put("DEBTOR BANK", rset.getString(15));
				map.put("DEBTOR BRANCH", rset.getString(16));
				map.put("DEBTOR NAME", rset.getString(17));
				map.put("DEBTOR ACCOUNT", rset.getString(18));
				map.put("DEBTOR IBAN", rset.getString(19));
				map.put("CREDITOR BANK", rset.getString(20));
				map.put("CREDITOR BRANCH", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("UPDATED ON", rset.getString(23));
				map.put("DUE ON", rset.getString(24));
				map.put("DELETED ON", rset.getString(25));
				map.put("CREDITOR NAME", rset.getString(26));
				map.put("CREATED BY", rset.getString(27));
				map.put("UPDATED BY", rset.getString(28));
				map.put("LOCKED BY", rset.getString(29));
				map.put("DELETED BY", rset.getString(30));
				map.put("DELETED", rset.getString(31));
				map.put("CREDITOR ACCOUNT", rset.getString(32));
				map.put("CREDITOR IBAN", rset.getString(33));
				map.put("TRANSACTION PURPOSE", rset.getString(34));
				map.put("SETTLEMENT DATE", rset.getString(35));
				map.put("SESSION NO", rset.getString(36));
				map.put("REASON", rset.getString(37));
				map.put("ADDITIONAL INFO", rset.getString(38));
				map.put("RELATED REASON", rset.getString(39));
				map.put("RELATED ADDITIONAL INFO", rset.getString(40));
				map.put("MANDATE ", rset.getString(41));
				map.put("CANDIDATE PAYMENT", rset.getString(42));
				map.put("COMMENTS", rset.getString(43));
				map.put("VERSION", rset.getString(44));
				map.put("ACTIVE INDEX", rset.getString(45));
				map.put("WORKFLOW STATUS", rset.getString(46));
				map.put("RECON STATUS", rset.getString(47));
				map.put("RECON ID", rset.getString(48));
				map.put("ACTIVITY COMMENTS", rset.getString(49));
				map.put("MAIN REV IND", rset.getString(50));
				map.put("OPERATION", rset.getString(51));
				map.put("FILE NAME", rset.getString(52));
				map.put("BUSINESS AREA", rset.getString(53));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> achExternalUnReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_EXTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("STATUS", rset.getString(2));
				map.put("DIRECTION", rset.getString(3));
				map.put("TYPE", rset.getString(4));
				map.put("TRANSACTION ID", rset.getString(5));
				map.put("END TO END ID", rset.getString(6));
				map.put("INSTRUCTION ID", rset.getString(7));
				map.put("BATCH", rset.getString(8));
				map.put("CURRENCY", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("INSTRUCTING PARTICIPANT", rset.getString(11));
				map.put("INSTRUCTING BRANCH", rset.getString(12));
				map.put("INSTRUCTED PARTICIPANT", rset.getString(13));
				map.put("INSTRUCTED BRANCH", rset.getString(14));
				map.put("DEBTOR BANK", rset.getString(15));
				map.put("DEBTOR BRANCH", rset.getString(16));
				map.put("DEBTOR NAME", rset.getString(17));
				map.put("DEBTOR ACCOUNT", rset.getString(18));
				map.put("DEBTOR IBAN", rset.getString(19));
				map.put("CREDITOR BANK", rset.getString(20));
				map.put("CREDITOR BRANCH", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("UPDATED ON", rset.getString(23));
				map.put("DUE ON", rset.getString(24));
				map.put("DELETED ON", rset.getString(25));
				map.put("CREDITOR NAME", rset.getString(26));
				map.put("CREATED BY", rset.getString(27));
				map.put("UPDATED BY", rset.getString(28));
				map.put("LOCKED BY", rset.getString(29));
				map.put("DELETED BY", rset.getString(30));
				map.put("DELETED", rset.getString(31));
				map.put("CREDITOR ACCOUNT", rset.getString(32));
				map.put("CREDITOR IBAN", rset.getString(33));
				map.put("TRANSACTION PURPOSE", rset.getString(34));
				map.put("SETTLEMENT DATE", rset.getString(35));
				map.put("SESSION NO", rset.getString(36));
				map.put("REASON", rset.getString(37));
				map.put("ADDITIONAL INFO", rset.getString(38));
				map.put("RELATED REASON", rset.getString(39));
				map.put("RELATED ADDITIONAL INFO", rset.getString(40));
				map.put("MANDATE", rset.getString(41));
				map.put("CANDIDATE PAYMENT", rset.getString(42));
				map.put("COMMENTS", rset.getString(43));
				map.put("VERSION", rset.getString(44));
				map.put("ACTIVE INDEX", rset.getString(45));
				map.put("WORKFLOW STATUS", rset.getString(46));
				if(rset.getString(47)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(47));
				map.put("RECON ID", rset.getString(48));
				map.put("ACTIVITY COMMENTS", rset.getString(49));
				map.put("MAIN REV IND", rset.getString(50));
				map.put("OPERATION", rset.getString(51));
				map.put("FILE NAME", rset.getString(52));
				map.put("BUSINESS AREA", rset.getString(53));
				map.put("AGE", rset.getString(54));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> achInternalSuppress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_SUPPRESS_INTERNAL_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CDM ID", rset.getString(2));
				map.put("CDM BRANCH", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("CDM ACCOUNT", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				
				map.put("VERIFIER USER ID", rset.getString(30));
				map.put("VERIFIER COMMENTS", rset.getString(31));
				map.put("MAKER USER ID", rset.getString(32));
				map.put("MAKER COMMENTS", rset.getString(33));

				
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> achExternalSuppress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_SUPPRESS_EXTERNAL_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("TXNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CARDNUMBER", rset.getString(11));
				map.put("ACCOUNTNO1", rset.getString(12));
				map.put("ACCOUNTNAME", rset.getString(13));
				map.put("AMOUNT", rset.getString(14));
				map.put("NOTEDETAILS", rset.getString(15));
				map.put("CARDTAKEN", rset.getString(16));
				map.put("CARDCAPTURE", rset.getString(17));
				map.put("NOTESENCASHED", rset.getString(18));
				map.put("CASHRETRACT", rset.getString(19));
				map.put("RESPONSECODE", rset.getString(20));
				map.put("RESPONSEDESC", rset.getString(21));
				map.put("HARDWARESTATUS", rset.getString(22));
				map.put("COMMENTS", rset.getString(23));
				map.put("VERSION", rset.getString(24));
				map.put("ACTIVE INDEX", rset.getString(25));
				map.put("WORKFLOW STATUS", rset.getString(26));
				map.put("UPDATED ON", rset.getString(27));
				map.put("CREATED ON", rset.getString(28));
				map.put("RECON STATUS", rset.getString(29));
				map.put("RECON ID", rset.getString(30));
				map.put("ACTIVITY COMMENTS", rset.getString(31));
				map.put("MAIN REV IND", rset.getString(32));
				map.put("OPERATION'", rset.getString(33));
				map.put("FILE NAME", rset.getString(34));
				map.put("BUSINESS AREA", rset.getString(35));
				
				map.put("VERIFIER USER ID", rset.getString(36));
				map.put("VERIFIER COMMENTS", rset.getString(37));
				map.put("MAKER USER ID", rset.getString(38));
				map.put("MAKER COMMENTS", rset.getString(39));
				
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> AchAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_AGING_REPORT );
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> achInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> achExternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_EXTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> achsummary(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ACH_SUMMARY);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public static void main(String[] args) {
		ACHReconciliation a = new ACHReconciliation();
		a.achInternalReconcile("2018-01-01", "2018-10-01");
		a.achInternalUnReconcile("2018-01-01", "2018-10-01");
		a.achExternalReconcile("2018-01-01", "2018-10-01");
		a.achExternalUnReconcile("2018-01-01", "2018-10-01");
		a.achInternalSuppress("2018-01-01", "2018-10-01");
		a.achExternalSuppress("2018-01-01", "2018-10-01");
		a.achInternalDrcr("2018-01-01", "2018-10-01");
		a.achExternalDrcr("2018-01-01", "2018-10-01");
		a.AchAgingMethod();
	}
}
