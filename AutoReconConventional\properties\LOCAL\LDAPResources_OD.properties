#Directory Server
# OpenLDAP

#Domain
domain=example

# Host: ldap://***********:636
# Local Port : 389 SSL Port: 636

#host=ldap://***********:3989
host=ldap://*************:389

#SearchBase
#searchbase=DC=testdomain,DC=com
searchbase=DC=com

#Organisation Unit
ou=system

#Admin User & Password
diruser=cn=manager
dirpassword=secret


#SSL - Port should be 636
ssl=false
#keystorepath=D:\\Java\\jdk1.6.0_04\\jre\\lib\\security\\cacerts
keystorepath=C:\Program Files\Java\jdk1.8.0_45\jre\lib\security\\cacerts