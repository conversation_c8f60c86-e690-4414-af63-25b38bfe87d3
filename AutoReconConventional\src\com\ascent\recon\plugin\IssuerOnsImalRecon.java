package com.ascent.recon.plugin;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.recon.ReconMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.custumize.recon.Recon;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.ReconUtil;
import com.ascent.recon.AscentAutoReconPlugin;


public class IssuerOnsImalRecon extends AscentAutoReconPlugin{
	private static Logger logger = LogManager.getLogger(IssuerOnsImalRecon.class.getName());

	Queries reconQueriesConf = null;

	String reconIdSeqName = null;
	String reconTableIdSeqName = null;

	Query insertQryConf = null;
	Query updateQryConf = null;
	Query upstreamQryConf = null;

	Connection connection = null;

	ResultSet upstreamRs = null;

	PreparedStatement upstreamPstmt = null;
	PreparedStatement reconInsertPstmt = null;
	PreparedStatement reconUpdatePstmt = null;
	PreparedStatement reconTableIdGenPstmt = null;
	PreparedStatement reconIdGenPstmt = null;
	ReconMetaInstance reconMetaInstance = null;
	String reconTableName = null;

	public static long start = 01;
	// iriselect
	// audit insert

	// gl select,audit gl
	Map<String, Map<String, Object>> dynamicPstmtMap = new HashMap<String, Map<String, Object>>();

	public IssuerOnsImalRecon() {

		this.reconMetaInstance = ReconMetaInstance.getInstance();

	}

	public static void main(String[] args) {

		IssuerOnsImalRecon atmIssRecon = new IssuerOnsImalRecon();

	}

	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		int recCnt = 0;
		int batchSize = 20000;
		while (recCnt < batchSize && rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));

			}
			recCnt++;
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}

	public static long totalRetrievedRecs = 0l;

	public Map<String, Object> process(Recon recon) throws Exception {

		Map<String, Object> result = new HashMap<String, Object>();
		totalRetrievedRecs = 0l;
		this.reconQueriesConf = this.reconMetaInstance.getReconQueries(recon.getName());

		this.connection = DbUtil.getConnection();
			//connection.setAutoCommit(false);
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();

		try {
			start = System.currentTimeMillis();
			insertQryConf = reconQueriesConf.getQueryConf(recon.getReconInsertQueryName());
			upstreamQryConf = reconQueriesConf.getQueryConf(recon.getReconUpstreamQueryName());

			List<String> upstreamTableNameList = upstreamQryConf.getTargetTableNameList();

			if (upstreamTableNameList == null || upstreamTableNameList.size() < 0) {
				throw new Exception("Please configure the Upstream table names for Upstream query");
			}

			if (insertQryConf.getTargetTableNameList() == null || insertQryConf.getTargetTableNameList().size() <= 0) {
				throw new Exception("Please configure the recon table name for recon insert query");
			}

			reconTableName = insertQryConf.getTargetTableNameList().get(0);

			this.reconIdSeqName = reconTableName + "_SEQ";
			String reconIdGenQry = "SELECT NEXT VALUE FOR " + reconIdSeqName + " as sno";
			this.reconIdGenPstmt = this.connection.prepareStatement(reconIdGenQry);

			this.reconTableIdSeqName = reconTableName + "_ID_SEQ";
			String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";

			this.reconTableIdGenPstmt = this.connection.prepareStatement(reconTableIdGenQry);

			this.reconInsertPstmt = this.connection.prepareStatement(insertQryConf.getQueryString());

			String reconUpdateQryString = "UPDATE " + reconTableName
					+ " SET RECON_ID=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,COMMENTS=?,RULE_NAME=?,ACTIVITY_STATUS=?,STATUS=?"
					+ " WHERE SID=? and RECON_SIDE=?";
			String reconUpdateQryParams = "RECON_ID@BIGINT,MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,COMMENTS@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,STATUS@VARCHAR,SID@BIGINT,RECON_SIDE@VARCHAR";
			this.updateQryConf = new Query();
			this.updateQryConf.setQueryString(reconUpdateQryString);
			this.updateQryConf.setQueryParam(reconUpdateQryParams);
			this.updateQryConf.bootConf();
			this.reconUpdatePstmt = this.connection.prepareStatement(reconUpdateQryString);

			for (String upstreamTable : upstreamTableNameList) {

				String updateQuery = "UPDATE " + upstreamTable
						+ " SET RECON_ID=?,UPDATED_ON=?,VERSION=?,OPERATION=?,RECON_STATUS=? WHERE SID=?";

				String selectQry = "select * from " + upstreamTable + " where SID=?";

				// TODO: plugin to create audit Query
				Query stagingAuditInsertQryConf = reconQueriesConf.getQueryConf(upstreamTable + "_AUDIT_INSERT_QRY");

				PreparedStatement pstmt = connection.prepareStatement(updateQuery);
				PreparedStatement selectPstmt = connection.prepareStatement(selectQry);
				PreparedStatement stagingAuditInsertPstmt = connection
						.prepareStatement(stagingAuditInsertQryConf.getQueryString());

				Map<String, Object> auditPstmtMap = (Map<String, Object>) dynamicPstmtMap.get(upstreamTable);
				if (auditPstmtMap == null) {
					auditPstmtMap = new HashMap<String, Object>();
					dynamicPstmtMap.put(upstreamTable, auditPstmtMap);

				}

				auditPstmtMap.put("STG_AUDIT_INSERT_QUERY_CONF", stagingAuditInsertQryConf);

				auditPstmtMap.put("STG_UPDATE_PSTMT", pstmt);
				auditPstmtMap.put("STG_SLECT_PSTMT", selectPstmt);
				auditPstmtMap.put("STG_AUDIT_INSERT_PSTMT", stagingAuditInsertPstmt);

			}

			String upstreamQry = upstreamQryConf.getQueryString();
			this.upstreamPstmt = connection.prepareStatement(upstreamQry);
			this.upstreamRs = upstreamPstmt.executeQuery();
			recordsData.addAll(getRsNextBatch(upstreamRs));
			totalRetrievedRecs = recordsData.size();

			while (recordsData.size() > 0) {
				List<String> keys = new ArrayList<String>();

				keys.add("RET_REF_NO");
				keys.add("TRA_AMT");

				// System.out.println("Size" + recordsData.size() + "Total:" +
				// totalRetrievedRecs);
				if (recordsData.size() < 50) {
					List<Map<String, Object>> temp = getRsNextBatch(upstreamRs);
					if (temp != null && !temp.isEmpty()) {
						totalRetrievedRecs = totalRetrievedRecs + temp.size();
						recordsData.addAll(temp);
					}
				}

				List<Map<String, Object>> subGroup = ReconUtil.getGroup(recordsData, keys);
				recordsData.removeAll(subGroup);

				Map<String, List<Map<String, Object>>> recGroup = new HashMap<String, List<Map<String, Object>>>();

				recGroup.put("IRIS", new ArrayList<Map<String, Object>>());
				recGroup.put("CBO", new ArrayList<Map<String, Object>>());
				recGroup.put("ONS_IMAL", new ArrayList<Map<String, Object>>());

				for (Map<String, Object> rec : subGroup) {

					List<Map<String, Object>> sourceGroup = recGroup.get((String) rec.get("RECON_SIDE"));

					sourceGroup.add(rec);

				}

				if (recGroup.get("IRIS") != null && ((List<Map<String, Object>>) recGroup.get("IRIS")).size() > 0) {
					reconCheck(recGroup);
				}

			}

		} catch (Exception e) {
			//e.printStackTrace();
			logger.error(e.getMessage(), e);
		} finally {
			
			DbUtil.closeResultSet(upstreamRs);

			DbUtil.closePreparedStatement(reconIdGenPstmt);
			for (Map<String, Object> auditStmtMap : dynamicPstmtMap.values()) {

				if (auditStmtMap != null) {
					PreparedStatement pstmtUpdate = (PreparedStatement) auditStmtMap.get("STG_UPDATE_PSTMT");
					PreparedStatement pstmtSelect = (PreparedStatement) auditStmtMap.get("STG_SLECT_PSTMT");
					PreparedStatement pstmtAudit = (PreparedStatement) auditStmtMap.get("STG_AUDIT_INSERT_PSTMT");

				DbUtil.closePreparedStatement(pstmtUpdate);
				DbUtil.closePreparedStatement(pstmtSelect);
				DbUtil.closePreparedStatement(pstmtAudit);

				}

			}
			DbUtil.closePreparedStatement(reconUpdatePstmt);
			//DbUtil.closePreparedStatement(reconTableIdGenPstmt);
			DbUtil.closePreparedStatement(reconIdGenPstmt);
			DbUtil.closePreparedStatement(upstreamPstmt);
			DbUtil.closePreparedStatement(reconInsertPstmt);
		DbUtil.closeConnection(connection);
			logger.trace(System.currentTimeMillis() - start);
		}
		return result;
	}

	public void handleUncompatibleGroup(Map<String, List<Map<String, Object>>> recGroup) {

		List<Map<String, Object>> irisTxns = recGroup.get("IRIS");
		List<Map<String, Object>> qcbTxns = recGroup.get("CBO");
		List<Map<String, Object>> gl1015Txns = recGroup.get("ONS_IMAL");

		for (Map<String, Object> irisRec : irisTxns) {
			Map<String, List<Map<String, Object>>> recSubGroup = new HashMap<String, List<Map<String, Object>>>();

			List<Map<String, Object>> irisSubList = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> qcbSubList = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> glSubList = new ArrayList<Map<String, Object>>();

			recSubGroup.put("IRIS", irisSubList);
			recSubGroup.put("CBO", qcbSubList);
			recSubGroup.put("ONS_IMAL", glSubList);

			irisSubList.add(irisRec);
			Date irisDate = (Date) getParseDate((String)irisRec.get("TRA_DATE"));
			BigDecimal irisAmt = (BigDecimal) irisRec.get("TRA_AMT");

			for (Map<String, Object> qcbRec : qcbTxns) {
				Date qcbDate = (Date) getParseDate((String)qcbRec.get("TRA_DATE"));

				BigDecimal qcbAmt = (BigDecimal) qcbRec.get("TRA_AMT");

				//String qcbTime = (String) qcbRec.get("TRAN_TIME");
				//String irisTime = (String) irisRec.get("TRAN_TIME");

				String qcbCardNo = (String) qcbRec.get("PAN");
				String irisCardNo = (String) irisRec.get("PAN");

				// TRA_DATE,TRA_AMT,TRAN_TIME,CARD_NUMBER

				if (qcbDate.equals(irisDate) //&& qcbTime.equalsIgnoreCase(irisTime) COMMENT BY SHIVAM
						&& qcbCardNo.equalsIgnoreCase(irisCardNo) && qcbAmt.doubleValue() == irisAmt.doubleValue()) {
					qcbSubList.add(qcbRec);
				}

			}

			for (Map<String, Object> glRec : gl1015Txns) {
				Date glDate = (Date) glRec.get("TRA_DATE");
				BigDecimal glAmt = (BigDecimal) glRec.get("TRA_AMT");
				String glStan = (String) glRec.get("STAN");
				String irisStan = (String) irisRec.get("STAN");
				// TRA_DATE,TRA_AMT,STAN

				if (glDate.equals(irisDate) && glAmt.doubleValue() == irisAmt.doubleValue()
						&& glStan.equalsIgnoreCase(irisStan) 
						) {
					glSubList.add(glRec);
				}

			}

			reconCheck(recSubGroup);
		}
		recGroup.clear();
	}

	public void reconCheck(Map<String, List<Map<String, Object>>> recGroup) {

		List<Map<String, Object>> iris = recGroup.get("IRIS");
		List<Map<String, Object>> qcb = recGroup.get("CBO");
		List<Map<String, Object>> gl1015 = recGroup.get("ONS_IMAL");

		//String respCode = (String) iris.get(0).get("RESP_CODE"); COMMENT BY SHIVAM

		Map<String, Object> qcbMainRec = null;
		Map<String, Object> qcbRevRec = null;

		Map<String, Object> gl1015MainRec = null;
		Map<String, Object> gl1015RevRec = null;

		for (Map<String, Object> rec : qcb) {
			/*if (ReconUtil.qcbReversalCodes.contains((String) rec.get("TRAN_TYPE"))) {
				qcbRevRec = rec;
			} else {
				qcbMainRec = rec;
			} COMMENT BY SHIVAM*/
			qcbMainRec = rec;
		}

		for (Map<String, Object> rec : gl1015) {
			/*if ("CREDIT".equalsIgnoreCase((String) rec.get("DEB_CRE_IND"))) {
				gl1015MainRec = rec;
			} else {
				gl1015RevRec = rec;
			}*/
			gl1015MainRec = rec;
		}

		if (iris.size() > 1) {

			handleUncompatibleGroup(recGroup);

		} //else if (respCode != null && ReconUtil.irisMainCodes.contains(respCode)) { COMMENT BY SHIVAM

			List<Map<String, Object>> qcbMainList = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> gl1015MainList = new ArrayList<Map<String, Object>>();

			if (qcbMainRec != null) {
				qcbMainList.add(qcbMainRec);
			}

			if (gl1015MainRec != null) {
				gl1015MainList.add(gl1015MainRec);
			}

			recGroup.put("CBO", qcbMainList);
			recGroup.put("ONS_IMAL", gl1015MainList);

			qcb = recGroup.get("CBO");
			gl1015 = recGroup.get("ONS_IMAL");

			if (iris.size() == 1 && qcb.size() == 1 && gl1015.size() == 1) {

				//Long irisTime = (Long) iris.get(0).get("TIME_IN_MILLIS");
				//Long qcbTime = (Long) qcb.get(0).get("TIME_IN_MILLIS");
				//Long gl1015Time = (Long) gl1015.get(0).get("TIME_IN_MILLIS");
				String irisCardNo = (String) iris.get(0).get("PAN");
				String irisStan = (String) iris.get(0).get("STAN");
				String glStan = (String) gl1015.get(0).get("STAN");
				String qcbCardNo = (String) qcb.get(0).get("PAN");
/*
				if (Math.abs(irisTime - qcbTime) <= ReconUtil.onedayMilliSec
						&& Math.abs(irisTime - gl1015Time) <= ReconUtil.onedayMilliSec) {
*/
					if (irisCardNo.equalsIgnoreCase(qcbCardNo) && irisStan.equalsIgnoreCase(glStan)) {
						ReconUtil.updateStatus(recGroup, ReconUtil.AM, "DATE,AMOUNT,RRN MATCH RULE SUCCESSFUL",
								"DATE,AMOUNT,RRN MATCH RULE");
					} else if (!irisCardNo.equalsIgnoreCase(qcbCardNo)) {
						recGroup.get("CBO").remove(qcb.get(0));
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, "MISSING TXNS QCB", "MISSING TXN");
					} else if (!irisStan.equalsIgnoreCase(glStan)) {
						recGroup.get("ONS_IMAL").remove(gl1015.get(0));
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, "MISSING TXNS GL_1015", "MISSING TXN");
					} else {
						recGroup.get("CBO").remove(qcb.get(0));
						recGroup.get("ONS_IMAL").remove(gl1015.get(0));
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, "MISSING TXNS QCB,GL_1015", "MISSING TXN");
					}

				/*//} /*else {

					StringBuilder commentsSb = new StringBuilder();

					if (irisCardNo.equalsIgnoreCase(qcbCardNo) && irisStan.equalsIgnoreCase(glStan)) {

						ReconUtil.validateTime(recGroup, irisTime, qcbTime, qcb.get(0), commentsSb, "QCB", "QCB");
						ReconUtil.validateTime(recGroup, irisTime, gl1015Time, gl1015.get(0), commentsSb, "GL_1015",
								"GL_1015");

						String comment = "DATE TIME MISSMATCH BETWEEN IRIS & " + commentsSb.toString();
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment, "DATE TIME MISSMATCH RULE");
					}

					else if (!irisCardNo.equalsIgnoreCase(qcbCardNo)) {
						recGroup.get("QCB").remove(qcb.get(0));
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, "MISSING TXNS QCB", "MISSING TXN");
					} else if (!irisStan.equalsIgnoreCase(glStan)) {
						recGroup.get("GL_1015").remove(gl1015.get(0));
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, "MISSING TXNS GL_1015", "MISSING TXN");
					} else {
						recGroup.get("QCB").remove(qcb.get(0));
						recGroup.get("GL_1015").remove(gl1015.get(0));
						ReconUtil.updateStatus(recGroup, ReconUtil.AU, "MISSING TXNS QCB,GL_1015", "MISSING TXN");
					}

				}*/

			} else {
				StringBuilder commentsSb = new StringBuilder();
				String irisCardNo = (String) iris.get(0).get("PAN");
				String irisStan = (String) iris.get(0).get("STAN");
				String qcbCardNo = null;
				String glStan = null;
				boolean cardNumBoolean = true;
				boolean glStanBoolean = true;

				if (qcb.size() != 0) {

					qcbCardNo = (String) qcb.get(0).get("PAN");
				}
				if (gl1015.size() != 0) {

					glStan = (String) gl1015.get(0).get("STAN");
				}

				if (qcb.size() == 0) {
					if (commentsSb.length() != 0) {
						commentsSb.append(ReconUtil.COMMA);
					}

					commentsSb.append("CBO");
				}
				if (gl1015.size() == 0) {
					if (commentsSb.length() != 0) {
						commentsSb.append(ReconUtil.COMMA);
					}

					commentsSb.append("ONS_IMAL");
				}

				String comment = "MISSING TXNS " + commentsSb.toString();
				if (qcbCardNo != null && irisCardNo.equalsIgnoreCase(qcbCardNo)) {
					cardNumBoolean = false;
					ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment, "MISSING TXNS RULE");
				}
				if (glStan != null && irisStan.equalsIgnoreCase(glStan)) {
					glStanBoolean = false;
					ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment, "MISSING TXNS RULE");
				}
				if (qcbCardNo == null && glStan == null) {
					ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment, "MISSING TXNS RULE");
				}

				if (qcbCardNo != null && glStan == null && cardNumBoolean) {
					recGroup.get("CBO").remove(qcb.get(0));

				}
				if (glStan != null && qcbCardNo == null && glStanBoolean) {
					recGroup.get("GL_1015").remove(gl1015.get(0));
				}

			}

		//} 
		ReconUtil.insertRecon(recGroup, this.connection, this.reconIdGenPstmt, reconIdSeqName, reconTableIdGenPstmt,
				reconTableIdSeqName, reconInsertPstmt, reconUpdatePstmt, insertQryConf, updateQryConf, dynamicPstmtMap,
				reconTableName, "87");

	}
	private Date getParseDate(String date){
		SimpleDateFormat sdf=new SimpleDateFormat("yyy-MM-dd");
		Date d=null;
		try {
			d=sdf.parse(date);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return d;
	}
}
