package com.ascent.admin.authorize;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.administration.AuthorizationBean;
import com.ascent.ds.operations.CreateGLEntryPlugIn;
import com.ascent.ds.operations.CreateGlExceptionPlugIn;
import com.ascent.ds.operations.CreateVisaEntryPlugin;
import com.ascent.ds.operations.ForceMatchPlugIn;
import com.ascent.ds.operations.ForceUnMatchPlugIn;
import com.ascent.ds.operations.ManualExternalEntries;
import com.ascent.ds.operations.ManualRecon;
import com.ascent.ds.operations.MarkOrphanPlugIn;
import com.ascent.ds.operations.ReProcessPlugIn;
import com.ascent.ds.operations.SameSideReversalPlugIn;
import com.ascent.ds.operations.SuppressPlugIn;
import com.ascent.ds.operations.UndoSuppressPlugIn;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.Privilege;
import com.ascent.service.dto.User;
import com.ascent.service.dto.UserController;
import com.ascent.util.PagesConstants;

public class UserAdminManager implements PagesConstants, Serializable {

	private static final long serialVersionUID = -6932307237369681727L;

	private static Object waitObject = new Object();

	private UserController userController = null;
	private static UserAdminManager authorizationManager;

	public static UserAdminManager getAuthorizationManagerSingleTon() {

		try {

			authorizationManager = new UserAdminManager();

		} catch (Exception e) {
			e.printStackTrace();
		} catch (Throwable t) {
			t.printStackTrace();
		} finally {
			// waitObject.notifyAll();
		}

		return authorizationManager;
	}

	private UserAdminManager() {

		CustomerDao customerDao = new CustomerDao();

		try {
			this.userController = customerDao.loadAllUserPrivilege(); // loadAllUserPrivilege();
			// setUserData(userData);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	public UserController getUsercontroller() throws Exception {
		System.out.println(userController.getUsers());
		if (userController == null) {
			CustomerDao customerDao = new CustomerDao();
			this.userController = customerDao.loadAllUserPrivilege();
		}
		if (userController.getUsers() == null) {
			userController.init();
		}

		return userController;
	}

	public void setUsercontroller(UserController usercontroller) {
		this.userController = usercontroller;
	}

	public boolean featureAuthorization() {
		boolean flag = false;

		try {

		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	public boolean isFeatureAuthorized(String moduleName, String operation, User user) {

		boolean flag = false;
		try {
			String userRole = user.getSystemRole();
			Privilege privilege = this.userController.getPrivileges().getPrivilege(userRole);
			flag = privilege.accessibility(moduleName, operation);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	public boolean isActivityApproved(Integer activityid, User user) {

		boolean flag = false;
		try {
			String userRole = user.getSystemRole();
			Privilege privilege = this.userController.getPrivileges().getPrivilege(userRole);
			// flag = privilege.accessibility(moduleName, operation);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	public boolean isUserUnderWorkflow(User user) {
		boolean flag = false;
		try {
			if (user.getApprovalRole() != null && !((user.getApprovalRole()).contains("Approver"))) {
				flag = true;
			} else {
				flag = false;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	public Map<String, Object> getActivity(Map<String, Object> dataMap)
			throws ClassNotFoundException, SQLException, IOException {
		CustomerDao customerDao = new CustomerDao();
		List<Map<String, Object>> activityDataList = null;
		try {
			activityDataList = customerDao.getData(dataMap, "GetActivity");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (activityDataList != null && !activityDataList.isEmpty() && activityDataList.size() > 0) {
			dataMap = activityDataList.get(0);

		} else {
			dataMap = new HashMap<String, Object>();
		}

		ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) dataMap.get("activity_data"));
		ObjectInputStream ois = new ObjectInputStream(bis);
		Object obj = ois.readUnshared();
		bis.close();
		ois.close();
		Map<String, Object> actData = (Map<String, Object>) obj;
		dataMap.put("activity_data", actData);
		return dataMap;
	}

	public List<Map<String, Object>> getActivityAudit(long activityID)
			throws ClassNotFoundException, SQLException, IOException {

		CustomerDao customerDao = new CustomerDao();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("activity_id", activityID);
		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, "GetActivityAudit");

		if (activityDataList != null && !activityDataList.isEmpty()) {

			for (Map<String, Object> data : activityDataList) {
				if (data.get("activity_data") != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();
					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}

		return activityDataList;
	}

	public List<Map<String, Object>> getActivitiesForUser(User user)
			throws ClassNotFoundException, SQLException, IOException {
		CustomerDao customerDao = new CustomerDao();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("user", user.getUserId());
		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, "GetActivityForUser");
		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();
					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}
		return activityDataList;
	}

	public List<Map<String, Object>> getPendingActivitiesForUser(User user, String recon)
			throws ClassNotFoundException, SQLException, IOException {
		CustomerDao customerDao = new CustomerDao();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		//dataMap.put("user", user.getUserName());
		dataMap.put("user", user.getUserId());
		dataMap.put("recon", recon);
		//dataMap.put("recon", user.getReconString());
		List<Map<String, Object>> activityDataList = null;
		if (!user.getReconString().equals("''")) {
			System.out.println("********** RECONSTRING ********* IS EMPty...");
			activityDataList = customerDao.getPendingActivityData(dataMap, "GetPendigActivityForUser");

		} else {
			System.out.println("********** RECONSTRING ********* IS ! EMPty...");
			activityDataList = customerDao.getData(dataMap, "GetPendigActivityForUser");
		}

		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();
					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}

		}
		return activityDataList;
	}

	public List<Map<String, Object>> getApprovedActivitiesForUser(User user)
			throws ClassNotFoundException, SQLException, IOException {
		CustomerDao customerDao = new CustomerDao();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("user", user.getUserId());
		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, "GetApprovedActivityForUser");
		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();
					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}
		return activityDataList;
	}

	public List<Map<String, Object>> getRejectedActivitiesForUser(User user)
			throws ClassNotFoundException, SQLException, IOException {
		CustomerDao customerDao = new CustomerDao();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("user", user.getUserId());
		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, "GetRejectedActivityForUser");
		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();
					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}
		return activityDataList;
	}

	public List<Map<String, Object>> getActivityByUser(User user, String recon)
			throws ClassNotFoundException, SQLException, IOException {
		CustomerDao customerDao = new CustomerDao();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("activity_owner", user.getUserId());
		dataMap.put("recon", recon);
		
		List<Map<String, Object>> activityDataList = customerDao.getData(dataMap, "GetActivityByUser");
		if (activityDataList != null && !activityDataList.isEmpty()) {
			for (Map<String, Object> data : activityDataList) {
				if (data.get("activity_data") != null) {
					ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) data.get("activity_data"));
					ObjectInputStream ois = new ObjectInputStream(bis);
					Object obj = ois.readUnshared();
					bis.close();
					ois.close();

					Map<String, Object> actData = (Map<String, Object>) obj;
					data.put("activity_data", actData);
				}
			}
		}
		return activityDataList;
	}

	public String processActivity(Long activityId, Integer currentActivityLevel, String action, String userId,
			String comment, Map<String, Object> reSubmittedData) throws Exception {

		String status = EMPTY_STRING;
		CustomerDao customerDao = new CustomerDao();
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);
			Map<String, Object> dataMap = new HashMap<String, Object>();
			if (reSubmittedData == null) {
				dataMap.put("activity_id", activityId);
				dataMap.put("activity_level", currentActivityLevel);
				dataMap = getActivity(dataMap);
			} else {
				dataMap = reSubmittedData;
				dataMap.put("activity_id", activityId);
				dataMap.put("activity_name", reSubmittedData.get("activity_name"));
				dataMap.put("activity_type", reSubmittedData.get("activity_type"));
				dataMap.put("allowed_approvers", reSubmittedData.get("allowed_approvers"));
				dataMap.put("activity_owner", reSubmittedData.get("activity_owner"));
				dataMap.put("active_index", reSubmittedData.get("active_index"));
				dataMap.put("business_area", reSubmittedData.get("business_area"));
				dataMap.put("recon", reSubmittedData.get("recon"));
				dataMap.put("created_on", reSubmittedData.get("created_on"));
			}
			dataMap.put("activity_level", currentActivityLevel + 1);
			dataMap.put("version", currentActivityLevel + 1);
			dataMap.put("comment", comment);
			User userObj = this.userController.getUsers().getUser(userId);
			if (userObj.getReporting() != null && !EMPTY_STRING.equalsIgnoreCase(userObj.getReporting())
					&& RE_SUBMIT.equalsIgnoreCase(action)) {
				if (action != null && REJECTED.equalsIgnoreCase(action)) {
					status = REJECTED;
				} else {
					status = PENDING_APPROVAL;
				}
			} else {
				if (action != null && REJECTED.equalsIgnoreCase(action)) {
					status = REJECTED;
				} else {
					status = APPROVED;
				}
			}
			Date d = new Date();
			String date = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(d);
			dataMap.put("status", status);
			dataMap.put("recent_actor", userObj.getUserId());
			dataMap.put("allowed_approvers", userObj.getReporting());
			dataMap.put("recently_updated_on", date);
			Map<String, Object> activityData = (Map<String, Object>) dataMap.get("activity_data");

			String integrationName = (String) activityData.get("INTEGRATION_NAME");
			if (APPROVED.equalsIgnoreCase(status) || REJECTED.equalsIgnoreCase(status)
					|| RE_SUBMIT.equalsIgnoreCase(action)) {
				String persist_class = (String) activityData.get(PERSIST_CLASS);
				if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.ReProcessPlugIn")) {

					try {
						ReProcessPlugIn reProcessPlugIn = (ReProcessPlugIn) Class
								.forName("com.ascent.ds.operations.ReProcessPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							reProcessPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							reProcessPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.ManualExternalEntries")) {// kaushal
					try {
						@SuppressWarnings("unused")
						ManualExternalEntries manualRecon = (ManualExternalEntries) Class
								.forName("com.ascent.ds.operations.ManualExternalEntries").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							ManualExternalEntries.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							ManualExternalEntries.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					} // kaushal
				}
				// for internal manual Entries//kaushal
				else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.ManualRecon")) {// kaushal
					try {
						@SuppressWarnings("unused")
						ManualRecon manualRecon = (ManualRecon) Class.forName("com.ascent.ds.operations.ManualRecon")
								.newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							ManualRecon.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							ManualRecon.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					} // kaushal
				}
				
				
				
				
				
				
				
				
				
				
				
				
				
				
				
				
				
				
				else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.SuppressPlugIn")) {
					try {
						SuppressPlugIn supressPlugIn = (SuppressPlugIn) Class
								.forName("com.ascent.ds.operations.SuppressPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							supressPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							supressPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.UndoSuppressPlugIn")) {
					try {
						UndoSuppressPlugIn undoSuppressPlugIn = (UndoSuppressPlugIn) Class
								.forName("com.ascent.ds.operations.UndoSuppressPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							undoSuppressPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							undoSuppressPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.MarkOrphanPlugIn")) {
					try {
						MarkOrphanPlugIn markOrphanPlugIn = (MarkOrphanPlugIn) Class
								.forName("com.ascent.ds.operations.MarkOrphanPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							markOrphanPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							markOrphanPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.ForceUnMatchPlugIn")) {
					try {
						ForceUnMatchPlugIn forceUnMatchPlugIn = (ForceUnMatchPlugIn) Class
								.forName("com.ascent.ds.operations.ForceUnMatchPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							forceUnMatchPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							forceUnMatchPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.ForceMatchPlugIn")) {

					try {
						ForceMatchPlugIn forceMatchPlugIn = (ForceMatchPlugIn) Class
								.forName("com.ascent.ds.operations.ForceMatchPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							forceMatchPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							forceMatchPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.CreateGLEntryPlugIn")) {
					try {
						CreateGLEntryPlugIn createGLEntryPlugIn = (CreateGLEntryPlugIn) Class
								.forName("com.ascent.ds.operations.CreateGLEntryPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							createGLEntryPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							createGLEntryPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.SameSideReversalPlugIn")) {
					try {
						SameSideReversalPlugIn sameSideReversalPlugIn = (SameSideReversalPlugIn) Class
								.forName("com.ascent.ds.operations.SameSideReversalPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							sameSideReversalPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							sameSideReversalPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.CreateGlExceptionPlugIn")) {
					try {
						CreateGlExceptionPlugIn createGlExceptionPlugIn = (CreateGlExceptionPlugIn) Class
								.forName("com.ascent.ds.operations.CreateGlExceptionPlugIn").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							createGlExceptionPlugIn.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							createGlExceptionPlugIn.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.ds.operations.CreateVisaEntryPlugin")) {

					try {
						CreateVisaEntryPlugin createVisaEntryPlugin = (CreateVisaEntryPlugin) Class
								.forName("com.ascent.ds.operations.CreateVisaEntryPlugin").newInstance();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							createVisaEntryPlugin.persist(dataMap, action, connection);
						} else {
							dataMap.put("userId", userId);
							createVisaEntryPlugin.persist(dataMap, actStatus, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.service.dto.Role")) {
					try {
						AuthorizationBean authorizationBean = new AuthorizationBean();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							authorizationBean.persistRole(dataMap, actStatus, false, false, connection);// persist(dataMap,
																										// action,connection);

						} else {
							dataMap.put("userId", userId);
							authorizationBean.persistRole(dataMap, actStatus, false, false, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} // com.ascent.service.dto.Department
				else if (persist_class.equalsIgnoreCase("com.ascent.service.dto.Department")) {
					try {
						AuthorizationBean authorizationBean = new AuthorizationBean();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							authorizationBean.persistDepartment(dataMap, actStatus, false, false, connection);// persist(dataMap,
																												// action,connection);

						} else {
							dataMap.put("userId", userId);
							authorizationBean.persistDepartment(dataMap, actStatus, false, false, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

				} else if (persist_class.equalsIgnoreCase("com.ascent.service.dto.User")) {
					try {
						AuthorizationBean authorizationBean = new AuthorizationBean();
						String actStatus = (String) dataMap.put("status", status);

						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							authorizationBean.persistUsers(dataMap, actStatus, false, false, connection);

						} else {
							dataMap.put("userId", userId);
							authorizationBean.persistUsers(dataMap, actStatus, false, false, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else if (persist_class.equalsIgnoreCase("com.ascent.service.dto.Privilege")) {
					String privilegeId = (String) activityData.get("privilegeId");
					dataMap.put("privilegeId", privilegeId);
					try {
						AuthorizationBean authorizationBean = new AuthorizationBean();
						String actStatus = (String) dataMap.put("status", status);
						if (RE_SUBMIT.equalsIgnoreCase(action)) {
							authorizationBean.persistPrivilege(dataMap, actStatus, false, false, connection);// persist(dataMap,
																												// action,connection);

						} else {
							dataMap.put("userId", userId);
							authorizationBean.persistPrivilege(dataMap, actStatus, false, false, connection);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

				}

			}
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(bos);
			oos.writeUnshared(activityData);
			oos.flush();
			oos.close();
			byte[] actData = bos.toByteArray();
			dataMap.put("activity_data", actData);
			bos.close();
			// set previous activity data to 'null' in escrow activity data flow
			Map<String, Object> paramValues = new HashMap<String, Object>();
			paramValues.put("activity_id", activityId);
			// paramValues.put("activity_data", null);
			customerDao.update(connection, paramValues, "SetActivityDataReconActivity");
			customerDao.insertData(connection, dataMap, "CreateActivity");
			try {
				connection.commit();
			} catch (Exception e) {
				e.printStackTrace();
			} catch (Throwable e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
			try {
				connection.rollback();
			} catch (Exception e1) {
				e1.printStackTrace();// set statu message as not persist entity
			} catch (Throwable e1) {
				e1.printStackTrace();
			}
			throw new Exception("Unsuccessful Activity");
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			} catch (Throwable e) {
				e.printStackTrace();
			}
		}
		return status;
	}

	public boolean createActivity(Connection connection, User user, String businessArea, String reconName,
			String module, String operation, Map<String, Object> dataMap, String activityStatus, String comments) {

		boolean flag = false;
		LoadRegulator loadRegulator = new LoadRegulator();
		try {
			CustomerDao customerDao = new CustomerDao();
			Long id = System.currentTimeMillis();
			id = id + (Long) loadRegulator.generateLazySeqNo("recon_activity_flow_seq");
			dataMap.put("activity_id", id);
			dataMap.put("activity_name", module);
			dataMap.put("activity_owner", user.getUserId());
			dataMap.put("recent_actor", user.getUserId());
			dataMap.put("allowed_approvers", user.getReporting());
			/*
			 * if(!module.equalsIgnoreCase(MODULE_USER_ADMIN)){
			 * dataMap.put("allowed_approvers",user.
			 * getAllAllowedUserNamesForApproval()); }else{
			 * dataMap.put("allowed_approvers",user.getReporting()); }
			 */
			dataMap.put("activity_type", operation);
			dataMap.put("status", activityStatus);
			dataMap.put("active_index", "Y");
			Date d = new Date();
			//java.sql.Timestamp time=new java.sql.Timestamp(d.getTime());
			String date = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(d);
			dataMap.put("created_on", date);
			dataMap.put("recently_updated_on", date);
			dataMap.put("activity_level", 1);
			dataMap.put("version", 1);
			dataMap.put("recon", reconName);
			dataMap.put("business_area", businessArea);
			dataMap.put("comment", comments);
			Map<String, Object> activityData = (Map<String, Object>) dataMap.get("activity_data");

			// TO SHOW transaction_amount of centrifugal data in
			// ACTIVITY_MANAGER TAB
			// if(operation.equals("FORCE_MATCH")
			// ||operation.equals("FORCE_UNMATCH")){
			dataMap.put("transaction_amount", activityData.get("centrifugalAmountField"));
			// }
			/*
			 * else{ dataMap.put("transaction_amount",activityData.get(
			 * "centrifugalAmountField")); }
			 */
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(bos);
			oos.writeUnshared(activityData);
			oos.flush();
			oos.close();
			byte[] actData = bos.toByteArray();
			dataMap.put("activity_data", actData);
			bos.close();
			customerDao.insertData(connection, dataMap, "CreateActivity");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	public static void main(String args[]) {
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		System.out.println(userAdminManager);
	}
	
}