package com.ascent.service;



import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class WindowsProcess
{
public static void main(String args[]){
	WindowsProcess windowsProcess=new WindowsProcess("Tomcat7.exe");
	try {
		windowsProcess.kill();
	//	windowsProcess.startProcess();
	} catch (Exception e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
}
	private String processName;

    public WindowsProcess(String processName)
    {
        this.processName = processName;
    }
public void startProcess(){
	 try {
		getRuntime().exec("cmd /c net start \"Tomcat7\"");
	} catch (IOException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
}
    public void kill() throws Exception
    {
        if (isRunning())
        {
            getRuntime().exec("taskkill /F /IM " + processName);
        }
    }

    private boolean isRunning() throws Exception
    {
        Process listTasksProcess = getRuntime().exec("tasklist");
        BufferedReader tasksListReader = new BufferedReader(
                new InputStreamReader(listTasksProcess.getInputStream()));

        String tasksLine;

        while ((tasksLine = tasksListReader.readLine()) != null)
        {
            if (tasksLine.contains(processName))
            {
                return true;
            }
        }

        return false;
    }

    private Runtime getRuntime()
    {
        return Runtime.getRuntime();
    }
}
