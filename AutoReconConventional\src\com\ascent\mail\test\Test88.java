package com.ascent.mail.test;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;



public class Test88 {
	
	/*List<Map<String,Object>> list= new ArrayList<Map<String,Object>>();
	public double min_balance= 2000.0;*/
	public static String recepient= "<EMAIL>";
	
	public void sendMailWithAttachments(String subject,String masssageformate, String to, String[] attachFiles) throws IOException {

		Properties prop = new Properties();
		String propFileName = "mail.properties";
		InputStream fileextraction = getClass().getClassLoader().getResourceAsStream(propFileName);
		prop.load(fileextraction);
		String username = prop.getProperty("fromUserName");
		String password = prop.getProperty("fromUserPassword");

		// Get system properties
		Properties properties = System.getProperties();

		String SMTPHost = prop.getProperty("SMTPHost");
		String SMTPPort = prop.getProperty("SMTPPort");
		// Setup mail server
		properties.setProperty("mail.smtp.host", SMTPHost);
		properties.setProperty("mail.smtp.auth", "true");
		/*properties.setProperty("mail.smtp.starttls.enable", "true");
		properties.setProperty("mail.smtp.ssl.trust", "smtp.gmail.com");*/
		properties.setProperty("mail.smtp.port", SMTPPort);

		Session session = Session.getInstance(properties, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, password);
			}
		});
		try {
			
			MimeMessage message = new MimeMessage(session);
			message.setFrom(new InternetAddress(username));
			message.addRecipient(Message.RecipientType.TO,new InternetAddress(to));
			message.setSubject(subject);

			Multipart multipart = new MimeMultipart();
			 
			MimeBodyPart messageBodyPart = new MimeBodyPart();
			messageBodyPart.setContent(masssageformate, "text/html");

			multipart.addBodyPart(messageBodyPart);
			
			if (attachFiles != null && attachFiles.length > 0) {
	            for (String filePath : attachFiles) {
	                MimeBodyPart attachPart = new MimeBodyPart();
	                try {
	                	  if(filePath != null){
	  	                	
	                		  attachPart.attachFile(filePath);
	  	                }    	
	                } catch (IOException ex) {
	                    ex.printStackTrace();
	                }
	 
	                multipart.addBodyPart(attachPart);
	            }
	        }
			message.setContent(multipart);

			// Send message
			Transport.send(message);
			System.out.println("Mail sent successfully....");
			for(String f: attachFiles)
			{
				File fileDelete= new File(f);
				if(fileDelete.exists()){
					fileDelete.delete();
				}
			}
		} 
		catch (MessagingException mex) {
			mex.printStackTrace();
		}
	}
	
	public String messageFormat()
	{
		String content = "Dear Sir/Madam, <br> <br> <br> Please find the attachment.<br> <br>";
				
				
				String resgrda= "</table> <br><br><br> Regards,<br> Auto Recon <br> </div></center> <br/>";

				String format = "<html>" + "<body>" + content + resgrda + "<html>" + "<body>";            

		 //String format = "<html>" + "<body>" + content + "<html>" + "<body>";

		//System.out.println("dataToAppendCaseTable   " + dataToAppendCaseTable);
		
		return format;
		
	
	}

	
}



			