package com.ascent.ds.operations;

import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.LoadRegulator;

public class CaseManagementPendingScheduler {
	
	private static final String GET_PENDING_CASES = "GET_PENDING_CASES";
	
	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	LoadRegulator loadRegulator = new LoadRegulator();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();

	public static void main(String[] args) {
		new CaseManagementPendingScheduler().schedulePendingCases();
	}
	
	
	private void schedulePendingCases() {
		
		Query queryConf = null;
		List<Map<String, Object>> pendingCasesList = null;
		
		try {
			queryConf = queryConfs.getQueryConf(GET_PENDING_CASES);
			pendingCasesList = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			
			
			for (Map<String, Object> caseMap : pendingCasesList) {
				
				System.out.println(caseMap.get("activity_data"));
				
				ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) caseMap.get("activity_data"));
				ObjectInputStream ois = new ObjectInputStream(bis);
				Object obj = ois.readUnshared();
				bis.close();
				ois.close();

				@SuppressWarnings("unchecked")
				List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) ((Map<String, Object>) obj).get("SELECTED_RECORDS");
				
				System.out.println(selectedRecords);
				
				//Mail Trigger Start
				
				String subject = "Case Management Pending ("+(caseMap.get("recon").toString())+") : "+caseMap.get("case_id");
				String content = "Dear Sir/Madam, <br> <br> <br> The captioned request has Pending from your side for further action. <br> <br>";
				
				Map<String,String> map = new HashMap<String,String>();
				map.put("subject", subject);
				map.put("content", content);
				map.put("toMail", caseMap.get("EMAIL_ID").toString());
				
				new CaseMailTrigger().setContentMailTrigger(selectedRecords,map);
				
				//Mail Trigger End
				
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

}
