//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.8-b130911.1802 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.11.03 at 07:49:11 PM IST 
//

package com.ascent.custumize.recon;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Recon" type="{}Recon" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "recon" })
@XmlRootElement(name = "Recons")
public class Recons {

	@XmlElement(name = "Recon", required = true)
	protected List<Recon> recon;
	
	@XmlTransient
	protected Map<String,Recon> reconConfMap;

	/**
	 * Gets the value of the recon property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the recon property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getRecon().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Recon }
	 * 
	 * 
	 */
	public List<Recon> getRecon() {
		if (recon == null) {
			recon = new ArrayList<Recon>();
		}
		return this.recon;
	}

	public void bootConf() throws Exception {
		reconConfMap=new HashMap<String, Recon>();
		if (recon != null && !recon.isEmpty()) {
			for (Recon rec : recon) {
				rec.bootConf();
				reconConfMap.put(rec.getName(), rec);
			}
		}

	}
	public Recon getReconConf(String reconConfName) {
		return reconConfMap.get(reconConfName); 
	}

	public Map<String, Recon> getReconConfMap() {
		return reconConfMap;
	}

	public void setReconConfMap(Map<String, Recon> reconConfMap) {
		this.reconConfMap = reconConfMap;
	}

}
