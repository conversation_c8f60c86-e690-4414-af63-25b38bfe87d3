package com.ascent.ds.login;

import java.io.Serializable;
import java.sql.Timestamp;

public class PasswordPolicy implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8221773635378705958L;

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */

	private Integer id;
	private String title;

	private Integer minLength;
	private Integer maxLength;
	private Integer pwdMaxAge;
	private Integer pwdExpiryWarning;
	private Integer pwdMaxFailure;
	private String allowUserToChangeOwnPwd;
	private String active_index;
	private Integer version;
	private String status;
	private Timestamp created_on;
	private Timestamp updated_on;
	private String workflow_status;
	private String activity_comments;
	private String isSpecialCharsAllowed;
	private String isUpperCaseAllowed;
	private String isNumbersAllowed;
	private String specialChars;
	public String getIsSpecialCharsAllowed() {
		return isSpecialCharsAllowed;
	}

	public void setIsSpecialCharsAllowed(String isSpecialCharsAllowed) {
		this.isSpecialCharsAllowed = isSpecialCharsAllowed;
	}

	public String getIsUpperCaseAllowed() {
		return isUpperCaseAllowed;
	}

	public void setIsUpperCaseAllowed(String isUpperCaseAllowed) {
		this.isUpperCaseAllowed = isUpperCaseAllowed;
	}

	public String getIsNumbersAllowed() {
		return isNumbersAllowed;
	}

	public void setIsNumbersAllowed(String isNumbersAllowed) {
		this.isNumbersAllowed = isNumbersAllowed;
	}

	public String getSpecialChars() {
		return specialChars;
	}

	public void setSpecialChars(String specialChars) {
		this.specialChars = specialChars;
	}

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @param title
	 *            the title to set
	 */
	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * @return the minLength
	 */
	public Integer getMinLength() {
		return minLength;
	}

	/**
	 * @param minLength
	 *            the minLength to set
	 */
	public void setMinLength(Integer minLength) {
		this.minLength = minLength;
	}

	/**
	 * @return the maxLength
	 */
	public Integer getMaxLength() {
		return maxLength;
	}

	/**
	 * @param maxLength
	 *            the maxLength to set
	 */
	public void setMaxLength(Integer maxLength) {
		this.maxLength = maxLength;
	}

	/**
	 * @return the pwdMaxAge
	 */
	public Integer getPwdMaxAge() {
		return pwdMaxAge;
	}

	/**
	 * @param pwdMaxAge
	 *            the pwdMaxAge to set
	 */
	public void setPwdMaxAge(Integer pwdMaxAge) {
		this.pwdMaxAge = pwdMaxAge;
	}

	/**
	 * @return the pwdExpiryWarning
	 */
	public Integer getPwdExpiryWarning() {
		return pwdExpiryWarning;
	}

	/**
	 * @param pwdExpiryWarning
	 *            the pwdExpiryWarning to set
	 */
	public void setPwdExpiryWarning(Integer pwdExpiryWarning) {
		this.pwdExpiryWarning = pwdExpiryWarning;
	}

	/**
	 * @return the pwdMaxFailure
	 */
	public Integer getPwdMaxFailure() {
		return pwdMaxFailure;
	}

	/**
	 * @param pwdMaxFailure
	 *            the pwdMaxFailure to set
	 */
	public void setPwdMaxFailure(Integer pwdMaxFailure) {
		this.pwdMaxFailure = pwdMaxFailure;
	}

	/**
	 * @return the allowUserToChangeOwnPwd
	 */
	public String getAllowUserToChangeOwnPwd() {
		return allowUserToChangeOwnPwd;
	}

	/**
	 * @param allowUserToChangeOwnPwd
	 *            the allowUserToChangeOwnPwd to set
	 */
	public void setAllowUserToChangeOwnPwd(String allowUserToChangeOwnPwd) {
		this.allowUserToChangeOwnPwd = allowUserToChangeOwnPwd;
	}

	/**
	 * @return the active_index
	 */
	public String getActive_index() {
		return active_index;
	}

	/**
	 * @param active_index
	 *            the active_index to set
	 */
	public void setActive_index(String active_index) {
		this.active_index = active_index;
	}

	/**
	 * @return the version
	 */
	public Integer getVersion() {
		return version;
	}

	/**
	 * @param version
	 *            the version to set
	 */
	public void setVersion(Integer version) {
		this.version = version;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	/**
	 * @return the created_on
	 */
	public Timestamp getCreated_on() {
		return created_on;
	}

	/**
	 * @param created_on
	 *            the created_on to set
	 */
	public void setCreated_on(Timestamp created_on) {
		this.created_on = created_on;
	}

	/**
	 * @return the updated_on
	 */
	public Timestamp getUpdated_on() {
		return updated_on;
	}

	/**
	 * @param updated_on
	 *            the updated_on to set
	 */
	public void setUpdated_on(Timestamp updated_on) {
		this.updated_on = updated_on;
	}

	/**
	 * @return the workflow_status
	 */
	public String getWorkflow_status() {
		return workflow_status;
	}

	/**
	 * @param workflow_status
	 *            the workflow_status to set
	 */
	public void setWorkflow_status(String workflow_status) {
		this.workflow_status = workflow_status;
	}

	/**
	 * @return the activity_comments
	 */
	public String getActivity_comments() {
		return activity_comments;
	}

	/**
	 * @param activity_comments
	 *            the activity_comments to set
	 */
	public void setActivity_comments(String activity_comments) {
		this.activity_comments = activity_comments;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
