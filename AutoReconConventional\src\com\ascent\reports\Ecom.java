package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class Ecom {

	private static final Logger logger = LogManager.getLogger(Ecom.class.getName());
	
	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();
	
	public List<Map<String, Object>> getReconData(String fromDate, String toDate, String reportType) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching "+reportType+" data..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(reportType);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			logger.debug(reportType+" data count =  "+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> getSummaryData(String date, String reportType) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching "+reportType+" data..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(reportType);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, date);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			logger.debug(reportType+" data count =  "+list.size());
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		while (rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));
			}
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}
}
