package com.ascent.integration.util;

import java.util.Map;

public class IntegrationUtil {

	public static boolean emptyCheck(String columnName, Map<String, Object> args,
			StringBuilder comments) {
		boolean flag=false;
		String columnVal=(String)args.get(columnName);
		if(columnVal==null|| (columnVal.trim()).isEmpty()){
			flag= true;
			comments.append(columnName+" IS NULL");
			args.put("ex", flag);
		}
		return flag;
	}
	
	
}
