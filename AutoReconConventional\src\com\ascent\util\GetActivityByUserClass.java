package com.ascent.util;

import java.util.ArrayList;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.ActivityManager;
import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GetActivityByUserClass extends BasicDataSource{
	ArrayList<Map<String, Object>> requestedActivities ;
	public GetActivityByUserClass()
	{
		//System.out.println("HAi");
	}
	@PostConstruct
	public void init(){
		requestedActivities = new ArrayList<Map<String,Object>>();
	}
	public DSResponse executeFetch(final DSRequest request)throws Exception
	{  DSResponse response=new DSResponse();
	HttpSession httpSession = request.getHttpServletRequest().getSession();
	User user=(User) httpSession.getAttribute("userId");
	String reconName = (String) httpSession.getAttribute("user_selected_recon");
		try{
			UserAdminManager userAdmin = UserAdminManager.getAuthorizationManagerSingleTon();					
			ArrayList<Map<String, Object>> requestedActivities =(ArrayList<Map<String, Object>>) userAdmin.getActivityByUser(user,reconName);
			response.setData(requestedActivities);
			//System.out.println(requestedActivities);
		} 
		catch(Exception e)
		{
			e.printStackTrace();
		}
		return response;
	}
}
