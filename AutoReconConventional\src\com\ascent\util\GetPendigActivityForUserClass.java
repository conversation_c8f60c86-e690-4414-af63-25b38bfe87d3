package com.ascent.util;

import java.util.ArrayList;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.ActivityManager;
import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GetPendigActivityForUserClass extends BasicDataSource{
	
	private static final long serialVersionUID = 1L;
	ArrayList<Map<String, Object>> requestedActivities ;
	ArrayList<Map<String, Object>> activitiesForApproval ;
	public GetPendigActivityForUserClass()
	{
		//System.out.println("Hai");
	}
	@PostConstruct
	public void init(){
		activitiesForApproval =  new ArrayList<Map<String,Object>>();
	}
	public DSResponse executeFetch(final DSRequest request)throws Exception
	{  DSResponse response=new DSResponse();

		try{
			HttpSession httpSession = request.getHttpServletRequest().getSession();
			User user=(User) httpSession.getAttribute("userId");
			String reconName = (String) httpSession.getAttribute("user_selected_recon");
			
			UserAdminManager userAdmin = UserAdminManager.getAuthorizationManagerSingleTon();			
			ArrayList<Map<String, Object>> activitiesForApproval  =(ArrayList<Map<String, Object>>) userAdmin.getPendingActivitiesForUser(user,reconName);
			response.setData(activitiesForApproval);
			//System.out.println(activitiesForApproval);
		} 
		catch(Exception e)
		{
			e.printStackTrace();
		}
		return response;
	}
		
		
}
