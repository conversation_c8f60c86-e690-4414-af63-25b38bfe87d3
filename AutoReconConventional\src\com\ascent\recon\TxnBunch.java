package com.ascent.recon;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TxnBunch {

	private long id;
	private List<Map<String,Object>> lhsTxns;
	private List<Map<String,Object>> rhsTxns;
	
	public TxnBunch(){
		this.id=0l;
		this.lhsTxns=new ArrayList<Map<String,Object>>();
		this.rhsTxns=new ArrayList<Map<String,Object>>();
	}
	
	public TxnBunch(long id, List<Map<String,Object>> lhsTxns, List<Map<String,Object>> rhsTxns){
		this.id=0l;
		this.lhsTxns=lhsTxns;
		this.rhsTxns=rhsTxns;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + (int) (id ^ (id >>> 32));
		result = prime * result + ((lhsTxns == null) ? 0 : lhsTxns.hashCode());
		result = prime * result + ((rhsTxns == null) ? 0 : rhsTxns.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TxnBunch other = (TxnBunch) obj;
		if (id != other.id)
			return false;
		if (lhsTxns == null) {
			if (other.lhsTxns != null)
				return false;
		} else if (!lhsTxns.equals(other.lhsTxns))
			return false;
		if (rhsTxns == null) {
			if (other.rhsTxns != null)
				return false;
		} else if (!rhsTxns.equals(other.rhsTxns))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return    "*********************************************************\\n"
				+ "TxnBunch [id=" + id + ", lhsTxn=" + lhsTxns + ", rhsTxn="
				+ rhsTxns + "]\\n"
				+ "*********************************************************\\n";
	}

	public List<Map<String, Object>> getLhsTxns() {
		return lhsTxns;
	}

	public void setLhsTxns(List<Map<String, Object>> lhsTxns) {
		this.lhsTxns = lhsTxns;
	}

	public List<Map<String, Object>> getRhsTxns() {
		return rhsTxns;
	}

	public void setRhsTxns(List<Map<String, Object>> rhsTxns) {
		this.rhsTxns = rhsTxns;
	}
	
	
}
