package com.ascent.cod.ach.export;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.mail.test.Test88;
import com.ascent.persistance.LoadRegulator;
import com.ascent.scheduler.MailTrigger;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;

/**
 * Kaushal 
 */

public class AchDataExport  extends BasicDataSource implements PagesConstants{

	private static final long serialVersionUID = 1L;

	private static final String GET_ACH_INTERNAL_RECONCILE_RECORDS = "GET_ACH_INTERNAL_RECONCILE_RECORDS";
	private static final String GET_ACH__INTERNAL_SUPPRESS_RECORDS = "GET_ACH__INTERNAL_SUPPRESS_RECORDS";
	private static final String GET_ACH_EXTERNAL_RECONCILE_RECORDS = "GET_ACH_EXTERNAL_RECONCILE_RECORDS";
	private static final String GET_ACH_EXTERNAL_SUPPRESS_RECORDS = "GET_ACH_EXTERNAL_SUPPRESS_RECORDS";

	@SuppressWarnings("unchecked")
	public Map<String, Object> getAchInternalreconcileDataLoad() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;

		List<Map<String, Object>> internalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> internaldatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_ACH_INTERNAL_RECONCILE_RECORDS);
			internalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			List internalDataList = new ArrayList();
			for (Map<String, Object> recordMap : internalReconcile) {

				internaldatamap.put("content",
						"PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm Internal Reconciles Data :");
				internalDataList.add(recordMap);

			}
			internaldatamap.put("internalrecondataList", internalDataList);
			exportExcelAndTriggerMailIInternalReconACH(internaldatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return internaldatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailIInternalReconACH(Map<String, Object> internaldatamap) {

		String fileName = null;

		try {
			// Excel Export Internal recon data export
			fileName = ExportExcelInternalReconcileACH.exportExcel3((List<Map<String, Object>>) internaldatamap.get("internalrecondataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;


	}

	///////// =============================================

	// atmInternalSuppressDataload

	@SuppressWarnings("unchecked")
	public Map<String, Object> achInternalSuppressDataload() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> suppressInternal = new ArrayList<Map<String, Object>>();
		Map<String, Object> internalSuppressdatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_ACH__INTERNAL_SUPPRESS_RECORDS);
			suppressInternal = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			List suppressDataList = new ArrayList();
			for (Map<String, Object> recordMap : suppressInternal) {

				internalSuppressdatamap.put("content",
						"PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm Internal Suppress Data:");
				suppressDataList.add(recordMap);

			}
			internalSuppressdatamap.put("internalsuppressdataList", suppressDataList);
			exportExcelAndTriggerMailInternalSuppressACH(internalSuppressdatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return internalSuppressdatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailInternalSuppressACH(Map<String, Object> internalSuppressdatamap) {

		String fileName = null;

		try {
			// Excel Export Internal Suppress Data
			fileName = ExportExcelInternalSuppressACH
					.exportExcel1((List<Map<String, Object>>) internalSuppressdatamap.get("internalsuppressdataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}
	/////// ================internal suppress End

	// External reconcile records

	@SuppressWarnings("unchecked")
	public Map<String, Object> getAchExternalreconcileDataLoad() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> externalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> externaldatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_ACH_EXTERNAL_RECONCILE_RECORDS);
			externalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			List externalDataList = new ArrayList();
			for (Map<String, Object> recordMap : externalReconcile) {

				externaldatamap.put("content",
						"PFA for :" + recordMap.get("ACTIVE INDEX") + "  Atm External Reconciles Data :");
				externalDataList.add(recordMap);

			}
			externaldatamap.put("externalrecondataList", externalReconcile);
			exportExcelAndTriggerMailIExternlReconACH(externaldatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return externaldatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailIExternlReconACH(Map<String, Object> externaldatamap) {


		String fileName = null;

		try {
			// Excel Export Internal Suppress Data
			fileName = ExportExcelExternalReconcileACH
					.exportExcel4((List<Map<String, Object>>) externaldatamap.get("externalrecondataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}

	//getExternalSuppressDataLoad

	@SuppressWarnings("unchecked")
	public 	Map<String, Object> getAchExternalSuppressDataLoad()
	{
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> suppressexternalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> externalSuppdatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_ACH_EXTERNAL_SUPPRESS_RECORDS);
			suppressexternalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);

			List externalsuppressDataList = new ArrayList();
			for (Map<String, Object> recordMap : suppressexternalReconcile) {


				externalSuppdatamap.put("content", "PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm External Suppress Data :");
				externalsuppressDataList.add(recordMap);

			}
			externalSuppdatamap.put("externalsuppressDataList", externalsuppressDataList);
			exportExcelAndTriggerMailIExternalSuppressACH(externalSuppdatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return externalSuppdatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailIExternalSuppressACH(Map<String, Object> externalSuppdatamap) {

		Map<String, Object> mailData = new HashMap<String, Object>();

		MailTrigger mailTrigger = new MailTrigger();
		String fileName = null;

		try {
			// Excel Export Internal recon data export
			fileName = ExportExcelExternalSuppressACH.exportExcel2((List<Map<String, Object>>) externalSuppdatamap.get("externalsuppressDataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}


	public  void trigarMailInternalorExternalRecon() {

		String[] reconcileFiles= new String[2];
		// String[] supressFilesFiles= new String[2];

		Map<String, Object> internalreconmap= getAchInternalreconcileDataLoad();
		String reconcile_internal_file_name = exportExcelAndTriggerMailIInternalReconACH(internalreconmap);
		reconcileFiles[0]= reconcile_internal_file_name;

		Map<String, Object> externalreconmap= getAchExternalreconcileDataLoad();
		String reconcile_external_file_name = exportExcelAndTriggerMailIExternlReconACH(externalreconmap);
		reconcileFiles[1]	=  reconcile_external_file_name ;

		Test88 testmail = new Test88();
		String subject = "AutoRecon: ACH Reconcile Data";
		String masssageformate = "Dear Sir/Madam,<br><br>Please find attached ACH Internal and External Reconciled Data."
				+ "<br><br>Regards<br>AutoRecon";
		try {
			testmail.sendMailWithAttachments(subject,masssageformate,"<EMAIL>", reconcileFiles);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}
	public  void trigarMailInternalorExternalSuppress() {

		String[] reconcileFiles= new String[2];
		// String[] supressFilesFiles= new String[2];

		Map<String, Object> internalsuppresmap= achInternalSuppressDataload();
		String reconcile_internal_file_name = exportExcelAndTriggerMailInternalSuppressACH(internalsuppresmap);
		reconcileFiles[0]= reconcile_internal_file_name;

		Map<String, Object> externalsuppressmap= getAchExternalSuppressDataLoad();
		String reconcile_external_file_name = exportExcelAndTriggerMailIExternalSuppressACH(externalsuppressmap);
		reconcileFiles[1]	=  reconcile_external_file_name ;

		Test88 testmail = new Test88();
		String subject = "AutoRecon: ACH Un-Reconcile Data";
		String masssageformate = "Dear Sir/Madam,<br><br>Please find attached ACH Unreconciled/Suppress Data."
				+ "<br><br>Regards<br>AutoRecon";
		try {
			testmail.sendMailWithAttachments(subject,masssageformate,"<EMAIL>", reconcileFiles);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}
	
	public static void main(String[] args) throws IOException {

		AchDataExport es = new AchDataExport();
		es.getAchInternalreconcileDataLoad();
		es.achInternalSuppressDataload();
		es.getAchExternalreconcileDataLoad();
		es.getAchExternalSuppressDataLoad();

		es.trigarMailInternalorExternalRecon();
		es.trigarMailInternalorExternalSuppress();

	}

}


