package com.ascent.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Roles implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -759631018799987207L;
	List<Role> roles=new ArrayList<Role>();
	Map<Integer,Role> roleMap=new HashMap<Integer, Role>();
	Map<String,Role> roleNameMap=new HashMap<String, Role>();
	public void init(){
		System.out.println(roles);
		if(this.roles!=null){
			for(Role role:this.roles){
				role.init();
				roleMap.put(role.getRoleId(), role);
				roleNameMap.put(role.getRole(), role);
			}
		}
	System.out.println(roleMap);

	}
	
	public Role getRole(Integer roleId){
		return roleMap.get(roleId);
	}
	public Role getRole(String roleName){
		return roleNameMap.get(roleName);
	}
	public List<Role> getRoles() {
		return roles;
	}

	public void setRoles(List<Role> roles) {
		this.roles = roles;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((roles == null) ? 0 : roles.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Roles other = (Roles) obj;
		if (roles == null) {
			if (other.roles != null)
				return false;
		} else if (!roles.equals(other.roles))
			return false;
		return true;
	}
	
	
	
}
