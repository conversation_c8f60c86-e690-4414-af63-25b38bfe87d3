package com.ascent.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

import com.ascent.integration.util.DbUtil;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;
import com.isomorphic.servlet.ISCFileItem;

public class SupportDocumentData extends BasicDataSource{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	DbUtil dbUtil = new DbUtil();
	public SupportDocumentData()
	{
		
	}
	public DSResponse executeUpdate(final DSRequest request) throws Exception {
		DSResponse response = new DSResponse();
		try {
			Connection connection = null;
			PreparedStatement pre = null;
			PreparedStatement createSeqPstmt = null;
			PreparedStatement seqNumPstmtTemp = null;
			String createSeqNumQry = null;
			connection = dbUtil.getConnection();
			Map SuppourtDocMap = request.getValues();
			String type =(String) SuppourtDocMap.get("type");
			System.out.println(SuppourtDocMap);
			final ISCFileItem fileItem = request.getUploadedFile("upload");
			final byte[] data = fileItem.get();
			String uploadedfile = (String) SuppourtDocMap.get("upload_filename");
			FileOutputStream fileOuputStream = new FileOutputStream(uploadedfile);
			fileOuputStream.write(data);
			fileOuputStream.close();
			File supportFile = new File(uploadedfile);
			FileInputStream fis = new FileInputStream(supportFile);
			if(type.equalsIgnoreCase("IRIS"))
			{
				String  seqNumQry = "SELECT NEXT VALUE FOR IRISDOC_ID as sno";
				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);
				long sno = irisgenerateSeqNo(seqNumPstmtTemp,"IRISDOC_ID",createSeqPstmt,createSeqNumQry);
			    pre = connection.prepareStatement("insert into IRIS_EX_SUP_DOC values(?,?) ");
				pre.setLong(1, sno);
				pre.setBinaryStream(2, (InputStream) fis,(int) supportFile.length());
				pre.executeUpdate();
				System.out.println("Successfully inserted the file into the database!");
				pre.close();
			}
			else if(type.equalsIgnoreCase("GL"))
			{
				String  seqNumQry = "SELECT NEXT VALUE FOR GLDOC_ID as sno";
				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);
				long sno = glgenerateSeqNo(seqNumPstmtTemp,"GLDOC_ID",createSeqPstmt,createSeqNumQry);
			    pre = connection.prepareStatement("insert into GL_EX_SUP_DOC values(?,?) ");
				pre.setLong(1, sno);
				pre.setBinaryStream(2, (InputStream) fis,(int) supportFile.length());
			    pre.executeUpdate();
				System.out.println("Successfully inserted the file into the database!");
				pre.close();
			}
			
			
			connection.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}
	public DSResponse executeAdd(final DSRequest request) throws Exception {
		DSResponse response = new DSResponse();
		try {
			Connection connection = null;
			PreparedStatement pre = null;
			PreparedStatement createSeqPstmt = null;
			PreparedStatement seqNumPstmtTemp = null;
			String createSeqNumQry = null;
			connection = dbUtil.getConnection();
			Map SuppourtDocMap = request.getValues();
			String type =(String) SuppourtDocMap.get("type");
			System.out.println(SuppourtDocMap);
			final ISCFileItem fileItem = request.getUploadedFile("upload");
			final byte[] data = fileItem.get();
			String uploadedfile = (String) SuppourtDocMap.get("upload_filename");
			FileOutputStream fileOuputStream = new FileOutputStream(uploadedfile);
			fileOuputStream.write(data);
			fileOuputStream.close();
			File supportFile = new File(uploadedfile);
			FileInputStream fis = new FileInputStream(supportFile);
			if(type.equalsIgnoreCase("IRIS"))
			{
				String  seqNumQry = "SELECT NEXT VALUE FOR IRISDOC_ID as sno";
				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);
				long sno = irisgenerateSeqNo(seqNumPstmtTemp,"IRISDOC_ID",createSeqPstmt,createSeqNumQry);
			    pre = connection.prepareStatement("insert into IRIS_EX_SUP_DOC values(?,?) ");
				pre.setLong(1, sno);
				pre.setBinaryStream(2, (InputStream) fis,(int) supportFile.length());
				pre.executeUpdate();
				System.out.println("Successfully inserted the file into the database!");
				pre.close();
			}
			else if(type.equalsIgnoreCase("GL"))
			{
				String  seqNumQry = "SELECT NEXT VALUE FOR GLDOC_ID as sno";
				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);
				long sno = glgenerateSeqNo(seqNumPstmtTemp,"GLDOC_ID",createSeqPstmt,createSeqNumQry);
			    pre = connection.prepareStatement("insert into GL_EX_SUP_DOC values(?,?) ");
				pre.setLong(1, sno);
				pre.setBinaryStream(2, (InputStream) fis,(int) supportFile.length());
			    pre.executeUpdate();
				System.out.println("Successfully inserted the file into the database!");
				pre.close();
			}
			
			
			connection.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}
	
	public  synchronized Long irisgenerateSeqNo(PreparedStatement seqNoPstmt,
			   String glseqName, PreparedStatement createSeqPstmt2, String createSeqNumQry2) {
			  DbUtil dbUtil = new DbUtil();
			  try {
			   ResultSet txnRs = seqNoPstmt.executeQuery();
			   if (txnRs.next()) {
			    return txnRs.getLong(1);
			   }
			  } catch (SQLException e) {
			   Connection connection = null;
			   PreparedStatement createSeqPstmt = null;
			   PreparedStatement seqNumPstmtTemp = null;
			   String createSeqNumQry = null;
			   String seqNumQry = null;
			   try {
			    connection = dbUtil.getConnection();
			    createSeqNumQry = "CREATE  SEQUENCE " +glseqName
			      + "   AS BIGINT START WITH 1  INCREMENT BY 1 ";
			    seqNumQry = "SELECT NEXT VALUE FOR " + glseqName + "  as sno";

			    createSeqPstmt = connection.prepareStatement(createSeqNumQry);
			    createSeqPstmt.executeUpdate();

			    seqNumPstmtTemp = connection.prepareStatement(seqNumQry);

			    ResultSet rs = seqNumPstmtTemp.executeQuery();
			    if (rs.next()) {
			     return rs.getLong(1);
			    }

			   } catch (Exception e1) {
			    ;
			    e1.printStackTrace();
			    

			   } finally {

			    DbUtil.closePreparedStatement(createSeqPstmt);
			    DbUtil.closePreparedStatement(seqNumPstmtTemp);
			    DbUtil.closeConnection(connection);
			   }
			  }
			  return 0l;

			 }
	public  synchronized Long glgenerateSeqNo(PreparedStatement seqNoPstmt,
			   String irisseqName, PreparedStatement createSeqPstmt2, String createSeqNumQry2) {
			  DbUtil dbUtil = new DbUtil();
			  try {
			   ResultSet txnRs = seqNoPstmt.executeQuery();
			   if (txnRs.next()) {
			    return txnRs.getLong(1);
			   }
			  } catch (SQLException e) {
			   Connection connection = null;
			   PreparedStatement createSeqPstmt = null;
			   PreparedStatement seqNumPstmtTemp = null;
			   String createSeqNumQry = null;
			   String seqNumQry = null;
			   try {
			    connection = dbUtil.getConnection();
			    createSeqNumQry = "CREATE  SEQUENCE " + irisseqName
			      + "   AS BIGINT START WITH 1  INCREMENT BY 1 ";
			    seqNumQry = "SELECT NEXT VALUE FOR " + irisseqName + "  as sno";

			    createSeqPstmt = connection.prepareStatement(createSeqNumQry);
			    createSeqPstmt.executeUpdate();

			    seqNumPstmtTemp = connection.prepareStatement(seqNumQry);

			    ResultSet rs = seqNumPstmtTemp.executeQuery();
			    if (rs.next()) {
			     return rs.getLong(1);
			    }

			   } catch (Exception e1) {
			    ;
			    e1.printStackTrace();
			    

			   } finally {

			    DbUtil.closePreparedStatement(createSeqPstmt);
			    DbUtil.closePreparedStatement(seqNumPstmtTemp);
			    DbUtil.closeConnection(connection);
			   }
			  }
			  return 0l;

			 }
	
}

