package com.ascent.service.dto;

import java.io.Serializable;
import java.util.List;

public class PrivilegeDetails implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3283549029460019130L;
	private Integer pid;  
	@Override
	public String toString() {
		return "PrivilegeDetails [pid=" + pid + ", sno=" + sno + ", module=" + module + ", operation=" + operation
				+ ", accesibility=" + accesibility + ", description=" + description + ", status=" + status
				+ ", version=" + version + ", geography=" + geography + ", buisinessArea=" + buisinessArea + ", Recon="
				+ Recon + ", source=" + source + "]";
	}
	private Integer sno;  
	private String module;
	private String operation;  //previelegeAccesibility previelegeDescription
	private String accesibility;
	private String description;
	private String status;
	private Integer version;
	private String geography;
	private String buisinessArea;
	private String Recon;
	private String source;
	
	//geography=QATAR, B_Area=ON US, Recon=[ATM - DEBIT CARD], operation=Recon Process,
	//description=dfghfgh, _selection_27=true}privilegeDetailsMap

	 
	 
	 
	
	public String getModule() {
		return module;
	}
	public void setModule(String module) {
		this.module = module;
	}
	public String getOperation() {
		return operation;
	}
	public void setOperation(String operation) {
		this.operation = operation;
	}
	public String getAccesibility() {
		return accesibility;
	}
	public void setAccesibility(String accesibility) {
		this.accesibility = accesibility;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	 
	public Integer getPid() {
		return pid;
	}
	public void setPid(Integer pId2) {
		this.pid = pId2;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((accesibility == null) ? 0 : accesibility.hashCode());
		result = prime * result
				+ ((description == null) ? 0 : description.hashCode());
		result = prime * result + ((module == null) ? 0 : module.hashCode());
		result = prime * result
				+ ((operation == null) ? 0 : operation.hashCode());
		result = prime * result + ((pid == null) ? 0 : pid.hashCode());
		return result;
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PrivilegeDetails other = (PrivilegeDetails) obj;
		if (accesibility == null) {
			if (other.accesibility != null)
				return false;
		} else if (!accesibility.equals(other.accesibility))
			return false;
		if (description == null) {
			if (other.description != null)
				return false;
		} else if (!description.equals(other.description))
			return false;
		if (module == null) {
			if (other.module != null)
				return false;
		} else if (!module.equals(other.module))
			return false;
		if (operation == null) {
			if (other.operation != null)
				return false;
		} else if (!operation.equals(other.operation))
			return false;
		if (pid == null) {
			if (other.pid != null)
				return false;
		} else if (!pid.equals(other.pid))
			return false;
		return true;
	}
	public Integer getSno() {
		return sno;
	}
	public void setSno(Integer sno) {
		this.sno = sno;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Integer getVersion() {
		return version;
	}
	public void setVersion(Integer version) {
		this.version = version;
	}
	public String getGeography() {
		return geography;
	}
	public void setGeography(String geography) {
		this.geography = geography;
	}
	public String getBuisinessArea() {
		return buisinessArea;
	}
	public void setBuisinessArea(String buisinessArea) {
		this.buisinessArea = buisinessArea;
	}
	public String getRecon() {
		return Recon;
	}
	public void setRecon(String recon) {
		Recon = recon;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	
	
	
	
}
