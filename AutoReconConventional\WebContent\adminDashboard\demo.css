/* FLEX ADMIN DEMO STYLES */

/* The following styles are used for demo purposes. */

/* Organizing the Icons Page */

.fa-hover {
    height: 45px;
}

.fa-hover:hover {
    font-size: 20px;
}

.bs-glyphicons {
    margin-bottom: 20px;
    padding-bottom: 1px;
    padding-left: 0;
    overflow: hidden;
    list-style: none;
}

.bs-glyphicons li {
    float: left;
    width: 25%;
    height: 115px;
    margin: 0 -1px -1px 0;
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
    font-size: 12px;
    line-height: 1.4;
}

.bs-glyphicons .glyphicon {
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 24px;
}

.bs-glyphicons .glyphicon-class {
    display: block;
    text-align: center;
    word-wrap: break-word; /* Help out IE10+ with class names */
}

.bs-glyphicons li:hover {
    background-color: #e0e7e8;
}

@media(min-width:768px) {
    .bs-glyphicons li {
        width: 12.5%;
    }
}

/* Slider Demo Spacing */

.vertical-slider-demo li {
    margin-right: 20px;
}

/* Flot Chart Homepage Demo */

.flot-dash-demo {
    height: 200px !important;
}

.dashboard-demo-table {
    overflow: auto;
}

/* Grid Demo */

.show-grid [class^="col-"] {
    padding-top: 10px;
    padding-bottom: 10px;
    border: 1px solid #ddd;
    border: 1px solid rgba(52,73,94,.2);
    background-color: #eee;
    background-color: rgba(52,73,94,.15);
}

.show-grid {
    margin: 15px 0;
}

/* Dashboard Map Demo */

#map {
    height: 250px;
}

@media(min-width:768px) {
    #map {
        height: 500px;
    }
}

/* Fixed Height Dashboard Demo Styles */

.dash-demo-tile {
    height: 250px;
    text-align: center;
}

.dash-demo-tile h4 {
    text-align: left;
}

#morris-chart-dashboard {
    width: 100%;
    height: 240px;
}
#chartdiv{
 width: 100%;
    height: 240px;

}

.flot-chart-dashboard {
    height: 240px;
}

/* Custom Easy Pie Chart Demo Styles */

.easy-pie-chart {
    width: 175px;
    height: 175px;
    margin: 15px 0 0;
}

.percent {
    font-size: 2em;
    font-weight: bold;
    line-height: 175px;
    color: rgba(255,255,255,.7);
}