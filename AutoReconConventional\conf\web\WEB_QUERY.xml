<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">

	<query id="110">
		<name>LOAD_PASSWORD_POLICY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM PASSWORD_POLICY WHERE VERSION=(SELECT
			MAX(VERSION) FROM
			PASSWORD_POLICY) AND ACTIVE_INDEX='Y'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="121">
		<name>LOCK_USER_ACCOUNT_QUERY</name>
		<queryType>Update</queryType>
		<queryString>UPDATE users set updated_on= ? ,account_status=? where
			user_id=?
		</queryString>
		<queryParam>updated_on@TIMESTAMP,account_status@VARCHAR,user_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="121">
		<name>GET_DATE_DIFF_FOR_PASSWORD_EXPIRY_NOTIFICATION</name>
		<queryType>Select</queryType>
		<queryString>select DATEDIFF(DAY,getdate(),pwd_exp_date) as expdate
			from users where user_id=?
		</queryString>
		<queryParam>user_id@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="120">
		<name>INSERT_USER_lOG_AUDIT_QRY</name>
		<queryType>Insert</queryType>
		<queryString>

			insert into User_Audit
			(
			user_id,action,date_time,bussiness_area,recon_name,user_role
			)
			values
			(
			?,?,?,?,?,?
			)

		</queryString>
		<queryParam>user_id@VARCHAR,action@VARCHAR,date_time@TIMESTAMP,bussiness_area@VARCHAR,recon_name@VARCHAR,user_role@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>GET_ALL_USERS_EMAILS_QUERY</name>
		<queryType>Select</queryType>
		<queryString>Select email_id,isLdapUser from users where email_id=? and
			status='APPROVED' and active_index='Y'
		</queryString>
		<queryParam>email_id@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>CHANGE_USER_PASSWORD_QUERY</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set password=?,updated_on=?,pwd_exp_date = ?
			where user_id=?
		</queryString>
		<queryParam>password@VARCHAR,updated_on@TIMESTAMP,pwd_exp_date@TIMESTAMP,user_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>RESET_USER_PASSWORD_QUERY</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set password=?,updated_on=?,pwd_exp_date = ?,account_status = ?
			where email_id=?
		</queryString>
		<queryParam>password@VARCHAR,updated_on@TIMESTAMP,pwd_exp_date@TIMESTAMP,account_status@VARCHAR,email_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="3">
  <name>select_recon_activity_flow</name>
  <queryType>Select</queryType>
  <queryString>select * from recon_activity_flow rec_fl  where status='APPROVED' and version=(select MAX(version) from recon_activity_flow where activity_id=rec_fl.activity_id and status='APPROVED') and activity_data is not null</queryString>
  <queryParam></queryParam>
  <queryParamLiteralValues></queryParamLiteralValues>
 </query>

<query id="3">
		<name>SELCT_PASS_POLICY</name>
		<queryType>Select</queryType>
		<queryString>Select * from PASS_POLICY</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="3">
		<name>SOURCES_NAME_QUERY</name>
		<queryType>Select</queryType>
		<queryString>select * from SOURCES</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>


<query id="3">
		<name>GET_ALL_MODULES_QUERY</name>
		<queryType>Select</queryType>
		<queryString>select * from MODULES</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1">
		<name>LoadAllUsers</name>
		<queryType>Insert</queryType>
		<queryString>select * from users  rl where status='APPROVED' and version = (select MAX(version) from users where user_id=rl.user_id and status='APPROVED')</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="2">
		<name>LoadAllRoles</name>
		<queryType>Insert</queryType>
		<queryString>select * from roles rl where status='APPROVED' and version = (select MAX(version) from roles where roleid=rl.roleid and status='APPROVED') </queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="3">
		<name>LoadAllDepartments</name>
		<queryType>Insert</queryType>
		<queryString>select * from departments dep  where status='APPROVED' and version = (select MAX(version) from departments where dept_id=dep.dept_id and status='APPROVED')</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="4">
		<name>LoadAllPrevieleges</name>
		<queryType>select</queryType>
		<queryString>select * from privileges prv where status='APPROVED' and version = (select MAX(version) from privileges where role=prv.role and status='APPROVED')</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="5">
		<name>LoadAllPrevielegeDetails</name>
		<queryType>select</queryType>
		<queryString>select * from privilegedetails where pid=? </queryString>
		<queryParam>pid@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="6">
		<name>GetActivityAudit</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,transaction_amount
			from recon_activity_flow a where a.activity_id=?
		</queryString>
		<queryParam>activity_id@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="7">
		<name>GetActivityByUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and activity_owner=? and
			activity_level=(
			select max(activity_level) from recon_activity_flow b
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>activity_owner@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="8">
		<name>GetActivity</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow where activity_id = ? and activity_level=?
		</queryString>
		<queryParam>activity_id@BIGINT,activity_level@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="9">
		<name>CreateActivity</name>
		<queryType>INSERT</queryType>
		<queryString>
			insert into recon_activity_flow
			(
			activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,
			active_index,created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			)
			values
			(
			?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,?,?,?,?

			)

		</queryString>
		<queryParam>activity_id@BIGINT,activity_name@VARCHAR,activity_type@VARCHAR,activity_level@INTEGER,
			allowed_approvers@VARCHAR,activity_owner@VARCHAR,recent_actor@VARCHAR,activity_data@LONGVARBINARY,status@VARCHAR,
			active_index@VARCHAR,created_on@TIMESTAMP,recently_updated_on@TIMESTAMP,version@INTEGER,comment@VARCHAR,business_area@VARCHAR,recon@VARCHAR,transaction_amount@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="10">
		<name>GetActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where CHARINDEX(?, allowed_approvers)!=0 and
			activity_level=(
			select max(activity_level) from recon_activity_flow b
			where b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="11">
		<name>GetRejectedActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and status like 'Rej%'
			and CHARINDEX(?, allowed_approvers)!=0 and activity_level=(
			select
			max(activity_level) from recon_activity_flow b where
			b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="12">
		<name>GetApprovedActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from
			recon_activity_flow a where active_index='Y' and status like
			'Appro%'and CHARINDEX(?, allowed_approvers)!=0 and activity_level=(
			select max(activity_level) from recon_activity_flow b where
			b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="13">
		<name>GetPendigActivityForUser</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
   sno,activity_id,activity_name,activity_type,activity_level,
   allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
   created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
   from
   recon_activity_flow a where active_index='Y' and status like 'Pend%'
   and(( CHARINDEX(?, allowed_approvers)!=0) )  and activity_level=(
   select
   max(activity_level) from recon_activity_flow b where
   b.activity_id=a.activity_id)
		</queryString>
		<queryParam>user@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="14">
		<name>GetActivityAudit</name>
		<queryType>SELECT</queryType>
		<queryString>
			select
			sno,activity_id,activity_name,activity_type,activity_level,
			allowed_approvers,activity_owner,recent_actor,activity_data,status,active_index,
			created_on,recently_updated_on,version,comment,business_area,recon,transaction_amount
			from recon_activity_flow a where a.activity_id=?
		</queryString>
		<queryParam>activity_id@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="15">
        <name>LoadAllFeatures</name>
        <queryType>select</queryType>
        <queryString>select * from features</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="16">
        <name>ALL_RECON_NAMES_QRY</name>
        <queryType>select</queryType>
        <queryString>select *  from RECON</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="17">
        <name>GET_ALL_GEOGRAPHY_NAME_QRY</name>
        <queryType>select</queryType>
        <queryString>select * from GEOGRAPHY</queryString>
        <queryParam></queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="18">
        <name>GET_ALL_BUSINESS_AREAS_FOR_SPECIFIC_GEOGRAPHY</name>
        <queryType>select</queryType>
        <queryString>select * from business_area where geography_sno=(select sno from GEOGRAPHY where geography_name=? )</queryString>
        <queryParam>geography_name@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
    <query id="19">
        <name>GET_ALL_RECONS_FOR_SPECIFIC_BUSINESS_AREA</name>
        <queryType>select</queryType>
        <queryString>
		
			select * from RECON where business_area_sno=(
				select SNO from Business_Area where business_area=? and geography_sno=(
					select sno from GEOGRAPHY where geography_name=?
				) 
			)
		
		</queryString>
        <queryParam>business_area@VARCHAR,geography_name@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="20">
		<name>SetActivityDataReconActivity</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE recon_activity_flow set activity_data = ? where activity_id = ?</queryString>
		<queryParam>activity_data@LONGVARBINARY,activity_id@BIGINT</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="2">
		<name>GET_MAX_ROLE_ID</name>
		<queryType>Select</queryType>
		<queryString>SELECT MAX(roleid) as maxId FROM ROLES</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="2">
		<name>GET_MAX_DEP_ID</name>
		<queryType>Select</queryType>
		<queryString>SELECT MAX(id) as maxId FROM departments</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues> 
	</query>
	<query id="2">
		<name>GET_MAX_PRIVILAGE_ID</name>
		<queryType>Select</queryType>
		<queryString>SELECT MAX(privilegeId) as maxId FROM privileges</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues> 
	</query>
	<query id="2">
        <name>getMaxVersionFromPrevileges</name>
        <queryType>SELECT</queryType>
        <queryString>SELECT  MAX(version) as max FROM privileges where status = 'APPROVED' and role=?</queryString>
        <queryParam>role@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
        <name>savePrevielege</name>
        <queryType>Insert</queryType>
        <queryString>INSERT INTO privileges(privilegeId,sno,role,privilge_details,status,version) VALUES (?,?, ?,?,?,?)</queryString>
        <queryParam>privilegeId@INTEGER,sno@INTEGER,role@VARCHAR,privilge_details@LONGVARBINARY,status@VARCHAR,version@INTEGER</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
     <query id="12">
		<name>privilegeStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE privileges set status=?,active_index=? where privilegeId=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,privilegeId@VARCHAR</queryParam>
		 <queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="2">
        <name>saveRoles</name>
        <queryType>Insert</queryType>
        <queryString>INSERT INTO roles(role, discription,roleid ,status,version,active_index,created_on,updated_on) VALUES (?, ?,?,?,?,?,?,?)</queryString>
        <queryParam>role@VARCHAR,discription@VARCHAR,roleid@INTEGER,status@VARCHAR,version@INTEGER,active_index@VARCHAR,created_on@TIMESTAMP,updated_on@TIMESTAMP</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
        <name>updateRoles</name>
        <queryType>UPDATE</queryType>
        <queryString>UPDATE roles set role=?, discription=?, status = ?, updated_on=? where roleid = ? and version=?</queryString>
        <queryParam>role@VARCHAR,discription@VARCHAR,status@VARCHAR,updated_on@TIMESTAMP,roleid@INTEGER,version@INTEGER</queryParam>
         <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
     <query id="12">
		<name>RoleStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE roles set status=?,active_index=?, updated_on=? where roleid=? and version=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,updated_on@TIMESTAMP,roleid@VARCHAR,version@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
     
    <query id="2">
        <name>saveDepartments</name>
        <queryType>Insert</queryType>
        <queryString>INSERT INTO departments(dept_name,category,location,dept_id,status,version,updated_on) VALUES (?,?,?,?,?,?,?)</queryString>
        <queryParam>dept_name@VARCHAR,category@VARCHAR,location@VARCHAR,dept_id@VARCHAR,status@VARCHAR,version@INTEGER,updated_on@TIMESTAMP</queryParam>
       <queryParamLiteralValues></queryParamLiteralValues>
    </query>
 
	<query id="2"> 
        <name>updateDeparments</name>
        <queryType>Insert</queryType>
        <queryString>UPDATE departments SET dept_name=?,category=?,location=?,status = ?, updated_on=? WHERE dept_id=? and version=?</queryString>
        <queryParam>dept_name@VARCHAR,category@VARCHAR,location@VARCHAR,status@VARCHAR,updated_on@TIMESTAMP,dept_id@VARCHAR,version@INTEGER</queryParam>
       <queryParamLiteralValues></queryParamLiteralValues>
    </query>
     <query id="2">
        <name>updateDepartMent</name>
        <queryType>UPDATE</queryType>
        <queryString>UPDATE departments set status = ?, updated_on=? where dept_id = ? and version=?</queryString>
        <queryParam>status@VARCHAR,updated_on@TIMESTAMP,dept_id@VARCHAR,version@INTEGER</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    <query id="2">
		<name>DepartmentStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE departments set status=?,active_index=?, updated_on=? where dept_id=? and version=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,updated_on@TIMESTAMP,dept_id@VARCHAR,version@INTEGER</queryParam>
	  <queryParamLiteralValues></queryParamLiteralValues>
	</query>
    
     <query id="2">
		<name>saveUsers</name>
		<queryType>Insert</queryType>
		<queryString>INSERT INTO
			users(user_id,user_name,email_id,phon_number,dept_name,reporting,system_role,approval_role,branch_location,approval_department,status,version,password,updated_on,isLdapUser,account_status,pwd_exp_date)
			VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>user_id@VARCHAR,user_name@VARCHAR,email_id@VARCHAR,phon_number@VARCHAR,dept_name@VARCHAR,reporting@VARCHAR,system_role@VARCHAR,approval_role@VARCHAR,branch_location@VARCHAR,approval_department@VARCHAR,status@VARCHAR,version@INTEGER,password@VARCHAR,updated_on@TIMESTAMP,isLdapUser@VARCHAR,account_status@VARCHAR,pwd_exp_date@TIMESTAMP
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="2">
		<name>updateUsers</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set
			user_name=?,phon_number=?,email_id=?,dept_name=?,reporting=?,system_role=?,approval_role=?,branch_location=?,status
			= ?,updated_on=?,isLdapUser=?,account_status=? where
			user_id = ? and
			version=?
		</queryString>
		<queryParam>user_name@VARCHAR,phon_number@VARCHAR,email_id@VARCHAR,dept_name@VARCHAR,reporting@VARCHAR,system_role@VARCHAR,approval_role@VARCHAR,branch_location@VARCHAR,status@VARCHAR,updated_on@TIMESTAMP,isLdapUser@VARCHAR,account_status@VARCHAR,user_id@VARCHAR,version@INTEGER
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
    
     <query id="12">
		<name>UserStatusUpdate</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE users set status=?,active_index=?,updated_on=? where user_id=? and version=?</queryString>
		<queryParam>status@VARCHAR,active_index@VARCHAR,updated_on@TIMESTAMP,user_id@VARCHAR,version@INTEGER</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	  <query id="2">
        <name>loadPrevielegesDetailsRoleWise</name>
        <queryType>Select</queryType>
        <queryString>select privilge_details from privileges prv where role=? and status='APPROVED' and version = (select MAX(version) from privileges where role=prv.role and status='APPROVED') </queryString>
        <queryParam>role@VARCHAR</queryParam>
        <queryParamLiteralValues></queryParamLiteralValues>
    </query>
    
     <query id="20">
		<name>UpdateReconActivity</name>
		<queryType>UPDATE</queryType>
		<queryString>UPDATE recon_activity_flow set active_index = ? where activity_id = ?</queryString>
		<queryParam>active_index@VARCHAR,activity_id@BIGINT</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	//ATM MODULE
	//WRITTEN BY SURENDRA FOR DOHA ATM MODULE
	<query id="21">
		<name>ATM_RECON_ATM_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'ATM_IRIS' AS RECON_SIDE,SID,RETR_REF_NO, AMT_SETT AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,TIME_LOC_TRAN,PROC_CODE_FIRST_2 as PROC_CODE,
					C_ACCEP_TERM_ID,DEB_CRE_INDI AS DEB_CRE_IND,RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,
					AUTHORIZER,ACQUIRING_CHANNEL_ID,PAN,time_in_MILLIS,
					'ATM_IRIS_STG' as  SOURCE_TARGET,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
				FROM 	ATM_IRIS_STG 
				WHERE 	 SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="22">
		<name>ATM_RECON_ATM_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 	'ATM_GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,TRA_TIME as TIME_LOC_TRAN,PROC_CODE,
				 C_ACCEP_TERM_ID,DEB_CRE_IND,'' AS RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,
						'' AS PAN,time_in_MILLIS,'ATM_GL_1002_STG' as  SOURCE_TARGET,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				FROM 	ATM_GL_1002_STG 
				WHERE 	 SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
//DEBIT ATM ONUS
<query id="21">
		<name>ONUS_ATM_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 
					'IRIS' AS RECON_SIDE,SID,RETR_REF_NO, AMT_SETT AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,TIME_LOC_TRAN,PROC_CODE_FIRST_2 as PROC_CODE,
					C_ACCEP_TERM_ID,DEB_CRE_INDI AS DEB_CRE_IND,RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,
					AUTHORIZER,ACQUIRING_CHANNEL_ID,PAN,time_in_MILLIS,
					'IRIS_STG' as  SOURCE_TARGET,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
				FROM 	IRIS_STG 
				WHERE 	BUSINESS_AREA='ONUS' AND AUTHORIZER IN ('0001' ,'0050','0101','9898','9999')
						AND  ACQUIRING_CHANNEL_ID IN ('0001','0050','0101','9898' ,'9999') 
						AND NETWORK='DOHA BANK' AND BIN IN ('434141','484823','428246','000000')
						AND RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 IN ('01','82') and SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="22">
		<name>ONUS_ATM_DEBIT_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		SELECT 	'GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,TRA_TIME as TIME_LOC_TRAN,PROC_CODE,
				 C_ACCEP_TERM_ID,DEB_CRE_IND,'' AS RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,
						'' AS PAN,time_in_MILLIS,'GL_1002_STG' as  SOURCE_TARGET,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				FROM 	GL_1002_STG 
				WHERE 	BUSINESS_AREA='ONUS' AND AUTHORIZING_CHANNEL IN ('0001','0050','0101','9898','9999')
						AND ACQUIRING_CHANNEL_ID IN ('0001','0050','0101','9898','9999') AND PROC_CODE IN ('01','82')  
						AND LED_CODE='1002' and SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//CREDIT_ATM
	
	<query id="23">
		<name>ONUS_ATM_CREDIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,AMT_TRAN AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,RETR_REF_NO,
TRAN_CUR,C_ACCEP_TERM_ID,SYS_TRACE_AUDIT_NO AS STAN,
PROC_CODE_FIRST_2 AS PROC_CODE,MAIN_REV_IND,BUSINESS_AREA,CHANNEL,TIME_IN_MILLIS,'IRIS_STG' as  SOURCE_TARGET,
	'' as  I000_MSG_TYPE,'' as DEB_CRE_IND,RESP_CODE,'' AS SERNO,MAIN_REV_IND,AMT_TRAN AS LOCAL_AMT
	FROM	IRIS_STG 
	WHERE	AUTHORIZER IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND ACQUIRING_CHANNEL_ID IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND PROC_CODE_FIRST_2=(select TRAN_CODE from MDT_PROCESS_CODES 
	 where TRAN_NAME='Withdrawal' and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
	  AND RESP_CODE IN(select CODE from MDT_RESPONSE_CODES 
	  where NAME in('APPROVED','ERR_ACQ_REVERSAL') 
	  and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='CREDIT CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			)
			and SID=? and status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="24">
		<name>ONUS_ATM_CREDIT_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		
		SELECT 'GL_1472' AS RECON_SIDE,gl1472.SID,gl1472.TRA_AMT,gl1472.TRA_DATE,gl1472.RETR_REF_NO,
gl1472.TRAN_CUR,gl1472.C_ACCEP_TERM_ID,STAN,
gl1472.PROC_CODE,gl1472.MAIN_REV_IND,gl1472.BUSINESS_AREA,gl1472.CHANNEL,
			gl1472.TIME_IN_MILLIS,'GL_1472_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,
			'' AS RESP_CODE,'' AS SERNO,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
			 FROM GL_1472_STG gl1472 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="25">
		<name>ONUS_ATM_CREDIT_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		
SELECT 'GL_1002' AS RECON_SIDE,gl1002.SID,gl1002.TRA_AMT,gl1002.TRA_DATE,gl1002.RETR_REF_NO,
	gl1002.TRAN_CUR,gl1002.C_ACCEP_TERM_ID,STAN,
gl1002.PROC_CODE,gl1002.MAIN_REV_IND,gl1002.BUSINESS_AREA,gl1002.CHANNEL,
	gl1002.TIME_IN_MILLIS,
	'GL_1002_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,'' AS RESP_CODE,'' AS SERNO,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				 FROM GL_1002_STG gl1002 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="26">
		<name>ONUS_ATM_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select	'CTL' AS RECON_SIDE,citxn.SID,I004_AMT_TRXN AS TRA_AMT, I013_TRXN_DATE AS TRA_DATE, 
			I037_RET_REF_NUM AS RETR_REF_NO,
			ctxn.TRAN_CUR,'' AS C_ACCEP_TERM_ID,'' AS STAN,
 PROC_CODE,MAIN_REV_IND,BUSINESS_AREA,CHANNEL,
			
			ctxn.TIME_IN_MILLIS,'CISO_STG' as  SOURCE_TARGET,I000_MSG_TYPE,'' as DEB_CRE_IND,'' AS RESP_CODE,
			citxn.SERNO,MAIN_REV_IND,I006_AMT_BILL AS LOCAL_AMT
			 from BATCHES_STG batch
	inner join CTXNS_STG ctxn on batch.serno=ctxn.BATCHSERNO
	inner join CISO_STG citxn on ctxn.SERNO=citxn.SERNO 
	where	charindex('Transfer',batch.FILENAME )!=0 and I018_MERCH_TYPE=6011 
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='CREDIT CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			) and STGENERAL='POST'
			AND citxn.SID=? and citxn.status not in ('Suppress')

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//DEPOSIT
	
	<query id="27">
		<name>ONUS_ATM_DEPOSIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
				SELECT 
					'IRIS' AS RECON_SIDE,SID,RETR_REF_NO, AMT_TRAN_BASE AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,TIME_LOC_TRAN,PROC_CODE_FIRST_2 as PROC_CODE,
					C_ACCEP_TERM_ID,DEB_CRE_INDI AS DEB_CRE_IND,RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,
					AUTHORIZER,ACQUIRING_CHANNEL_ID,PAN,time_in_MILLIS,
					'IRIS_STG' as  SOURCE_TARGET,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
				FROM 	IRIS_STG 
				WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('21','25') and SID=?	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="28">
		<name>ONUS_ATM_DEPOSIT_RECON_GL_1006</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1006' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,TRA_TIME as TIME_LOC_TRAN,PROC_CODE,
				 C_ACCEP_TERM_ID,DEB_CRE_IND,'' AS RESP_CODE,BUSINESS_AREA,CHANNEL,TRAN_CUR,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,
						'' AS PAN,time_in_MILLIS,'GL_1006_STG' as  SOURCE_TARGET,MAIN_REV_IND,EQU_TRA_AMT AS LOCAL_AMT
				FROM 	GL_1006_STG 
				WHERE 	 PROC_CODE in ('21','25') and SID=?	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//PAYROLL_ATM
	
	<query id="29">
		<name>ONUS_ATM_PAYROL_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	select	'CTL' AS RECON_SIDE,citxn.SID,I004_AMT_TRXN AS TRA_AMT, I013_TRXN_DATE AS TRA_DATE, 
			I037_RET_REF_NUM AS RETR_REF_NO,ctxn.TIME_IN_MILLIS,I000_MSG_TYPE,'' AS RESP_CODE,
			'' AS DEB_CRE_IND, 'CISO_STG' as  SOURCE_TARGET,'' AS TRAN_CUR,MAIN_REV_IND,I006_AMT_BILL AS LOCAL_AMT
			 from BATCHES_STG batch
	inner join CTXNS_STG ctxn on batch.serno=ctxn.BATCHSERNO
	inner join CISO_STG citxn on ctxn.SERNO=citxn.SERNO 
	where	charindex('Transfer',batch.FILENAME )!=0 and I018_MERCH_TYPE=6011 
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='PAYROL CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			)
			and citxn.SID=? and citxn.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="30">
		<name>ONUS_ATM_PAYROL_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,AMT_TRAN AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,RETR_REF_NO,TIME_IN_MILLIS,'' AS I000_MSG_TYPE,
	RESP_CODE,'' AS DEB_CRE_IND,
	'IRIS_STG' as  SOURCE_TARGET,TRAN_CUR,MAIN_REV_IND,AMT_TRAN_BASE AS LOCAL_AMT
	FROM	IRIS_STG 
	WHERE	AUTHORIZER IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND ACQUIRING_CHANNEL_ID IN(select CHANNEL_ID from MDT_CHANNELS 
	where CHANNEL_NAME in('ATM','CTL','IRIS','HSM','HOST') 
	and active_index='Y' and WORKFLOW_STATUS='N') 
	AND PROC_CODE_FIRST_2=(select TRAN_CODE from MDT_PROCESS_CODES 
	 where TRAN_NAME='Withdrawal' and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
	  AND RESP_CODE IN(select CODE from MDT_RESPONSE_CODES 
	  where NAME in('APPROVED','ERR_ACQ_REVERSAL') 
	  and ACTIVE_INDEX='Y' and WORKFLOW_STATUS='N')
			and BIN in(
				select	BIN 
				from	MDT_DB_CARD_BINS 
				where	card_type='PAYROL CARD' and active_index='Y' and WORKFLOW_STATUS='N'
			) 
			and SID=? and status not in ('Suppress')
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	<query id="31">
		<name>ONUS_ATM_PAYROL_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
		select 'GL_1472' AS RECON_SIDE,gl1472.SID,gl1472.TRA_AMT,gl1472.TRA_DATE,gl1472.RETR_REF_NO,
gl1472.TRAN_CUR,gl1472.C_ACCEP_TERM_ID,STAN,
gl1472.PROC_CODE,gl1472.MAIN_REV_IND,gl1472.BUSINESS_AREA,gl1472.CHANNEL,
			gl1472.TIME_IN_MILLIS,'GL_1472_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,'' AS RESP_CODE,'' AS SERNO,
			gl1472.WORKFLOW_STATUS,EQU_TRA_AMT AS LOCAL_AMT
	FROM	GL_1472_STG gl1472  
 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?
	
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	<query id="32">
		<name>ONUS_ATM_PAYROL_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_1002' AS RECON_SIDE,gl1002.SID,gl1002.TRA_AMT,gl1002.TRA_DATE,gl1002.RETR_REF_NO,
	gl1002.TRAN_CUR,gl1002.C_ACCEP_TERM_ID,STAN,
gl1002.PROC_CODE,gl1002.MAIN_REV_IND,gl1002.BUSINESS_AREA,gl1002.CHANNEL,
	gl1002.TIME_IN_MILLIS,
	'GL_1002_STG' as  SOURCE_TARGET,'' as  I000_MSG_TYPE,DEB_CRE_IND,'' AS RESP_CODE,'' AS SERNO,
	gl1002.WORKFLOW_STATUS,EQU_TRA_AMT AS LOCAL_AMT
	FROM	GL_1002_STG gl1002
	 WHERE AUTHORIZING_CHANNEL IN('0001' ,'0050','0101','9898','9999') AND ACQUIRING_CHANNEL_ID IN('0001' ,'0050','0101','9898','9999')
 AND PROC_CODE='01' AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//CREDIT_POS
	
	<query id="33">
		<name>ONUS_POS_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
	SELECT	'CTL' AS RECON_SIDE,MITXN.SID,I037_RET_REF_NUM,I004_AMT_TRXN,I002_NUMBER,I013_TRXN_DATE AS TRA_DATE,
					I000_MSG_TYPE,I039_RESP_CD AS I039_RSP_CD,MTXN.PROC_CODE,I018_MERCH_TYPE,I038_AUTH_ID,MITXN.BUSINESS_AREA,CHANNEL,BIN,MTXN.TIME_IN_MILLIS,
					'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
	FROM	BATCHES_STG BATCH INNER JOIN MTXNS_STG MTXN 
	ON		BATCH.SERNO=MTXN.INBATCHSERNO INNER JOIN MISO_STG MITXN 
	ON		MTXN.SERNO=MITXN.SERNO 
	WHERE	MITXN.CHANNELSERNO=180 AND I018_MERCH_TYPE!=6011 
			AND BIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461) 
			and  ((CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0))
			and MITXN.SID=? and MITXN.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="34">
		<name>ONUS_POS_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'AUTH_ISS' AS RECON_SIDE,SID,I037_RET_REF_NUM,I004_AMT_TRXN,I002_NUMBER,TRA_DATE,
					I000_MSG_TYPE,I039_RSP_CD,PROC_CODE,I018_MERCH_TYPE,I038_AUTH_ID,BUSINESS_AREA,CHANNEL,BIN, 
					TIME_IN_MILLIS,'AUTH_ISSUER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
	FROM	AUTH_ISSUER_STG 
	WHERE	SOURCE NOT IN('MAST','VISA') AND I018_MERCH_TYPE!=6011 AND I039_RSP_CD IN('00')
			AND BIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
			AND SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//DEBIT_POS
	
<query id="35">
		<name>ONUS_POS_DEBIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'CTL' AS RECON_SIDE,MITXN.SID,I037_RET_REF_NUM AS RETR_REF_NO,I004_AMT_TRXN AS TRA_AMT,
			I013_TRXN_DATE AS TRA_DATE,'' AS DEB_CRE_IND,I000_MSG_TYPE,MTXN.TIME_IN_MILLIS,'MISO_STG' as  SOURCE_TARGET,
			MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
	 FROM BATCHES_STG BATCH
	INNER JOIN MTXNS_STG MTXN ON BATCH.SERNO=MTXN.INBATCHSERNO
	INNER JOIN MISO_STG MITXN ON MTXN.SERNO=MITXN.SERNO 
	WHERE	MITXN.CHANNELSERNO=181 AND I018_MERCH_TYPE!=6011 AND BIN IN(428246,484823,434141) 
			AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0)
			and MITXN.SID=? and MITXN.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>	
	
	
	<query id="36">
		<name>ONUS_POS_DEBIT_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_1472' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,DEB_CRE_IND,'' AS I000_MSG_TYPE,
	TIME_IN_MILLIS,'GL_1472_STG' as  SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM	GL_1472_STG 
	WHERE	PROC_CODE=00 AND AUTHORIZING_CHANNEL IN(0001,0050,9898,9999,01010) 
			AND ACQUIRING_CHANNEL_ID IN(0001,0050,9898,9999,01010)
			and SID=? and status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>	
	
	
	
	//PAYROLL_POS
	
	
	<query id="37">
		<name>ONUS_POS_PAYROL_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
				SELECT	'CTL' AS RECON_SIDE,MITXN.SID,I037_RET_REF_NUM,I002_NUMBER,I004_AMT_TRXN,
				I000_MSG_TYPE,MTXN.TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,
				I013_TRXN_DATE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
				FROM	BATCHES_STG BATCH INNER JOIN MTXNS_STG MTXN 
				ON		BATCH.SERNO=MTXN.INBATCHSERNO INNER JOIN MISO_STG MITXN 
				ON		MTXN.SERNO=MITXN.SERNO 
				WHERE	MITXN.CHANNELSERNO=180 AND I018_MERCH_TYPE!=6011 
						AND BIN IN(404618) 
					
						AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0)
						and MITXN.SID=? and MITXN.status not in ('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="38">
		<name>ONUS_POS_PAYROL_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'AUTH_ISS' AS RECON_SIDE,SID,I037_RET_REF_NUM,I002_NUMBER,I004_AMT_TRXN,I000_MSG_TYPE, TIME_IN_MILLIS,'AUTH_ISSUER_STG' AS SOURCE_TARGET,
LTIMESTAMP AS I013_TRXN_DATE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
				FROM	AUTH_ISSUER_STG 
				WHERE	SOURCE NOT IN('MAST','VISA') AND I018_MERCH_TYPE!=6011 
						AND BIN IN(404618) AND I039_RSP_CD IN('00') and SID=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	/*-------------------------ISSUER QUERIES------------------------*/
	
	//MASTER_CREDIT_ATM
	
	
	<query id="40">
		<name>ISSUER_MATM_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE ,I037_RET_REF_NUM,I000_MSG_TYPE,I013_TRXN_DATE AS TRA_DATE,
'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,I038_AUTH_ID,'' AS RESP_CODE,'' as REASONCODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
 and CHARINDEX ('TT11',FILENAME)!=0 and I018_MERCH_TYPE=6011
  AND ciso.SID=?  AND ciso.STATUS NOT IN('Supress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="41">
		<name>ISSUER_MATM_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,I005_AMT_SETTLE,I037_RET_REF_NUM,I000_MSG_TYPE,LTIMESTAMP AS TRA_DATE,
'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN as BIN,I018_MERCH_TYPE,I038_AUTH_ID,I039_RSP_CD AS RESP_CODE,
REASONCODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='MAST'  and I018_MERCH_TYPE=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD='00' AND REASONCODE IN('0') AND SID=? AND  STATUS NOT IN('Suppress')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//MASTER_CREDIT_POS
	
	<query id="42">
		<name>ISSUER_MPOS_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE ,I037_RET_REF_NUM,I000_MSG_TYPE,I013_TRXN_DATE AS TRA_DATE,
'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,I038_AUTH_ID,'' AS RESP_CODE,'' as REASONCODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
 and CHARINDEX ('TT112T0',FILENAME)!=0 and I018_MERCH_TYPE!=6011
  AND ciso.SID=? AND ciso.STATUS NOT IN('Supress')

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="43">
		<name>ISSUER_MPOS_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,I005_AMT_SETTLE,I037_RET_REF_NUM,I000_MSG_TYPE,LTIMESTAMP AS TRA_DATE,
'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN as BIN,I018_MERCH_TYPE,I038_AUTH_ID,I039_RSP_CD AS RESP_CODE,
REASONCODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='MAST'  and I018_MERCH_TYPE!=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD='00' AND REASONCODE IN('0') AND SID=? AND  STATUS NOT IN('Suppress') 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	// NAPS_DEBIT_ATM
	
	<query id="44">
		<name>ISSUER_NATM_DEBIT_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,RETR_REF_NO,TRAN_DATE AS TRA_DATE,TRANS_AMOUNT AS TRA_AMT,TRAN_TIME,TIME_IN_MILLIS, 
		'QCB_STG' AS SOURCE_TARGET,
		'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PROC_COD,BIN,'' AS C_ACCEP_TERM_ID,'' AS RESP_CODE,
		'' AS STAN,CARD_NUMBER,'' AS DEB_CRE_IND,TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT
		 FROM QCB_STG 
		WHERE BIN IN(434141,484823,428246) 
		AND CHANNEL='ATM' AND ISS_BANK_CODE=3 AND ACQ_BANK_CODE!=3
		AND TRAN_TYPE IN(10,98,99) 
		AND SID=? 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="45">
		<name>ISSUER_NATM_DEBIT_RECON_GL_1015</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1015' AS RECON_SIDE,SID,RETR_REF_NO,SETTL_DATE as TRA_DATE,TRA_AMT,TRA_TIME,TIME_IN_MILLIS,'GL_1015_STG' AS SOURCE_TARGET,
		AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_COD,'' AS BIN,C_ACCEP_TERM_ID,'' AS RESP_CODE,STAN,
		'' AS CARD_NUMBER,DEB_CRE_IND,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1015_STG
		 WHERE AUTHORIZING_CHANNEL IN('9999')
		 AND PROC_CODE=01 AND ACQUIRING_CHANNEL_ID=28 
		 AND TELL_ID=9953 AND SID=? 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="46">
		<name>ISSUER_NATM_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,AMT_SETT AS TRA_AMT,TIME_LOC_TRAN AS TRA_TIME,TIME_IN_MILLIS, 
		'IRIS_STG' AS SOURCE_TARGET,AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PROC_COD,BIN,C_ACCEP_TERM_ID,RESP_CODE,
		SYS_TRACE_AUDIT_NO AS STAN,PAN AS CARD_NUMBER,'' AS DEB_CRE_IND,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
		 FROM IRIS_STG 
		WHERE AUTHORIZER IN('9999') 
		AND ACQUIRING_CHANNEL_ID=28 AND RESP_CODE IN('000','036','037') AND PROC_CODE_FIRST_2=01
		AND BIN IN(434141,484823,428246) 
		AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS_DEBIT_POS
	
	
	<query id="46">
		<name>ISSUER_NPOS_DEBIT_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,RETR_REF_NO,TRAN_DATE AS TRA_DATE,TRANS_AMOUNT AS TRA_AMT,TRAN_TIME,TIME_IN_MILLIS, 
		'QCB_STG' AS SOURCE_TARGET,
		'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PROC_COD,BIN,'' AS C_ACCEP_TERM_ID,'' AS RESP_CODE,'' AS STAN,CARD_NUMBER,
		TRAN_TYPE,'' AS DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT
		 FROM QCB_STG 
		WHERE BIN IN(434141,484823,428246) 
		AND CHANNEL='POS' AND ISS_BANK_CODE=3 AND ACQ_BANK_CODE!=3
		AND TRAN_TYPE IN(10,98,99) 
		AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="47">
		<name>ISSUER_NPOS_DEBIT_RECON_GL_1016</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1016' AS RECON_SIDE,SID,RETR_REF_NO,SETTL_DATE AS TRA_DATE,TRA_AMT,TRA_TIME,TIME_IN_MILLIS,'GL_1016_STG' AS SOURCE_TARGET,
		AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_COD,'' AS BIN,C_ACCEP_TERM_ID,'' AS RESP_CODE,
		STAN,'' AS CARD_NUMBER,'' AS TRAN_TYPE,DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1016_STG
		 WHERE AUTHORIZING_CHANNEL='9999'
		 AND PROC_CODE IN(00,06) AND ACQUIRING_CHANNEL_ID=0028 
		 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="48">
		<name>ISSUER_NPOS_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,AMT_SETT AS TRA_AMT,TIME_LOC_TRAN AS TRA_TIME,TIME_IN_MILLIS, 
		'IRIS_STG' AS SOURCE_TARGET,AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PROC_COD,BIN,C_ACCEP_TERM_ID,RESP_CODE,
		SYS_TRACE_AUDIT_NO AS STAN,PAN AS CARD_NUMBER,'' AS TRAN_TYPE,'' AS DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
		 FROM IRIS_STG 
		WHERE AUTHORIZER IN('9999') 
		AND ACQUIRING_CHANNEL_ID=28 AND RESP_CODE IN('000','036','037') AND PROC_CODE_FIRST_2 IN(00,06)
		AND BIN IN(434141,484823,428246) 
		AND SID=?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	
	// VISA_CREDIT_ATM
	
	<query id="49">
		<name>ISSUER_VATM_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE as AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,I013_TRXN_DATE AS TRA_DATE,
'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,I038_AUTH_ID,'' AS RESP_CODE,'' as REASONCODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
 and CHARINDEX ('SAVE.INCOMING',FILENAME)!=0 and I018_MERCH_TYPE=6011 and STGENERAL IN('NEW','POST')
  AND ciso.SID=?  AND ciso.STATUS NOT IN('Supress')


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="50">
		<name>ISSUER_VATM_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,LTIMESTAMP AS TRA_DATE,
'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN as BIN,I018_MERCH_TYPE,I038_AUTH_ID,I039_RSP_CD AS RESP_CODE,
REASONCODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='VISA'  and I018_MERCH_TYPE=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD='00' AND REASONCODE IN('0') AND SID=? AND  STATUS NOT IN('Suppress') 


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//VISA_DEBIT_ATM
	
	<query id="51">
		<name>ISSUER_VATM_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,SYS_TRACE_AUDIT_NO AS STAN,AMT_TRAN AS IRIS_AMT_TRAN,AMT_SETT AS IRIS_AMT_SETT,
					0 AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,DATE_LOC_TRAN AS TRA_DATE,
					'IRIS_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,RESP_CODE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
			FROM	IRIS_STG_VISA_ISS_ATM where SID=?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="52">
		<name>ISSUER_VATM_DEBIT_RECON_GL_2247</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_2247' AS RECON_SIDE,SID,STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,TRA_AMT AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,
					0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,TRA_DATE,'GL_2247_STG' AS SOURCE_TARGET,
					DEB_CRE_IND,'' AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT 
			FROM	GL_2247_STG 
			WHERE PROC_CODE=01 AND AUTHORIZING_CHANNEL='9999' 
AND ACQUIRING_CHANNEL_ID=0007 AND TELL_ID=9953 
					AND SID=?


		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="53">
		<name>ISSUER_VATM_DEBIT_RECON_VISA</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'VISA' AS RECON_SIDE,VISA.SID,VISA.AUTH_CODE AS STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,0 AS GL_TRA_AMT,
					SOURCE_AMT AS VISA_SOURCE_AMT,VISA.DEST_AMT AS VISA_DEST_AMT,'' AS RETR_REF_NO ,VISA.TRAN_CODE AS TRAN_CODE,VISA.PURCHASE_DATE AS TRA_DATE,
					'VISA_ISSUER_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,'' AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,VISA.DEST_AMT AS LOCAL_AMT
			FROM	ISSUER_VATM_DEBIT_VISA_ISSUER_STG VISA WHERE SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	// VISA_CREDIT_POS
	
	<query id="54">
		<name>ISSUER_VPOS_CREDIT_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,CISO.SERNO,ciso.SID,I002_NUMBER,I005_AMT_SETTLE AS AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,I038_AUTH_ID,'' AS REASONCODE,
I013_TRXN_DATE AS TRA_DATE,'CISO_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,BIN,I018_MERCH_TYPE,'' AS SOURCE,'' AS RESP_CODE,
MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 
FROM BATCHES_STG batch
 inner join CTXNS_STG ctxn
 on ctxn.BATCHSERNO=batch.SERNO
 inner join CISO_STG ciso
 on ciso.SERNO=ctxn.SERNO 
 where BIN in(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
  and CHARINDEX ('SAVE.INCOMING',FILENAME)!=0 and I018_MERCH_TYPE!=6011 AND STGENERAL IN('NEW','POST')
  AND ciso.SID=? AND CISO.STATUS NOT IN ('Suppress')

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="55">
		<name>ISSUER_VPOS_CREDIT_RECON_AUTH_ISS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ISS' AS RECON_SIDE,0 AS SERNO,SID,I002_NUMBER,AMT_CENTER,I037_RET_REF_NUM,I000_MSG_TYPE,I038_AUTH_ID,REASONCODE,
LTIMESTAMP AS TRA_DATE,'AUTH_ISSUER_STG' AS SOURCE_TARGET,TIME_IN_MILLIS,CARDBIN AS BIN,I018_MERCH_TYPE,SOURCE,
I039_RSP_CD AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
 FROM AUTH_ISSUER_STG 
WHERE SOURCE='VISA'  and I018_MERCH_TYPE!=6011
AND CARDBIN IN(464421, 464423, 471369, 444460, 428131, 428132, 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481, 466155, 428245, 428131, 444461)
AND I039_RSP_CD IN('00') AND REASONCODE IN('0') 
AND SID=? 

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	// VISA_DEBIT_POS
	
	
	<query id="56">
		<name>ISSUER_VPOS_DEBIT_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'IRIS' AS RECON_SIDE,SID,SYS_TRACE_AUDIT_NO AS STAN,AMT_TRAN AS IRIS_AMT_TRAN,AMT_SETT AS IRIS_AMT_SETT,
					0 AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,DATE_LOC_TRAN AS TRA_DATE,
					'IRIS_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,RESP_CODE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
			FROM	IRIS_STG_VISA_ISS_POS where SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="57">
		<name>ISSUER_VPOS_DEBIT_RECON_GL_2247</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'GL_2247' AS RECON_SIDE,SID,STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,TRA_AMT AS GL_TRA_AMT,0 AS VISA_SOURCE_AMT,
					0 AS VISA_DEST_AMT,RETR_REF_NO,'' AS TRAN_CODE,TRA_DATE,'GL_2247_STG' AS SOURCE_TARGET,DEB_CRE_IND
					,'' AS RESP_CODE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
			FROM	GL_2247_STG 
			WHERE	PROC_CODE=00 AND AUTHORIZING_CHANNEL='9999' AND ACQUIRING_CHANNEL_ID=0007 AND TELL_ID=9953
					AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="58">
		<name>ISSUER_VPOS_DEBIT_RECON_VISA</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT	'VISA' AS RECON_SIDE,VISA.SID,VISA.AUTH_CODE AS STAN,0 AS IRIS_AMT_TRAN,0 AS IRIS_AMT_SETT,0 AS GL_TRA_AMT,
					SOURCE_AMT AS VISA_SOURCE_AMT,VISA.DEST_AMT AS VISA_DEST_AMT,'' AS RETR_REF_NO ,VISA.TRAN_CODE AS TRAN_CODE,VISA.PURCHASE_DATE AS TRA_DATE,
					'VISA_ISSUER_STG' AS SOURCE_TARGET,'' AS DEB_CRE_IND,'' AS RESP_CODE,MAIN_REV_IND,'' AS TRAN_CUR,VISA.Source_Amt AS LOCAL_AMT
			FROM	ISSUER_VATM_DEBIT_VISA_ISSUER_STG VISA WHERE SID=?
			

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	/*-------------------------ACCQUIRER QUERIES------------------------*/
	
	//MASTER_CREDIT_ATM
	
	<query id="59">
		<name>ACQ_MATM_CRDS_RECON_MAST</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'MAST' AS RECON_SIDE,SID,refrence_no AS RETR_REF_NO,trans_amount as TRA_AMT,0 AS AMT_SETT,tran_date AS TRA_DATE,TIME_IN_MILLIS,
	'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,resp_code AS RESP_CODE,
	 trans_amt_dr_cr_indicator AS DEB_CRE_IND,'MASTER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,trans_amount AS LOCAL_AMT
  FROM MASTER_STG 
		WHERE processor_acq_iss='A'  and resp_code='00' AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="60">
		<name>ACQ_MATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SID,SID,RETR_REF_NO,AMT_TRAN AS TRA_AMT,AMT_SETT,DATE_LOC_TRAN AS TRA_DATE,TIME_IN_MILLIS,
		AUTHORIZER, ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE ,RESP_CODE,
		'' AS DEB_CRE_IND,'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT

FROM IRIS_STG
	WHERE AUTHORIZER=0040 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE_FIRST_2=01  AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="61">
		<name>ACQ_MATM_CRDS_RECON_GL_1482</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1482' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,0 AS AMT_SETT,TRA_DATE,TIME_IN_MILLIS,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,DEB_CRE_IND,MAIN_REV_IND,TRAN_CUR,
'GL_1482_STG' AS SOURCE_TARGET,EQU_TRA_AMT AS LOCAL_AMT 
FROM GL_1482_STG
		WHERE AUTHORIZING_CHANNEL=0040 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE=01 
		AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="62">
		<name>ACQ_MATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,0 AS AMT_SETT,TRA_DATE,TIME_IN_MILLIS,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,DEB_CRE_IND,
'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT FROM GL_1002_STG
WHERE AUTHORIZING_CHANNEL=0040 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE=01 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//MASTER_CREDIT_POS 
	
	
	<query id="63">
		<name>ACQ_MPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I037_RET_REF_NUM, I002_NUMBER, MISO.CHANNELSERNO,I004_AMT_TRXN, I013_TRXN_DATE AS TRA_DATE, I000_MSG_TYPE,MISO.SERNO,'' AS RESP_CODE,
TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT  FROM BATCHES_STG BATCH
	INNER JOIN  MTXNS_STG MTXN
	ON MTXN.INBATCHSERNO=BATCH.SERNO
	INNER JOIN  MISO_STG MISO
	ON MISO.SERNO=MTXN.SERNO WHERE (MISO.CHANNELSERNO=177 AND I018_MERCH_TYPE!=6011 AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0)
	OR (MISO.CHANNELSERNO=179  AND CHARINDEX('a2016',FILENAME)!=0 AND BIN LIKE '5%')) AND STGENERAL IN('NEW','POST')
	AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="64">
		<name>ACQ_MPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,I037_RET_REF_NUM, I002_NUMBER,'' AS CHANNELSERNO, I004_AMT_TRXN, LTIMESTAMP AS TRA_DATE, I000_MSG_TYPE,
	'' AS SERNO,I039_RESP_CD AS RESP_CODE,TIME_IN_MILLIS,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM AUTH_ACQUIRER_STG 
	WHERE PROCESS_NAME='APIMAST' AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS2_CREDIT_ATM
	
	<query id="65">
		<name>ACQ_NAPS2_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,AMT_SETT AS TRA_AMT,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,TIME_IN_MILLIS,
	AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE,RESP_CODE,'' AS DEB_CRE_IND,PAN AS CARD_NO,
	'IRIS_STG' AS SOURCE_TARGET,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
	FROM IRIS_STG WHERE AUTHORIZER in('0028','0036') AND ACQUIRING_CHANNEL_ID='0001' AND PROC_CODE_FIRST_2='73'
	AND BIN NOT IN(SELECT BIN MDT_CARD_BIN FROM MDT_DB_CARD_BINS)
	 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="66">
		<name>ACQ_NAPS2_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRANS_AMOUNT AS TRA_AMT,RETR_REF_NO,TRAN_DATE AS TRA_DATE,TIME_IN_MILLIS,
'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' AS RESP_CODE,'' AS DEB_CRE_IND,CARD_NUMBER AS CARD_NO,
'QCB_STG' AS SOURCE_TARGET,TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT
 FROM QCB_STG WHERE ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='POS'
	AND TRAN_TYPE IN(10,98,99) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="67">
		<name>ACQ_NAPS2_CRDS_RECON_GL_1016</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1016' AS RECON_SIDE,SID,TRA_AMT,RETR_REF_NO,TRA_DATE,TIME_IN_MILLIS, 
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' AS RESP_CODE,DEB_CRE_IND,'' AS CARD_NO,
'GL_1016_STG' AS SOURCE_TARGET,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
FROM GL_1016_STG WHERE TELL_ID=9911 AND AUTHORIZING_CHANNEL =0028 AND ACQUIRING_CHANNEL_ID=0001
	AND PROC_CODE=73 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS_CREDIT_ATM
	
	<query id="68">
		<name>ACQ_NATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,DATE_LOC_TRAN AS TRA_DATE,AMT_SETT AS TRA_AMT,AMT_TRAN AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
	AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE,RESP_CODE,
	'' AS DEB_CRE_IND,'' AS TRAN_TYPE,'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
	 FROM IRIS_STG WHERE AUTHORIZER=0028 AND ACQUIRING_CHANNEL_ID=0001 
	AND PROC_CODE_FIRST_2=01 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="69">
		<name>ACQ_NATM_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRAN_DATE AS TRA_DATE,TRANS_AMOUNT AS TRA_AMT,0 AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
	'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' AS RESP_CODE,'' AS DEB_CRE_IND,TRAN_TYPE,
	'QCB_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT 
	FROM QCB_STG WHERE ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='ATM'
	AND TRAN_TYPE IN(10,98,99) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="70">
		<name>ACQ_NATM_CRDS_RECON_GL_1015</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1015' AS RECON_SIDE,SID,TRA_DATE,EQU_TRA_AMT AS TRA_AMT,TRA_AMT AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_CODE,'' AS RESP_CODE,DEB_CRE_IND,'' AS TRAN_TYPE,
	'GL_1015_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1015_STG WHERE AUTHORIZING_CHANNEL=0028 AND ACQUIRING_CHANNEL_ID=0001 
	AND PROC_CODE=01 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="71">
		<name>ACQ_NATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,TRA_DATE,EQU_TRA_AMT AS TRA_AMT,TRA_AMT AS ADD_AMT,RETR_REF_NO,TIME_IN_MILLIS,
		AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PROC_CODE,'' AS RESP_CODE,DEB_CRE_IND,
		'' AS TRAN_TYPE,'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
 FROM GL_1002_STG WHERE AUTHORIZING_CHANNEL=0028 AND ACQUIRING_CHANNEL_ID=0001 AND TELL_ID=9911
	AND PROC_CODE=01 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//NAPS_POS
	
	<query id="72">
		<name>ACQ_NPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I013_TRXN_DATE AS TRA_DATE,'' AS TRA_TIME,MISO.CHANNELSERNO,I004_AMT_TRXN AS TRA_AMT,I037_RET_REF_NUM AS RETR_REF_NO,
I002_NUMBER AS CARD_NO,MISO.SERNO,'' AS TELL_ID,I039_RESP_CD AS RESP_CODE,I000_MSG_TYPE,
'' as DEB_CRE_IND,'' AS TRAN_TYPE,'' as EDCFLAG,'' as PROCESS_NAME,
'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM BATCHES_STG BATCH
	INNER JOIN MTXNS_STG MTXN
	ON BATCH.SERNO=MTXN.INBATCHSERNO
	INNER JOIN MISO_STG MISO
	ON MTXN.SERNO=MISO.SERNO WHERE CHARINDEX('a2016',FILENAME)!=0  AND MISO.CHANNELSERNO=179 
	AND I018_MERCH_TYPE!=6011 AND STGENERAL IN('NEW','POST')	
	 AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="73">
		<name>ACQ_NPOS_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRAN_DATE AS TRA_DATE,TRAN_TIME AS TRA_TIME,'' AS CHANNELSERNO,TRANS_AMOUNT AS TRA_AMT,RETR_REF_NO,
	CARD_NUMBER AS CARD_NO,0 AS SERNO,'' AS TELL_ID,'' AS RESP_CODE,'' as I000_MSG_TYPE,'' as DEB_CRE_IND,TRAN_TYPE,'' as EDCFLAG,'' as PROCESS_NAME,
	'QCB_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT AS LOCAL_AMT FROM QCB_STG 
	WHERE  ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 
		AND CHANNEL='POS' AND TRAN_TYPE IN('10','98','99') 
		 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="74">
		<name>ACQ_NPOS_CRDS_RECON_GL_1016</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1016' AS RECON_SIDE,SID,TRA_DATE, TRA_TIME,'' AS CHANNELSERNO,  TRA_AMT, RETR_REF_NO, ''  AS CARD_NO,'' AS SERNO,TELL_ID,'' AS RESP_CODE,'' as I000_MSG_TYPE,
	DEB_CRE_IND,'' AS TRAN_TYPE,'' as EDCFLAG,'' as PROCESS_NAME,'GL_1016_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
 FROM GL_1016_STG WHERE AUTHORIZING_CHANNEL IN('0028','0029','0031') 
			AND ACQUIRING_CHANNEL_ID IN('0050','0001') AND PROC_CODE IN('00','86') AND TELL_ID IN(9946)
			AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="75">
		<name>ACQ_NPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,LTIMESTAMP AS TRA_DATE,'' AS TRA_TIME,'' AS CHANNELSERNO,I004_AMT_TRXN AS TRA_AMT,I037_RET_REF_NUM AS RETR_REF_NO, 
I002_NUMBER AS CARD_NO,'' AS SERNO,'' AS TELL_ID,'' AS RESP_CODE,I000_MSG_TYPE,'' as DEB_CRE_IND,'' AS TRAN_TYPE,EDCFLAG,
PROCESS_NAME,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT
  FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='CTIDHI2' AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') 
   AND (CARDBIN IN(SELECT PREFIX FROM MDT_NAPS_CARD_BINS) OR(CARDBIN LIKE '34%' OR CARDBIN LIKE '37%')) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//QPAY_POS
	
	<query id="76">
		<name>ACQ_QPAY_CRDS_RECON_QCB</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QCB' AS RECON_SIDE,SID,TRAN_DATE,TRANS_AMOUNT,RETR_REF_NO,ACQ_BANK_CODE,ISS_BANK_CODE,CHANNEL,TRAN_TYPE,CARD_NUMBER,
'' AS SOURCE_TYPE,'' AS DEST_TYPE,TIME_IN_MILLIS,'QCB_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,TRANS_AMOUNT as LOCAL_AMT 
FROM QCB_STG
	 WHERE ACQ_BANK_CODE=3 AND ISS_BANK_CODE=3 OR (ISS_BANK_CODE=3 OR ISS_BANK_CODE!=3)  
	 AND CHANNEL='QPY' AND TRAN_TYPE IN(10,98,99) AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="77">
		<name>ACQ_QPAY_CRDS_RECON_QPAY</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'QPAY' AS RECON_SIDE,SID,TRAN_DATE,AMOUNT,RETR_REF_NO,
'' AS ACQ_BANK_CODE,'' AS ISS_BANK_CODE,'' AS CHANNEL,'' AS TRAN_TYPE,CARD_NUMBER,
SOURCE_TYPE,DEST_TYPE,TIME_IN_MILLIS,'QPAY_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMOUNT as LOCAL_AMT 
FROM QPAY_STG
 WHERE SOURCE_TYPE=5 AND DEST_TYPE=2 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//UNION_PAY_ATM
	
	<query id="78">
		<name>ACQ_UATM_CRDS_RECON_CUP</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CUP' AS RECON_SIDE,SID,trans_amount AS TRA_AMT,retr_ref_no AS RETR_REF_NO,sett_date as TRA_DATE,'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,
'' AS RESP_CODE,'' AS DEB_CRE_IND,'' AS TRAN_TYPE,MAIN_REV_IND,TRAN_CUR,
'CUP_STG' AS SOURCE_TARGET,trans_amount AS LOCAL_AMT  FROM CUP_STG where SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="79">
		<name>ACQ_UATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,AMT_C_HLDR_BILL AS TRA_AM,RETR_REF_NO,DATE_LOC_TRAN AS TRA_DATE,AUTHORIZER,ACQUIRING_CHANNEL_ID,RESP_CODE,'' AS DEB_CRE_IND,
'' AS TRAN_TYPE,'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT FROM IRIS_STG 
WHERE AUTHORIZER IN(0046) AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE_FIRST_2=01
	AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="80">
		<name>ACQ_UATM_CRDS_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1472' AS RECON_SIDE, SID,EQU_TRA_AMT AS TRA_AMT,RETR_REF_NO,TRA_DATE,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,'' AS RESP_CODE,DEB_CRE_IND,'' AS TRAN_TYPE,
		'GL_1472_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	 FROM GL_1472_STG WHERE SUB_ACCT_CODE=2 
	AND  AUTHORIZING_CHANNEL IN('0046') AND ACQUIRING_CHANNEL_ID=0001 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="81">
		<name>ACQ_UATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,EQU_TRA_AMT AS TRA_AMT,RETR_REF_NO,TRA_DATE,AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,'' AS RESP_CODE,DEB_CRE_IND,'' AS TRAN_TYPE,
	'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	 FROM GL_1002_STG WHERE AUTHORIZING_CHANNEL IN('0046') AND ACQUIRING_CHANNEL_ID=0001 AND TELL_ID=9911
	 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	//UNION_PAY_POS
	<query id="82">
		<name>ACQ_UPOS_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO AS I037_RET_REF_NUM,PAN AS I002_NUMBER ,'' AS CHANNELSERNO,AMT_SETT AS I004_AMT_TRXN,DATE_LOC_TRAN AS TRA_DATE,
	'' AS I000_MSG_TYPE,'' AS SERNO,RESP_CODE,PROC_CODE_FIRST_2 AS PROC_COD,AUTHORIZER,ACQUIRING_CHANNEL_ID,TIME_IN_MILLIS,'IRIS_STG' AS SOURCE_TARGET,
		MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
		 FROM IRIS_STG 
		WHERE AUTHORIZER IN('0046') 
		AND ACQUIRING_CHANNEL_ID IN('0050') AND PROC_CODE_FIRST_2=00 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="83">
		<name>ACQ_UPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I037_RET_REF_NUM, I002_NUMBER,MISO.CHANNELSERNO ,I004_AMT_TRXN, I013_TRXN_DATE AS TRA_DATE, I000_MSG_TYPE,MISO.SERNO,
	'' AS RESP_CODE,'' AS PROC_COD,'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,
	TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM BATCHES_STG BATCH
	INNER JOIN  MTXNS_STG MTXN
	ON MTXN.INBATCHSERNO=BATCH.SERNO
	INNER JOIN  MISO_STG MISO
	ON MISO.SERNO=MTXN.SERNO WHERE MISO.CHANNELSERNO=219  
	AND I018_MERCH_TYPE!=6011 AND (CHARINDEX('a2016',FILENAME)!=0 
	OR CHARINDEX('m2016',FILENAME)!=0) 
	AND STGENERAL IN('NEW','POST')
	AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="84">
		<name>ACQ_UPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,I037_RET_REF_NUM, I002_NUMBER,'' AS CHANNELSERNO, I004_AMT_TRXN, LTIMESTAMP AS TRA_DATE, I000_MSG_TYPE,
	'' AS SERNO,'' AS RESP_CODE,'' AS PROC_COD,'' AS AUTHORIZER,'' AS ACQUIRING_CHANNEL_ID,TIME_IN_MILLIS,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET, 
	MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM AUTH_ACQUIRER_STG 
	WHERE PROCESS_NAME='CTIDHI2' AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') 
	AND CARDBIN NOT IN (SELECT PREFIX FROM MDT_NAPS_CARD_BINS)
	AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	//VISA_ATM
	
	
	<query id="85">
		<name>ACQ_VATM_CRDS_RECON_VISA</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'VISA' AS RECON_SIDE,SID,retrieval_ref_number AS RETR_REF_NO,transaction_amount AS TRA_AMT,report_date AS TRA_DATE,
	'' AS AUTHORIZER,'' AS  ACQUIRING_CHANNEL_ID,'' AS PRC_CODE,'' as RESP_CODE,0 AS C_ACCEP_TERM_ID,0 AS TELL_ID,'' AS DEB_CRE_IND,tran_type AS TRAN_TYPE,
	'visa_stg' AS SOURCE_TARGET,MAIN_REV_IND,transaction_amount AS LOCAL_AMT
 FROM   visa_stg WHERE SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="86">
		<name>ACQ_VATM_CRDS_RECON_IRIS</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'IRIS' AS RECON_SIDE,SID,RETR_REF_NO,AMT_SETT AS TRA_AMT,DATE_LOC_TRAN AS TRA_DATE,
		AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE_FIRST_2 AS PRC_CODE,RESP_CODE,C_ACCEP_TERM_ID,0 AS TELL_ID,'' AS DEB_CRE_IND,'' AS TRAN_TYPE,
		'IRIS_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,AMT_TRAN_BASE AS LOCAL_AMT
 FROM IRIS_STG 
		WHERE AUTHORIZER=0030 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE_FIRST_2=01 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="87">
		<name>ACQ_VATM_CRDS_RECON_GL_1472</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1472' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE,
AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,C_ACCEP_TERM_ID,TELL_ID,DEB_CRE_IND,
'' AS TRAN_TYPE,'GL_1472_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
  FROM GL_1472_STG 

		WHERE AUTHORIZING_CHANNEL=0030 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE=01 AND 
		SUB_ACCT_CODE ='0' AND TELL_ID=9911 AND  SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="88">
		<name>ACQ_VATM_CRDS_RECON_GL_1002</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_1002' AS RECON_SIDE,SID,RETR_REF_NO,TRA_AMT,TRA_DATE ,
	AUTHORIZING_CHANNEL AS AUTHORIZER,ACQUIRING_CHANNEL_ID,PROC_CODE AS PRC_CODE,'' AS RESP_CODE,
	C_ACCEP_TERM_ID,TELL_ID,DEB_CRE_IND,'' AS TRAN_TYPE,'GL_1002_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,EQU_TRA_AMT AS LOCAL_AMT
	FROM GL_1002_STG 
		WHERE AUTHORIZING_CHANNEL=0030 AND ACQUIRING_CHANNEL_ID=0001 AND PROC_CODE =01 AND TELL_ID=9911 AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	// VISA_POS
	
	<query id="89">
		<name>ACQ_VPOS_CRDS_RECON_CTL</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'CTL' AS RECON_SIDE,MISO.SID,I037_RET_REF_NUM, I002_NUMBER,MISO.CHANNELSERNO,I004_AMT_TRXN, I013_TRXN_DATE AS TRA_DATE, I000_MSG_TYPE,MISO.SERNO,'' AS RESP_CODE,
TIME_IN_MILLIS,'MISO_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM BATCHES_STG BATCH
	INNER JOIN  MTXNS_STG MTXN
	ON MTXN.INBATCHSERNO=BATCH.SERNO
	INNER JOIN  MISO_STG MISO
	ON MISO.SERNO=MTXN.SERNO WHERE (MISO.CHANNELSERNO=182 AND I018_MERCH_TYPE!=6011 AND (CHARINDEX('a2016',FILENAME)!=0 OR CHARINDEX('m2016',FILENAME)!=0))
	OR (MISO.CHANNELSERNO=179  AND CHARINDEX('m2016',FILENAME)!=0 AND MISO.CHANNELSERNO=179 AND BIN LIKE '4%' ) AND STGENERAL IN('NEW','POST')
	AND MISO.SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="90">
		<name>ACQ_VPOS_CRDS_RECON_AUTH_ACQ</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'AUTH_ACQ' AS RECON_SIDE,SID,I037_RET_REF_NUM, I002_NUMBER,'' AS CHANNELSERNO, I004_AMT_TRXN, LTIMESTAMP AS TRA_DATE, I000_MSG_TYPE,
	'' AS SERNO,I039_RESP_CD AS RESP_CODE,TIME_IN_MILLIS,'AUTH_ACQUIRER_STG' AS SOURCE_TARGET,MAIN_REV_IND,TRAN_CUR,I006_AMT_BILL AS LOCAL_AMT FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='APIVISA' 
	AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') AND SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="199">
		<name>PAYROL_RECON_GL_2279</name>
		<queryType>ForceMatchStagingSelect</queryType>
		<queryString>
SELECT 'GL_2279' AS RECON_SIDE,SID,DOC_NUM AS COMP_CODE,TRA_AMT AS AMOUNT,ORIGT_TRA_DATE AS TRA_DATE,'GL_2279_STG' AS SOURCE_TARGET,
						WORKFLOW_STATUS,RECON_ID,VERSION FROM GL_2279_STG WHERE  SID=?

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	      <!-- REPORT  QUERIES -->
               
            <!-- GL_1002 REPORT QUERIES-->
            
         <query id="1000">
		<name>REPORT_GL_1002_STG_DEBIT</name>
		<queryType>GL_1002_STG_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1002_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1001">
		<name>REPORT_GL_1002_STG_CREDIT</name>
		<queryType>GL_1002_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1002_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1002">
		<name>REPORT_GL_1002_STG_EX_DEBIT</name>
		<queryType>GL_1002_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1002_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1003">
		<name>REPORT_GL_1002_STG_EX_CREDIT</name>
		<queryType>GL_1002_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1002_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1004">
		<name>REPORT_GL_1002_IRIS_STG_MAIN</name>
		<queryType>GL_1002_IRIS_STG_MAIN</queryType>
		<queryString>
          SELECT count(*),ISNULL(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('01','82') AND MAIN_REV_IND='MAIN'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1005">
		<name>REPORT_GL_1002_IRIS_STG_REVERSAL</name>
		<queryType>GL_1002_IRIS_STG_REVERSAL</queryType>
		<queryString>
          SELECT count(*),ISNULL(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('01','82') AND MAIN_REV_IND='REVERSAL'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
		<!-- GL_1006 REPORT QUERIES -->
	
	
	<query id="1006">
		<name>REPORT_GL_1006_STG_DEBIT</name>
		<queryType>GL_1006_STG_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) FROM GL_1006_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1007">
		<name>REPORT_GL_1006_STG_CREDIT</name>
		<queryType>GL_1006_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) FROM GL_1006_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1008">
		<name>REPORT_GL_1006_STG_EX_DEBIT</name>
		<queryType>GL_1006_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) FROM GL_1006_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1009">
		<name>REPORT_GL_1006_STG_EX_CREDIT</name>
		<queryType>GL_1006_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) FROM GL_1006_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1010">
		<name>REPORT_GL_1006_IRIS_STG_MAIN</name>
		<queryType>GL_1006_IRIS_STG_MAIN</queryType>
		<queryString>
          SELECT count(*),ISNULL(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('21','25') AND MAIN_REV_IND='MAIN'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1011">
		<name>REPORT_GL_1006_IRIS_STG_REVERSAL</name>
		<queryType>GL_1006_IRIS_STG_REVERSAL</queryType>
		<queryString>
          SELECT count(*),ISNULL(SUM(AMT_TRAN),0.00) FROM IRIS_STG
					WHERE  RESP_CODE IN ('000','036','037') AND PROC_CODE_FIRST_2 in ('21','25') AND MAIN_REV_IND='REVERSAL'
					AND DATE_LOC_TRAN BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- GL_1015 REPORT QUERIES -->
	
	<query id="1012">
		<name>REPORT_GL_1015_STG_DEBIT</name>
		<queryType>GL_1015_STG_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1015_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1013">
		<name>REPORT_GL_1015_STG_CREDIT</name>
		<queryType>GL_1015_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1015_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1014">
		<name>REPORT_GL_1015_STG_EX_DEBIT</name>
		<queryType>GL_1015_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1015_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1015">
		<name>REPORT_GL_1015_STG_EX_CREDIT</name>
		<queryType>GL_1015_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1015_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1016">
		<name>REPORT_GL_1015_QCB_STG_MAIN</name>
		<queryType>GL_1015_QCB_STG_MAIN</queryType>
		<queryString>
          SELECT COUNT(*),ISNULL(SUM(TRANS_AMOUNT),0.00)  FROM QCB_STG WHERE 
	    	CHANNEL='ATM' AND MAIN_REV_IND='MAIN' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1017">
		<name>REPORT_GL_1015_QCB_STG_REVERSAL</name>
		<queryType>GL_1015_QCB_STG_REVERSAL</queryType>
		<queryString>
          SELECT COUNT(*),ISNULL(SUM(TRANS_AMOUNT),0.00)  FROM QCB_STG WHERE 
	    	CHANNEL='ATM' AND MAIN_REV_IND='REVERSAL' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
               
               
               <!-- GL_1016 REPORT QUERIES -->
               
     <query id="1018">
     <name>REPORT_GL_1016_STG_DEBIT</name>
     <queryType>GL_1016_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1016_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1019">
		<name>REPORT_GL_1016_STG_CREDIT</name>
		<queryType>GL_1016_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1016_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1020">
		<name>REPORT_GL_1016_STG_EX_DEBIT</name>
		<queryType>GL_1016_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1016_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1021">
		<name>REPORT_GL_1016_STG_EX_CREDIT</name>
		<queryType>GL_1016_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1016_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1022">
		<name>REPORT_GL_1016_QCB_STG_MAIN</name>
		<queryType>GL_1016_QCB_STG_MAIN</queryType>
		<queryString>
          SELECT COUNT(*),ISNULL(SUM(TRANS_AMOUNT),0.00) FROM QCB_STG WHERE
		  ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='POS' AND MAIN_REV_IND='MAIN' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1023">
		<name>REPORT_GL_1016_QCB_STG_REVERSAL</name>
		<queryType>GL_1016_QCB_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),ISNULL(SUM(TRANS_AMOUNT),0.00) FROM QCB_STG WHERE
		ACQ_BANK_CODE=3 AND ISS_BANK_CODE!=3 AND CHANNEL='POS' AND MAIN_REV_IND='REVERSAL' AND SETTL_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>  
	
	
	<query id="1024">
		<name>REPORT_GL_1016_ACQUIRER_STG_MAIN</name>
		<queryType>GL_1016_ACQUIRER_STG_MAIN</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,SUM(I004_AMT_TRXN) AS AMOUNT FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='CTIDHI2' 
		AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00') 
		AND (CARDBIN IN(SELECT PREFIX FROM MDT_NAPS_CARD_BINS) OR (CARDBIN LIKE '34%' OR CARDBIN LIKE '37%'))
		AND MAIN_REV_IND='MAIN' AND LTIMESTAMP BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1025">
		<name>REPORT_GL_1016_ACQUIRER_STG_REVERSAL</name>
		<queryType>GL_1016_ACQUIRER_STG_REVERSAL</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,SUM(I004_AMT_TRXN) AS AMOUNT FROM AUTH_ACQUIRER_STG WHERE PROCESS_NAME='CTIDHI2'
	   AND I018_MERCH_TYPE!=6011 AND I039_RESP_CD IN('00')
	   AND (CARDBIN IN(SELECT PREFIX FROM MDT_NAPS_CARD_BINS) OR (CARDBIN LIKE '34%' OR CARDBIN LIKE '37%'))
	   AND MAIN_REV_IND='REVERSAL' AND LTIMESTAMP BETWEEN ? AND ?
       </queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>  
	
	
	<!-- GL_1472 REPORT QURIES -->
	
	 <query id="1026">
     <name>REPORT_GL_1472_STG_DEBIT</name>
     <queryType>GL_1472_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1472_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1027">
		<name>REPORT_GL_1472_STG_CREDIT</name>
		<queryType>GL_1472_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1472_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1028">
		<name>REPORT_GL_1472_STG_EX_DEBIT</name>
		<queryType>GL_1472_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1472_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1029">
		<name>REPORT_GL_1472_STG_EX_CREDIT</name>
		<queryType>GL_1472_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1472_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1030">
		<name>REPORT_GL_1472_CTL_STG_MAIN</name>
		<queryType>GL_1472_CTL_STG_MAIN</queryType>
		<queryString>
         SELECT count(*),ISNULL(SUM(I004_AMT_TRXN),0.00) FROM BATCHES_STG BATCH 
		 INNER JOIN CTXNS_STG CTXN ON CTXN.BATCHSERNO=BATCH.SERNO INNER JOIN CISO_STG CISO ON 
		 CISO.SERNO=CTXN.SERNO  WHERE charindex('Transfer',batch.FILENAME )!=0 
		 AND BIN in(select BIN from	MDT_DB_CARD_BINS) AND STGENERAL='POST' AND MAIN_REV_IND='MAIN'
		 AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1031">
		<name>REPORT_GL_1472_CTL_STG_REVERSAL</name>
		<queryType>GL_1472_CTL_STG_REVERSAL</queryType>
		<queryString>
         SELECT count(*),ISNULL(SUM(I004_AMT_TRXN),0.00) FROM BATCHES_STG BATCH 
		 INNER JOIN CTXNS_STG CTXN ON CTXN.BATCHSERNO=BATCH.SERNO INNER JOIN CISO_STG CISO ON 
		 CISO.SERNO=CTXN.SERNO  WHERE charindex('Transfer',batch.FILENAME )!=0 
		 AND BIN in(select BIN from	MDT_DB_CARD_BINS) AND STGENERAL='POST' AND MAIN_REV_IND='REVERSAL'
		 AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- GL_1482 REPORT QUERIES -->
	
	 <query id="1032">
     <name>REPORT_GL_1482_STG_DEBIT</name>
     <queryType>GL_1482_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1482_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1033">
		<name>REPORT_GL_1482_STG_CREDIT</name>
		<queryType>GL_1482_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1482_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1034">
		<name>REPORT_GL_1482_STG_EX_DEBIT</name>
		<queryType>GL_1482_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1482_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1035">
		<name>REPORT_GL_1482_STG_EX_CREDIT</name>
		<queryType>GL_1482_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_1482_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1036">
		<name>REPORT_GL_1482_MASTER_STG_MAIN</name>
		<queryType>GL_1482_MASTER_STG_MAIN</queryType>
		<queryString>
         SELECT COUNT(*),ISNULL(SUM(trans_amount),0.00) FROM MASTER_STG WHERE  MAIN_REV_IND='MAIN'
		 AND tran_date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1037">
		<name>REPORT_GL_1482_MASTER_STG_REVERSAL</name>
		<queryType>GL_1482_MASTER_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),ISNULL(SUM(trans_amount),0.00) FROM MASTER_STG WHERE  MAIN_REV_IND='REVERSAL'
		AND tran_date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	
	<!-- GL-2247 REPORT QUERIES -->
	
	
	 <query id="1038">
     <name>REPORT_GL_2247_STG_DEBIT</name>
     <queryType>GL_2247_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2247_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1039">
		<name>REPORT_GL_2247_STG_CREDIT</name>
		<queryType>GL_2247_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2247_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1040">
		<name>REPORT_GL_2247_STG_EX_DEBIT</name>
		<queryType>GL_2247_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2247_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1041">
		<name>REPORT_GL_2247_STG_EX_CREDIT</name>
		<queryType>GL_2247_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2247_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1042">
		<name>REPORT_GL_2247_VISA_ISSUER_STG_MAIN</name>
		<queryType>GL_2247_VISA_ISSUER_STG_MAIN</queryType>
		<queryString>
         SELECT COUNT(*),ISNULL(SUM(Source_Amt),0.00)  FROM VISA_ISSUER_STG  WHERE   MAIN_REV_IND='MAIN'
		 AND Purchase_Date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1043">
		<name>REPORT_GL_2247_VISA_ISSUER_STG_REVERSAL</name>
		<queryType>GL_2247_VISA_ISSUER_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),ISNULL(SUM(Source_Amt),0.00)  FROM VISA_ISSUER_STG  WHERE   MAIN_REV_IND='REVERSAL'
		 AND Purchase_Date BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
             
             
             <!-- GL_2279 REPORT QUERIES -->
             
             
             <query id="1044">
     <name>REPORT_GL_2279_STG_DEBIT</name>
     <queryType>GL_2279_STG_DEBIT</queryType>
     <queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2279_STG WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1045">
		<name>REPORT_GL_2279_STG_CREDIT</name>
		<queryType>GL_2279_STG_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_2279_STG WHERE 
          TRA_DATE BETWEEN ? AND ? AND DEBIT_CREDIT_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1046">
		<name>REPORT_GL_2279_STG_EX_DEBIT</name>
		<queryType>GL_2279_STG_EX_DEBIT</queryType>
		<queryString>
       SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2279_STG_EX WHERE 
       TRA_DATE BETWEEN ? AND ? AND DEB_CRE_IND='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1047">
		<name>REPORT_GL_2279_STG_EX_CREDIT</name>
		<queryType>GL_2279_STG_EX_CREDIT</queryType>
		<queryString>
          SELECT COUNT(*) AS COUNT,ISNULL(SUM(TRA_AMT),0.00) AS AMOUNT FROM GL_2279_STG_EX WHERE 
          TRA_DATE  BETWEEN ? AND ? AND DEB_CRE_IND='2' 
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1048">
		<name>REPORT_GL_2279_DPAY_STG_MAIN</name>
		<queryType>GL_2279_DPAY_STG_MAIN</queryType>
		<queryString>
         SELECT COUNT(*),ISNULL(SUM(I004_AMT_TRXN),0.00)FROM DPAY_STG WHERE   MAIN_REV_IND='MAIN' AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1049">
		<name>REPORT_GL_2279_DPAY_STG_REVERSAL</name>
		<queryType>GL_2279_DPAY_STG_REVERSAL</queryType>
		<queryString>
        SELECT COUNT(*),ISNULL(SUM(I004_AMT_TRXN),0.00)FROM DPAY_STG WHERE   MAIN_REV_IND='REVERSAL' AND I013_TRXN_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- GL_1015 BALANCING REPORT QUERIES -->
	
	<query id="1050">
		<name>BALANCE_REPORT_GL_1015_ISSUER_NATM_DEBIT_RECON</name>
		<queryType>ISSUER_NATM_DEBIT_RECON</queryType>
		<queryString>
          SELECT COUNT(*)AS COUNT, ISNULL(SUM(TRA_AMT),0.00)AS AMOUNT FROM ISSUER_NATM_DEBIT_RECON 
	       WHERE MATCH_TYPE IN('AU','MU') AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1051">
		<name>BALANCE_REPORT_GL_1015</name>
		<queryType>GL_1015_STG_ORPHANS</queryType>
		<queryString>
         SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_NATM_DEBIT_GL_1015_STG
		 WHERE RECON_ID IS NULL AND SID NOT IN (SELECT SID FROM ISSUER_NATM_DEBIT_RECON WHERE RECON_SIDE='GL_1015' AND ACTIVE_INDEX='Y')
		 AND ACTIVE_INDEX='Y' AND TRA_DATE  BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1052">
		<name>BALANCE_REPORT_GL_1015_ACQ_NATM_CRDS_RECON</name>
		<queryType>ACQ_NATM_CRDS_RECON</queryType>
		<queryString>
        SELECT COUNT(*),ISNULL(SUM(TRA_AMT),0.00)FROM ACQ_NATM_CRDS_RECON WHERE MATCH_TYPE IN('MU','AU')
		AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	
	<!-- GL_1016 BALANCING REPORT QURIES -->
               
               <query id="1053">
		<name>BALANCE_REPORT_GL_1016_ISSUER_NPOS_DEBIT_RECON</name>
		<queryType>ISSUER_NPOS_DEBIT_RECON</queryType>
		<queryString>
          SELECT COUNT(*)AS COUNT, ISNULL(SUM(TRA_AMT),0.00)AS AMOUNT FROM ISSUER_NPOS_DEBIT_RECON
		  WHERE MATCH_TYPE IN('AU','MU') AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1054">
		<name>BALANCE_REPORT_GL_1016</name>
		<queryType>GL_1016_STG_ORPHANS</queryType>
		<queryString>
         SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_NPOS_DEBIT_GL_1016_STG
		 WHERE RECON_ID IS NULL AND SID NOT IN (SELECT SID FROM ISSUER_NPOS_DEBIT_RECON WHERE RECON_SIDE='GL_1016' AND ACTIVE_INDEX='Y')
		 AND ACTIVE_INDEX='Y' AND TRA_DATE  BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1055">
		<name>BALANCE_REPORT_GL_1016_ACQ_NPOS_CRDS_RECON</name>
		<queryType>ACQ_NPOS_CRDS_RECON</queryType>
		<queryString>
        SELECT COUNT(*),ISNULL(SUM(TRA_AMT),0.00)FROM ACQ_NPOS_CRDS_RECON WHERE MATCH_TYPE IN('MU','AU')
		AND ACTIVE_INDEX='Y' AND TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_1472/0 BALANCING REPORT QUERIES -->
	
	      <query id="1056">
		<name>BALANCE_REPORT_GL_14720_ACQ_VATM_CRDS_RECON_UNMATCH</name>
		<queryType>ACQ_VATM_CRDS_RECON_UNMATCH</queryType>
		<queryString>
          SELECT COUNT(*)AS COUNT, ISNULL(SUM(TRA_AMT),0.00)AS AMOUNT FROM ACQ_VATM_CRDS_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1057">
		<name>BALANCE_REPORT_GL_14720</name>
		<queryType>GL_14720_STG_ORPHANS</queryType>
		<queryString>
         SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ACQ_VATM_CRDS_GL_1472_STG_ORPHANS WHERE TRA_DATE  BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_1472/2 BALANCING REPORT QUERIES -->
	
	
	<query id="1058">
		<name>BALANCE_REPORT_GL_14722_ACQ_UATM_CRDS_RECON_UNMATCH</name>
		<queryType>ACQ_UATM_CRDS_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, ISNULL(SUM(TRA_AMT),0.00)AS AMOUNT FROM ACQ_UATM_CRDS_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1059">
		<name>BALANCE_REPORT_GL_14722</name>
		<queryType>GL_14722_STG_ORPHANS</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ACQ_UATM_CRDS_GL_1472_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<!-- GL_1482 BALANCING REPORT QUERIES -->
	
	<query id="1060">
		<name>BALANCE_REPORT_GL_1482_ACQ_MATM_CRDS_RECON_UNMATCH</name>
		<queryType>ACQ_MATM_CRDS_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, ISNULL(SUM(TRA_AMT),0.00)AS AMOUNT FROM ACQ_MATM_CRDS_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1061">
		<name>BALANCE_REPORT_GL_1482</name>
		<queryType>GL_1482_STG_ORPHANS</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ACQ_MATM_CRDS_GL_1482_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_2247 BALANCE REPORT QUERIES -->
	
	<query id="1062">
		<name>BALANCE_REPORT_GL_2247_ISSUER_VATM_DEBIT_RECON_UNMATCH</name>
		<queryType>ISSUER_VATM_DEBIT_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VATM_DEBIT_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1063">
		<name>BALANCE_REPORT_GL_2247_VATM</name>
		<queryType>GL_2247_STG_ORPHANS_VATM</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VATM_DEBIT_GL_2247_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1064">
		<name>BALANCE_REPORT_GL_2247_ISSUER_VPOS_DEBIT_RECON_UNMATCH</name>
		<queryType>ISSUER_VPOS_DEBIT_RECON_UNMATCH</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT, ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VPOS_DEBIT_RECON_UNMATCH WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1065">
		<name>BALANCE_REPORT_GL_2247_VPOS</name>
		<queryType>GL_2247_STG_ORPHANS_VPOS</queryType>
		<queryString>
        SELECT COUNT(*)AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00)AS AMOUNT FROM ISSUER_VPOS_DEBIT_GL_2247_STG_ORPHANS  WHERE TRA_DATE BETWEEN ? AND ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

<!-- GL_1002 TREMINAL BALANCING REPORT --> 

	<query id="1066">
		<name>GL_1002_TERMINALS</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM FROM MDT_ATM_SUSPENSE_ACCOUNTS WHERE
			LEDGER='1002' AND CUR='1' AND ACTIVE_INDEX='Y' ORDER BY ATM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1067">
		<name>GL_1002_ATM_LOCATION</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM_LOCATION,CUR FROM MDT_ATM_SUSPENSE_ACCOUNTS
			WHERE ATM=? AND LEDGER='1002' AND ACTIVE_INDEX='Y' AND CUR='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1068">
		<name>GL_1002_ADJMENTS_DISPUTES</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT (SELECT ISNULL(sum(TRA_AMT),0.00) FROM
			GL_1002_STG_EX WHERE TELL_ID
			NOT IN('9971','9953','9911') AND
			VAL_DATE
			BETWEEN ? AND ? AND CUR_CODE='1' AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
			AND REMARKS NOT LIKE 'RES ATM%' AND REMARKS NOT LIKE 'RPL ATM%' AND
			DEB_CRE_IND='2')-(SELECT ISNULL(sum(TRA_AMT),0.00) FROM
			GL_1002_STG_EX WHERE TELL_ID NOT IN('9971','9953','9911') AND
			VAL_DATE BETWEEN ? AND ? AND CUR_CODE='1' AND
			CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND REMARKS NOT LIKE 'RES ATM%' AND
			REMARKS NOT LIKE 'RPL ATM%' AND DEB_CRE_IND='1') ADJ_DIS
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1069">
		<name>GL_1002_CASH_RECEIVED_FROM_ATM</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ISNULL(SUM(CONVERT(DECIMAL(15,2),TRA_AMT)),0.00)
			AS RSD_AMT FROM GL_1002_STG_EX WHERE CHARINDEX('RES ATM',REMARKS)!=0
			AND TELL_ID='9930' AND VAL_DATE BETWEEN ? AND ? AND CUR_CODE='1' AND
			CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1070">
		<name>GL_1002_CASH_LOADED_IN_ATM</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT
			ISNULL(SUM(CONVERT(DECIMAL(15,2),-TRA_AMT)+.00),0.00) AS RPL_AMT FROM
			GL_1002_STG_EX WHERE CHARINDEX('RPL ATM',REMARKS)!=0
			AND
			TELL_ID='9930' AND VAL_DATE BETWEEN ? AND ?
			AND CUR_CODE='1' AND
			CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1071">
		<name>GL_1002_CLOSING_BALANCE_AS_PER_FILE</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE
			LED_CODE='1002' AND BRA_CODE='201'
			AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND CUR_CODE='1' AND PRE_BANK_DATE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1072">
		<name>GL_1002_OPENING_BALANCE</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE LED_CODE='1002' AND
			BRA_CODE='201'
			AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND CUR_CODE='1' AND
			PRE_BANK_DATE=DATEADD(DD,-1,?)

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1073">
		<name>GL_1002_ADDED_TRANSACTIONS</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT((SELECT ISNULL(SUM(TRA_AMT),0.00) FROM GL_1002_STG WHERE PROC_CODE
			IN('01','82') AND MAIN_REV_IND='MAIN'
			AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND
			CUR_CODE='1')-
			(SELECT ISNULL(SUM(TRA_AMT),0.00) FROM GL_1002_STG WHERE PROC_CODE
			IN('01','82') AND MAIN_REV_IND='REVERSAL'
			AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND
			CUR_CODE='1')) RESULT

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1074">
		<name>GL_1002_GL_CUT_OFF</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM
			GL_1002_STG WHERE
			TRAN_DATE &gt;= (SELECT MAX(END_DATE) FROM BCS_ATM_REPL WHERE END_DATE=? AND
			DISPLAYID=?)
			AND TRA_TIME &gt;=(SELECT MAX(END_TIME) FROM BCS_ATM_REPL WHERE
			END_DATE=? AND DISPLAYID=?)
			AND TRA_DATE=? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND  RETR_REF_NO NOT IN (SELECT RETR_REF_NO  FROM GL_1002_STG WHERE MAIN_REV_IND='REVERSAL')
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1075">
		<name>GL_1002_END_DATE_TIME</name>
		<queryType>GL_1002 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT MAX(END_DATETIME) FROM BCS_ATM_REPL WHERE DISPLAYID=? AND END_DATE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<!-- GL_1006 TREMINAL BALANCING REPORT -->
	
  <query id="1076">
		<name>GL_1006_TERMINALS</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM FROM MDT_ATM_SUSPENSE_ACCOUNTS WHERE LEDGER='1006' AND CUR='1' AND ACTIVE_INDEX='Y' ORDER BY ATM
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1077">
		<name>GL_1006_ATM_LOCATION</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ATM_LOCATION,CUR FROM MDT_ATM_SUSPENSE_ACCOUNTS WHERE ATM=? AND LEDGER='1006' AND ACTIVE_INDEX='Y' AND CUR='1'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1078">
		<name>GL_1006_ADJMENTS_DISPUTES</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT (-(SELECT ISNULL(SUM(TRA_AMT),0.0) AS ADJ FROM GL_1006_STG_EX
			WHERE TELL_ID NOT IN('9971','9953','9911') AND DEB_CRE_IND=1 
			AND REMARKS NOT LIKE 'DEP ATM%' AND VAL_DATE BETWEEN ? AND ?
			AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)+
			(SELECT ISNULL(SUM(TRA_AMT),0.0) AS ADJ FROM GL_1006_STG_EX
			WHERE TELL_ID NOT IN('9971','9953','9911') AND DEB_CRE_IND=2 AND VAL_DATE BETWEEN ? AND ?
			AND REMARKS NOT LIKE 'DEP ATM%' AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)) ADJ
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	<query id="1079">
		<name>GL_1006_CASH_RECEIVED_FROM_ATM</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT ISNULL(SUM(CONVERT(DECIMAL(20,2),TRA_AMT)),0.00) as TOTAL
			FROM GL_1006_STG_EX WHERE TELL_ID='9930' AND CHARINDEX('DEP ATM',REMARKS)!=0
			AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	<query id="1080">
		<name>GL_1006_CLOSING_BALANCE_AS_PER_FILE</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE LED_CODE='1006' AND BRA_CODE='201'
			AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND PRE_BANK_DATE= ?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1081">
		<name>GL_1006_OPENING_BALANCE</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT PRE_DAY_CRNT_BAL FROM LEDG_CLOSING_STG WHERE LED_CODE='1006' AND BRA_CODE='201'
			 AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=? AND PRE_BANK_DATE=DATEADD(DD,-1,?)

		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	 <query id="1082">
		<name>GL_1006_LESS_TRANSACTIONS</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT((SELECT ISNULL(SUM(-TRA_AMT),0.00) FROM GL_1006_STG WHERE   MAIN_REV_IND='MAIN'
			AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)-
		   (SELECT ISNULL(ISNULL(SUM(-TRA_AMT),0),0.00) FROM GL_1006_STG WHERE  MAIN_REV_IND='REVERSAL'
		    AND VAL_DATE BETWEEN ? AND ? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?)) RESULT
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> 
	
	<query id="1083">
		<name>GL_1006_GL_CUT_OFF</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT COUNT(*) AS COUNT,ISNULL(SUM(LOCAL_AMT),0.00) AS AMOUNT FROM GL_1006_STG WHERE
			TRAN_DATE&gt;=(SELECT MAX(END_DATE) FROM BCS_ATM_REPL WHERE END_DATE=? AND DISPLAYID=?)
			AND TRA_TIME&gt;=(SELECT MAX(END_TIME) FROM BCS_ATM_REPL WHERE END_DATE=? AND DISPLAYID=?)
			AND TRA_DATE=? AND CONCAT(CUS_NUM,SUB_ACCT_CODE)=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>

	<query id="1084">
		<name>GL_1006_END_DATE_TIME</name>
		<queryType>GL_1006 TREMINAL BALANCING REPORT</queryType>
		<queryString>
			SELECT MAX(END_DATETIME) FROM BCS_ATM_REPL WHERE DISPLAYID=? AND END_DATE=?
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	

	
</queries>


