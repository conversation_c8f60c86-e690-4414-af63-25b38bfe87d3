
package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.integration.Integrations;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.persistance.AscentPersistanceIntf;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ReProcessPlugIn extends BasicDataSource implements PagesConstants {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6870261822605048138L;
	
	AscentWebMetaInstance ascentWebMetaInstance= AscentWebMetaInstance.getInstance();//getting instance of this SingleTon Class
	EtlMetaInstance etlMetaInstance =EtlMetaInstance.getInstance();//getting instance of this SingleTon Class
	public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();

		User user = (User) httpSession.getAttribute("userId");

		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		//TODO: is user authorized utility to verify user priviliges.
		
		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
		String integrationName = (String) reqCriteria.get("integrationName");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String dsName=(String)reqCriteria.get("dsName");
		String centrifugalAmountField="";
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
		if(reqCriteria.get("centrifugalAmount")!=null){
		 centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
		 
		}else{
			centrifugalAmountField=null;
		}
		//TODO: operation synchronization.
		if (selectedRecords != null) {

			StringBuilder commentSb = new StringBuilder();
			
			List<Object> workflowIds = new ArrayList<Object>();
			 for (Map<String, Object> rec : selectedRecords) {
						System.out.println(rec.get("WORKFLOW_STATUS"));
				if ((rec != null && rec.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec != null && rec.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
					
				}else{
					workflowIds.add(rec.get(SID));
				}
				
			} 
			
			if (workflowIds.size() > 0) {
				result = new HashMap<String, Object>();
				String commentPrefix = "";
				if (workflowIds.size() == 1) {
					commentSb.append("Selected record with SID ");
				} else if (workflowIds.size() > 1) {
					commentSb.append("Selected records with SIDs ");
				}
				for (Object obj : workflowIds) {
					if (commentSb.length() != 0) {
						commentSb.append(",");
					}
					commentSb.append(obj);
				}
				commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
				updateResultStatus(result, FAILED, commentPrefix);
				response.setData(result);
				return response;
			}
						
		}

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(DS_NAME, dsName);
		
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		//if(centrifugalAmountField!=null){
		paramsMap.put("centrifugalAmountField",centrifugalAmountField);
		//}	
		
		

		result = process(paramsMap);

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);

		return result;
	}

	

	public Map<String, Object> reject() {
		return null;
	}

	public Map<String, Object> approve() {
		return null;
	}

	// will Submit the operation basis on user credintials
	@SuppressWarnings("finally")
	private Map<String, Object> process(Map<String, Object> reprocessArgs) {

		Connection connection = null;
		Map<String, Object> result = null;
		try {
			connection = DbUtil.getConnection();
		
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			String userId = (String) reprocessArgs.get(USER_ID);
		
			reprocessArgs.put(PERSIST_CLASS, RE_PROCESS_PLUGIN_CLASS_NAME);
			activityDataMap.put("activity_data", reprocessArgs);
			
			

			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

			if (userAdminManager.isUserUnderWorkflow(user)) {
				result = new HashMap<String, Object>();

				String activityStatus = PENDING_APPROVAL;

				String businessArea = (String) reprocessArgs.get(BUSINES_AREA);
				String reconName = (String) reprocessArgs.get(RECON_NAME);
				String comments = (String) reprocessArgs.get(COMMENTS);
				
				userAdminManager.createActivity(connection, user, businessArea, reconName, UPSTREAM_MODULE,
						RE_PROCESS_OPERATION, activityDataMap, activityStatus, comments);

				updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				///////////////////////////////////////////////////////////////////////////
				
				String integrationName= (String) reprocessArgs.get(INTEGRATION_NAME);
				LoadRegulator loadRegulator=new LoadRegulator();
				InsertRegulator insertRegulator=new InsertRegulator();
				
				//String tableNameStg=integrationName+"_STG";
				//String tableNameAudit=integrationName+"_AUDIT";
				//String tableNameStg="IRIS_STG_EX";
				//String tableNameAudit="IRIS_STG_AUDIT";
				String tableNameStg=integrationName+"_STG_EX";
			//	String tableNameAudit=integrationName+"_STG_AUDIT";
				String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
				Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameStg, connection);
				
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				try{
			
					
			
					
					List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) reprocessArgs.get(SELECTED_RECORDS);
					for(Map<String,Object> selectedRec:selectedRecords){
						selectedRec.put("WORKFLOW_STATUS", "Y");
						String reqComments=user.getUserId()+" : "+(String) reprocessArgs.get(COMMENTS);
						selectedRec.put("ACTIVITY_COMMENTS", reqComments);
						
						selectAuditStmt=connection.prepareStatement(auditSelectQry);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
			//	audit(loadRegulator, insertRegulator, insertQueryConf, selectAuditStmt, auditInsertPstmt, selectedRec,true);
					}
				
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
				}
				//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////	
				//audit();
				return result;
			} else {

			

				result = persist(activityDataMap, APPROVED, connection);

			}
		} catch (Exception e) {
			e.printStackTrace();

			updateResultStatus(result, FAILED, OPERATION_FAILED);
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return result;
		}
		
	}

	public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection
			) {

		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator=new LoadRegulator();
		InsertRegulator insertRegulator=new InsertRegulator();
		try {
            String userid=(String) activityDataMap.get("userId");
			String comment=(String) activityDataMap.get("comment");
			Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
			String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
			List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
			Integration integration=null;
			//for(){}
			connection = DbUtil.getConnection();

			if (APPROVED.equalsIgnoreCase(status)) {
				
				EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();

				// meta data test
				Properties bootProps = etlMetaInstance.getBootProperties();
				Properties dbProps = etlMetaInstance.getDbProperties();
				Properties appProps = etlMetaInstance.getApplicationProperties();
				Integrations integrations = etlMetaInstance.getEtlConfs();
				Queries queries=etlMetaInstance.getEtlQueryConfs();
				List<Integration> list=integrations.getIntegration();
				for(Integration integr : list){
					if(integr.getName().equalsIgnoreCase(integrationName)){
						
						integration=integr;
					}
				}
				
AscentPersistanceIntf PersistancePlugin =(AscentPersistanceIntf)((AscentPersistanceIntf)((Class.forName((integration.getPersistancePlugin()).trim())).newInstance()));

				String tableNameStg=integrationName+"_STG_EX";
			//	String tableNameAudit=integrationName+"_STG_AUDIT";
				//String tableNameStg="IRIS_STG_EX";
			//	String tableNameAudit="IRIS_STG_AUDIT";
				
				String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";

				Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameStg, connection);
				
			//	String query="UPDATE "+tableNameStg+" set ACTIVE_INDEX='N',Status='SUPPRESS',WORKFLOW_STATUS='N',VERSION=? where SID=?";
				
				PreparedStatement updateStmt=null;
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				
				try{
					
				//	updateStmt=connection.prepareStatement(query);
					List<Map<String,Object>> recordslist=new ArrayList<Map<String,Object>>(); 
					
				for(Map<String,Object> rec:records){
					
					selectAuditStmt=connection.prepareStatement(auditSelectQry);
					auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
					
					String approveComments=userid+" : "+comment;
					//rec.put("VERSION",version);
					rec.put("WORKFLOW_STATUS", "N");
					//rec.put("ACTIVE_INDEX", "N");
					rec.put("ACTIVITY_COMMENTS", approveComments);
					int version=auditApproved(loadRegulator, insertRegulator, insertQueryConf, selectAuditStmt, auditInsertPstmt, rec);
					//long version=   (long) rec.get("VERSION");
				//	++version;
					
					rec.put("VERSION",version);
					rec.put("COMMENTS","");
					System.out.println("fgdgdf");
				//List auditrecords=	
					//Long version=(Long) rec.get("VERSION");
				//	++version;
				//	updateStmt.setLong(1,version);
				//	updateStmt.setLong(2,(Long)rec.get(SID));
					//rec.put("VERSION", version);
					
			//	recordslist.addAll(auditrecords);
					//updateStmt.addBatch();
				}
				PersistancePlugin.persist(integration, queries, connection, records, true);
				
				
				//updateStmt.executeUpdate();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(updateStmt);
					
				}
				
			} else if (REJECTED.equalsIgnoreCase(status)) {
				
				
				String tableNameStg=integrationName+"_STG_EX";
				//String tableNameAudit=integrationName+"_STG_AUDIT";
				String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?)-1 and sid=?";
				Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameStg, connection);
				
				//String query="UPDATE "+tableNameStg+" set ACTIVE_INDEX='Y',Status='STAGING',WORKFLOW_STATUS='N',VERSION=? where SID=?";
				
				PreparedStatement updateStmt=null;
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				try{
					
				//	updateStmt=connection.prepareStatement(query);
					
					
				for(Map<String,Object> rec:records){
					
					selectAuditStmt=connection.prepareStatement(auditSelectQry);
					auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
					
					String approveComments=userid+" : "+comment;
					//rec.put("VERSION",version);
					rec.put("WORKFLOW_STATUS", "N");
				//	rec.put("ACTIVE_INDEX", "N");
					rec.put("ACTIVITY_COMMENTS", approveComments);
				//	audit(loadRegulator, insertRegulator, insertQueryConf, selectAuditStmt, auditInsertPstmt, rec,false);
					//Long version=(Long) rec.get("VERSION");
				//	++version;
				//	updateStmt.setLong(1,version);
				//	updateStmt.setLong(2,(Long)rec.get(SID));
					//rec.put("VERSION", version);
					
					
					//updateStmt.addBatch();
				}
			//	updateStmt.executeUpdate();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(updateStmt);
					
				}
				
			} else if("PENDING".equalsIgnoreCase(status)){/*

				String tableName=integrationName+"_STG";
				String query="UPDATE "+tableName+" set ACTIVE_INDEX='Y',Status='APPROVED',WORKFLOW_STATUS='Y' where SID=?";
				PreparedStatement updateStmt=null;
				
				try{
					updateStmt=connection.prepareStatement(query);
					
				for(Map<String,Object> rec:records){
					updateStmt.setLong(1,(Long)rec.get(SID));
					updateStmt.addBatch();
				}
				updateStmt.executeUpdate();
				
				}catch(Exception e){
					e.printStackTrace();
				}
				
			
				updateResultStatus(result, FAILED, "Undefined Action");
			*/}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/*private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec, boolean b)
					throws ClassNotFoundException, SQLException {
		String QueryParam=insertQueryConf.getQueryParam();
		List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
		System.out.println(auditData);
		
		if(auditData!=null){
			for(Map<String,Object> auditRec:auditData){
				Map paramValueMap=new HashMap();
				int version=   Integer.valueOf(auditRec.get("VERSION")+"");
				++version;
				auditRec.put("WORKFLOW_STATUS",rec.get("WORKFLOW_STATUS"));
				
				auditRec.put("ACTIVITY_COMMENTS", rec.get("ACTIVITY_COMMENTS"));
				
				auditRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				rec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				if(b){
					
				//	auditRec.put("ACTIVE_INDEX", "N");
					rec.put("ACTIVE_INDEX", "N");
					rec.put("VERSION", version);
					paramValueMap.put("PARAM_VALUE_MAP", rec);
					}
					else{
						auditRec.put("ACTIVE_INDEX", "Y");
						rec.put("ACTIVE_INDEX", "Y");
						rec.put("VERSION", version);
						++version;
						auditRec.put("VERSION",version);
						
						paramValueMap.put("PARAM_VALUE_MAP", auditRec);
					}
				
				
				
			insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
		}
	}*/
	
	private int auditApproved(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
					throws ClassNotFoundException, SQLException {
		String QueryParam=insertQueryConf.getQueryParam();
		List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
		int version = 0;
		if(auditData!=null){
			for(Map<String,Object> auditRec:auditData){
				Map paramValueMap=new HashMap();
				 version=   Integer.valueOf(auditRec.get("VERSION")+"");
				++version;
				auditRec.put("WORKFLOW_STATUS",rec.get("WORKFLOW_STATUS"));
				auditRec.put("VERSION",version);
				auditRec.put("ACTIVITY_COMMENTS", rec.get("ACTIVITY_COMMENTS"));
				auditRec.put("ACTIVE_INDEX", rec.get("ACTIVE_INDEX"));
				auditRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				paramValueMap.put("PARAM_VALUE_MAP", auditRec);
				
				
				
		//	insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
			
		}
		return version;
	}


}
