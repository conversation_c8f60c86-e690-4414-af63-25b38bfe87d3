//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.8-b130911.1802 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.11.03 at 07:49:11 PM IST 
//


package com.ascent.custumize.recon;

import java.math.BigInteger;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for Recon complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Recon">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Audit" type="{}Audit" maxOccurs="unbounded"/>
 *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="QueryConName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="sequenceName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="columnNamesString" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReconCreateQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReconInsertQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReconUpdateQueryName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReconMatchHandler" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *       &lt;attribute name="id" use="required" type="{http://www.w3.org/2001/XMLSchema}integer" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Recon", propOrder = {
   
    "name",
    "queryConName",
    "sequenceName",
    "columnNamesString",
    "reconCreateQueryName",
    "reconInsertQueryName",
    "reconUpdateQueryName",
    "reconUpstreamQueryName",
    "reconMatchHandler",
    "plugin"
})
public class Recon {

   @XmlElement(name = "Name", required = true)
    protected String name;
    @XmlElement(name = "QueryConName", required = true)
    protected String queryConName;
    @XmlElement(name = "SequenceName",required = true)
    protected String sequenceName;
    @XmlElement(name = "ColumnNamesString",required = true)
    protected String columnNamesString;
    @XmlElement(name = "ReconCreateQueryName", required = true)
    protected String reconCreateQueryName;
    @XmlElement(name = "ReconInsertQueryName", required = true)
    protected String reconInsertQueryName;
    @XmlElement(name = "ReconUpdateQueryName", required = true)
    protected String reconUpdateQueryName;
    @XmlElement(name = "ReconUpstreamQueryName", required = true)
    protected String reconUpstreamQueryName;
    
    
    @XmlElement(name = "ReconMatchHandler", required = true)
    protected String reconMatchHandler;
    @XmlElement(name = "Plugin", required = true)
    protected String plugin;
    @XmlAttribute(name = "id", required = true)
    protected BigInteger id;
    @XmlAttribute(name = "enable", required = true)
    protected boolean enable;
   

    
    public void bootConf(){
    	if(columnNamesString!=null){
    		columnNamesString=columnNamesString.replaceAll("\t", "");
    		columnNamesString=columnNamesString.replaceAll("\n", "");
    	}
    }
       /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the queryConName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQueryConName() {
        return queryConName;
    }

    /**
     * Sets the value of the queryConName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQueryConName(String value) {
        this.queryConName = value;
    }

    /**
     * Gets the value of the sequenceName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSequenceName() {
        return sequenceName;
    }

    /**
     * Sets the value of the sequenceName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSequenceName(String value) {
        this.sequenceName = value;
    }

    /**
     * Gets the value of the columnNamesString property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getColumnNamesString() {
        return columnNamesString;
    }

    /**
     * Sets the value of the columnNamesString property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setColumnNamesString(String value) {
        this.columnNamesString = value;
    }

    /**
     * Gets the value of the reconCreateQueryName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReconCreateQueryName() {
        return reconCreateQueryName;
    }

    /**
     * Sets the value of the reconCreateQueryName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReconCreateQueryName(String value) {
        this.reconCreateQueryName = value;
    }

    /**
     * Gets the value of the reconInsertQueryName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReconInsertQueryName() {
        return reconInsertQueryName;
    }

    /**
     * Sets the value of the reconInsertQueryName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReconInsertQueryName(String value) {
        this.reconInsertQueryName = value;
    }

    /**
     * Gets the value of the reconUpdateQueryName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReconUpdateQueryName() {
        return reconUpdateQueryName;
    }

    /**
     * Sets the value of the reconUpdateQueryName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReconUpdateQueryName(String value) {
        this.reconUpdateQueryName = value;
    }

    /**
     * Gets the value of the reconMatchHandler property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReconMatchHandler() {
        return reconMatchHandler;
    }

    /**
     * Sets the value of the reconMatchHandler property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReconMatchHandler(String value) {
        this.reconMatchHandler = value;
    }

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setId(BigInteger value) {
        this.id = value;
    }
	public String getPlugin() {
		return plugin;
	}
	public void setPlugin(String plugin) {
		this.plugin = plugin;
	}
	public boolean isEnable() {
		return enable;
	}
	public void setEnable(boolean enable) {
		this.enable = enable;
	}
	public String getReconUpstreamQueryName() {
		return reconUpstreamQueryName;
	}
	public void setReconUpstreamQueryName(String reconUpstreamQueryName) {
		this.reconUpstreamQueryName = reconUpstreamQueryName;
	}

}
