package com.ascent.banknizwa.source.ej;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.banknizwa.metainstance.EtlMetaInstance;
import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;
import com.ascent.integration.persistance.AscentPersistanceIntf;
import com.ascent.integration.util.DbUtil;
import com.ascent.util.AscentAutoReconConstants;
import com.date.util.DateForamtConversion;
import com.onus.atm.etl.EJEtl;

public class EJParser implements AscentAutoReconConstants {
	private static Logger logger = LogManager.getLogger(EJParser.class.getName());
	DbUtil dbUtil = new DbUtil();
	Queries queries = null;
	Integration integration = null;
	String integrationName;
	AscentPersistanceIntf ascentPersistancePlugin = null;
	EtlMetaInstance integrationMetaInstance = null;
	Properties appProps = null;
	public static long totalTxnRecs = 0l;
	public static long totalTxnRecsTemp = 0l;
	public static long insertCount = 0l;
	public static long insertCountEx = 0l;
	public static long ignCnt = 0l;
	public static long start = 0l;
	public static long counter=0l;
	public static long bcounter=0l;
	
	
	String denomination="C1\\s*\\[\\d*\\],\\s*C2\\s*\\[\\d*\\],\\s*C3\\s*\\[\\d*],\\s*C4\\s*\\[\\d*]";
	Pattern pattern= null;
	Map<String,String> denomMap=null;
	private String START_BLOCK="TRANSACTION STARTED";
	private String END_BLOCK="TRANSACTION END";
	String tokens[]={"DATE:\\s*\\d{2}\\/s*\\d{2}\\/s*\\d{2}","TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}","ATM ID:\\s*\\d*","(TXN:\\s*CASH DEPOSIT)|(TXN:\\s*WITHDRAWAL)",
			"A\\/C:\\s*\\d*\\**\\d*","SEQ:\\s*\\d*","STAN:\\s*\\d*","RRN:\\s*\\d*","\\s*AMOUNT:\\s*\\OMR\\s*\\d*\\.\\d*"};
	public EJParser(){
		
	}
	
	public EJParser(String integrationName) throws Exception{
	this.integrationMetaInstance = EtlMetaInstance.getInstance();
	this.integrationName = integrationName;
	this.integration = this.integrationMetaInstance.getEtlConf(this.integrationName);
	this.queries = this.integrationMetaInstance.getEtlQueryConfs();
	this.appProps = this.integrationMetaInstance.getApplicationProperties();
	
	}
	
	public static void main(String[] args) throws Exception {
		File file =new File("D:\\BANK NIZwA\\SampleData for Reconciliaton\\Cards\\ATM ONUS\\Sample EG.txt");
		EJParser ej=new EJParser("EJ");
		//ej.extractFile(file);
		ej.process();
	}
	{
		 denomMap=new HashMap<String, String>();
		
			
		denomMap.put("C1", "RM5");
		denomMap.put("C2", "RM10");
		denomMap.put("C3", "RM20");
		denomMap.put("C4", "RM50");
	}
	
	public void process() throws Exception{
		String extPath = (String) appProps.get(AscentAutoReconConstants.AUTO_RECON_HOME)
				+ (String) appProps.get(AscentAutoReconConstants.SFTP_FOLDER_NAME);
		File sftpFolder = new File(extPath);
		processFile(sftpFolder);
	}
	
	
	public void processFile(File folder) throws Exception {

		File[] listOfFiles = folder.listFiles();

		for (int i = 0; i < listOfFiles.length; i++) {
			if (listOfFiles[i].isFile()) {

				File file = listOfFiles[i];
				String fileName = file.getName();
				boolean check = false;
				String name = file.getParentFile().getName();
				File duplicateFile = new File(appProps.getProperty("PROCESS_PATH") + name);
				File ProcessedDuplicate = new File(appProps.getProperty("PROCESSDUPLICATE_PATH") + name);
				
				Path movefrom = FileSystems.getDefault().getPath(file.getPath());

				Path processDup = FileSystems.getDefault()
						.getPath(ProcessedDuplicate.getPath() + "\\" + file.getName());

				if (duplicateFile.isDirectory()) {
					check = new File(duplicateFile, file.getName()).exists();

					if (check) {

						if (!ProcessedDuplicate.exists()) {
							ProcessedDuplicate.mkdirs();

						}
						Files.move(movefrom, processDup, StandardCopyOption.REPLACE_EXISTING);
					}

				}
				if (!check) {
					Pattern pattern = Pattern.compile(this.integration.getFileNamePattern());

					Matcher matcher = pattern.matcher(fileName);
					boolean matchFound = false;
					while (matcher.find()) {

						matchFound = true;

					}

					if (matchFound) {
						



						logger.trace("Processing " + fileName);
						//extractFile(file);
						extractFile(file);
	


					}
				}
			} else if (listOfFiles[i].isDirectory()) {
				processFile(listOfFiles[i]);
				System.out.println("Directory " + listOfFiles[i].getName());
			}
		}

	}
	public void extractFile(File file) throws Exception{
	
		try{	
			List<String>list=new ArrayList<String>();
			
			
			/*start */
			BufferedReader br =null;
			Connection connection = null;
			long fileLength=file.length();
			List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
			try {
				
				logger.trace("Started extracting the data from " + file.getName() + " For the Pattern:"
						+ integration.getFileNamePattern());
				this.totalTxnRecs = 0l;

				this.insertCount = 0l;
				this.insertCountEx = 0l;
				this.ignCnt = 01;
				
				
				pattern= Pattern.compile(denomination);
				
				String line="";
				
				

				try {

					start = System.currentTimeMillis();
					String currentLine = null;

					br = new BufferedReader(new FileReader(file));

					List<String> columnNames = integration.getColumnNameList();
					

					try {
						this.ascentPersistancePlugin = (AscentPersistanceIntf) ((AscentPersistanceIntf) ((Class
								.forName((integration.getPersistancePlugin()).trim())).newInstance()));
						logger.trace("persistance plugin" + integration.getPersistancePlugin()
								+ " instantiated For the file: " + file.getName() + " Etl Process");
					} catch (Exception e) {
						//e.printStackTrace();
						logger.error("Unable to instantiate the plugin class:" + integration.getPersistancePlugin()
								+ " for the file: " + file.getName() + " Etl Process", e);
						throw e;

					}
					connection = DbUtil.getConnection();
					int lineNo = 0;
					connection.setAutoCommit(false);
					String currentLineRead="";
					int fileCountLine = 0;
			/*end */
			while((line=br.readLine())!=null){
				
				if(!line.isEmpty() && line.contains(START_BLOCK)){
					list.add(line);
					do{
						line=br.readLine();
						if(!line.isEmpty())
						list.add(line.trim());
					}while( !line.contains(END_BLOCK));
					dataList.add(extractBlock(list));
					
					
					list.clear();
				}
			}
			

			if ((dataList.size() % 2) == 0) {

				List<Map<String, Object>> tempDataList = new ArrayList<Map<String, Object>>();
				tempDataList.addAll(dataList);
				totalTxnRecsTemp = totalTxnRecsTemp + dataList.size();

				try {
					Map<String, Object> result = this.ascentPersistancePlugin.persist(integration,
							queries, connection, tempDataList, false);
					if (result.get(STAG_CNT) != null) {
						insertCount = insertCount + (Integer) result.get(STAG_CNT);
					}
					if (result.get(STAG_EX_CNT) != null) {
						insertCountEx = insertCountEx + (Integer) result.get(STAG_EX_CNT);
					}

					if (result.get("IGN_CNT") != null) {
						ignCnt = ignCnt + (Integer) result.get("IGN_CNT");
					}

				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);

				}
				dataList.clear();
			}
		
	} catch (Exception e) {
		
		logger.error(e.getMessage(), e);
	} finally {

	}


if (dataList.size() != 0) {

	List<Map<String, Object>> tempDataList = new ArrayList<Map<String, Object>>();
	tempDataList.addAll(dataList);
	totalTxnRecsTemp = totalTxnRecsTemp + dataList.size();
	try {
		Map<String, Object> result = this.ascentPersistancePlugin.persist(integration, queries,
				connection, tempDataList, false);
		if (result.get(STAG_CNT) != null) {
			insertCount = insertCount + (Integer) result.get(STAG_CNT);
		}
		if (result.get(STAG_EX_CNT) != null) {
			insertCountEx = insertCountEx + (Integer) result.get(STAG_CNT);
		}
		if (result.get("IGN_CNT") != null) {
			ignCnt = ignCnt + (Integer) result.get("IGN_CNT");
		}

	} catch (Exception e) {
		e.printStackTrace();
		logger.error(e.getMessage(), e);
		throw e;
	} finally {
		// br.close();
	}
	dataList.clear();
}

} catch (Exception e) {

logger.error(e.getMessage(), e);
throw e;
} finally {


br.close();
logger.trace(file.getName() +" moving started ");
String name = file.getParentFile().getName();
File file1 = new File(appProps.getProperty("PROCESS_PATH") + name);
File duplicateFile = new File(appProps.getProperty("DUPLICATE_PATH") + name);

Path movefrom = FileSystems.getDefault().getPath(file.getPath());
Path target = FileSystems.getDefault().getPath(file1.getPath() + "\\" + file.getName());
Path duplicate = FileSystems.getDefault().getPath(duplicateFile.getPath() + "\\" + file.getName());

boolean check = new File(file1, file.getName()).exists();
if (check) {
	if (!duplicateFile.exists()) {
		duplicateFile.mkdirs();
	}

	Files.move(movefrom, duplicate, StandardCopyOption.REPLACE_EXISTING);

} else {
	if (!file.exists()) {
		file.mkdirs();
	}
	System.out.println(bcounter+"------->"+counter);
	if(bcounter==counter){
	Files.move(movefrom, target, StandardCopyOption.REPLACE_EXISTING);
	counter=0;
	bcounter=0;
	logger.trace(file.getName() +" has moved ");
	connection.commit();
	}
	else{
		counter=0;
		bcounter=0;
		logger.trace(file.getName() +" is not proecess complete so It will not moved ");
		connection.rollback();
	}
/*	String val=appProps.getProperty("FILEPROCESSING");
	if(val.equalsIgnoreCase("false")){
	Thread.currentThread().stop();	
	}
	*/
	
	
	 
	
}
DbUtil.closeConnection(connection);

}

} catch (Exception e) {
		logger.error("Exception occuredwhile extracting the data from " + file.getName() + " For the Pattern:"
			+ integration.getFileNamePattern(), e);
		logger.error("Total transactions parsed by the system " + totalTxnRecs);
		logger.error("Total inserted transactions in staging" + insertCount);
		logger.error("Total inserted transactions in staging exception" + insertCountEx);
		logger.error("total  duration:" + ((System.currentTimeMillis() - start)) + "in msec");
		e.printStackTrace();
		throw e;
	} finally {
		logger.trace("Successfully completed the data extraction from " + file.getName() + " For the Pattern:"
			+ integration.getFileNamePattern());
		logger.trace("Total transactions parsed by the system " + totalTxnRecs);
		logger.trace("Total transactions parsed by the system Temp" + totalTxnRecsTemp);
		logger.trace("Total inserted transactions in staging" + insertCount);
		logger.trace("Total inserted transactions in staging exception" + insertCountEx);
		logger.trace("Total inserted transactions in staging ignore" + ignCnt);
		logger.trace("Total  duration:" + ((System.currentTimeMillis() - start)) + "in msec");
	}
		
}
	
	private Map<String,Object> extractBlock(List<String>list){
		Map<String,Object> map=new HashMap<String,Object>();
		for(String data:list){
			if(data.startsWith("DATE")){
				map.put("TRA_DATE", DateForamtConversion.formateDateDDMMYY(getData(getData(data, "DATE:\\s*\\d{2}\\/s*\\d{2}\\/s*\\d{2}"), "\\d{2}\\/s*\\d{2}\\/s*\\d{2}")));
				map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
			}else if(data.startsWith("ATM ID:")){
				map.put("ATM_ID", getData(getData(data, "ATM ID:\\s*\\d*"), "\\d*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(data.startsWith("STAN:")){
				map.put("STAN", getData(getData(data, "STAN:\\s*\\d*"), "\\d*"));
				map.put("RRN", getData(getData(data, "RRN:\\s*\\d*"), "\\d*"));
		
			}else if(data.startsWith("PAN:")){
				map.put("PAN", getData(getData(data, "PAN:\\s*\\d*\\**\\d*"), "\\d*\\**\\d*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(data.startsWith("A/C:")){
				map.put("ACCOUNT_NUMBER", getData(getData(data, "A\\/C:\\s*\\d*\\**\\d*"), "\\d*\\**\\d*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(data.startsWith("TXN:")){
				map.put("TXN_TYPE", getData(getData(data, "(TXN:\\s*CASH DEPOSIT)|(TXN:\\s*WITHDRAWAL)"), "(WITHDRAWAL)|(CASH DEPOSIT)"));
				map.put("SEQ", getData(getData(data, "SEQ:\\s*\\d*"), "\\d*"));
		
			}else if(data.startsWith("AMOUNT:")){
				map.put("AMOUNT", getData(getData(data, "AMOUNT:\\s*OMR\\s*\\d*.\\d*"), "\\s*\\d*\\.\\d*").trim());
				map.put("CURRENCY", getData(getData(data, "AMOUNT:\\s*OMR\\s*"), "\\:\\s*\\w{3}\\s").replace(":", "").trim());
		
			}else if(data.startsWith("STATUS:")){
				map.put("STATUS", getData(data, "\\:\\s*\\w*s*"));
				//map.put("TRA_TIME", getData(getData(data, "TIME:\\s*\\d{2}\\:\\d{2}\\:\\d{2}"), "\\d{2}\\:\\d{2}\\:\\d{2}"));
		
			}else if(pattern.matcher(data).matches()){
				for(String denom:data.split(",")){
					
					String rm= denomMap.get(getData(denom, "C\\d*"));
					map.put(rm, getData(denom, "\\[\\d*]").replaceAll("\\[|\\]", ""));
				}
				
			}
		}
		System.out.println(map);
		return map;
	}
	 public static String getData(String data, String token) {
	        String retStr;
	        StringBuilder result = new StringBuilder();
	        if (data != null) {
	            Pattern pattern = Pattern.compile(token);
	            Matcher matcher = pattern.matcher(data);
	            String sep = "";
	            while (matcher.find()) {
	                result.append(String.valueOf(String.valueOf("")) + matcher.group().toString());
	               
	            }
	        }
	        return (retStr = result.toString()).isEmpty() ? null : retStr;
	    }
	 
	 
	 
	 private void processData(File irisFile) throws Exception {
			long fileLength=irisFile.length();
			try {
				
				logger.trace("Started extracting the data from " + irisFile.getName() + " For the Pattern:"
						+ integration.getFileNamePattern());
				this.totalTxnRecs = 0l;

				this.insertCount = 0l;
				this.insertCountEx = 0l;
				this.ignCnt = 01;
				BufferedReader br = null;
				Connection connection = null;
				

				try {

					start = System.currentTimeMillis();
					String currentLine = null;

					br = new BufferedReader(new FileReader(irisFile));

					List<String> columnNames = integration.getColumnNameList();
					List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

					try {
						this.ascentPersistancePlugin = (AscentPersistanceIntf) ((AscentPersistanceIntf) ((Class
								.forName((integration.getPersistancePlugin()).trim())).newInstance()));
						logger.trace("persistance plugin" + integration.getPersistancePlugin()
								+ " instantiated For the file: " + irisFile.getName() + " Etl Process");
					} catch (Exception e) {
						//e.printStackTrace();
						logger.error("Unable to instantiate the plugin class:" + integration.getPersistancePlugin()
								+ " for the file: " + irisFile.getName() + " Etl Process", e);
						throw e;

					}
					connection = DbUtil.getConnection();
					int lineNo = 0;
					connection.setAutoCommit(false);
					String currentLineRead="";
					int fileCountLine = 0;
					BufferedReader	fileCountBr = new BufferedReader(new FileReader(irisFile));
					try{	while ((currentLineRead = fileCountBr.readLine()) != null) {
							if (fileCountLine == 0) {
								fileCountLine++;
								continue;
							} else
								bcounter++;
						}
					
					fileCountBr.close();
					}catch(Exception e){
						logger.error(e.getMessage(),e);
					}

					while ((currentLine = br.readLine()) != null) {
						//currentLine = currentLine.replace("\"\"\"\"", "\"\"");
						try {
							if (lineNo == 0) {
								lineNo++;
								continue;
							}
							totalTxnRecs++;
							int index = 0;
							String txn[] = currentLine.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)", -1);
							if (txn != null && txn.length > 0) {
								Map<String, Object> txnRec = new HashMap<String, Object>();
								/*if (txn[0] != null && txn[0].length() > 0) {
									txn[0] = txn[0].substring(1, txn[0].length());
								}
								if (txn[txn.length - 1] != null && txn[txn.length - 1].length() > 0) {

									txn[txn.length - 1] = txn[txn.length - 1].substring(0,
											(txn[txn.length - 1].length() - 1));
								}*/
								for (String colValue : txn) {
									String columnName = columnNames.get(index);
									if (colValue != null && !(colValue.trim()).isEmpty()) {
										colValue = colValue.trim().replace("\"", "");
										if(columnName.endsWith("DATE")){
											System.out.println("comming");
											txnRec.put(columnName, DateForamtConversion.formateDateMMddyyyyhhmmsss(colValue));	
											System.out.println("  commmmmmmmm  ");
										}else{
										txnRec.put(columnName, colValue);
										}
									}
									index++;
								}
								if(irisFile.getName().startsWith("ATM")){
									txnRec.put("TRANSACTION_TYPE", "WITHDRAW");
								}else{
									txnRec.put("TRANSACTION_TYPE", "DEPOSIT");
								}
								txnRec.put("FILE_NAME", irisFile.getName());
								dataList.add(txnRec);

								if ((dataList.size() % 2) == 0) {

									List<Map<String, Object>> tempDataList = new ArrayList<Map<String, Object>>();
									tempDataList.addAll(dataList);
									totalTxnRecsTemp = totalTxnRecsTemp + dataList.size();

									try {
										Map<String, Object> result = this.ascentPersistancePlugin.persist(integration,
												queries, connection, tempDataList, false);
										if (result.get(STAG_CNT) != null) {
											insertCount = insertCount + (Integer) result.get(STAG_CNT);
										}
										if (result.get(STAG_EX_CNT) != null) {
											insertCountEx = insertCountEx + (Integer) result.get(STAG_EX_CNT);
										}

										if (result.get("IGN_CNT") != null) {
											ignCnt = ignCnt + (Integer) result.get("IGN_CNT");
										}

									} catch (Exception e) {
										e.printStackTrace();
										logger.error(e.getMessage(), e);

									}
									dataList.clear();
								}
							}
						} catch (Exception e) {
							
							logger.error(e.getMessage(), e);
						} finally {

						}
					}

					if (dataList.size() != 0) {

						List<Map<String, Object>> tempDataList = new ArrayList<Map<String, Object>>();
						tempDataList.addAll(dataList);
						totalTxnRecsTemp = totalTxnRecsTemp + dataList.size();
						try {
							Map<String, Object> result = this.ascentPersistancePlugin.persist(integration, queries,
									connection, tempDataList, false);
							if (result.get(STAG_CNT) != null) {
								insertCount = insertCount + (Integer) result.get(STAG_CNT);
							}
							if (result.get(STAG_EX_CNT) != null) {
								insertCountEx = insertCountEx + (Integer) result.get(STAG_CNT);
							}
							if (result.get("IGN_CNT") != null) {
								ignCnt = ignCnt + (Integer) result.get("IGN_CNT");
							}

						} catch (Exception e) {
							e.printStackTrace();
							logger.error(e.getMessage(), e);
							throw e;
						} finally {
							// br.close();
						}
						dataList.clear();
					}

				} catch (Exception e) {
					
					logger.error(e.getMessage(), e);
					throw e;
				} finally {
					

					br.close();
					logger.trace(irisFile.getName() +" moving started ");
					String name = irisFile.getParentFile().getName();
					File file = new File(appProps.getProperty("PROCESS_PATH") + name);
					File duplicateFile = new File(appProps.getProperty("DUPLICATE_PATH") + name);

					Path movefrom = FileSystems.getDefault().getPath(irisFile.getPath());
					Path target = FileSystems.getDefault().getPath(file.getPath() + "\\" + irisFile.getName());
					Path duplicate = FileSystems.getDefault().getPath(duplicateFile.getPath() + "\\" + irisFile.getName());

					boolean check = new File(file, irisFile.getName()).exists();
					if (check) {
						if (!duplicateFile.exists()) {
							duplicateFile.mkdirs();
						}
					
						Files.move(movefrom, duplicate, StandardCopyOption.REPLACE_EXISTING);

					} else {
						if (!file.exists()) {
							file.mkdirs();
						}
						System.out.println(bcounter+"------->"+counter);
						if(bcounter==counter){
						Files.move(movefrom, target, StandardCopyOption.REPLACE_EXISTING);
						counter=0;
						bcounter=0;
						logger.trace(irisFile.getName() +" has moved ");
						connection.commit();
						}
						else{
							counter=0;
							bcounter=0;
							logger.trace(irisFile.getName() +" is not proecess complete so It will not moved ");
							connection.rollback();
						}
					/*	String val=appProps.getProperty("FILEPROCESSING");
						if(val.equalsIgnoreCase("false")){
						Thread.currentThread().stop();	
						}
						*/
						
						
						 
						
					}
					DbUtil.closeConnection(connection);

				}

			} catch (Exception e) {
				logger.error("Exception occuredwhile extracting the data from " + irisFile.getName() + " For the Pattern:"
						+ integration.getFileNamePattern(), e);
				logger.error("Total transactions parsed by the system " + totalTxnRecs);
				logger.error("Total inserted transactions in staging" + insertCount);
				logger.error("Total inserted transactions in staging exception" + insertCountEx);
				logger.error("total  duration:" + ((System.currentTimeMillis() - start)) + "in msec");
				e.printStackTrace();
				throw e;
			} finally {
				logger.trace("Successfully completed the data extraction from " + irisFile.getName() + " For the Pattern:"
						+ integration.getFileNamePattern());
				logger.trace("Total transactions parsed by the system " + totalTxnRecs);
				logger.trace("Total transactions parsed by the system Temp" + totalTxnRecsTemp);
				logger.trace("Total inserted transactions in staging" + insertCount);
				logger.trace("Total inserted transactions in staging exception" + insertCountEx);
				logger.trace("Total inserted transactions in staging ignore" + ignCnt);
				logger.trace("Total  duration:" + ((System.currentTimeMillis() - start)) + "in msec");
			}
		}
	 
}
