package com.ascent.util;

public interface AscentAutoReconConstants {
	public static final String STAG_EX_CNT = "STAG_EX_CNT";
	public static final String STAG_CNT = "STAG_CNT";
	public static final String AUTO_RECON_HOME = "AUTO_RECON_HOME";
	public static final String SFTP_FOLDER_NAME = "SFTP_FOLDER_NAME";
	public static final String INTERNAL_FOLDER_NAME = "INTERNAL_FOLDER_NAME";
	public static final String RECON_QUERY_CONF = "RECON_QUERY_CONF";
	public static final String RECON_CONF = "RECON_CONF";
	public static final String RECON_CONF_PATH_PREFIX = "RECON_CONF_PATH_PREFIX";
	public static final String ETL_CONF_PATH_PREFIX = "ETL_CONF_PATH_PREFIX";

	// String constants

	public static final String UNPROCESS_FOLDER_NAME = "UnderProcess";
	public static final String PROCESS_FOLDER_NAME = "Processed";
	public static final String EXCEPTION_FOLDER_NAME = "Exception";
	public static final String ARCHIVED_FOLDER_NAME = "Archived";

	// GENRATE_GL_ENTRY

	public static final String GENRATE_GL_ENTRY = "GENRATE_GL_ENTRY";
	public static final String GENRATE_GL_ENTRY_PDF = "GENRATE_GL_ENTRY_PDF";
	public static final String OnUsBulkMail = "OnUsBulkMail";
	public static final String Issuer_Acc_BulkMail = "Issuer_Acc_BulkMail";

	public static final String DOHA_LOGO = "IMG_NAME";

	// export grid data in pdf

	public static final String EXPORT_PDF_FILE_PATH = "EXPORT_PDF_FILE_PATH";
		
	
	//
	public static final String	from="from";
	public static final String username="username";
	public static final String password="password";
	public static final String SMTPHost="SMTPHost";
	public static final String SMTPPort="SMTPPort";
	public static final String cronExpression="cronExpression";
			
	
	/*public static final */
}
