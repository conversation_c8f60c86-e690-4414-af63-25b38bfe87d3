package com.ascent.recon;

public class Sample {
	static String name=" **  kjhgsdefg  **11**lkjfgljjdfgljl**22**  jhghjg  **deepthi**";
	public static void main(String[] args) {
		String[] str1 = new String[10];
		
					int i=1;
					
				while(name.contains("**"))
					{
						String s1=name.substring(name.indexOf("**")+2, name.length());
						
							if(s1.contains("**"))
							{
									String s= name.substring(name.indexOf("**")+2, s1.indexOf("**")+name.indexOf("**")+2);
									str1[i-1]=s;
								}
							
					name=s1;
						i++;
					}
				System.out.println(str1[0]+"===="+str1[2] +  str1[4]);
				}
				
}

	
	


