package com.ascent.ds.login;

public class MessageConstants {

	/**
	 * 
	 */
	
	public static final String EMAIL_SUCCESSFULLY_SENT = "CHECK YOUR MAIL , SENT A LINK TO RESET YOUR PASSWORD..!";
	public static final String NOT_VALID_EMAIL_ID = "NOT VALID EMAIL_ID ,PLEASE ENTER VALID EMAIL..!! ";
	public static final String FORGOT_RESET_CHANAGE_PWD_NOT_ALLOWED = "User not allowed to reset / change the password ,Please contact Administrator";
	public static final String ADMINISTRATOR_MESAAGE_CHECK_MAIL = " Your Account has been unlocked by Administrator,please check your email and reset your password";
	public static final String ADMINISTRATOR_MESAAAGE = " Your Account has been unlocked by Administrator , please reset your password";
	public static final String USER = "User ";
	public static final String RECON = " Recon ";
	public static final String INVALID_CREDENTIALS = "Invalid Credentials.. Please Contact Administrator.. ";
	public static final String INVALID_USERNAME = "Invalid UserName ";
	public static final String INVALID_USERNAME_REQ_MSG = " Please, Enter Valid UserName ";
	public static final String EMPTY_USERNAME_FIELD = "UserName Should Not Be Empty";
	public static final String NOT_AUTORISED_REQ_MSG = "Not Authorised To Access The ";
	public static final String ADMIN_SUGGESTIONS = " , Select CheckBox '<i>Login as a Admin</i>' ";
	public static final String NO_LOGIN_ACCESS = "  Not Authorize to Login because  your role is ";
	public static final String PASSWORD_ERR_MSG = "Password Doesn't Match, Please Re-Enter Your Password ";
	public static final String PASSWORD_EXPIRED = " Your Password is expired , please reset your password, Click 'OK' to reset password";
	public static final String MAX_ATTEMPTS_OF_PWD = "Your account is locked after exceeding maximum number of trials !  Please contact to administrator";
	public static final String ACCOUNT_LOCKED = " Your account is locked , Please contact to administrator for recovery";
	public static final String ALREADY_LOGGED_IN = "You are already logged in !!";

}
