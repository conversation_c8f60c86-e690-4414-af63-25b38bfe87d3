package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class TestSummery {
	

	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		while (rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));
			}
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}
	
	public List<Map<String, Object>> onsSummary(String fromDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			PreparedStatement pstmt = connection.prepareStatement("EXEC ONS_UNRECONCILE_SUMMERY '2019-09-20'");
			//pstmt.setString(1, fromDate);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
			System.out.println("list = "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}
	
	
	
	public static void main(String[] args) {
		TestSummery c = new TestSummery();
		c.onsSummary("'2019-09-20'");
		
		
	}

}
