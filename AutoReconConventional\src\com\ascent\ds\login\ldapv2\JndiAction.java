package com.ascent.ds.login.ldapv2;

import java.util.Hashtable;
import java.util.Properties;
import java.util.ResourceBundle;

import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;

import org.apache.log4j.Logger;

/**
 * The application must supply a PrivilegedAction that is to be run
 * inside a Subject.doAs() or Subject.doAsPrivileged().
 */
@SuppressWarnings("rawtypes")
class JndiAction implements java.security.PrivilegedAction {   
	private static Properties p =null;
	public static Logger logger = Logger.getLogger(JndiAction.class);
    public JndiAction() {	
    }
    
    public JndiAction(Properties p ) {	
    	this.p=p;
    }
    public Object run() {
	
	return performJndiOperation();
    }

    @SuppressWarnings({ "unchecked" })
	private static LdapContext performJndiOperation() {
   
    
    //Properties p =new Properties();
	String host = p.getProperty("host");
	String user = p.getProperty("client.principal.name");
	String pass = p.getProperty("client.password");
	String dn = p.getProperty("dn");;
	// Set up environment for creating initial context
	Hashtable env = new Hashtable(11);
	env.put(Context.INITIAL_CONTEXT_FACTORY, 
	    "com.sun.jndi.ldap.LdapCtxFactory");
	env.put(Context.SECURITY_PRINCIPAL, user);
	env.put(Context.SECURITY_CREDENTIALS, pass);
	// Must use fully qualified hostname
	env.put(Context.PROVIDER_URL, host);
    // Request the use of the "GSSAPI" SASL mechanism
	// Authenticate by using already established Kerberos credentials
	env.put(Context.SECURITY_AUTHENTICATION, "GSSAPI");
	// Optional first argument is comma-separated list of auth, auth-int, 
	// auth-conf
	env.put("javax.security.sasl.qop", "auth");
	env.put("javax.security.sasl.server.authentication", "true");
 
	LdapContext ctx=null;
	try {
	    /* Create initial context */
	    ctx = new InitialLdapContext(env,null);
	  //  System.out.println(ctx.getAttributes(ctx.getNameInNamespace()));;
	   // logger.info(ctx.getAttributes("cn=Sri,ou=Escrow1,dc=ascentitgroup,dc=com"));
	    // do something useful with ctx
	    // Close the context when we're done	    
	} catch (NamingException e) {
	    e.printStackTrace();
	    logger.error("Unable to initialize ldap context",e);
	}
	return ctx;
    }
}