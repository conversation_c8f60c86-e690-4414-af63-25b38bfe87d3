package com.ascent.ds.login;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.persistance.UpdateRegulator;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class LoginDetails extends BasicDataSource {

	private static final String INSERT_USER_lOG_AUDIT_QRY = "INSERT_USER_lOG_AUDIT_QRY";
	private static final String ALL_RECON_NAMES_QRY = "ALL_RECON_NAMES_QRY";
	private static final String GET_ALL_BUSINESS_AREAS_FOR_SPECIFIC_GEOGRAPHY = "GET_ALL_BUSINESS_AREAS_FOR_SPECIFIC_GEOGRAPHY";
	private static final String GET_ALL_RECONS_FOR_SPECIFIC_BUSINESS_AREA = "GET_ALL_RECONS_FOR_SPECIFIC_BUSINESS_AREA";
	private static final String SOURCES_NAME_QUERY = "SOURCES_NAME_QUERY";
	private static final String GET_ALL_MODULES_QUERY = "GET_ALL_MODULES_QUERY";
	private static final String GET_ALL_GEOGRAPHY_NAME_QRY = "GET_ALL_GEOGRAPHY_NAME_QRY";
	private static final long serialVersionUID = 1L;
	private static final String GET_DATE_DIFF_FOR_PASSWORD_EXPIRY_NOTIFICATION = "GET_DATE_DIFF_FOR_PASSWORD_EXPIRY_NOTIFICATION";
	private static final String LOCK_USER_ACCOUNT_QUERY = "LOCK_USER_ACCOUNT_QUERY";

	static Connection connection = null;
	PreparedStatement preparedStatement = null;
	ResultSet resultSet = null;

	public LoginDetails() {
	}

	public DSResponse executeFetch(final DSRequest request) throws Exception {

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();

		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();

		DSResponse dsResponse = new DSResponse();

		Map requestParams = request.getCriteria();
		String action = (String) requestParams.get("action");
		if (action != null && action.equals("allModulesNamesAction")) {
			List<Map<String, Object>> moduleNameList = getAllModules();

			dsResponse.setData(moduleNameList);
			return dsResponse;
		}
		if (action != null && action.equals("UpstreamAllSources")) {
			List<Map<String, Object>> sourceNameList = getSourcesNameList();

			dsResponse.setData(sourceNameList);
			return dsResponse;
		}
		if (action != null && action.equals("BusinessAreaRequest")) {

			String selectedGeography = String.valueOf(requestParams.get("selectedGeography"));

			List<Map<String, Object>> businessAreaList = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(GET_ALL_BUSINESS_AREAS_FOR_SPECIFIC_GEOGRAPHY);

			paramMap.put("geography_name", selectedGeography);
			businessAreaList = loadRegulator.loadCompleteData(paramMap, queryConf);

			dsResponse.setData(businessAreaList);

			return dsResponse;

		} else if (action != null && action.equals("ReconRequest")) {

			String selectedBusinessArea = String.valueOf(requestParams.get("selectedBusinessArea"));
			String selectedGeography = String.valueOf(requestParams.get("selectedGeogrphy"));
			Query queryConf = queryConfs.getQueryConf(GET_ALL_RECONS_FOR_SPECIFIC_BUSINESS_AREA);

			Map<String, Object> params = new HashMap<String, Object>();

			params.put("business_area", selectedBusinessArea);
			params.put("geography_name", selectedGeography);

			List<Map<String, Object>> data = loadRegulator.loadCompleteData(params, queryConf);
			dsResponse.setData(data);

			return dsResponse;
		} else {
			List<Map<String, Object>> geographyList = new ArrayList<Map<String, Object>>();
			Query queryConf = queryConfs.getQueryConf(GET_ALL_GEOGRAPHY_NAME_QRY);

			geographyList = loadRegulator.loadCompleteData(null, queryConf);
			Map<String, Object> geogrphyData = new HashMap<String, Object>();
			geogrphyData.put("geogrphyData", geographyList);

			dsResponse.setData(geographyList);

		}
		return dsResponse;

	}

	public List<Map<String, Object>> getAllReconName() throws Exception {
		List<Map<String, Object>> reconNameList = new ArrayList<Map<String, Object>>();

		try {
			Map<String, Object> params = new HashMap<String, Object>();
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ALL_RECON_NAMES_QRY);
			LoadRegulator loadRegulator = new LoadRegulator();
			reconNameList = loadRegulator.loadCompleteData(params, queryConf);

		} catch (SQLException e) {

			e.printStackTrace();
		}

		return reconNameList;
	}

	public List<Map<String, Object>> getSourcesNameList() throws Exception {
		List<Map<String, Object>> sourcesNameList = new ArrayList<Map<String, Object>>();
		//
		Map<String, Object> params = new HashMap<String, Object>();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(SOURCES_NAME_QUERY);
		LoadRegulator loadRegulator = new LoadRegulator();
		sourcesNameList = loadRegulator.loadCompleteData(params, queryConf);
		return sourcesNameList;
	}

	public List<Map<String, Object>> getAllModules() throws Exception {
		List<Map<String, Object>> moduleNameList = new ArrayList<Map<String, Object>>();
		//
		Map<String, Object> params = new HashMap<String, Object>();
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(GET_ALL_MODULES_QUERY);
		LoadRegulator loadRegulator = new LoadRegulator();
		moduleNameList = loadRegulator.loadCompleteData(params, queryConf);
		return moduleNameList;

	}

	public int userAuditLogs(Map<String, Object> userAuditLogsMap) throws Exception {

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(INSERT_USER_lOG_AUDIT_QRY);

		InsertRegulator insertRegulator = new InsertRegulator();

		int insertedValue = insertRegulator.insert(userAuditLogsMap, queryConf);

		return insertedValue;

	}

	public static String createLdapUserString(String user, String oraganization, String dc) {

		String string = "cn=" + user + ",o=" + oraganization + "," + dc;
		System.out.println("** %%%   :--- " + string);
		return string;

	}

	/**
	 * 
	 * @param user_id
	 * @return calculate the remaining days to expire password , and returns the
	 *         days to expire the password
	 */

	public Integer remainingDaysToExpiry(String user_id) {
		Integer dateDiff = 0;
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("user_id", user_id);
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Query queryConf;
		try {
			queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(GET_DATE_DIFF_FOR_PASSWORD_EXPIRY_NOTIFICATION);
			LoadRegulator loadRegulator = new LoadRegulator();
			List<Map<String, Object>> loadCompleteData = loadRegulator.loadCompleteData(params, queryConf);

			for (Map<String, Object> map : loadCompleteData) {
				dateDiff = (Integer) map.get("expdate");

			}
			// expdate
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return dateDiff;
	}

	public Integer lockUserAccount(String user_id, String account_status) {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Integer update = 0;
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(LOCK_USER_ACCOUNT_QUERY);
			UpdateRegulator updateRegulator = new UpdateRegulator();
			Map<String, Object> args = new HashMap<String, Object>();
			Map<String, Object> updateAcntMap = new HashMap<String, Object>();
			Calendar calendar = Calendar.getInstance();
			updateAcntMap.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
			updateAcntMap.put("account_status", account_status);
			updateAcntMap.put("user_id", user_id);
			String UPDATE_QRY = queryConf.getQueryString();
			String UPDATE_QRY_PARAM = queryConf.getQueryParam();
			args.put("UPDATE_QRY", UPDATE_QRY);
			args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAM);
			args.put("PARAM_VALUE_MAP", updateAcntMap);
			update = updateRegulator.update(args);

			System.out.println("STATUS  -- update" + update);
		} catch (Exception e) {

			e.printStackTrace();
		}

		return update;
	}

}
