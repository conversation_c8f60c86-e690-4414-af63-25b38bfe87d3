package com.ascent.ds.useradministration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpSession;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import com.ascent.integration.util.DbUtil;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
public class GettingUsersData  extends BasicDataSource implements PagesConstants{
	private static Logger logger = (Logger) LogManager.getLogger(DbUtil.class.getName());
	private static final long serialVersionUID = 1L;

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		String UserID =(String)reqCriteria.get("userID");
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		Connection connection =null;
		Statement stmt=null;
		ResultSet rs=null;
		List userIdLsit=new ArrayList();
		try{
		 //String userQuery="SELECT user_id FROM users  rl where status='APPROVED' and version = (select MAX(version) from users where user_id=rl.user_id and status='APPROVED')";
			String userQuery="SELECT user_id FROM users  ";
		 connection =DbUtil.getConnection();
		 stmt=connection.createStatement();
		 rs=stmt.executeQuery(userQuery);
		while(rs.next()){
			String user_id=rs.getString("user_id");
			//System.out.println("User Id of the users table"+ user_id);
			userIdLsit.add(user_id);
		}
		}
		catch(Exception e){
			logger.error("You got the error at"+ e);
			logger.error("You got the Exception as"+e.getMessage());
			}
		finally{
			rs.close();
			stmt.close();
			connection.close();
		}
		for(int i=0; i<userIdLsit.size();i++){
			if(UserID.equals(userIdLsit.get(i))){
				response.setData(true);
				return response ;
			}else{
				response.setData(false);
				//return response;
			}
		}
		return response;
	}

}
