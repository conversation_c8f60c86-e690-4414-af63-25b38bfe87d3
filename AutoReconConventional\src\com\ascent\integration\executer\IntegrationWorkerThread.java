package com.ascent.integration.executer;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;
import com.ascent.integration.persistance.AscentPersistanceIntf;

public class IntegrationWorkerThread implements Runnable {
	private static Logger logger = LogManager.getLogger(IntegrationWorkerThread.class.getName());
	private String message;
	Integration integration = null;
	Queries queries = null;
	Connection connection = null;
	AscentPersistanceIntf ascentPersistancePlugin = null;

	private List<Map<String, Object>> txnList = null;

	public IntegrationWorkerThread(String s, Integration integration, Queries queries, Connection connection,
			List<Map<String, Object>> txnList) {
		this.message = s;

		this.integration = integration;
		this.queries = queries;
		this.connection = connection;
		this.txnList = txnList;
		try {
			ascentPersistancePlugin = (AscentPersistanceIntf) ((AscentPersistanceIntf) ((Class
					.forName((integration.getPersistancePlugin()).trim())).newInstance()));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	public void run() {
		System.out.println(Thread.currentThread().getName() + " (Start) for request No: = " + message);
		processmessage();
		System.out.println(Thread.currentThread().getName() + " (End) for requestNo: " + message);
	}

	private void processmessage() {

		try {
			this.ascentPersistancePlugin.persist(this.integration, this.queries, this.connection, this.txnList, false);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {

		}
	}
}