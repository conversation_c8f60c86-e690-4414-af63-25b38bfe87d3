package com.ascent.ds.login;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.UpdateRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.util.PasswordGeneratorUtil;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ResetPasswordDs extends BasicDataSource {

	/**
	 * 
	 */

	private static final String RESET_PASSWORD_USING_EMAILID_PARAMETER = "RESET_PASSWORD_USING_EMAILID_PARAMETER";
	private static final String RESET_PASSWORD_USING_USERID_PARAMETER = "RESET_PASSWORD_USING_USERID_PARAMETER";
	private static final long serialVersionUID = 6522972375431718483L;
	private static final String RESET_USER_PASSWORD_QUERY = "RESET_USER_PASSWORD_QUERY";
	private static final String CHANGE_USER_PASSWORD_QUERY = "CHANGE_USER_PASSWORD_QUERY";
	private static final String ACTIVE = "ACTIVE";
	private static final String UNLOCKED = "UNLOCKED";
	final Integer expiryMonthDuration = 1;
	CustomerDao customerDao= new CustomerDao();
	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

	public DSResponse executeFetch(final DSRequest request) {

		DSResponse dsResponse = new DSResponse();

		Map<String, Object> criteriaMap = request.getValues();
		String action = (String) criteriaMap.get("action");
		if (action != null && action.equals(RESET_PASSWORD_USING_EMAILID_PARAMETER)) {
			try {
				System.out.println("criteriaMap   "+criteriaMap);
				Map<String, Object> resetPassword = resetPassword(criteriaMap);

				dsResponse.setData(resetPassword);
				System.out.println("RESET_PASSWORD_USING_EMAILID_PARAMETER");
			} catch (Exception e) {

				e.printStackTrace();
			}
		} else if (action != null && action.equals(RESET_PASSWORD_USING_USERID_PARAMETER)) {
			Map<String, Object> resetPasswordUsingUser_id;
			try {
				resetPasswordUsingUser_id = resetPasswordUsingUser_id(criteriaMap);
				dsResponse.setData(resetPasswordUsingUser_id);
				System.out.println("RESET_PASSWORD_USING_USERID_PARAMETER");
			} catch (Exception e) {

				e.printStackTrace();
			}

		}

		return dsResponse;
	}

	public Map<String, Object> resetPasswordOld(Map<String, Object> criteriaMap) throws Exception {

		Map<String, Object> respMap = new HashMap<String, Object>();
System.out.println("criteriaMap  "+criteriaMap);
		String email_id = (String) criteriaMap.get("mailID");
		String newPassword = (String) criteriaMap.get("newPassword");
		String confirmPassword = (String) criteriaMap.get("confirmPassword");
		String a_status = (String) criteriaMap.get("a_status");
		String account_status = a_status;
	
		//@modified by ankush 8/08/2017 for auth code validation email reset password
		String authId = (String) criteriaMap.get("authId");
		
		Map <String, Object>dataMap= new HashMap<String, Object>();
		dataMap.put("email_id",email_id );
		dataMap.put("a_status",a_status );
		dataMap.put("unique_id", authId);
		
		boolean status=authenticateUserAuthCode(dataMap);
		
		if(!status){
			respMap.put("SUCCESS", "Link is expired please reset password Again ..");
			//respMap.put("SUCCESS", "Access denied ..");
			respMap.put("Authstatus", false);
			return respMap;
		}else{
			// TODO reset auth code and reset password UPDATE_UNIQUE_CODE_FOREMAIL_AUTH query
		
		if (newPassword != null) {
			if (confirmPassword != null) {
				if (email_id != null && newPassword.equals(confirmPassword)) {

					if (a_status.equals(UNLOCKED)) {
						account_status = ACTIVE;
					} 
					AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
					Map<String, Object> args = new HashMap<String, Object>();
					Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(RESET_USER_PASSWORD_QUERY);

					String UPDATE_QRY = queryConf.getQueryString();
					String UPDATE_QRY_PARAMS = queryConf.getQueryParam();
					System.out.println(UPDATE_QRY + " -----" + UPDATE_QRY_PARAMS);
					Map<String, Object> PARAM_VALUE_MAP = new HashMap<String, Object>();
					
					// START PASSWORD ENCRIPTION POLICY IS ADDED --28-05-2017 BY AVINASH
					
					PasswordGeneratorUtil passwordgeneratorutil = new PasswordGeneratorUtil();
					passwordgeneratorutil.generateRandomString();
					
			   /////////// START  RAMA ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
					
					String passwordToDB = "";
						try {
							passwordToDB = PasswordGeneratorUtil.generateStorngPasswordHash(newPassword);
						} catch (NoSuchAlgorithmException  | InvalidKeySpecException e) {
							e.printStackTrace();
						}
						String password = passwordToDB;
				
		        /////////// END  RAMA ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
					
					
					PARAM_VALUE_MAP.put("password", password);
					Calendar calendar = Calendar.getInstance();
					PARAM_VALUE_MAP.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
					CustomerDao customerDao = new CustomerDao();
					PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
					Integer pwdAge = loadPasswordPolicy.getPwdMaxAge();
					calendar.add(Calendar.MONTH, pwdAge);
					PARAM_VALUE_MAP.put("pwd_exp_date", new Timestamp(calendar.getTimeInMillis()));
					PARAM_VALUE_MAP.put("account_status", account_status);
					PARAM_VALUE_MAP.put("email_id", email_id);

					args.put("UPDATE_QRY", UPDATE_QRY);
					args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAMS);
					args.put("PARAM_VALUE_MAP", PARAM_VALUE_MAP);

					UpdateRegulator updateRegulator = new UpdateRegulator();
					int updated = updateRegulator.update(args);
					System.out.println(" --------" + updated);

					if (updated >= 1) {
					boolean updateAuthStatus=updateAuthCodeForEmail(dataMap);
					System.out.println("update auth code is"+updateAuthStatus);
						String msg = "Your Password has been reset successfully,login again..!!";
						respMap.put("SUCCESS", msg);
					} else {
						String msg = "Reset your password once again..!!";
						respMap.put("ERR_MSG", msg);

					}

				} else {
					String msg = "New password and confirm password don't match..!!";
					respMap.put("ERR_MSG", msg);
				}
			} else {
				String msg = "Confirm password should not be empty..!!";
				respMap.put("ERR_MSG", msg);
			}
		} else {
			String msg = "New password  should not be empty..!!";
			respMap.put("ERR_MSG", msg);
		}
	}
		return respMap;
	}
	public Map<String, Object> resetPasswordChangedCheckBelowMethod(Map<String, Object> criteriaMap) throws Exception {

		Map<String, Object> respMap = new HashMap<String, Object>();

		String email_id = (String) criteriaMap.get("mailID");
		String newPassword = (String) criteriaMap.get("newPassword");
		String confirmPassword = (String) criteriaMap.get("confirmPassword");
		String a_status = (String) criteriaMap.get("a_status");
		String account_status = a_status;
		if (newPassword != null) {
			if (confirmPassword != null) {
				if (email_id != null && newPassword.equals(confirmPassword)) {

					if (a_status.equals(UNLOCKED)) {
						account_status = ACTIVE;
					} 
					AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
					Map<String, Object> args = new HashMap<String, Object>();
					Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(RESET_USER_PASSWORD_QUERY);

					String UPDATE_QRY = queryConf.getQueryString();
					String UPDATE_QRY_PARAMS = queryConf.getQueryParam();
					System.out.println(UPDATE_QRY + " -----" + UPDATE_QRY_PARAMS);
					Map<String, Object> PARAM_VALUE_MAP = new HashMap<String, Object>();
					PARAM_VALUE_MAP.put("password", newPassword);
					Calendar calendar = Calendar.getInstance();
					PARAM_VALUE_MAP.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
					CustomerDao customerDao = new CustomerDao();
					PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
					Integer pwdAge = loadPasswordPolicy.getPwdMaxAge();
					calendar.add(Calendar.MONTH, pwdAge);
					PARAM_VALUE_MAP.put("pwd_exp_date", new Timestamp(calendar.getTimeInMillis()));
					PARAM_VALUE_MAP.put("account_status", account_status);
					PARAM_VALUE_MAP.put("email_id", email_id);

					args.put("UPDATE_QRY", UPDATE_QRY);
					args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAMS);
					args.put("PARAM_VALUE_MAP", PARAM_VALUE_MAP);

					UpdateRegulator updateRegulator = new UpdateRegulator();
					int updated = updateRegulator.update(args);
					System.out.println(" --------" + updated);

					if (updated >= 1) {

						String msg = "Your Password has been reset successfully,login again..!!";
						respMap.put("SUCCESS", msg);
					} else {
						String msg = "Reset your password once again..!!";
						respMap.put("ERR_MSG", msg);

					}

				} else {
					String msg = "New password and confirm password don't match..!!";
					respMap.put("ERR_MSG", msg);
				}
			} else {
				String msg = " Confirm password should not be empty..!!";
				respMap.put("ERR_MSG", msg);
			}
		} else {
			String msg = "New password  should not be empty..!!";
			respMap.put("ERR_MSG", msg);
		}

		return respMap;
	}
	public Map<String, Object> resetPassword(Map<String, Object> criteriaMap) throws Exception {

		Map<String, Object> respMap = new HashMap<String, Object>();

		String email_id = (String) criteriaMap.get("mailID");
		String userId = (String) criteriaMap.get("userId");
		String unique_id = (String) criteriaMap.get("unique_id");
		
		//////////  CHECK CRITERIA FOR RESET PASSWORD START  30oct,2018  ///////////
		
		Map map = getDataBasedOnUniqueId( unique_id,email_id,userId);
		
		if(map.isEmpty())
		{
			String msg = "No Any User Is Found...!";
			respMap.put("ERR_MSG", msg);
		}
		else
		{
		
		String STATUS = map.get("status").toString();
		
		if(STATUS.equals("ACTIVE"))
		{
			Date created_on_time =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse( map.get("created_on").toString());
			long cureenttime = new java.util.Date().getTime();
			if(cureenttime - created_on_time.getTime() < 1800000)
			{
				String newPassword = (String) criteriaMap.get("newPassword");
				String confirmPassword = (String) criteriaMap.get("confirmPassword");
				String a_status = (String) criteriaMap.get("a_status");
				String account_status = a_status;
				if (newPassword != null) {
					if (confirmPassword != null) {
						if (email_id != null && newPassword.equals(confirmPassword)) {

							if (a_status.equals(UNLOCKED)) {
								account_status = ACTIVE;
							}
							
							Map<String, Object> dataMap = new HashMap<String, Object>();
							dataMap.put("email_id", email_id);
							dataMap.put("user_id", userId);
							customerDao.insertData(DbUtil.getConnection(), dataMap, "INSERT_USER_AUDIT_QUERY");
							
							AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
							Map<String, Object> args = new HashMap<String, Object>();
							Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(RESET_USER_PASSWORD_QUERY);

							String UPDATE_QRY = queryConf.getQueryString();
							String UPDATE_QRY_PARAMS = queryConf.getQueryParam();
							System.out.println(UPDATE_QRY + " -----" + UPDATE_QRY_PARAMS);
							Map<String, Object> PARAM_VALUE_MAP = new HashMap<String, Object>();
							
							/////////// START  RAMA ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
							
								String passwordToDB = "";
								try {
									passwordToDB = PasswordGeneratorUtil.generateStorngPasswordHash(newPassword);
								} catch (NoSuchAlgorithmException  | InvalidKeySpecException e) {
									e.printStackTrace();
								}
								String password = passwordToDB;
						
				          /////////// END  RAMA ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
								
							PARAM_VALUE_MAP.put("password", password);
							Calendar calendar = Calendar.getInstance();
							PARAM_VALUE_MAP.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
							CustomerDao customerDao = new CustomerDao();
							PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
							Integer pwdAge = loadPasswordPolicy.getPwdMaxAge();
							calendar.add(Calendar.MONTH, pwdAge);
							PARAM_VALUE_MAP.put("pwd_exp_date", new Timestamp(calendar.getTimeInMillis()));
							PARAM_VALUE_MAP.put("account_status", account_status);
							PARAM_VALUE_MAP.put("email_id", email_id);
							PARAM_VALUE_MAP.put("user_id", userId);

							args.put("UPDATE_QRY", UPDATE_QRY);
							args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAMS);
							args.put("PARAM_VALUE_MAP", PARAM_VALUE_MAP);

							UpdateRegulator updateRegulator = new UpdateRegulator();
							int updated = updateRegulator.update(args);
							System.out.println(" --------" + updated);

							if (updated >= 1) {
								updateAuthToken(unique_id);
								String msg = "Your Password has been reset successfully,login again..!!";
								respMap.put("SUCCESS", msg);
							} else {
								String msg = "Reset your password once again..!!";
								respMap.put("ERR_MSG", msg);

							}

						} else {
							String msg = "New password and confirm password don't match..!!";
							respMap.put("ERR_MSG", msg);
						}
					} else {
						String msg = " Confirm password should not be empty..!!";
						respMap.put("ERR_MSG", msg);
					}
				} else {
					String msg = "New password  should not be empty..!!";
					respMap.put("ERR_MSG", msg);
				}

				//return respMap;
			}else{
				
				updateAuthToken(unique_id);
				String msg = "Link has been Expired/Deactivated...!";
				respMap.put("ERR_MSG", msg);
			}
		}else{
			String msg = "Link has been Expired/Deactivated...!";
			respMap.put("ERR_MSG", msg);
		}
	}
		
		return respMap;
		//////////CHECK CRITERIA FOR RESET PASSWORD END ///////////
		
	
	}
	public Map<String, Object> resetPasswordUsingUser_id(Map<String, Object> criteriaMap) throws Exception {

		Map<String, Object> respMap = new HashMap<String, Object>();

		String user_Id = (String) criteriaMap.get("user_Id");
		String oldPassword = (String) criteriaMap.get("oldPassword");
		String newPassword = (String) criteriaMap.get("newPassword");
		String confirmPassword = (String) criteriaMap.get("confirmPassword");
		
		Map<String, Object> args = new HashMap<String, Object>();
		Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
				.getQueryConf(CHANGE_USER_PASSWORD_QUERY);
		if (oldPassword != null) {
			//VALIDATE_RESET_PASSWORD
			Query queryConf1 = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf("VALIDATE_RESET_PASSWORD");
			List<Map<String, Object>>  outData=CustomerDao.retrieveData(DbUtil.getConnection(), queryConf1, criteriaMap);
			
			if(outData!=null&&outData.size()>0){
				
				Map<String, Object> outDataMap=outData.get(0);
				//String dbUser=outDataMap.get("user_name")!=null?outDataMap.get("user_name").toString():null;
				String dbUser=outDataMap.get("user_id")!=null?outDataMap.get("user_id").toString():null;
				String dbPass=outDataMap.get("password")!=null?outDataMap.get("password").toString():null;
				
				/////////////  RAMA ADDED PASSWORD DECRYPTION CODE START ////////////////////

				boolean password_Check_flag = PasswordGeneratorUtil.validatePassword(oldPassword, dbPass);
				
				System.out.println("password_Check_flag  "+password_Check_flag);
				
				/////////////  RAMA ADDED PASSWORD DECRYPTION CODE END ////////////////////
				/////////// START  RAMA ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
				
				String passwordToDB = "";
				try {
					passwordToDB = PasswordGeneratorUtil.generateStorngPasswordHash(newPassword);
				} catch (NoSuchAlgorithmException  | InvalidKeySpecException e) {
					e.printStackTrace();
				}
				String password_after_encrypt = passwordToDB;
		
          /////////// END  RAMA ADDED PASSWORD ENCRYPTION  ON 25oct, 2018 /////////////
				
			
				if((dbUser!=null&&dbUser.equals(user_Id))&&password_Check_flag ==true){  //(dbPass!=null&&dbPass.equals(oldPassword))
					if (newPassword != null) {
						if (confirmPassword != null) {

							if (!oldPassword.equals(newPassword)) {
								if (newPassword.equals(confirmPassword)) {
									PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
									String pwd = newPassword;
									boolean flagupper = true;
									boolean flagnumber = true;
									boolean flagSpecial = true;
									if(loadPasswordPolicy.getIsUpperCaseAllowed().equals("Y"))
									{
										Pattern textPattern = Pattern.compile("^(?=.*[A-Z]).+$");
										flagupper = textPattern.matcher(pwd).matches();
										
									} if(loadPasswordPolicy.getIsNumbersAllowed().equals("Y"))
									{
										Pattern textPattern = Pattern.compile("^(?=.*\\d).+$");
										flagnumber = textPattern.matcher(pwd).matches();
										
									} if(loadPasswordPolicy.getIsSpecialCharsAllowed().equals("Y"))
									{
										Pattern special = Pattern.compile ("["+loadPasswordPolicy.getSpecialChars()+"]");
										//flagSpecial = textPattern.matcher(pwd).matches();
										 Matcher hasSpecial = special.matcher(pwd);
										 flagSpecial = hasSpecial.find();
									}
									if(flagupper == false) {
										String msg = "Password Should Contain Atleast One UppserCase...!";
										respMap.put("ERR_MSG", msg);
									} if(flagnumber == false) {
										String msg = "Password Should Contain Atleast One Number...!";
										respMap.put("ERR_MSG", msg);
									} if(flagSpecial == false) {
										String msg = "Password Should Contain Atleast One Special Character...!";
										respMap.put("ERR_MSG", msg);
									}
									if(flagupper == true && flagnumber == true && flagSpecial == true){
										
										String UPDATE_QRY = queryConf.getQueryString();
										String UPDATE_QRY_PARAMS = queryConf.getQueryParam();
										System.out.println(UPDATE_QRY + " -----" + UPDATE_QRY_PARAMS);
										Map<String, Object> PARAM_VALUE_MAP = new HashMap<String, Object>();
										PARAM_VALUE_MAP.put("password", password_after_encrypt);
										Calendar calendar = Calendar.getInstance();
										PARAM_VALUE_MAP.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
										CustomerDao customerDao = new CustomerDao();
										//PasswordPolicy loadPasswordPolicy = customerDao.loadPasswordPolicy();
										Integer pwdAge = loadPasswordPolicy.getPwdMaxAge();
										calendar.add(Calendar.MONTH, pwdAge);
										PARAM_VALUE_MAP.put("pwd_exp_date", new Timestamp(calendar.getTimeInMillis()));
										PARAM_VALUE_MAP.put("user_id", user_Id);

										args.put("UPDATE_QRY", UPDATE_QRY);
										args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAMS);
										args.put("PARAM_VALUE_MAP", PARAM_VALUE_MAP);
										System.out.println(" GOING TO UPDATE THE PASSWORD" + args);
										UpdateRegulator updateRegulator = new UpdateRegulator();
										int updated = updateRegulator.update(args);
										System.out.println(" --------" + updated);

										if (updated >= 1) {
											

											String msg = "Your Password has been reset successfully,login again..!!";
											respMap.put("SUCCESS", msg);
										} else {
											String msg = "Reset your password once again..!!";
											respMap.put("ERR_MSG", msg);

										}
									}

									

								} else {
									String msg = "New password and confirm password don't match..!!";
									respMap.put("ERR_MSG", msg);
								}

							} else {
								String msg = "Old Password and New Password Should Be Different..!!";
								respMap.put("ERR_MSG", msg);
							}

						} else {
							String msg = " Confirm password should not be empty..!!";
							respMap.put("ERR_MSG", msg);
						}
					} else {
						String msg = "New password  should not be empty..!!";
						respMap.put("ERR_MSG", msg);
					}
				}else{
					String msg = "Incorrect Old Password!!";
					respMap.put("ERR_MSG", msg);
			    }
		}else{
			String msg =user_Id+ " : is not valid user Contact Admin";
			respMap.put("ERR_MSG", msg);
		}
				
			
			
			
			
		} else {
			String msg = " Old password should not be empty..!!";
			respMap.put("ERR_MSG", msg);
		}

		return respMap;

	}
	
	public boolean authenticateUserAuthCode(Map <String, Object>dataMap){
		boolean authStatus=false;
		List<Map<String, Object>> outData=null;;
		try {
			System.out.println("dataMap   "+dataMap);
			Map<String, Object> mapData1=customerDao.getDataBaseMap("FETCH_UNIQUE_CODE_FOREMAIL_AUTH", dataMap);
			String unique_id1=	mapData1.get("unique_id").toString();
			System.out.println("unique_id1   "+unique_id1);
			//String unique_id1=	dataMap.get("unique_id").toString(); // RAMA COMMENTED ONE LINE
			//int DataList=customerDao.getDataBaseMap("FETCH_UNIQUE_CODE_FOREMAIL_AUTH", dataMap);
			
	/*		try {
			//	outData = customerDao.getData(dataMap, "FETCH_UNIQUE_CODE_FOREMAIL_AUTH");
			} catch (ClassNotFoundException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}*/
			
			Map<String, Object> mapData=customerDao.getDataBaseMap("FETCH_UNIQUE_CODE_FOREMAIL_AUTH", dataMap);
			if(mapData!=null&&!mapData.isEmpty()){
			String unique_id=	mapData.get("unique_id").toString();
			String status=	mapData.get("status").toString();
			String user_id=	mapData.get("user_id").toString();
			if(unique_id.equalsIgnoreCase(unique_id1)){
				authStatus=true;
		
			}
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			authStatus=false;
		}
		return authStatus;
	}
	
	public boolean updateAuthCodeForEmail(Map <String, Object>dataMap) throws Exception{
		Map<String, Object> args = new HashMap<String, Object>();
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf("UPDATE_UNIQUE_CODE_FOREMAIL_AUTH");
			String UPDATE_QRY = queryConf.getQueryString();
			String UPDATE_QRY_PARAMS = queryConf.getQueryParam();
			
			System.out.println(UPDATE_QRY + " -----" + UPDATE_QRY_PARAMS);
			Map<String, Object> PARAM_VALUE_MAP = new HashMap<String, Object>();
			PARAM_VALUE_MAP.putAll(dataMap);
			Calendar calendar = Calendar.getInstance();
			PARAM_VALUE_MAP.put("updated_on", new Timestamp(calendar.getTimeInMillis()));
			args.put("UPDATE_QRY", UPDATE_QRY);
			args.put("UPDATE_QRY_PARAMS", UPDATE_QRY_PARAMS);
			args.put("PARAM_VALUE_MAP", PARAM_VALUE_MAP);
			 
			UpdateRegulator updateRegulator = new UpdateRegulator();
			int updated = updateRegulator.update(args);
			System.out.println("updated count"+updated);
			if(updated>0){
				return true;
			}else if(updated==0){
				return false;
			}else{
				return false;
			}
			
			 
		 
	}

	
	public Map getDataBasedOnUniqueId(String unique_id,String email_id,String userId) {

		List datalist = new ArrayList();
		Map<String, Object> paramValues = new HashMap<String, Object>();
		paramValues.put("unique_id", unique_id);
		paramValues.put("email_id", email_id);
		paramValues.put("user_id", userId);
		
		CustomerDao cDao = new CustomerDao();
		Map<String, Object> IdMap = null;
		try {
			IdMap = cDao.getDataBaseMap("GETRESET_MAIL_DATE_BASEDON_UNIQUEID", paramValues);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return IdMap;
	}
	
	public int updateAuthToken( String unique_auth_id) throws Exception{
		int status= 0;
			Map <String, Object>dataMap= new HashMap<String, Object>();
			
			dataMap.put("updated_on",new Timestamp(new java.util.Date().getTime())) ;
			dataMap.put("unique_id", unique_auth_id);
			
			
			Connection connection = DbUtil.getConnection();
			CustomerDao  customerDao = new CustomerDao();
			try {
				customerDao.update(connection, dataMap, "UPDATE_RESET_AFTER_SUBMIT");
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		 
			return status;
		}
}
