package com.ascent.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

 

public class Departments implements Serializable{
/**
	 * 
	 */
	private static final long serialVersionUID = 1703359156585240888L;

List<Department> departments=new ArrayList<Department>();

Map<Integer,Department> departmentMap=new HashMap<Integer, Department>();
Map<String,Department> departmentWithNameMap=new HashMap<String, Department>();

public void init(){
	if(this.departments!=null){
		for(Department department:this.departments){
			department.init();
			departmentMap.put(department.getId(), department);
			departmentWithNameMap.put(department.getDeptName(), department);
		}
	}
}


public Department getDepartment(Integer id){
	return departmentMap.get(id);
}

public Department getDepartment(String  name){
	return departmentWithNameMap.get(name);
}

public List<Department> getDepartments() {
	return departments;
}

public void setDepartments(List<Department> departments) {
	this.departments = departments;
}

@Override
public int hashCode() {
	final int prime = 31;
	int result = 1;
	result = prime * result
			+ ((departments == null) ? 0 : departments.hashCode());
	return result;
}

@Override
public boolean equals(Object obj) {
	if (this == obj)
		return true;
	if (obj == null)
		return false;
	if (getClass() != obj.getClass())
		return false;
	Departments other = (Departments) obj;
	if (departments == null) {
		if (other.departments != null)
			return false;
	} else if (!departments.equals(other.departments))
		return false;
	return true;
}


}
